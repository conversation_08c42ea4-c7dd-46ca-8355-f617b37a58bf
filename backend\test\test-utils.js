/**
 * Test utilities for the JustPrototype backend
 */

/**
 * Creates a mock Express request object
 */
const mockRequest = (options = {}) => {
  const {
    body = {},
    params = {},
    query = {},
    headers = {},
    method = 'GET',
    url = '/',
    user = null,
    session = {},
    get = (header) => headers[header.toLowerCase()],
  } = options;

  return {
    body,
    params,
    query,
    headers,
    method,
    url,
    user,
    session,
    get,
  };
};

/**
 * Creates a mock Express response object
 */
const mockResponse = () => {
  const res = {};
  
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.setHeader = jest.fn().mockReturnValue(res);
  res.write = jest.fn().mockReturnValue(true);
  res.end = jest.fn().mockReturnValue(res);
  res.flushHeaders = jest.fn().mockReturnValue(res);
  
  return res;
};

/**
 * Creates a mock next function for Express middleware
 */
const mockNext = () => jest.fn();

/**
 * Creates a mock AST node for testing
 */
const createMockASTNode = (overrides = {}) => ({
  id: 'test-node-1',
  type: 'div',
  props: {
    className: 'test-class',
    style: { color: 'red' },
  },
  children: [],
  ...overrides,
});

module.exports = {
  mockRequest,
  mockResponse,
  mockNext,
  createMockASTNode
};
