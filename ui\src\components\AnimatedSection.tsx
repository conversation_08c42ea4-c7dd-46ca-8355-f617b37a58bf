import { ReactNode, useEffect, useRef } from 'react';
import '../styles/animations.css';

type AnimationType = 'fade-in' | 'slide-up' | 'slide-left' | 'slide-right' | 'scale-up';

interface AnimatedSectionProps {
  children: ReactNode;
  animation?: AnimationType;
  delay?: number;
  threshold?: number;
  className?: string;
}

export function AnimatedSection({
  children,
  animation = 'fade-in',
  delay = 0,
  threshold = 0.2,
  className = '',
}: AnimatedSectionProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Add a small delay before adding the active class
            setTimeout(() => {
              entry.target.classList.add('active');
            }, delay);
            
            // Unobserve after animation is triggered
            observer.unobserve(entry.target);
          }
        });
      },
      {
        root: null, // viewport
        rootMargin: '0px',
        threshold: threshold, // trigger when 20% of the element is visible
      }
    );
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [delay, threshold]);
  
  // Map animation type to CSS class
  const getAnimationClass = () => {
    switch (animation) {
      case 'fade-in':
        return 'reveal';
      case 'slide-up':
        return 'reveal';
      case 'slide-left':
        return 'reveal-left';
      case 'slide-right':
        return 'reveal-right';
      case 'scale-up':
        return 'reveal scale-up';
      default:
        return 'reveal';
    }
  };
  
  return (
    <div ref={sectionRef} className={`${getAnimationClass()} ${className}`}>
      {children}
    </div>
  );
}
