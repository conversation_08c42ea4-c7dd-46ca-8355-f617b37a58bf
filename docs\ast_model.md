# AST Model Documentation

## Overview

The AST (Abstract Syntax Tree) model provides a structured representation of UI components. The model supports immutable versioning, validation, and efficient tree operations.

## Schema Structure

```json
{
  "type": "object",
  "required": ["id", "type", "props"],
  "properties": {
    "id": { "type": "string" },
    "type": { 
      "enum": ["Page", "Section", "Button", "Input", "Text", "Image", "Link", "Form"]
    },
    "props": { "type": "object" },
    "children": { "type": "array", "items": { "$ref": "#" } }
  }
}
```

## Core Services

### ASTStore

Singleton service managing the UI tree state:

- `getCurrentAST()`: Get current tree state
- `setCurrentAST(ast)`: Update tree state
- `findNodeById(id)`: Find node by ID
- `addChild(parentId, child)`: Add child to parent
- `updateNodeProps(id, props)`: Update node properties
- `removeNode(id)`: Remove node from tree
- `saveSnapshot(message)`: Create version
- `restoreSnapshot(id)`: Restore version
- `listSnapshots()`: List versions

### SchemaValidator

JSON Schema validation service:

- `validateNode(node)`: Validate single node
- `validateTree(node)`: Validate entire tree
- `isValidNodeType(type)`: Check type validity

## React Integration

### useAst Hook

React hook for component integration:

```typescript
const {
  ast,              // Current AST
  error,            // Validation errors
  updateAst,        // Update tree
  addChild,         // Add node
  updateNodeProps,  // Update props
  removeNode,       // Remove node
  saveSnapshot,     // Save version
  restoreSnapshot,  // Restore version
  listSnapshots     // List versions
} = useAst();
```

## Version Management

Each AST version includes:
- Unique version ID
- Complete AST snapshot
- Timestamp
- Optional commit message

## Best Practices

1. Always validate before updates
2. Use immutable operations
3. Save snapshots for significant changes
4. Handle validation errors gracefully
5. Use type-safe operations through TypeScript interfaces

## Example Usage

```typescript
// Create a new button
const button = {
  id: 'button1',
  type: 'Button',
  props: {
    className: 'primary',
    text: 'Click Me'
  }
};

// Add to parent section
const { addChild } = useAst();
addChild('section1', button);

// Update properties
const { updateNodeProps } = useAst();
updateNodeProps('button1', { disabled: true });

// Save version
const { saveSnapshot } = useAst();
saveSnapshot('Added disabled button');
```

## Implementation Notes

1. The JSON Schema uses 2020-12 draft specification
2. Tree operations maintain referential integrity
3. Version history is kept in-memory
4. All operations trigger validation
5. Props allow arbitrary properties
6. Node types are strictly enforced
7. IDs must be unique across tree

## Future Enhancements

1. Persistent storage integration
2. Diff visualization
3. Undo/redo operations
4. Tree traversal utilities
5. Performance optimizations for large trees
