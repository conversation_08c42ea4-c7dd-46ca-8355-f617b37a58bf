/**
 * Comprehensive tests for EditorPageV3Refactored component
 * Tests all core functionality including page management, creation, and rendering
 */

import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import React from 'react';
import EditorPageV3Refactored from '../pages/EditorPageV3Refactored';

// Mock the useEditorV3 hook
const mockActions = {
  generateFromPrompt: vi.fn(),
  setStreamingContent: vi.fn(),
  setHtmlContent: vi.fn(),
  setCurrentPageId: vi.fn(),
  setIsGenerating: vi.fn(),
};

const mockState = {
  isGenerating: false,
  htmlContent: '',
  stableIframeContent: '',
  streamingContent: '',
  currentPageId: null,
  selectedElement: null,
  pages: [],
};

vi.mock('../hooks/useEditorV3', () => ({
  useEditorV3: () => ({
    state: mockState,
    actions: mockActions,
  }),
}));

// Mock services
vi.mock('../services/pageGenService', () => ({
  getSession: vi.fn(),
  getPageList: vi.fn(),
  getProjectList: vi.fn(),
  updatePage: vi.fn(),
  deletePage: vi.fn(),
}));

vi.mock('../services/pageCreationService', () => ({
  createPageWithCleanName: vi.fn(),
  generatePageContentPrompt: vi.fn(),
}));

vi.mock('../services/promptEnhancementService', () => ({
  previewEnhancement: vi.fn(),
}));

vi.mock('../services/elementSelectorService', () => ({
  createElementSelectorManager: vi.fn(() => ({
    activate: vi.fn(),
    deactivate: vi.fn(),
    cleanup: vi.fn(),
  })),
  extractEditParameters: vi.fn(),
}));

// Mock components
vi.mock('../components/ProgressiveRenderer', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="progressive-renderer">{children}</div>,
}));

vi.mock('../components/SPAShell', () => ({
  SPAShell: ({ children }: { children: React.ReactNode }) => <div data-testid="spa-shell">{children}</div>,
}));

vi.mock('../components/Editor/ErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="error-boundary">{children}</div>,
}));

vi.mock('../components/Editor/ModalDialogs', () => ({
  default: () => <div data-testid="modal-dialogs" />,
}));

vi.mock('../components/Editor/ImplementationModal', () => ({
  default: () => <div data-testid="implementation-modal" />,
}));

vi.mock('../components/Editor/EditModeControls', () => ({
  default: () => <div data-testid="edit-mode-controls" />,
}));

vi.mock('../components/shared/PromptInput', () => ({
  PromptInput: ({ onSubmit }: { onSubmit: (text: string) => void }) => (
    <div data-testid="prompt-input">
      <textarea data-testid="prompt-textarea" />
      <button data-testid="submit-button" onClick={() => onSubmit('test prompt')}>
        Submit
      </button>
    </div>
  ),
}));

vi.mock('../components/shared/PageSidebar', () => ({
  PageSidebar: ({ pages, onPageSelect }: { pages: any[]; onPageSelect: (page: any) => void }) => (
    <div data-testid="page-sidebar">
      {pages.map((page) => (
        <button
          key={page.id}
          data-testid={`page-${page.id}`}
          onClick={() => onPageSelect(page)}
        >
          {page.name}
        </button>
      ))}
    </div>
  ),
}));

vi.mock('../components/PlanDisplay', () => ({
  PlanDisplay: () => <div data-testid="plan-display" />,
}));

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock router params
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ projectId: '123' }),
    useLocation: () => ({ state: null, pathname: '/editor-v3-refactored' }),
    useNavigate: () => vi.fn(),
  };
});

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('EditorPageV3Refactored', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset mock state
    Object.assign(mockState, {
      isGenerating: false,
      htmlContent: '',
      stableIframeContent: '',
      streamingContent: '',
      currentPageId: null,
      selectedElement: null,
      pages: [],
    });

    // Mock successful API responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ pages: [], projects: [] }),
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Component Rendering', () => {
    test('should render without crashing', () => {
      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });

    test('should render main layout components', () => {
      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      expect(screen.getByTestId('page-sidebar')).toBeInTheDocument();
      expect(screen.getByTestId('prompt-input')).toBeInTheDocument();
    });
  });

  describe('Display Mode Logic', () => {
    test('should show generation UI when isGenerating is true', () => {
      mockState.isGenerating = true;
      
      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Should show progressive renderer during generation
      expect(screen.getByTestId('progressive-renderer')).toBeInTheDocument();
    });

    test('should show content when page is selected and has content', () => {
      mockState.currentPageId = 'page-1';
      mockState.htmlContent = '<div>Test content</div>';
      
      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      expect(screen.getByTestId('spa-shell')).toBeInTheDocument();
    });

    test('should show new page creation UI when no pages exist', async () => {
      // Mock empty project pages response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ pages: [] }),
      });

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('prompt-input')).toBeInTheDocument();
      });
    });
  });

  describe('Page Management', () => {
    test('should handle page selection', async () => {
      const mockPages = [
        { id: 1, name: 'Page 1', content: '<div>Page 1 content</div>' },
        { id: 2, name: 'Page 2', content: '<div>Page 2 content</div>' },
      ];

      // Mock page list response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ pages: mockPages }),
      });

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('page-1')).toBeInTheDocument();
      });

      // Click on page 1
      fireEvent.click(screen.getByTestId('page-1'));

      // Should trigger page selection logic
      await waitFor(() => {
        expect(mockActions.setCurrentPageId).toHaveBeenCalled();
      });
    });

    test('should handle new page creation', async () => {
      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Click submit button to create new page
      fireEvent.click(screen.getByTestId('submit-button'));

      await waitFor(() => {
        expect(mockActions.generateFromPrompt).toHaveBeenCalledWith('test prompt');
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      // Mock API error
      mockFetch.mockRejectedValueOnce(new Error('API Error'));

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Component should still render without crashing
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });

    test('should handle missing project ID', () => {
      // Mock useParams to return no projectId
      vi.mocked(require('react-router-dom').useParams).mockReturnValue({});

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Should show project selector
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });
  });
});
