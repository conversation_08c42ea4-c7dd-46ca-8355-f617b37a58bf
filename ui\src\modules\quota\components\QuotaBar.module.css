.barContainer {
  width: 100%;
  margin-bottom: 0.5rem;
}

.clickable {
  cursor: pointer;
}

.barLabels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.progressBar {
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.3s ease;
}

.freeBar {
  background-color: #0ea5e9;
}

.proBar {
  background-color: #4f46e5;
}

.warningBar {
  background-color: #f59e0b;
}

.exceededBar {
  background-color: #ef4444;
}

.remainingLabel {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.warningLabel {
  color: #92400e;
  font-weight: 500;
}

.exceededLabel {
  color: #b91c1c;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upgradeButton {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.125rem 0.375rem;
  font-size: 0.625rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.upgradeButton:hover {
  background-color: #dc2626;
}
