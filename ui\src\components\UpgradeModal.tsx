import React from "react";

export interface UpgradeModalProps {
  open: boolean;
  onClose: () => void;
  onUpgrade?: () => void;
  onBuyCredits?: () => void;
}

export const UpgradeModal: React.FC<UpgradeModalProps> = ({
  open,
  onClose,
  onUpgrade,
  onBuyCredits,
}) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className="bg-white dark:bg-zinc-900 rounded-xl shadow-lg p-8 max-w-md w-full relative">
        <button
          className="absolute top-2 right-2 text-zinc-400 hover:text-zinc-700 dark:hover:text-zinc-200 text-xl"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
        <h2 className="text-2xl font-bold mb-2 text-center">Upgrade Required</h2>
        <p className="mb-4 text-center text-zinc-600 dark:text-zinc-300">
          You've reached your free prototype limit. Buy more credits or upgrade to unlock unlimited prototyping and more features!
        </p>
        <div className="flex flex-col gap-3">
          <button
            className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded transition"
            onClick={onBuyCredits}
          >
            Buy credits
          </button>
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded transition"
            onClick={onUpgrade}
          >
            Upgrade to Pro
          </button>
        </div>
        <div className="text-xs text-zinc-500 mt-4 text-center">
          Need more info?{" "}
          <a
            href="#"
            className="text-blue-600 underline hover:text-blue-800"
            onClick={onUpgrade}
          >
            View Plans
          </a>
        </div>
      </div>
    </div>
  );
};
