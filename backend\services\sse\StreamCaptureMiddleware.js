/**
 * Middleware to capture stream output and convert to SSE events
 */
class StreamCaptureMiddleware {
  constructor(options = {}) {
    this.bufferData = options.bufferData ?? true;
    this.bufferSize = options.bufferSize || 1024;
    this.data = '';
  }

  getStream() {
    const writableStream = new WritableStream({
      write: (chunk) => {
        if (this.bufferData) {
          this.data += chunk;
          if (this.data.length >= this.bufferSize) {
            this.flushData();
          }
        }
      },
      close: () => {
        if (this.bufferData && this.data.length > 0) {
          this.flushData();
        }
      }
    });

    return writableStream;
  }

  flushData() {
    if (this.data.length > 0) {
      try {
        // Try to parse as JSON first
        const jsonData = JSON.parse(this.data);
        this.emit('data', jsonData);
      } catch (e) {
        // If not JSON, emit as text
        this.emit('data', this.data);
      }
      this.data = '';
    }
  }

  emit(event, data) {
    if (typeof data === 'object') {
      data = JSON.stringify(data);
    }
    
    return `event: ${event}\ndata: ${data}\n\n`;
  }
}

module.exports = StreamCaptureMiddleware;
