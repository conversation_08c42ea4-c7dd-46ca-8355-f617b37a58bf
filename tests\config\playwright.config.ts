import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './tests/config/test.env' });

/**
 * Playwright configuration for JustPrototype E2E tests
 * 
 * Features:
 * - Cross-browser testing (Chrome, Firefox, Safari)
 * - Parallel execution for faster test runs
 * - Video recording and screenshots on failure
 * - Network interception for LLM API mocking
 * - Multiple test environments support
 */
export default defineConfig({
  // Test directory
  testDir: '../e2e/specs',
  
  // Global test timeout (5 minutes for LLM operations)
  timeout: 5 * 60 * 1000,
  
  // Expect timeout for assertions
  expect: {
    timeout: 30 * 1000,
  },
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter configuration
  reporter: [
    ['html', { outputFolder: '../reports/html' }],
    ['json', { outputFile: '../reports/results.json' }],
    ['junit', { outputFile: '../reports/junit.xml' }],
    process.env.CI ? ['github'] : ['list']
  ],
  
  // Global setup and teardown
  globalSetup: require.resolve('../e2e/fixtures/global-setup.ts'),
  globalTeardown: require.resolve('../e2e/fixtures/global-teardown.ts'),
  
  // Shared settings for all projects
  use: {
    // Base URL for tests
    baseURL: process.env.TEST_BASE_URL || 'http://localhost:5173',
    
    // API base URL
    extraHTTPHeaders: {
      'X-Test-Mode': 'true'
    },
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Record video on failure
    video: 'retain-on-failure',
    
    // Take screenshot on failure
    screenshot: 'only-on-failure',
    
    // Ignore HTTPS errors
    ignoreHTTPSErrors: true,
    
    // Default navigation timeout
    navigationTimeout: 30 * 1000,
    
    // Default action timeout
    actionTimeout: 15 * 1000,
  },

  // Configure projects for major browsers
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      use: { ...devices['Desktop Chrome'] },
    },
    
    // Chrome tests
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Use prepared auth state
        storageState: '../reports/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Firefox tests
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        storageState: '../reports/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Safari tests (WebKit)
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        storageState: '../reports/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Mobile Chrome
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['Pixel 5'],
        storageState: '../reports/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Mobile Safari
    {
      name: 'Mobile Safari',
      use: { 
        ...devices['iPhone 12'],
        storageState: '../reports/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // API testing
    {
      name: 'api',
      testMatch: /.*\.api\.spec\.ts/,
      use: {
        baseURL: process.env.TEST_API_BASE_URL || 'http://localhost:3001/api',
      },
    },
  ],

  // Run your local dev server before starting the tests
  webServer: process.env.CI ? undefined : [
    {
      command: 'npm run dev',
      cwd: '../ui',
      port: 5173,
      reuseExistingServer: !process.env.CI,
      timeout: 120 * 1000,
    },
    {
      command: 'npm run dev',
      cwd: '../backend',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      timeout: 120 * 1000,
    },
  ],

  // Test output directory
  outputDir: '../reports/test-results',
  
  // Global test configuration
  globalTimeout: 60 * 60 * 1000, // 1 hour for entire test suite
  
  // Metadata
  metadata: {
    testType: 'e2e',
    environment: process.env.TEST_ENVIRONMENT || 'local',
    version: process.env.npm_package_version || '1.0.0',
  },
});
