import React, { useState, useEffect } from 'react';
import styles from './QuotaModule.module.css';
import { QuotaBar } from './components/QuotaBar';
import { QuotaModal } from './components/QuotaModal';
import { QuotaBadge } from './components/QuotaBadge';

export interface QuotaInfo {
  /**
   * User's current plan (e.g., "free", "pro")
   */
  plan: string;

  /**
   * Total number of items allowed
   */
  totalCount: number;

  /**
   * Number of items used
   */
  usedCount: number;

  /**
   * Number of items remaining
   */
  remainingCount: number;

  /**
   * Optional additional quota information
   */
  [key: string]: any;
}

interface QuotaModuleProps {
  /**
   * Quota information
   */
  quota: QuotaInfo;

  /**
   * Function to refresh quota information
   */
  refreshQuota?: () => Promise<void>;

  /**
   * URL to upgrade page or checkout
   */
  upgradeUrl?: string;

  /**
   * Optional custom upgrade handler
   */
  onUpgrade?: () => void;

  /**
   * Optional display mode
   */
  displayMode?: 'badge' | 'bar' | 'full';

  /**
   * Optional item name (e.g., "prototype", "credit")
   */
  itemName?: string;

  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * A reusable quota display and management module
 */
export const QuotaModule: React.FC<QuotaModuleProps> = ({
  quota,
  refreshQuota,
  upgradeUrl = "https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa",
  onUpgrade,
  displayMode = 'full',
  itemName = 'prototype',
  className = ''
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Calculate percentage used
  const percentUsed = Math.min(100, Math.round((quota.usedCount / quota.totalCount) * 100));

  // Determine if quota is exceeded or low
  const isExceeded = quota.remainingCount <= 0;
  const isLow = !isExceeded && quota.remainingCount <= Math.max(1, Math.floor(quota.totalCount * 0.1));

  // Handle upgrade click
  const handleUpgrade = () => {
    setLoading(true);

    if (onUpgrade) {
      onUpgrade();
    } else if (upgradeUrl) {
      window.open(upgradeUrl, '_blank', 'noopener,noreferrer');
    } else {
      // Default payment URL if none provided
      window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');
    }

    setModalOpen(false);
  };

  // Refresh quota periodically
  useEffect(() => {
    if (!refreshQuota) return;

    const intervalId = setInterval(() => {
      refreshQuota();
    }, 30000); // 30 seconds

    return () => clearInterval(intervalId);
  }, [refreshQuota]);

  // Render appropriate component based on display mode
  const renderQuotaComponent = () => {
    switch (displayMode) {
      case 'badge':
        return (
          <QuotaBadge
            quota={quota}
            itemName={itemName}
            onClick={() => setModalOpen(true)}
          />
        );

      case 'bar':
        return (
          <QuotaBar
            quota={quota}
            itemName={itemName}
            onClick={() => setModalOpen(true)}
          />
        );

      case 'full':
      default:
        return (
          <div className={styles.fullDisplay}>
            <QuotaBadge
              quota={quota}
              itemName={itemName}
              onClick={() => setModalOpen(true)}
            />
            <QuotaBar
              quota={quota}
              itemName={itemName}
              onClick={() => setModalOpen(true)}
            />
          </div>
        );
    }
  };

  return (
    <div className={`${styles.quotaModule} ${className}`}>
      {renderQuotaComponent()}

      <QuotaModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onUpgrade={handleUpgrade}
        quota={quota}
        itemName={itemName}
        loading={loading}
      />
    </div>
  );
};
