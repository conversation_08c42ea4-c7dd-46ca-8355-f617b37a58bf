/**
 * Token counting utilities using tiktoken
 */
const { get_encoding, encoding_for_model } = require('@dqbd/tiktoken');

// Model to encoding mapping
const MODEL_TO_ENCODING = {
  // OpenAI models
  'gpt-4': 'cl100k_base',
  'gpt-4-0314': 'cl100k_base',
  'gpt-4-0613': 'cl100k_base',
  'gpt-4-32k': 'cl100k_base',
  'gpt-4-32k-0314': 'cl100k_base',
  'gpt-4-32k-0613': 'cl100k_base',
  'gpt-4-turbo': 'cl100k_base',
  'gpt-4-1106-preview': 'cl100k_base',
  'gpt-4-vision-preview': 'cl100k_base',
  'gpt-3.5-turbo': 'cl100k_base',
  'gpt-3.5-turbo-0301': 'cl100k_base',
  'gpt-3.5-turbo-0613': 'cl100k_base',
  'gpt-3.5-turbo-1106': 'cl100k_base',
  'gpt-3.5-turbo-16k': 'cl100k_base',
  'gpt-3.5-turbo-16k-0613': 'cl100k_base',
  'text-embedding-ada-002': 'cl100k_base',
  // Claude models (also use cl100k_base)
  'claude-3-opus-20240229': 'cl100k_base',
  'claude-3-sonnet-20240229': 'cl100k_base',
  'claude-3-haiku-20240307': 'cl100k_base',
  'claude-3-7-sonnet-20250219': 'cl100k_base',
  // Default for other models
  'default': 'cl100k_base'
};

// Token limits for different models
const MODEL_TOKEN_LIMITS = {
  // OpenAI models
  'gpt-4': 8192,
  'gpt-4-0314': 8192,
  'gpt-4-0613': 8192,
  'gpt-4-32k': 32768,
  'gpt-4-32k-0314': 32768,
  'gpt-4-32k-0613': 32768,
  'gpt-4-turbo': 128000,
  'gpt-4-1106-preview': 128000,
  'gpt-4-vision-preview': 128000,
  'gpt-3.5-turbo': 4096,
  'gpt-3.5-turbo-0301': 4096,
  'gpt-3.5-turbo-0613': 4096,
  'gpt-3.5-turbo-1106': 16385,
  'gpt-3.5-turbo-16k': 16385,
  'gpt-3.5-turbo-16k-0613': 16385,
  // Claude models
  'claude-3-opus-20240229': 200000,
  'claude-3-sonnet-20240229': 200000,
  'claude-3-haiku-20240307': 200000,
  'claude-3-7-sonnet-20250219': 200000,
  // DeepSeek models
  'deepseek-chat': 8192,
  // Default
  'default': 4096
};

/**
 * Get the encoding for a specific model
 * @param {string} model - The model name
 * @returns {object} - The encoding object
 */
function getEncodingForModel(model) {
  try {
    const encodingName = MODEL_TO_ENCODING[model] || MODEL_TO_ENCODING.default;
    return get_encoding(encodingName);
  } catch (error) {
    console.error(`[TokenUtils] Error getting encoding for model ${model}:`, error);
    // Fallback to cl100k_base
    return get_encoding('cl100k_base');
  }
}

/**
 * Count tokens in a text string for a specific model
 * @param {string} text - The text to count tokens for
 * @param {string} model - The model name
 * @returns {number} - The number of tokens
 */
function countTokens(text, model = 'default') {
  if (!text) return 0;
  
  try {
    const encoding = getEncodingForModel(model);
    const tokens = encoding.encode(text);
    const count = tokens.length;
    encoding.free(); // Important: free the encoding to prevent memory leaks
    return count;
  } catch (error) {
    console.error(`[TokenUtils] Error counting tokens:`, error);
    // Fallback to rough estimation: ~4 chars per token
    return Math.ceil(text.length / 4);
  }
}

/**
 * Count tokens in a messages array for chat completions
 * @param {Array} messages - The messages array
 * @param {string} model - The model name
 * @returns {number} - The number of tokens
 */
function countMessageTokens(messages, model = 'default') {
  if (!messages || !Array.isArray(messages)) return 0;
  
  try {
    let totalTokens = 0;
    
    // Count tokens in each message
    for (const message of messages) {
      // Count content tokens
      if (message.content) {
        totalTokens += countTokens(message.content, model);
      }
      
      // Add tokens for message metadata (role, etc.)
      // This is a rough approximation and may vary by model
      totalTokens += 4; // ~4 tokens per message for metadata
    }
    
    // Add tokens for the messages format (varies by model)
    totalTokens += 3; // ~3 tokens for the overall structure
    
    return totalTokens;
  } catch (error) {
    console.error(`[TokenUtils] Error counting message tokens:`, error);
    // Fallback to rough estimation
    return messages.reduce((sum, msg) => sum + Math.ceil((msg.content?.length || 0) / 4) + 4, 3);
  }
}

/**
 * Get the token limit for a specific model
 * @param {string} model - The model name
 * @returns {number} - The token limit
 */
function getModelTokenLimit(model) {
  return MODEL_TOKEN_LIMITS[model] || MODEL_TOKEN_LIMITS.default;
}

/**
 * Trim HTML content to fit within a token limit
 * @param {string} htmlContent - The HTML content to trim
 * @param {number} maxTokens - The maximum number of tokens
 * @param {string} model - The model name
 * @returns {string} - The trimmed HTML content
 */
function trimHtmlToTokenLimit(htmlContent, maxTokens, model = 'default') {
  if (!htmlContent) return '';
  
  try {
    const encoding = getEncodingForModel(model);
    const tokens = encoding.encode(htmlContent);
    
    if (tokens.length <= maxTokens) {
      encoding.free();
      return htmlContent; // No trimming needed
    }
    
    // Trim to the token limit
    const trimmedTokens = tokens.slice(0, maxTokens);
    const trimmedBytes = encoding.decode(trimmedTokens);
    encoding.free();
    
    // Convert bytes to string
    const trimmedHtml = new TextDecoder().decode(trimmedBytes);
    
    return trimmedHtml;
  } catch (error) {
    console.error(`[TokenUtils] Error trimming HTML to token limit:`, error);
    // Fallback to rough character-based trimming
    const charsPerToken = 4;
    const maxChars = maxTokens * charsPerToken;
    return htmlContent.slice(0, maxChars);
  }
}

module.exports = {
  countTokens,
  countMessageTokens,
  getModelTokenLimit,
  trimHtmlToTokenLimit
};
