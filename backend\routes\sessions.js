const express = require('express');
const router = express.Router();
const sessionService = require('../services/sessionService');

/**
 * Session management routes for Readdy-style intent generation
 */

/**
 * POST /api/sessions
 * Create a new prototype session
 */
router.post('/', async (req, res) => {
  try {
    const { prototype_id, user_id, page_url, page_html, session_state } = req.body;

    // Validate required fields
    if (!prototype_id || !user_id || !page_url || !page_html) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'Missing required fields: prototype_id, user_id, page_url, page_html'
        }
      });
    }

    // Create session
    const session = await sessionService.createSession({
      prototype_id,
      user_id,
      page_url,
      page_html,
      session_state: session_state || 'active'
    });

    res.json({
      success: true,
      session: session
    });

  } catch (error) {
    console.error('Error creating session:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_CREATION_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/sessions/:sessionId
 * Get session by ID
 */
router.get('/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.query.userId; // Optional user validation

    const session = await sessionService.getSession(sessionId, userId);

    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Session not found or expired'
        }
      });
    }

    res.json({
      success: true,
      session: session
    });

  } catch (error) {
    console.error('Error getting session:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_RETRIEVAL_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * PUT /api/sessions/:sessionId/state
 * Update session state
 */
router.put('/:sessionId/state', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { state, userId } = req.body;

    if (!state) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_STATE',
          message: 'Session state is required'
        }
      });
    }

    const updatedSession = await sessionService.updateSessionState(sessionId, state, userId);

    if (!updatedSession) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Session not found or access denied'
        }
      });
    }

    res.json({
      success: true,
      session: updatedSession
    });

  } catch (error) {
    console.error('Error updating session state:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_UPDATE_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * DELETE /api/sessions/:sessionId
 * Delete session
 */
router.delete('/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.body.userId; // Optional user validation

    const success = await sessionService.deleteSession(sessionId, userId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Session not found or access denied'
        }
      });
    }

    res.json({
      success: true,
      message: 'Session deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting session:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_DELETION_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/sessions/user/:userId
 * Get sessions for a user
 */
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit, state } = req.query;

    const options = {};
    if (limit) options.limit = parseInt(limit);
    if (state) options.state = state;

    const sessions = await sessionService.getUserSessions(parseInt(userId), options);

    res.json({
      success: true,
      sessions: sessions,
      count: sessions.length
    });

  } catch (error) {
    console.error('Error getting user sessions:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'USER_SESSIONS_RETRIEVAL_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/sessions/cleanup
 * Cleanup expired sessions (admin endpoint)
 */
router.post('/cleanup', async (req, res) => {
  try {
    const { force } = req.body;

    const cleanedCount = await sessionService.cleanupExpiredSessions({ force });

    res.json({
      success: true,
      message: `Cleaned up ${cleanedCount} expired sessions`,
      cleanedCount: cleanedCount
    });

  } catch (error) {
    console.error('Error cleaning up sessions:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_CLEANUP_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/sessions/stats
 * Get session statistics (admin endpoint)
 */
router.get('/stats', async (req, res) => {
  try {
    const { userId } = req.query;

    const stats = await sessionService.getSessionStats(userId ? parseInt(userId) : null);

    res.json({
      success: true,
      stats: stats
    });

  } catch (error) {
    console.error('Error getting session stats:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_STATS_FAILED',
        message: error.message
      }
    });
  }
});

module.exports = router;
