import React, { createContext, useContext, useState, useEffect } from "react";
import { getPrototypeQuota } from "../services/planService";

export type PlanType = "Free" | "Pay-as-you-go" | "Pro";

export interface BillingInfo {
  plan: PlanType;
  prototypeCount: number;
  prototypeLimit: number;
  tokenUsage?: number;
  tokenLimit?: number;
}

interface BillingContextType extends BillingInfo {
  refresh: () => Promise<void>;
}

const BillingContext = createContext<BillingContextType | undefined>(undefined);

export const useBilling = () => {
  const ctx = useContext(BillingContext);
  if (!ctx) throw new Error("useBilling must be used within BillingProvider");
  return ctx;
};

export const BillingProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [billing, setBilling] = useState<BillingInfo>({
    plan: "Free",
    prototypeCount: 0,
    prototypeLimit: 3,
    tokenUsage: 0,
    tokenLimit: 10000,
  });

  // Fetch billing information from the prototype quota API
  const fetchBilling = async () => {
    try {
      // Get quota information from the API
      const quotaInfo = await getPrototypeQuota();

      // Map the quota information to our billing context
      setBilling({
        plan: quotaInfo.plan === 'free' ? "Free" : "Pro",
        prototypeCount: quotaInfo.usedCount,
        prototypeLimit: quotaInfo.totalCount,
        tokenUsage: 0, // Not tracking tokens anymore
        tokenLimit: 0, // Not tracking tokens anymore
      });
    } catch (error) {
      console.error('Error fetching billing information:', error);

      // Fallback to default values if there's an error
      setBilling({
        plan: "Free",
        prototypeCount: 0,
        prototypeLimit: 3,
        tokenUsage: 0,
        tokenLimit: 0,
      });
    }
  };

  // Fetch billing information on mount and every 30 seconds
  useEffect(() => {
    // Initial fetch
    fetchBilling();

    // Set up interval to refresh quota information
    const intervalId = setInterval(() => {
      fetchBilling();
    }, 30000); // 30 seconds

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  return (
    <BillingContext.Provider
      value={{
        ...billing,
        refresh: fetchBilling,
      }}
    >
      {children}
    </BillingContext.Provider>
  );
};
