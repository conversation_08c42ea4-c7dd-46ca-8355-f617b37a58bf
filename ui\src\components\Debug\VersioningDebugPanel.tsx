/**
 * Debug panel for versioning system
 * Shows real-time versioning state and decisions
 */

import React, { useState, useEffect } from 'react';

interface VersioningDebugPanelProps {
  prototypeId: number | null;
  htmlContent: string | null;
  isGenerating: boolean;
  currentPageId: string | null;
  pages: any[];
  isVersionCreationInProgress: boolean;
  isVersionSwitching?: boolean;
}

export default function VersioningDebugPanel({
  prototypeId,
  htmlContent,
  isGenerating,
  currentPageId,
  pages,
  isVersionCreationInProgress,
  isVersionSwitching = false
}: VersioningDebugPanelProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  // Capture console logs related to versioning
  useEffect(() => {
    const originalLog = console.log;
    console.log = (...args) => {
      const message = args.join(' ');
      if (message.includes('Rule') || message.includes('versioning') || message.includes('Version')) {
        setLogs(prev => [...prev.slice(-20), `${new Date().toLocaleTimeString()}: ${message}`]);
      }
      originalLog(...args);
    };

    return () => {
      console.log = originalLog;
    };
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed top-4 right-4 z-50 bg-purple-600 text-white px-3 py-1 rounded text-xs hover:bg-purple-700"
      >
        Debug Versioning
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-gray-900">Versioning Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ×
        </button>
      </div>

      {/* Current State */}
      <div className="space-y-2 text-xs">
        <div className="bg-gray-50 p-2 rounded">
          <h4 className="font-medium text-gray-700 mb-1">Current State</h4>
          <div>Prototype ID: {prototypeId || 'None'}</div>
          <div>Current Page: {currentPageId || 'None'}</div>
          <div>Pages Count: {pages.length}</div>
          <div>Is Generating: {isGenerating ? 'Yes' : 'No'}</div>
          <div>Has HTML: {htmlContent ? 'Yes' : 'No'}</div>
          <div>HTML Length: {htmlContent?.length || 0}</div>
          <div>Version Creating: {isVersionCreationInProgress ? 'Yes' : 'No'}</div>
          <div>Version Switching: {isVersionSwitching ? 'Yes' : 'No'}</div>
        </div>

        {/* Page List */}
        <div className="bg-blue-50 p-2 rounded">
          <h4 className="font-medium text-blue-700 mb-1">Pages</h4>
          {pages.length === 0 ? (
            <div className="text-gray-500">No pages</div>
          ) : (
            pages.map((page, index) => (
              <div key={index} className={`text-xs ${page.id === currentPageId ? 'font-bold' : ''}`}>
                {index + 1}. {page.name || page.id} {page.id === currentPageId ? '(current)' : ''}
              </div>
            ))
          )}
        </div>

        {/* Recent Logs */}
        <div className="bg-green-50 p-2 rounded">
          <h4 className="font-medium text-green-700 mb-1">Recent Logs</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet</div>
            ) : (
              logs.slice(-10).map((log, index) => (
                <div key={index} className="text-xs font-mono text-gray-600">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          <button
            onClick={() => setLogs([])}
            className="px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300"
          >
            Clear Logs
          </button>
        </div>
      </div>
    </div>
  );
}
