import { createContext, useContext, ReactNode } from 'react';
import { useCounter } from '../hooks/useCounter';

interface CounterContextType {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  setValue: (value: number) => void;
}

const CounterContext = createContext<CounterContextType | undefined>(undefined);

interface CounterProviderProps {
  children: ReactNode;
  initialValue?: number;
  step?: number;
  min?: number;
  max?: number;
}

export const CounterProvider = ({
  children,
  initialValue = 0,
  step = 1,
  min = Number.MIN_SAFE_INTEGER,
  max = Number.MAX_SAFE_INTEGER,
}: CounterProviderProps) => {
  const counter = useCounter({ initialValue, step, min, max });

  return (
    <CounterContext.Provider value={counter}>
      {children}
    </CounterContext.Provider>
  );
};

// Custom hook to use the counter context
export const useCounterContext = (): CounterContextType => {
  const context = useContext(CounterContext);
  if (context === undefined) {
    throw new Error('useCounterContext must be used within a CounterProvider');
  }
  return context;
};
