.container {
  min-height: 100vh;
  background-color: #f3f4f6;
  transition: background-color 0.2s;
}

.header {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 80rem;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.main {
  max-width: 80rem;
  margin: 0 auto;
  padding: 1.5rem 1rem;
}

.content {
  padding: 1rem 0;
}

.infoCard {
  margin-top: 2rem;
  background-color: white;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
}

.infoCardContent {
  padding: 1.25rem;
}

.infoCardTitle {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
}

.infoCardText {
  margin-top: 0.75rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.featureList {
  list-style-type: disc;
  padding-left: 1.25rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.featureItem {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Dark mode styles */
.darkContainer {
  background-color: #111827;
}

.darkHeader {
  background-color: #1f2937;
}

.darkTitle {
  color: white;
}

.darkInfoCard {
  background-color: #1f2937;
}

.darkInfoCardTitle {
  color: white;
}

.darkInfoCardText {
  color: #9ca3af;
}
