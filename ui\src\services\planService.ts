const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';

/**
 * Interface for user quota information
 */
export interface QuotaInfo {
  plan: string;
  totalCount: number;
  usedCount: number;
  remainingCount: number;
}

// Cache for quota information to reduce API calls
let quotaCache: {
  data: QuotaInfo | null;
  timestamp: number;
  expiresInMs: number;
} = {
  data: null,
  timestamp: 0,
  expiresInMs: 10000 // Cache expires after 10 seconds
};

/**
 * Gets the user's prototype quota information with caching
 * @param forceRefresh - Whether to force a refresh from the server
 * @returns Promise<QuotaInfo> - The user's quota information
 */
export async function getPrototypeQuota(forceRefresh = false): Promise<QuotaInfo> {
  // Check if we have valid cached data
  const now = Date.now();
  if (!forceRefresh &&
      quotaCache.data &&
      now - quotaCache.timestamp < quotaCache.expiresInMs) {
    console.log('Using cached quota data');
    return quotaCache.data;
  }

  try {
    console.log('Fetching fresh quota data from server');
    const response = await fetch(`${API_BASE}/prototype/quota`, {
      method: 'GET',
      credentials: 'include', // Include cookies for authentication
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get prototype quota');
    }

    const data = await response.json();

    // Update cache
    quotaCache = {
      data,
      timestamp: now,
      expiresInMs: 10000
    };

    return data;
  } catch (error) {
    console.error('Error getting prototype quota:', error);
    // Return default values if there's an error
    return {
      plan: 'free',
      totalCount: 3,
      usedCount: 0,
      remainingCount: 3
    };
  }
}

/**
 * Invalidates the quota cache, forcing the next call to getPrototypeQuota to fetch fresh data
 */
export function invalidateQuotaCache(): void {
  console.log('Invalidating quota cache');
  quotaCache.timestamp = 0;
  quotaCache.data = null;
}

/**
 * Creates a new prototype and increments the user's prototype count
 * @param prototype - The prototype data to save
 * @returns Promise<Object> - The created prototype object with quota status
 */
export async function createPrototype(prototype: {
  title: string;
  description?: string;
  html: string;
  css?: string;
  preview_image_url?: string;
  prompt_id?: number;
}): Promise<{
  success: boolean;
  prototype?: any;
  quotaExceeded: boolean;
  remainingCount: number;
  plan: string;
}> {
  try {
    // If prompt_id is not provided, use the last promptId
    if (!prototype.prompt_id) {
      const lastPromptId = getLastPromptId();
      if (lastPromptId) {
        console.log(`Using last promptId: ${lastPromptId} for prototype creation`);
        prototype.prompt_id = lastPromptId;
      }
    }

    const response = await fetch(`${API_BASE}/prototype/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(prototype),
      credentials: 'include', // Include cookies for authentication
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create prototype');
    }

    const result = await response.json();

    // Invalidate quota cache since we've created a new prototype
    invalidateQuotaCache();

    return result;
  } catch (error) {
    console.error('Error creating prototype:', error);
    throw error;
  }
}

/**
 * Stores the promptId from the last plan generation
 * This is used to link subsequent code generation to the same prompt
 */
let lastPromptId: number | null = null;

/**
 * Gets the last promptId from plan generation
 */
export function getLastPromptId(): number | null {
  return lastPromptId;
}

/**
 * Sets the last promptId
 */
export function setLastPromptId(promptId: number | null): void {
  lastPromptId = promptId;
}

/**
 * Streams the plan from the backend as plain text.
 * Calls POST /api/llm/plan with { prompt, provider: 'openai', stream: true }
 * Returns an async generator yielding text chunks.
 *
 * Checks quota before generating to prevent users from exceeding their limit.
 */
export async function* streamFeaturePlan(prompt: string): AsyncGenerator<string, void, unknown> {
  // Check quota before generating
  try {
    const quota = await getPrototypeQuota();

    // If user has no remaining prototypes, throw an error
    if (quota.remainingCount <= 0 || quota.usedCount >= quota.totalCount) {
      throw new Error(`QUOTA_EXCEEDED: You've reached your ${quota.plan} plan limit of ${quota.totalCount} prototypes.`);
    }

    // Proceed with generation if quota is available
    const res = await fetch(`${API_BASE}/llm/v3/plan/stream`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt, stream: true }),
      credentials: 'include', // Add this to send cookies with the request
    });

    if (!res.ok) {
      const errorText = await res.text();
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.error || `HTTP error! status: ${res.status}`);
      } catch (e) {
        throw new Error(`HTTP error! status: ${res.status}, message: ${errorText}`);
      }
    }

    if (!res.body) throw new Error('No response body for streaming');

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // Process SSE events
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6); // Remove 'data: ' prefix
            if (data.trim() && data !== '[DONE]') {
              yield data;
            }
          } else if (line.startsWith('event: end')) {
            return; // End of stream
          } else if (line.startsWith('event: error')) {
            throw new Error('Stream error occurred');
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  } catch (error: any) {
    console.error('Error in streamFeaturePlan:', error);

    // Format the error message for better display
    let errorMessage = error.message || 'Unknown error occurred';

    // Check if this is a quota exceeded error
    if (errorMessage.includes('QUOTA_EXCEEDED')) {
      // Don't yield anything for quota errors - we'll handle this in the UI
      console.warn('Quota exceeded error detected');
    } else {
      // For other errors, yield the message
      yield `Error: ${errorMessage}`;
    }

    // Re-throw to stop the generator
    throw error;
  }
}

/**
 * Generate a feature plan from a prompt (non-streaming version)
 * @param prompt - The user's prompt
 * @returns Promise<{features: string[], promptId: number | null}>
 */
export async function generateFeaturePlan(prompt: string): Promise<{features: string[], promptId: number | null}> {
  try {
    // Check quota before generating
    const quota = await getPrototypeQuota();

    // If user has no remaining prototypes, throw an error
    if (quota.remainingCount <= 0 || quota.usedCount >= quota.totalCount) {
      throw new Error(`QUOTA_EXCEEDED: You've reached your ${quota.plan} plan limit of ${quota.totalCount} prototypes.`);
    }

    const response = await fetch(`${API_BASE}/llm/v3/plan`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt, stream: false }),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to generate plan: ${response.statusText}`);
    }

    const result = await response.json();

    // Store the promptId for later use
    if (result.promptId) {
      setLastPromptId(result.promptId);
    }

    return result;
  } catch (error) {
    console.error('Error generating feature plan:', error);
    throw error;
  }
}

/**
 * Streams generated code from the backend as plain text.
 * Calls POST /api/llm/generate with { plan, provider: 'deepseek', stream: true }
 * Returns an async generator yielding code chunks.
 *
 * Checks quota before generating to prevent users from exceeding their limit.
 */
export async function* streamGeneratedCode(plan: string): AsyncGenerator<string, void, unknown> {
  console.log('Starting streamGeneratedCode...');

  try {
    // Check quota before generating
    const quota = await getPrototypeQuota();

    // If user has no remaining prototypes, throw an error
    if (quota.remainingCount <= 0 || quota.usedCount >= quota.totalCount) {
      throw new Error(`QUOTA_EXCEEDED: You've reached your ${quota.plan} plan limit of ${quota.totalCount} prototypes.`);
    }

    // Get the promptId from the last plan generation
    const promptId = getLastPromptId();
    console.log(`Using promptId: ${promptId} for code generation`);

    // Proceed with generation if quota is available
    const res = await fetch(`${API_BASE}/llm/v3/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        plan,
        stream: true,
        promptId // Include the promptId to link this code generation to the original prompt
      }),
      credentials: 'include', // Add this to send cookies with the request
    });

    if (!res.ok) {
      const errorText = await res.text();
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.error || `HTTP error! status: ${res.status}`);
      } catch (e) {
        throw new Error(`HTTP error! status: ${res.status}, message: ${errorText}`);
      }
    }

    if (!res.body) {
      throw new Error('No response body for streaming');
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let chunkCount = 0;

    console.log('Starting to read chunks...');

    while (!done) {
      const { value, done: doneReading } = await reader.read();
      done = doneReading;

      if (value) {
        const chunk = decoder.decode(value, { stream: !done });
        chunkCount++;
       // console.log(`Received chunk #${chunkCount}: ${chunk.length} bytes`);
        yield chunk;
      }

      if (done) {
        console.log('Stream completed, total chunks:', chunkCount);
      }
    }
  } catch (error: any) {
    console.error('Error in streamGeneratedCode:', error);

    // Format the error message for better display
    let errorMessage = error.message || 'Unknown error occurred';

    // Check if this is a quota exceeded error
    if (errorMessage.includes('QUOTA_EXCEEDED')) {
      // Don't yield anything for quota errors - we'll handle this in the UI
      console.warn('Quota exceeded error detected');
    } else {
      // For other errors, yield the message
      yield `Error: ${errorMessage}`;
    }

    // Re-throw to stop the generator
    throw error;
  }
}

/**
 * Interface for element modification request
 * htmlContent should be the code content without iframe wrapper HTML
 */
export interface ElementModificationRequest {
  htmlContent: string; // The code content without iframe wrapper HTML
  elementSelector: string;
  prompt: string;
}

/**
 * Interface for content modification request
 * htmlContent should be the code content without iframe wrapper HTML
 */
export interface ContentModificationRequest {
  htmlContent: string; // The code content without iframe wrapper HTML
  prompt: string;
}

/**
 * Modifies a specific element in the HTML content based on the prompt.
 * Returns the modified HTML content.
 *
 * Note: htmlContent should be the code content without iframe wrapper HTML.
 */
export async function modifyElement(request: ElementModificationRequest): Promise<string> {
  try {
    console.log('Modifying element with code content...');

    const res = await fetch(`${API_BASE}/llm/modify-element`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        htmlContent: request.htmlContent,
        elementSelector: request.elementSelector,
        prompt: request.prompt,
        provider: 'deepseek',
        stream: false
      }),
      credentials: 'include', // Add this to send cookies with the request
    });

    if (!res.ok) {
      throw new Error(`Failed to modify element: ${res.statusText}`);
    }

    const modifiedHtml = await res.text();
    return modifiedHtml;
  } catch (error) {
    console.error('Error modifying element:', error);
    throw error;
  }
}

/**
 * Streams the modified element HTML from the backend as plain text.
 * Calls POST /api/llm/modify-element with { htmlContent, elementSelector, prompt, provider: 'deepseek', stream: true }
 * Returns an async generator yielding HTML chunks.
 *
 * Note: htmlContent should be the code content without iframe wrapper HTML.
 */
export async function* streamModifyElement(request: ElementModificationRequest): AsyncGenerator<string, void, unknown> {
  console.log('Starting streamModifyElement with code content...');

  try {

    // Create an AbortController to handle timeouts
    const abortController = new AbortController();
    const signal = abortController.signal;

    // Set a timeout for the entire request (3 minutes to match Deepseek's limitations)
    const timeoutId = setTimeout(() => {
      console.warn('Element modification request timed out after 3 minutes');
      abortController.abort('timeout'); // Provide a reason for the abort
    }, 180000); // 3 minutes

    const res = await fetch(`${API_BASE}/llm/modify-element`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        htmlContent: request.htmlContent,
        elementSelector: request.elementSelector,
        prompt: request.prompt,
        provider: 'deepseek',
        stream: true
      }),
      credentials: 'include', // Add this to send cookies with the request
      signal // Add abort signal
    });

    // Clear the timeout since the request completed
    clearTimeout(timeoutId);

    if (!res.ok) {
      const errorText = await res.text();
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.error || `HTTP error! status: ${res.status}`);
      } catch (e) {
        throw new Error(`HTTP error! status: ${res.status}, message: ${errorText}`);
      }
    }

    if (!res.body) {
      throw new Error('No response body for streaming');
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let chunkCount = 0;
    let accumulatedHtml = '';
    let lastChunkTime = Date.now();
    const MAX_CHUNK_WAIT_TIME = 5000; // 5 seconds max wait time between chunks

    // Track if we've seen a DOCTYPE or <html> tag to verify we're getting HTML
    let hasHtmlStart = false;
    let hasHtmlEnd = false;
    let hasAppDiv = false;

    // Track if we've received any valid HTML content
    let hasReceivedValidContent = false;

    console.log('Starting to read element modification chunks...');

    while (!done) {
      // Check for timeout between chunks
      const currentTime = Date.now();
      if (currentTime - lastChunkTime > MAX_CHUNK_WAIT_TIME && chunkCount > 0) {
        console.warn(`No chunks received for ${MAX_CHUNK_WAIT_TIME}ms, considering stream complete`);
        break;
      }

      try {
        const { value, done: doneReading } = await reader.read();
        done = doneReading;

        if (value) {
          lastChunkTime = Date.now(); // Reset the timeout counter
          const chunk = decoder.decode(value, { stream: !done });
          chunkCount++;
          console.log(`Received element modification chunk #${chunkCount}: ${chunk.length} bytes`);

          // Add to accumulated HTML
          accumulatedHtml += chunk;

          // Check for HTML structure indicators
          if (!hasHtmlStart && (accumulatedHtml.includes('<!DOCTYPE') || accumulatedHtml.includes('<html'))) {
            hasHtmlStart = true;
            console.log('Detected HTML start tag');
          }

          if (!hasHtmlEnd && accumulatedHtml.includes('</html>')) {
            hasHtmlEnd = true;
            console.log('Detected HTML end tag');
          }

          if (!hasAppDiv && accumulatedHtml.includes('<div id="app"')) {
            hasAppDiv = true;
            console.log('Detected app div');
          }

          // If we have at least some content, mark as valid
          if (accumulatedHtml.length > 100) {
            hasReceivedValidContent = true;
          }

          yield chunk;
        }

        if (done) {
          console.log('Element modification stream completed, total chunks:', chunkCount);

          // Validate HTML completeness
          if (!hasHtmlStart || !hasHtmlEnd || !hasAppDiv) {
            console.warn('Warning: Received HTML may be incomplete. Missing HTML structure tags.');

            // If we have accumulated HTML but it's incomplete, try to fix it
            if (accumulatedHtml.length > 0) {
              if (!hasHtmlStart) {
                console.warn('HTML start tag missing, content may be incomplete');
              }

              if (!hasHtmlEnd) {
                console.warn('HTML end tag missing, content may be truncated');
              }

              if (!hasAppDiv) {
                console.warn('App div missing, content may be malformed');
              }

              // If we have valid content but it's incomplete, try to use it anyway
              if (hasReceivedValidContent) {
                console.log('Received valid but incomplete HTML, using it anyway');
              } else {
                console.warn('HTML content is invalid or too incomplete to use');

                // If we don't have valid content, try a non-streaming fallback
                try {
                  console.log('Attempting non-streaming fallback for element modification');
                  const fallbackResult = await modifyElement(request);
                  if (fallbackResult && fallbackResult.length > 0) {
                    console.log('Non-streaming fallback successful, using result');
                    yield fallbackResult;
                    return;
                  }
                } catch (fallbackError) {
                  console.error('Non-streaming fallback failed:', fallbackError);
                }
              }
            }
          } else {
            console.log('HTML content appears to be complete and valid');
          }
        }
      } catch (readError) {
        console.error('Error reading from stream:', readError);

        // If we have accumulated some valid HTML, try to use it
        if (hasReceivedValidContent) {
          console.log('Stream read error, but using accumulated HTML');
          break;
        } else {
          throw readError;
        }
      }
    }

    // If we've exited the loop but haven't received valid content, try the non-streaming fallback
    if (!hasReceivedValidContent && accumulatedHtml.length < 100) {
      console.warn('Stream completed but no valid HTML received, trying non-streaming fallback');
      try {
        const fallbackResult = await modifyElement(request);
        if (fallbackResult && fallbackResult.length > 0) {
          console.log('Non-streaming fallback successful, using result');
          yield fallbackResult;
        } else {
          throw new Error('Non-streaming fallback returned empty result');
        }
      } catch (fallbackError) {
        console.error('Non-streaming fallback failed:', fallbackError);
        throw fallbackError;
      }
    } else if (accumulatedHtml.length > 0) {
      // If we have accumulated HTML but haven't yielded the final chunk, yield it now
      console.log('Ensuring all accumulated HTML is yielded');
      // We don't yield here because we've already yielded chunks as they came in
    }
  } catch (error: any) {
    console.error('Error in streamModifyElement:', error);

    // Format the error message for better display
    let errorMessage = error.message || 'Unknown error occurred';

    // For errors, yield the message so UI can display it
    yield `Error: ${errorMessage}`;

    // Re-throw to stop the generator
    throw error;
  }
}

/**
 * Modifies the entire HTML content based on the prompt.
 * Returns the modified HTML content.
 */
export async function modifyContent(request: ContentModificationRequest): Promise<string> {
  try {
    const res = await fetch(`${API_BASE}/llm/modify-content`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        htmlContent: request.htmlContent,
        prompt: request.prompt,
        provider: 'deepseek',
        stream: false
      }),
      credentials: 'include', // Add this to send cookies with the request
    });

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const modifiedHtml = await res.text();
    return modifiedHtml;
  } catch (error) {
    console.error('Error modifying content:', error);
    throw error;
  }
}

/**
 * Streams the modified HTML content from the backend as plain text.
 * Calls POST /api/llm/modify-content with { htmlContent, prompt, provider: 'deepseek', stream: true }
 * Returns an async generator yielding HTML chunks.
 *
 * Note: htmlContent should be the code content without iframe wrapper HTML.
 */
export async function* streamModifyContent(request: ContentModificationRequest): AsyncGenerator<string, void, unknown> {
  console.log('Starting streamModifyContent with code content...');

  // Create an AbortController to handle timeouts
  const abortController = new AbortController();
  const signal = abortController.signal;
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  try {
    // Set a timeout for the entire request (3 minutes to match Deepseek's limitations)
    // The Deepseek LLM provider stops responding after approximately 3.2 minutes
    timeoutId = setTimeout(() => {
      console.warn('Content modification request timed out after 3 minutes');
      abortController.abort('timeout'); // Provide a reason for the abort
    }, 180000); // 3 minutes

    console.log('Sending modify-content request with stream=true...');
    const res = await fetch(`${API_BASE}/llm/modify-content`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        htmlContent: request.htmlContent,
        prompt: request.prompt,
        provider: 'deepseek', // Consider making this configurable
        stream: true
      }),
      credentials: 'include', // Add this to send cookies with the request
      signal // Add abort signal
    });

    // Clear the timeout since the request completed successfully
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (!res.ok) {
      const errorText = await res.text();
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.error || `HTTP error! status: ${res.status}`);
      } catch (e) {
        throw new Error(`HTTP error! status: ${res.status}, message: ${errorText}`);
      }
    }

    if (!res.body) {
      throw new Error('No response body for streaming');
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let chunkCount = 0;
    let accumulatedHtml = '';
    let lastChunkTime = Date.now();
    const MAX_CHUNK_WAIT_TIME = 30000; // 30 seconds max wait time between chunks

    // Track if we've seen a DOCTYPE or <html> tag to verify we're getting HTML
    let hasHtmlStart = false;
    let hasHtmlEnd = false;
    let hasAppDiv = false;

    // Track if we've received any valid HTML content
    let hasReceivedValidContent = false;

    // Set up a chunk timeout checker
    let chunkTimeoutId: ReturnType<typeof setTimeout> | null = null;

    const setupChunkTimeout = () => {
      // Clear any existing timeout
      if (chunkTimeoutId) {
        clearTimeout(chunkTimeoutId);
      }

      // Set a new timeout to check for stalled streams
      chunkTimeoutId = setTimeout(() => {
        const timeSinceLastChunk = Date.now() - lastChunkTime;
        if (timeSinceLastChunk > MAX_CHUNK_WAIT_TIME && chunkCount > 0) {
          console.warn(`No chunks received for ${MAX_CHUNK_WAIT_TIME}ms, considering stream complete`);
          // We'll handle this in the next loop iteration
        }
      }, MAX_CHUNK_WAIT_TIME);
    };

    console.log('Starting to read content modification chunks...');
    setupChunkTimeout(); // Initial timeout setup

    while (!done) {
      // Check for timeout between chunks
      const currentTime = Date.now();
      if (currentTime - lastChunkTime > MAX_CHUNK_WAIT_TIME && chunkCount > 0) {
        console.warn(`No chunks received for ${MAX_CHUNK_WAIT_TIME}ms, considering stream complete`);

        // If we have received valid content, we can break and use what we have
        if (hasReceivedValidContent) {
          console.log('Stream appears to be stalled, but we have valid content. Using what we have.');
          break;
        }

        // Otherwise, we'll try one more read before giving up
        console.log('Stream appears to be stalled. Trying one more read before giving up...');
      }

      try {
        // Set up a promise that will reject after a timeout
        const readWithTimeout = Promise.race([
          reader.read(),
          new Promise<never>((_, reject) => {
            setTimeout(() => {
              reject(new Error('Read operation timed out'));
            }, 30000); // 30 second timeout for individual read operations
          })
        ]);

        const { value, done: doneReading } = await readWithTimeout as ReadableStreamReadResult<Uint8Array>;
        done = doneReading;

        if (value) {
          lastChunkTime = Date.now(); // Reset the timeout counter
          setupChunkTimeout(); // Reset the chunk timeout

          let chunk = decoder.decode(value, { stream: !done });
          chunkCount++;
          //console.log(`Received content modification chunk #${chunkCount}: ${chunk.length} bytes`);

          // Check for duplicate app divs in the chunk
          const appDivCount = (chunk.match(/<div id="app"/g) || []).length;

          if (appDivCount > 1) {
            console.warn(`Detected ${appDivCount} app divs in a single chunk, fixing...`);

            // Split the chunk by app div
            const parts = chunk.split('<div id="app"');

            // Keep only the first app div
            if (parts.length > 1) {
              const fixedChunk = '<div id="app"' + parts[1];
              console.log('Fixed chunk to include only one app div');

              // Update the chunk
              chunk = fixedChunk;
            }
          }

          // Check if we already have an app div and this chunk has another one
          if (hasAppDiv && chunk.includes('<div id="app"')) {
            console.warn('Detected duplicate app div across chunks, fixing...');

            // Remove the new app div and everything before it
            const appDivIndex = chunk.indexOf('<div id="app"');
            if (appDivIndex >= 0) {
              chunk = chunk.substring(appDivIndex + '<div id="app"'.length);
              console.log('Removed duplicate app div from chunk');
            }
          }

          // Add to accumulated HTML
          accumulatedHtml += chunk;

          // Check for HTML structure indicators
          if (!hasHtmlStart && (accumulatedHtml.includes('<!DOCTYPE') || accumulatedHtml.includes('<html'))) {
            hasHtmlStart = true;
            console.log('Detected HTML start tag');
          }

          if (!hasHtmlEnd && accumulatedHtml.includes('</html>')) {
            hasHtmlEnd = true;
            console.log('Detected HTML end tag');
          }

          if (!hasAppDiv && accumulatedHtml.includes('<div id="app"')) {
            hasAppDiv = true;
            console.log('Detected app div');
          }

          // If we have at least some content, mark as valid
          if (accumulatedHtml.length > 100) {
            hasReceivedValidContent = true;
          }

          yield chunk;
        }

        if (done) {
          console.log('Content modification stream completed, total chunks:', chunkCount);

          // Validate HTML completeness
          if (!hasHtmlStart || !hasHtmlEnd || !hasAppDiv) {
            console.warn('Warning: Received HTML may be incomplete. Missing HTML structure tags.');

            // If we have accumulated HTML but it's incomplete, try to fix it
            if (accumulatedHtml.length > 0) {
              if (!hasHtmlStart) {
                console.warn('HTML start tag missing, content may be incomplete');
              }

              if (!hasHtmlEnd) {
                console.warn('HTML end tag missing, content may be truncated');
              }

              if (!hasAppDiv) {
                console.warn('App div missing, content may be malformed');
              }

              // If we have valid content but it's incomplete, try to use it anyway
              if (hasReceivedValidContent) {
                console.log('Received valid but incomplete HTML, using it anyway');
              } else {
                console.warn('HTML content is invalid or too incomplete to use');

                // If we don't have valid content, try a non-streaming fallback
                try {
                  console.log('Attempting non-streaming fallback for content modification');
                  const fallbackResult = await modifyContent(request);
                  if (fallbackResult && fallbackResult.length > 0) {
                    console.log('Non-streaming fallback successful, using result');
                    yield fallbackResult;
                    return;
                  }
                } catch (fallbackError) {
                  console.error('Non-streaming fallback failed:', fallbackError);
                }
              }
            }
          } else {
            console.log('HTML content appears to be complete and valid');
          }
        }
      } catch (error) {
        const readError = error as Error;
        console.error('Error reading from stream:', readError);

        // If we have accumulated some valid HTML, try to use it
        if (hasReceivedValidContent) {
          console.log('Stream read error, but using accumulated HTML');
          break;
        } else {
          // If the error is due to an aborted request, handle it gracefully
          if (readError instanceof Error &&
              (readError.name === 'AbortError' || readError.message?.includes('aborted'))) {
            console.warn('Stream was aborted:', readError.message);
            throw new Error(`Stream was aborted: ${readError.message || 'Unknown reason'}`);
          } else if (readError instanceof Error && readError.message?.includes('timed out')) {
            console.warn('Read operation timed out:', readError.message);
            // If we have some content, we can try to use it
            if (accumulatedHtml.length > 0) {
              console.log('Read timed out, but using accumulated HTML');
              break;
            } else {
              throw new Error('Read operation timed out and no content was received');
            }
          } else {
            throw readError;
          }
        }
      } finally {
        // Clean up the chunk timeout
        if (chunkTimeoutId) {
          clearTimeout(chunkTimeoutId);
          chunkTimeoutId = null;
        }
      }
    }

    // Clean up resources
    if (chunkTimeoutId) {
      clearTimeout(chunkTimeoutId);
      chunkTimeoutId = null;
    }

    // If we've exited the loop but haven't received valid content, try the non-streaming fallback
    if (!hasReceivedValidContent && accumulatedHtml.length < 100) {
      console.warn('Stream completed but no valid HTML received, trying non-streaming fallback');
      try {
        console.log('Attempting non-streaming fallback for content modification');
        const fallbackResult = await modifyContent(request);
        if (fallbackResult && fallbackResult.length > 0) {
          console.log('Non-streaming fallback successful, using result');
          yield fallbackResult;
        } else {
          throw new Error('Non-streaming fallback returned empty result');
        }
      } catch (fallbackError) {
        console.error('Non-streaming fallback failed:', fallbackError);
        throw fallbackError;
      }
    } else if (accumulatedHtml.length > 0) {
      // Post-process the accumulated HTML to fix any issues
      console.log('Post-processing accumulated HTML');

      // Check for duplicate app divs in the final HTML
      const appDivMatches = accumulatedHtml.match(/<div id="app"[\s\S]*?<\/div>/g);
      if (appDivMatches && appDivMatches.length > 1) {
        console.warn(`Found ${appDivMatches.length} app divs in final HTML, using only the first one`);
        // Use only the first app div
        accumulatedHtml = appDivMatches[0];
      }

      // If we have accumulated HTML but haven't yielded the final chunk, yield it now
      console.log('Ensuring all accumulated HTML is yielded');
      // We don't yield here because we've already yielded chunks as they came in
    }
  } catch (error: any) {
    // Clean up timeout if it exists
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    console.error('Error in streamModifyContent:', error);

    // Check if this is an abort error
    if (error.name === 'AbortError') {
      const reason = error.message || 'Request was aborted';
      console.warn(`Stream aborted: ${reason}`);

      // For abort errors, provide a more user-friendly message
      yield `Error: The request was interrupted. ${reason === 'timeout' ? 'The operation took too long to complete.' : reason}`;
    } else {
      // Format the error message for better display
      let errorMessage = error.message || 'Unknown error occurred';

      // For errors, yield the message so UI can display it
      yield `Error: ${errorMessage}`;
    }

    // Re-throw to stop the generator
    throw error;
  }
}

/**
 * Interface for functionality generation request
 * htmlContent should be the code content without iframe wrapper HTML
 */
export interface FunctionalityGenerationRequest {
  htmlContent: string; // The code content without iframe wrapper HTML
  elementSelector: string;
  elementType: string;
  elementContext: string;
  prompt: string;
}

/**
 * Generates functionality for an unimplemented interactive element.
 * Returns the modified HTML content with implemented functionality.
 */
export async function generateFunctionality(request: FunctionalityGenerationRequest): Promise<string> {
  try {
    const res = await fetch(`${API_BASE}/llm/generate-functionality`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        htmlContent: request.htmlContent,
        elementSelector: request.elementSelector,
        elementType: request.elementType,
        elementContext: request.elementContext,
        prompt: request.prompt,
        stream: false
      }),
    });

    if (!res.ok) {
      throw new Error(`Failed to generate functionality: ${res.statusText}`);
    }

    const modifiedHtml = await res.text();
    return modifiedHtml;
  } catch (error) {
    console.error('Error generating functionality:', error);
    throw error;
  }
}

/**
 * Streams the generation of functionality for an unimplemented element.
 * Calls POST /api/llm/generate-functionality with the request parameters.
 * Returns an async generator yielding HTML chunks.
 */
export async function* streamGenerateFunctionality(request: FunctionalityGenerationRequest): AsyncGenerator<string, void, unknown> {
  console.log('Starting streamGenerateFunctionality with code content...');

  try {
    const res = await fetch(`${API_BASE}/llm/generate-functionality`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        htmlContent: request.htmlContent,
        elementSelector: request.elementSelector,
        elementType: request.elementType,
        elementContext: request.elementContext,
        prompt: request.prompt,
        provider: 'deepseek',
        stream: true
      }),
    });

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    if (!res.body) {
      throw new Error('No response body for streaming');
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let chunkCount = 0;

    console.log('Starting to read functionality generation chunks...');

    while (!done) {
      const { value, done: doneReading } = await reader.read();
      done = doneReading;

      if (value) {
        const chunk = decoder.decode(value, { stream: !done });
        chunkCount++;
        console.log(`Received functionality generation chunk #${chunkCount}: ${chunk.length} bytes`);
        yield chunk;
      }

      if (done) {
        console.log('Functionality generation stream completed, total chunks:', chunkCount);
      }
    }
  } catch (error) {
    console.error('Error in streamGenerateFunctionality:', error);
    throw error;
  }
}
