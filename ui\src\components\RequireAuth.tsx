import { useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

// Keep track of whether we've already attempted to refresh auth
// This is a global variable to ensure we only refresh once across all instances
let globalRefreshAttempted = false;

export default function RequireAuth({ children }: { children: React.ReactNode }) {
  const { authState, refreshAuth } = useAuth();
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // First check if we have the isLoggedIn cookie
        const isLoggedIn = document.cookie.includes('isLoggedIn=true');

        // If we have the cookie or we're already authenticated, we can render immediately
        if (isLoggedIn || authState.isAuthenticated) {
          console.log("RequireAuth: Already authenticated via cookie or auth state");
          setIsAuthenticated(true);
          setLoading(false);
          return;
        }

        // Check if we have a cached auth state
        const cachedAuthString = localStorage.getItem('authState');
        if (cachedAuthString) {
          try {
            const cachedAuth = JSON.parse(cachedAuthString);
            const isRecent = (Date.now() - cachedAuth.lastChecked) < 30 * 60 * 1000; // 30 minutes

            if (isRecent && cachedAuth.isAuthenticated) {
              console.log("RequireAuth: Using cached auth state");
              setIsAuthenticated(true);
              setLoading(false);
              return;
            }
          } catch (e) {
            console.error("RequireAuth: Error parsing cached auth state:", e);
          }
        }

        // Only refresh once globally to avoid multiple calls
        if (!globalRefreshAttempted) {
          console.log("RequireAuth: First refresh attempt, refreshing auth");
          globalRefreshAttempted = true;
          const success = await refreshAuth();
          console.log("RequireAuth: Auth refreshed, success:", success);
          setIsAuthenticated(success || false);
        } else {
          console.log("RequireAuth: Refresh already attempted, skipping");
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("RequireAuth: Error checking auth:", error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Show a loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-lg">Checking authentication...</span>
      </div>
    );
  }

  // Redirect to landing page if not authenticated
  if (!isAuthenticated && !authState.isAuthenticated) {
    console.log("RequireAuth: Not authenticated, redirecting to landing page");
    return <Navigate to="/" replace />;
  }

  // Render children if authenticated
  console.log("RequireAuth: Authenticated, rendering children");
  return <>{children}</>;
}
