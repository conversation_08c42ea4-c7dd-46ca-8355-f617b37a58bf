import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/Button';
import styles from './PromptInputPage.module.css';

/**
 * Concise prompts (no markdown, no heading markup), described in a few words then truncated with ...
 * If user clicks, set<PERSON><PERSON><PERSON> uses full text.
 */
const EXAMPLES = [
  {
    label: "Gaming dashboard with friends, voice chat, stats ...",
    full: "Design a modern gaming dashboard for a PC gamer. The layout should include a friends list, live voice chat panel, active game stats, achievements tracker, and a section for trending games. Use a dark theme with neon accents. Optimize for desktop usage with responsive card layouts."
  },
  {
    label: "Multi-agent GenAI workspace: agents, context, tasks ...",
    full: "Create a multi-agent GenAI workspace UI for enterprise users. Include panels for real-time conversation with different AI agents (DataBot, LegalBot, CodeBot), a context memory window, task queue, and an action history log. The interface should allow drag-and-drop tasks between agents and visualize tool use with icons. Focus on productivity and transparency."
  },
  {
    label: "Minimalist fashion e-commerce homepage ...",
    full: "Design a minimalist fashion e-commerce homepage. It should feature a hero banner with a seasonal offer, product category tiles, best-selling items carousel, and a floating cart widget. Include mobile responsiveness and support quick filter options by brand, color, and price. Prioritize clean typography and high-quality product images."
  },
  {
    label: "Customer support portal: tickets, chat, FAQ ...",
    full: "Prototype a customer support portal where users can raise tickets, chat with a bot, or schedule a callback. Include ticket history, live chat window, FAQ search bar, and service status updates. Add a user-friendly dashboard for tracking ticket progress with timeline view. Focus on accessibility and intuitive navigation."
  }
];

export function PromptInputPage() {
  const [prompt, setPrompt] = useState('');
  const [fadeOut, setFadeOut] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleGenerate = () => {
    setError(null);
    setLoading(true);
    setFadeOut(true);
    // Only navigate, do not fetch or stream here!
    navigate('/plan', { state: { prompt } });
  };

  return (
    <div className={`${styles.fullPage} ${fadeOut ? styles.fadeOut : ''}`}>
      <div className={styles.centerBox}>
        <h2 className={styles.heading}>Describe Your Prototype</h2>
        <textarea
          className={styles.input}
          placeholder="Describe the prototype you'd like to create (e.g., 'A simple homepage with a header, navigation, and a footer')."
          value={prompt}
          onChange={e => setPrompt(e.target.value)}
          rows={7}
          autoFocus
        />
        <div className={styles.examples}>
          <div className={styles.examplesLabel}>Examples:</div>
          <ul>
            {EXAMPLES.map((ex, i) => (
              <li
                key={i}
                className={styles.example}
                onClick={() => setPrompt(ex.full)}
                title={ex.full}
              >
                {ex.label}
              </li>
            ))}
          </ul>
        </div>
        {error && <div className={styles.error}>{error}</div>}
        <Button
          size="large"
          animated
          className={styles.generateBtn}
          onClick={handleGenerate}
          disabled={!prompt.trim() || loading}
        >
          {loading ? 'Generating...' : 'Generate'}
        </Button>
      </div>
    </div>
  );
}
