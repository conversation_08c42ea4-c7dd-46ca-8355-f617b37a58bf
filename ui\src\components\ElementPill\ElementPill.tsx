import React from 'react';
import { FiX } from 'react-icons/fi';
import styles from './ElementPill.module.css';

export interface SelectedElement {
  id: string;
  tagName: string;
  className?: string;
  elementId?: string;
  ref: string;
}

interface ElementPillProps {
  element: SelectedElement;
  onRemove: (id: string) => void;
}

export const ElementPill: React.FC<ElementPillProps> = ({ element, onRemove }) => {
  return (
    <div className={styles.pill}>
      <span className={styles.tagName}>{element.tagName}</span>
      {element.elementId && <span className={styles.elementId}>#{element.elementId}</span>}
      {element.className && <span className={styles.className}>.{element.className}</span>}
      <button 
        className={styles.removeButton} 
        onClick={() => onRemove(element.id)}
        aria-label="Remove element"
      >
        <FiX size={14} />
      </button>
    </div>
  );
};
