import { useEffect, useState, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { FiArrowLeft, FiEdit2, <PERSON>Loa<PERSON>, <PERSON>Share2, FiZap } from 'react-icons/fi';
import styles from './PrototypeViewerPage.module.css';
import { getPrototypeById, Prototype } from '../services/prototypeService';
import ShareButton from '../components/ShareButton';

// Returns the full HTML for the iframe, using a template approach
function getIframeHtml(prototype: Prototype): string {
  try {
    // Create a safe version of the content with scripts properly tagged
    const processedContent = prototype.html.replace(/<script>/g, '<script data-exec="inline">');

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${prototype.title}</title>
      <script src="https://cdn.tailwindcss.com/3.4.16"></script>
      <script>tailwind.config={theme:{extend:{colors:{primary:'#0047AB',secondary:'#4682B4'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="stylesheet" crossorigin href="https://static.readdy.ai/static/index-DXIoEVCZ.css">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
      <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
      <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
      <script>
        // This will execute all inline scripts after DOMContentLoaded (needed for charts)
        document.addEventListener('DOMContentLoaded', function() {
          var scripts = document.querySelectorAll('script[data-exec="inline"]');
          scripts.forEach(function(script) {
            try {
              // eslint-disable-next-line no-eval
              eval(script.textContent);
            } catch (e) {
              console.error('Error executing inline script:', e);
            }
          });
        });
      </script>
      <style>
      ${prototype.css || ''}
      :where([class^="ri-"])::before { content: "\\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }
      </style>
    </head>
    <body>
      ${processedContent}
    </body>
    </html>
    `;
  } catch (error) {
    console.error('Error in getIframeHtml:', error);

    // Return a fallback HTML with error message
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Error</title>
      <style>
        body { font-family: sans-serif; padding: 20px; color: #333; }
        .error { color: #e53e3e; border: 1px solid #e53e3e; padding: 15px; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="error">
        <h2>Error rendering content</h2>
        <p>There was an error processing the content. Please try again.</p>
        <p>Error details: ${error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    </body>
    </html>
    `;
  }
}

export function PrototypeViewerPage() {
  const { id } = useParams<{ id: string }>();
  const [prototype, setPrototype] = useState<Prototype | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (!id) return;

    const fetchPrototype = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getPrototypeById(parseInt(id, 10));
        setPrototype(data);
      } catch (err: any) {
        console.error('Error fetching prototype:', err);
        setError('Failed to load prototype. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchPrototype();
  }, [id]);

  // Update iframe when prototype changes
  useEffect(() => {
    if (prototype && iframeRef.current) {
      iframeRef.current.srcdoc = getIframeHtml(prototype);
    }
  }, [prototype]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <FiLoader className={styles.loadingIcon} />
        <span>Loading prototype...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.error}>{error}</div>
        <Link to="/prototypes" className={styles.backLink}>
          <FiArrowLeft />
          Back to My Prototypes
        </Link>
      </div>
    );
  }

  if (!prototype) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.error}>Prototype not found</div>
        <Link to="/prototypes" className={styles.backLink}>
          <FiArrowLeft />
          Back to My Prototypes
        </Link>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Link to="/prototypes" className={styles.backLink}>
            <FiArrowLeft />
            Back
          </Link>
          <h1 className={styles.title}>{prototype.title}</h1>
        </div>
        <div className={styles.headerRight}>
          <ShareButton
            prototypeId={prototype.id.toString()}
            prototypeName={prototype.title}
            className={styles.shareButton}
          />
          <Link to={`/editor-v3-refactored/${prototype.id}`} className={styles.editButton} style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}>
            <FiEdit2 />
            Edit
          </Link>
        </div>
      </div>

      <div className={styles.previewContainer}>
        <iframe
          ref={iframeRef}
          title={prototype.title}
          className={styles.previewFrame}
          sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
        />
      </div>
    </div>
  );
}
