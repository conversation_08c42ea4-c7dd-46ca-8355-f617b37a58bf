import React from "react";

interface IntentMessageProps {
  userIntent: string;
  suggestion?: string;
  confidence?: number;
  canGenerate?: boolean;
  implementationType?: string;
  estimatedTokens?: number;
}

const IntentMessage: React.FC<IntentMessageProps> = ({
  userIntent,
  suggestion,
  confidence,
  canGenerate,
  implementationType,
  estimatedTokens,
}) => (
  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 my-2 shadow-sm">
    <div className="font-semibold text-blue-800 mb-1">AI Intent Analysis</div>
    <div className="text-gray-800 mb-2">
      <span className="font-medium">Intent:</span> {userIntent}
    </div>
    {suggestion && (
      <div className="text-gray-700 mb-2">
        <span className="font-medium">AI Suggestion:</span> {suggestion}
      </div>
    )}
    <div className="flex flex-wrap gap-4 text-xs text-gray-600 mt-2">
      {typeof confidence === "number" && (
        <span>
          <span className="font-medium">Confidence:</span> {(confidence * 100).toFixed(0)}%
        </span>
      )}
      {typeof canGenerate === "boolean" && (
        <span>
          <span className="font-medium">Can Generate:</span> {canGenerate ? "Yes" : "No"}
        </span>
      )}
      {implementationType && (
        <span>
          <span className="font-medium">Type:</span> {implementationType}
        </span>
      )}
      {typeof estimatedTokens === "number" && (
        <span>
          <span className="font-medium">Est. Tokens:</span> {estimatedTokens}
        </span>
      )}
    </div>
  </div>
);

export default IntentMessage;
