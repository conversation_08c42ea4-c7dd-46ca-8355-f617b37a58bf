/**
 * Test script for the modify-content API
 * This script sends a request to the modify-content API with a large HTML content
 * to test the auto-continuation logic.
 */
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Sample HTML content (large enough to potentially trigger truncation)
const htmlContent = `
  <div id="app">
  <div id="page-feed" class="page bg-stone-50 min-h-screen" style="display: block;">
    <header class="bg-white shadow-sm p-4 sticky top-0 z-10">
      <div class="container mx-auto flex justify-between items-center">
        <h1 class="text-2xl font-bold text-amber-800">Wanderlog</h1>
        <nav class="flex gap-4">
          <button class="text-amber-700" data-nav="create">New Story</button>
          <button class="text-amber-700" data-nav="discover">Discover</button>
          <button class="text-amber-700" data-nav="profile">Profile</button>
        </nav>
      </div>
    </header>

    <main class="container mx-auto py-6 px-4">
      <h2 class="text-xl font-semibold text-amber-900 mb-6">Recent Journeys</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Story Card 1 -->
        <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
          <div class="h-48 bg-amber-200 relative overflow-hidden">
            <img src="https://source.unsplash.com/random/600x400/?paris" alt="Paris" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <h3 class="text-white font-medium">Hidden Cafés in Paris</h3>
              <p class="text-white/80 text-sm">By Sophie M.</p>
            </div>
          </div>
          <div class="p-4">
            <p class="text-stone-600 mb-3">Discovering the coziest spots away from tourist crowds...</p>
            <button class="text-amber-700 text-sm font-medium">Read Story →</button>
          </div>
        </div>

        <!-- Story Card 2 -->
        <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
          <div class="h-48 bg-amber-200 relative overflow-hidden">
            <img src="https://source.unsplash.com/random/600x400/?desert" alt="Desert" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <h3 class="text-white font-medium">Desert Road Trips</h3>
              <p class="text-white/80 text-sm">By Carlos J.</p>
            </div>
          </div>
          <div class="p-4">
            <p class="text-stone-600 mb-3">Three weeks crossing the Atacama with just a tent and camera...</p>
            <button class="text-amber-700 text-sm font-medium">Read Story →</button>
          </div>
        </div>

        <!-- Story Card 3 -->
        <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
          <div class="h-48 bg-amber-200 relative overflow-hidden">
            <img src="https://source.unsplash.com/random/600x400/?japan" alt="Japan" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <h3 class="text-white font-medium">Rural Japan</h3>
              <p class="text-white/80 text-sm">By Aiko T.</p>
            </div>
          </div>
          <div class="p-4">
            <p class="text-stone-600 mb-3">Finding serenity in the countryside's ancient ryokans...</p>
            <button class="text-amber-700 text-sm font-medium">Read Story →</button>
          </div>
        </div>
      </div>
    </main>
  </div>

  <div id="page-create" class="page hidden bg-stone-50 min-h-screen" style="display: none;">
    <header class="bg-white shadow-sm p-4 sticky top-0 z-10">
      <div class="container mx-auto flex justify-between items-center">
        <button class="text-amber-700 flex items-center gap-1" data-nav="feed">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
          </svg>
          Back
        </button>
        <h1 class="text-xl font-bold text-amber-800">New Story</h1>
        <button class="text-amber-700">Publish</button>
      </div>
    </header>

    <main class="container mx-auto py-6 px-4">
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-lg font-semibold text-amber-900 mb-4">Story Details</h2>
        <input type="text" placeholder="Title" class="w-full p-3 border border-stone-200 rounded-lg mb-4">
        <textarea placeholder="Tell your story..." class="w-full p-3 border border-stone-200 rounded-lg h-32"></textarea>
      </div>

      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-lg font-semibold text-amber-900 mb-4">Add Media</h2>
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="h-32 bg-stone-100 rounded-lg flex items-center justify-center">
            <button class="text-stone-400">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold text-amber-900 mb-4">Map Your Journey</h2>
        <div id="chart1" class="h-64 w-full bg-stone-100 rounded-lg" _echarts_instance_="ec_1747306505139" style="user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);"><div style="position: relative; width: 100px; height: 256px; padding: 0px; margin: 0px; border-width: 0px;"></div></div>
        <button class="mt-4 text-amber-700">Add Location Pins</button>
      </div>
    </main>
  </div>

  <div id="page-discover" class="page hidden bg-stone-50 min-h-screen" style="display: none;">
    <header class="bg-white shadow-sm p-4 sticky top-0 z-10">
      <div class="container mx-auto flex justify-between items-center">
        <button class="text-amber-700 flex items-center gap-1" data-nav="feed">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
          </svg>
          Back
        </button>
        <h1 class="text-xl font-bold text-amber-800">Discover</h1>
        <div></div>
      </div>
    </header>

    <main class="container mx-auto py-6 px-4">
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-amber-900 mb-4">Trending Destinations</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-amber-100 h-32 rounded-lg flex items-center justify-center text-amber-800 font-medium">Bali</div>
          <div class="bg-amber-100 h-32 rounded-lg flex items-center justify-center text-amber-800 font-medium">Portugal</div>
          <div class="bg-amber-100 h-32 rounded-lg flex items-center justify-center text-amber-800 font-medium">Japan</div>
          <div class="bg-amber-100 h-32 rounded-lg flex items-center justify-center text-amber-800 font-medium">Mexico</div>
        </div>
      </div>

      <div class="mb-8">
        <h2 class="text-xl font-semibold text-amber-900 mb-4">Popular Creators</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="h-20 w-20 bg-stone-200 rounded-full mx-auto mb-2"></div>
            <p class="text-sm font-medium">Sophie M.</p>
          </div>
          <div class="text-center">
            <div class="h-20 w-20 bg-stone-200 rounded-full mx-auto mb-2"></div>
            <p class="text-sm font-medium">Carlos J.</p>
          </div>
          <div class="text-center">
            <div class="h-20 w-20 bg-stone-200 rounded-full mx-auto mb-2"></div>
            <p class="text-sm font-medium">Aiko T.</p>
          </div>
          <div class="text-center">
            <div class="h-20 w-20 bg-stone-200 rounded-full mx-auto mb-2"></div>
            <p class="text-sm font-medium">Jamal K.</p>
          </div>
        </div>
      </div>

      <div>
        <h2 class="text-xl font-semibold text-amber-900 mb-4">Themed Collections</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-stone-100 p-4 rounded-lg">
            <h3 class="font-medium mb-2">Beach Getaways</h3>
            <p class="text-sm text-stone-600">25 stories</p>
          </div>
          <div class="bg-stone-100 p-4 rounded-lg">
            <h3 class="font-medium mb-2">Mountain Adventures</h3>
            <p class="text-sm text-stone-600">18 stories</p>
          </div>
        </div>
      </div>
    </main>
  </div>

  <div id="page-profile" class="page hidden bg-stone-50 min-h-screen" style="display: none;">
    <header class="bg-white shadow-sm p-4 sticky top-0 z-10">
      <div class="container mx-auto flex justify-between items-center">
        <button class="text-amber-700 flex items-center gap-1" data-nav="feed">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
          </svg>
          Back
        </button>
        <h1 class="text-xl font-bold text-amber-800">Your Profile</h1>
        <button class="text-amber-700">Edit</button>
      </div>
    </header>

    <main class="container mx-auto py-6 px-4">
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex items-center gap-4 mb-4">
          <div class="h-16 w-16 bg-stone-200 rounded-full"></div>
          <div>
            <h2 class="text-lg font-semibold">Your Name</h2>
            <p class="text-stone-600 text-sm">Travel enthusiast since 2020</p>
          </div>
        </div>
        <p class="text-stone-600 mb-4">Bio: Tell others about your travel style and interests...</p>
        <div class="flex gap-4">
          <div class="text-center">
            <p class="font-bold">24</p>
            <p class="text-sm text-stone-600">Stories</p>
          </div>
          <div class="text-center">
            <p class="font-bold">1.2K</p>
            <p class="text-sm text-stone-600">Followers</p>
          </div>
        </div>
      </div>

      <div class="mb-6">
        <h2 class="text-lg font-semibold text-amber-900 mb-4">Your Collections</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-stone-100 p-4 rounded-lg">
            <h3 class="font-medium mb-2">European Cafés</h3>
            <p class="text-sm text-stone-600">8 stories</p>
          </div>
          <div class="bg-stone-100 p-4 rounded-lg">
            <h3 class="font-medium mb-2">Hiking Trails</h3>
            <p class="text-sm text-stone-600">5 stories</p>
          </div>
        </div>
      </div>

      <div>
        <h2 class="text-lg font-semibold text-amber-900 mb-4">Your Stories</h2>
        <div class="grid grid-cols-1 gap-4">
          <div class="bg-white p-4 rounded-lg shadow-sm">
            <h3 class="font-medium">Hidden Cafés in Paris</h3>
            <p class="text-sm text-stone-600 mb-2">Last updated: 2 weeks ago</p>
            <div class="flex gap-4 text-sm">
              <span class="text-amber-700">125 likes</span>
              <span class="text-amber-700">24 comments</span>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script data-exec="inline">
    const pages = document.querySelectorAll('.page');
    function showPage(pageId) {
      const trigger = event?.target || null;
      const target = document.getElementById('page-' + pageId);
      if (target) {
        pages.forEach(p => p.style.display = 'none');
        target.style.display = 'block';
      } else if (trigger) {
        try {
          const wrapper = document.createElement('div');
          wrapper.className = 'unimplemented border-2 border-dashed border-red-500 p-2';
          wrapper.setAttribute('data-feature', 'navigation-to-' + pageId);

          const button = document.createElement('button');
          button.textContent = 'Implement: navigation-to-' + pageId;
          button.className = 'mt-2 text-red-600 underline block';

          trigger.parentNode.replaceChild(wrapper, trigger);
          wrapper.appendChild(trigger);
          wrapper.appendChild(button);

          setTimeout(() => {
            wrapper.replaceWith(trigger);
          }, 3000);
        } catch (e) {
          console.error('Failed to mark unimplemented navigation:', e);
        }
      }
    }

    document.addEventListener('DOMContentLoaded', () => {
      showPage('feed');

      // Initialize map chart
      try {
        echarts.init(document.getElementById('chart1')).setOption({
          tooltip: { trigger: 'item' },
          series: [{
            type: 'scatter',
            coordinateSystem: 'geo',
            data: [
              { name: 'Paris', value: [2.3522, 48.8566, 1] },
              { name: 'Tokyo', value: [139.6917, 35.6895, 1] },
              { name: 'New York', value: [-74.0060, 40.7128, 1] }
            ],
            symbolSize: 12,
            itemStyle: {
              color: '#b45309'
            }
          }],
          geo: {
            map: 'world',
            roam: true,
            itemStyle: {
              areaColor: '#f5f5f4',
              borderColor: '#d6d3d1'
            },
            emphasis: {
              itemStyle: {
                areaColor: '#fef3c7'
              }
            }
          }
        });
      } catch (e) {
        console.error('Chart1 failed:', e);
      }
    });

    document.addEventListener('click', (e) => {
      const target = e.target.closest('[data-nav]');
      if (target) {
        e.preventDefault();
        const pageId = target.getAttribute('data-nav');
        showPage(pageId);
      }
    });
  </script>
</div>
`;

// Prompt to modify the content
const prompt = "Use your imagination to fullest and write a story on cafes in paris.Provide detailed view on few of the cafes in paris";

// Updated function to handle stream: true
async function testModifyContent() {
  try {
     console.log('Sending request to modify-content API...');

    // // Stream: false request
    // const response = await axios.post('http://localhost:5000/api/llm/modify-content', {
    //   htmlContent,
    //   prompt,
    //   stream: false
    // });

    // console.log('Response received for stream: false!');

    // // Save the response to a file
    // const outputPath = path.join(__dirname, 'modified-content.html');
    // fs.writeFileSync(outputPath, response.data);

    // console.log(`Modified content saved to ${outputPath}`);

    // Stream: true request
    console.log('Sending request to modify-content API with stream: true...');

    const streamResponse = await axios.post('http://localhost:5000/api/llm/modify-content', {
      htmlContent,
      prompt,
      stream: true
    }, {
      responseType: 'stream'
    });

    const streamOutputPath = path.join(__dirname, 'modified-content-stream.html');
    const writer = fs.createWriteStream(streamOutputPath);

    streamResponse.data.pipe(writer);

    writer.on('finish', () => {
      console.log(`Modified content (stream) saved to ${streamOutputPath}`);
      console.log('All tests completed successfully!');
    });

    writer.on('error', (err) => {
      console.error('Error writing stream response:', err);
    });

  } catch (error) {
    console.error('Error testing modify-content API:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
      if (error.response.data && error.response.data.stack) {
        console.error('Error stack:', error.response.data.stack);
      }
    } else {
      console.error('Full error:', error);
    }
  }
}

// Run the test
testModifyContent();
