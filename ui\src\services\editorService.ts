/**
 * Production-ready Editor Service
 * Handles all backend communication with proper error handling, retries, and streaming
 */

import {
  EditRequest,
  EditResponse,
  EditOperation,
  PageContent,
  EditOptions,
  ApiError,
  ResponseMetadata
} from '../types/editor.types';

// ============================================================================
// CONFIGURATION
// ============================================================================

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';
const DEFAULT_TIMEOUT = 30000; // 30 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// ============================================================================
// CORE EDITOR SERVICE CLASS
// ============================================================================

export class EditorService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private abortControllers: Map<string, AbortController>;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/llm/v3`;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
    this.abortControllers = new Map();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /**
   * Generate new HTML content from a prompt
   */
  async generateContent(
    prompt: string,
    options: EditOptions = {}
  ): Promise<EditResponse> {
    const operation: EditOperation = {
      id: this.generateOperationId(),
      type: 'create',
      target: { pageId: 'new', scope: 'page' },
      payload: { prompt },
      metadata: {
        timestamp: new Date(),
        sessionId: this.getSessionId(),
        priority: 'medium'
      }
    };

    return this.executeOperation(operation, options);
  }

  /**
   * Edit existing content with targeted changes
   */
  async editContent(
    pageId: string,
    htmlContent: string,
    prompt: string,
    elementSelector?: string,
    options: EditOptions = {}
  ): Promise<EditResponse> {
    const operation: EditOperation = {
      id: this.generateOperationId(),
      type: 'update',
      target: {
        pageId,
        elementSelector,
        scope: elementSelector ? 'element' : 'page'
      },
      payload: {
        prompt,
        context: {
          userIntent: prompt,
          preserveElements: options.preserveFormatting ? ['*'] : undefined
        }
      },
      metadata: {
        timestamp: new Date(),
        sessionId: this.getSessionId(),
        priority: 'high'
      }
    };

    const request: EditRequest = {
      operation,
      options: {
        streaming: true,
        validateOutput: true,
        ...options
      }
    };

    return this.processEditRequest(htmlContent, request);
  }

  /**
   * Link multiple pages with navigation
   */
  async linkPages(
    pages: Array<{ id: string; name: string; content: string }>,
    options: EditOptions = {}
  ): Promise<EditResponse[]> {
    const operations = pages.map(page => ({
      id: this.generateOperationId(),
      type: 'navigation' as const,
      target: { pageId: page.id, scope: 'page' as const },
      payload: {
        prompt: this.generateNavigationPrompt(pages, page.id),
        context: {
          userIntent: 'Update navigation links',
          relatedElements: ['nav', 'header', 'menu']
        }
      },
      metadata: {
        timestamp: new Date(),
        sessionId: this.getSessionId(),
        priority: 'medium' as const
      }
    }));

    // Execute operations in parallel with concurrency limit
    return this.executeBatchOperations(operations, pages, options);
  }

  /**
   * Cancel an ongoing operation
   */
  cancelOperation(operationId: string): void {
    const controller = this.abortControllers.get(operationId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(operationId);
    }
  }

  /**
   * Get operation status
   */
  async getOperationStatus(operationId: string): Promise<{
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress?: number;
    message?: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/operations/${operationId}/status`, {
        headers: this.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to get operation status: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting operation status:', error);
      return { status: 'failed', message: 'Failed to get status' };
    }
  }

  // ============================================================================
  // PRIVATE IMPLEMENTATION METHODS
  // ============================================================================

  private async executeOperation(
    operation: EditOperation,
    options: EditOptions
  ): Promise<EditResponse> {
    const controller = new AbortController();
    this.abortControllers.set(operation.id, controller);

    try {
      const endpoint = operation.type === 'create' ? '/generate-html' : '/edit';
      const response = await this.makeRequest(endpoint, {
        method: 'POST',
        headers: this.defaultHeaders,
        credentials: 'include',
        signal: controller.signal,
        body: JSON.stringify({
          prompt: operation.payload.prompt,
          elementSelector: operation.target.elementSelector,
          options
        })
      });

      if (options.streaming) {
        return this.handleStreamingResponse(response, operation);
      } else {
        return this.handleStandardResponse(response, operation);
      }
    } catch (error) {
      return this.handleError(error, operation);
    } finally {
      this.abortControllers.delete(operation.id);
    }
  }

  private async processEditRequest(
    htmlContent: string,
    request: EditRequest
  ): Promise<EditResponse> {
    const controller = new AbortController();
    this.abortControllers.set(request.operation.id, controller);

    try {
      const response = await this.makeRequest('/edit', {
        method: 'POST',
        headers: this.defaultHeaders,
        credentials: 'include',
        signal: controller.signal,
        body: JSON.stringify({
          htmlContent,
          prompt: request.operation.payload.prompt,
          elementSelector: request.operation.target.elementSelector,
          ...request.options
        })
      });

      return this.handleStreamingResponse(response, request.operation);
    } catch (error) {
      return this.handleError(error, request.operation);
    } finally {
      this.abortControllers.delete(request.operation.id);
    }
  }

  private async executeBatchOperations(
    operations: EditOperation[],
    pages: Array<{ id: string; name: string; content: string }>,
    options: EditOptions
  ): Promise<EditResponse[]> {
    const CONCURRENCY_LIMIT = 3;
    const results: EditResponse[] = [];
    
    for (let i = 0; i < operations.length; i += CONCURRENCY_LIMIT) {
      const batch = operations.slice(i, i + CONCURRENCY_LIMIT);
      const batchPromises = batch.map(async (operation) => {
        const page = pages.find(p => p.id === operation.target.pageId);
        if (!page) {
          throw new Error(`Page not found: ${operation.target.pageId}`);
        }
        
        return this.processEditRequest(page.content, { operation, options });
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            error: {
              code: 'BATCH_OPERATION_FAILED',
              message: result.reason?.message || 'Operation failed',
              retryable: true
            },
            metadata: {
              requestId: this.generateRequestId(),
              duration: 0,
              timestamp: new Date()
            }
          });
        }
      }

      // Small delay between batches to avoid overwhelming the API
      if (i + CONCURRENCY_LIMIT < operations.length) {
        await this.delay(500);
      }
    }

    return results;
  }

  private async handleStreamingResponse(
    response: Response,
    operation: EditOperation
  ): Promise<EditResponse> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body available for streaming');
    }

    const decoder = new TextDecoder();
    let accumulatedContent = '';
    let isCollectingHTML = false;
    const startTime = Date.now();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              break;
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
          }
        }
      }

      const cleanHtml = this.extractHtmlFromResponse(accumulatedContent);
      const duration = Date.now() - startTime;

      return {
        success: true,
        result: {
          content: { html: cleanHtml },
          changes: [{
            type: 'modification',
            target: operation.target.elementSelector || 'page',
            after: cleanHtml,
            description: `Applied: ${operation.payload.prompt}`
          }]
        },
        metadata: {
          requestId: operation.id,
          duration,
          timestamp: new Date()
        }
      };
    } catch (error) {
      throw new Error(`Streaming failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleStandardResponse(
    response: Response,
    operation: EditOperation
  ): Promise<EditResponse> {
    const data = await response.json();
    const duration = Date.now() - operation.metadata.timestamp.getTime();

    return {
      success: true,
      result: {
        content: { html: data.html || data.content },
        changes: [{
          type: 'modification',
          target: operation.target.elementSelector || 'page',
          after: data.html || data.content,
          description: `Applied: ${operation.payload.prompt}`
        }]
      },
      metadata: {
        requestId: operation.id,
        duration,
        timestamp: new Date()
      }
    };
  }

  private handleError(error: any, operation: EditOperation): EditResponse {
    const apiError: ApiError = {
      code: error.name || 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      retryable: !error.name?.includes('Abort') && error.status !== 400
    };

    return {
      success: false,
      error: apiError,
      metadata: {
        requestId: operation.id,
        duration: Date.now() - operation.metadata.timestamp.getTime(),
        timestamp: new Date()
      }
    };
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private async makeRequest(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;
    
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const response = await fetch(url, {
          ...options,
          signal: options.signal
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      } catch (error) {
        if (attempt === MAX_RETRIES || error instanceof DOMException) {
          throw error;
        }
        
        await this.delay(RETRY_DELAY * attempt);
      }
    }

    throw new Error('Max retries exceeded');
  }

  private generateNavigationPrompt(
    pages: Array<{ id: string; name: string }>,
    currentPageId: string
  ): string {
    const otherPages = pages.filter(p => p.id !== currentPageId);
    
    return `Update the navigation bar to include links to all pages: ${otherPages.map(p => p.name).join(', ')}
    
🎯 REQUIREMENTS:
- Keep existing design exactly the same
- Add proper <a> tags for each page link
- Maintain responsive behavior
- Preserve all other content on the page`;
  }

  private extractHtmlFromResponse(content: string): string {
    // Remove any markdown formatting and extract clean HTML
    const htmlMatch = content.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      return htmlMatch[1].trim();
    }
    
    // If no markdown, return content as-is but clean it
    return content.replace(/^\s*```\s*/, '').replace(/\s*```\s*$/, '').trim();
  }

  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getSessionId(): string {
    // In a real app, this would come from authentication
    return `session_${Date.now()}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const editorService = new EditorService();
export default editorService;
