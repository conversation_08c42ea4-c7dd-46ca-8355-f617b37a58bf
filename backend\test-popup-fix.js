/**
 * Test script to verify popup implementation fixes
 */

const LLMServiceV3 = require('./services/llmServiceV3');

// Test data - simple button (like the original issue)
const testHtmlContent = `<button data-nav="dashboard" class="border-blue-500 text-gray-900 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm">Dashboard</button>`;

const testPrompt = "add popup for new lead";

// Mock response object for testing
class MockResponse {
  constructor() {
    this.events = [];
    this.ended = false;
  }

  write(data) {
    console.log('📡 SSE Event:', data.trim());
    this.events.push(data);
  }

  end() {
    this.ended = true;
    console.log('📡 Response ended');
  }
}

async function testPopupFix() {
  console.log('🧪 Testing Popup Implementation Fix');
  console.log('=' .repeat(50));
  
  console.log('📏 Input HTML length:', testHtmlContent.length);
  console.log('📝 Input HTML:', testHtmlContent);
  console.log('📝 Prompt:', testPrompt);
  console.log('');

  // Test 1: Intent Analysis
  console.log('🔍 Testing Intent Analysis...');
  const intentAnalysis = await LLMServiceV3.analyzePromptIntent(testPrompt, testHtmlContent);
  console.log('📊 Intent Analysis Result:', JSON.stringify(intentAnalysis, null, 2));
  console.log('');

  // Test 2: Response Cleaning
  console.log('🧹 Testing Response Cleaning...');
  const mockLLMResponse = `<div class="test">
  <button onclick="openPopup()">New Lead</button>
</div>

<script>
function openPopup() {
  alert('Popup opened!');
}
</script>

I've made the minimal change by:
1. Adding a new "New Lead" button
2. Including JavaScript functions
3. Ensuring the popup works properly

The styling matches the existing design.`;

  console.log('📝 Mock LLM Response (before cleaning):');
  console.log(mockLLMResponse);
  console.log('');

  const cleanedResponse = LLMServiceV3.cleanLLMResponse(mockLLMResponse);
  console.log('🧹 Cleaned Response:');
  console.log(cleanedResponse);
  console.log('');

  // Test 3: Check if context decision is correct
  console.log('🎯 Context Decision Test:');
  if (intentAnalysis.needsFullContext === true) {
    console.log('✅ CORRECT: Will use full context for popup request');
  } else if (intentAnalysis.needsFullContext === false) {
    console.log('❌ INCORRECT: Will use fragment for popup request (this was the bug)');
  } else {
    console.log('🤔 AMBIGUOUS: Will default to full context (safe)');
  }
  console.log('');

  console.log('🎯 Summary:');
  console.log('- Intent Analysis:', intentAnalysis.changeType);
  console.log('- Needs Full Context:', intentAnalysis.needsFullContext);
  console.log('- Confidence:', intentAnalysis.confidence);
  console.log('- Response Cleaning: Removed explanatory text');
  
  console.log('');
  console.log('🚀 Test Complete!');
}

// Run the test
testPopupFix().catch(console.error);
