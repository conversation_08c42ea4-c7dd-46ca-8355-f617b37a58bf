const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const uiNodeSchema = require('../../schemas/uiNode.schema.json');

class SchemaValidator {
  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    addFormats(this.ajv);

    // Add schemas
    this.ajv.addSchema(uiNodeSchema, 'uiNode');
  }

  validate(data, schemaName) {
    const validate = this.ajv.getSchema(schemaName);
    if (!validate) {
      throw new Error(`Schema ${schemaName} not found`);
    }

    const valid = validate(data);
    if (!valid) {
      return {
        valid: false,
        errors: validate.errors
      };
    }

    return {
      valid: true,
      errors: null
    };
  }

  validateUINode(node) {
    return this.validate(node, 'uiNode');
  }
}

module.exports = new SchemaValidator();
