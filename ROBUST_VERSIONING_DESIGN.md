# Robust Page-Level Versioning System Design

## Overview

This design implements a robust, maintainable versioning system that tracks changes at the page level while providing excellent user experience.

## Core Principles

1. **Page-Level Granularity**: Track individual page changes, not just prototype-level changes
2. **Version Snapshots**: Each version contains a complete snapshot of all pages at that point in time
3. **Clear Change Attribution**: Users can see exactly what changed between versions
4. **Efficient Storage**: Avoid duplicate storage while maintaining fast access
5. **Intuitive UX**: Users understand what they're seeing when switching versions

## Database Schema Design

### 1. Enhanced Version Table
```sql
CREATE TABLE prototype_versions (
    id SERIAL PRIMARY KEY,
    prototype_id INTEGER NOT NULL REFERENCES prototypes(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    change_description TEXT,
    operation_type VARCHAR(50) NOT NULL, -- 'initial', 'page_added', 'page_modified', 'page_deleted'
    changed_page_ids INTEGER[], -- Array of page IDs that changed in this version
    user_prompt TEXT,
    llm_response TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(prototype_id, version_number)
);
```

### 2. Page Version Snapshots Table
```sql
CREATE TABLE page_version_snapshots (
    id SERIAL PRIMARY KEY,
    version_id INTEGER NOT NULL REFERENCES prototype_versions(id) ON DELETE CASCADE,
    page_id INTEGER NOT NULL REFERENCES prototype_pages(id) ON DELETE CASCADE,
    page_title VARCHAR(500) NOT NULL,
    html_content TEXT NOT NULL,
    css_content TEXT,
    page_order INTEGER NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    change_type VARCHAR(20) NOT NULL, -- 'added', 'modified', 'unchanged', 'deleted'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(version_id, page_id)
);
```

### 3. Version Navigation Index
```sql
CREATE INDEX idx_page_snapshots_version_order ON page_version_snapshots(version_id, page_order);
CREATE INDEX idx_page_snapshots_page_history ON page_version_snapshots(page_id, version_id);
```

## Versioning Rules Implementation

### Rule 1: First Page Creation → V1
```typescript
async createInitialVersion(prototypeId: number, pageId: number) {
  const version = await createVersion({
    prototypeId,
    versionNumber: 1,
    operationType: 'initial',
    changeDescription: 'Initial prototype version',
    changedPageIds: [pageId]
  });
  
  await createPageSnapshot({
    versionId: version.id,
    pageId,
    changeType: 'added'
  });
}
```

### Rule 2: New Page Addition → Add to Current Version
```typescript
async addPageToCurrentVersion(prototypeId: number, pageId: number) {
  const currentVersion = await getCurrentVersion(prototypeId);
  
  // Add page snapshot to current version
  await createPageSnapshot({
    versionId: currentVersion.id,
    pageId,
    changeType: 'added'
  });
  
  // Update version metadata
  await updateVersion(currentVersion.id, {
    changedPageIds: [...currentVersion.changedPageIds, pageId],
    operationType: 'page_added'
  });
}
```

### Rule 3: Page Deletion → Remove from Current Version
```typescript
async removePageFromCurrentVersion(prototypeId: number, pageId: number) {
  const currentVersion = await getCurrentVersion(prototypeId);
  
  // Mark page as deleted in current version
  await updatePageSnapshot(currentVersion.id, pageId, {
    changeType: 'deleted'
  });
  
  // Update version metadata
  await updateVersion(currentVersion.id, {
    changedPageIds: [...currentVersion.changedPageIds, pageId],
    operationType: 'page_deleted'
  });
}
```

### Rule 4: Page Modification → Create New Version
```typescript
async createVersionForPageModification(prototypeId: number, pageId: number) {
  const currentVersion = await getCurrentVersion(prototypeId);
  const nextVersionNumber = currentVersion.versionNumber + 1;
  
  // Create new version
  const newVersion = await createVersion({
    prototypeId,
    versionNumber: nextVersionNumber,
    operationType: 'page_modified',
    changeDescription: `Modified page: ${pageName}`,
    changedPageIds: [pageId]
  });
  
  // Copy all pages from current version
  const allPages = await getPageSnapshotsForVersion(currentVersion.id);
  
  for (const page of allPages) {
    const changeType = page.page_id === pageId ? 'modified' : 'unchanged';
    await createPageSnapshot({
      versionId: newVersion.id,
      pageId: page.page_id,
      changeType
    });
  }
}
```

## API Design

### Version Management API
```typescript
// Get version with all pages
GET /api/prototypes/:id/versions/:versionNumber
Response: {
  version: VersionMetadata,
  pages: PageSnapshot[],
  changes: ChangesSummary
}

// Switch to version
POST /api/prototypes/:id/versions/:versionNumber/switch
Response: {
  pages: PageSnapshot[],
  changedPages: number[]
}

// Get version comparison
GET /api/prototypes/:id/versions/:v1/compare/:v2
Response: {
  added: PageSnapshot[],
  modified: PageSnapshot[],
  deleted: PageSnapshot[],
  unchanged: PageSnapshot[]
}
```

## UI/UX Enhancements

### 1. Version Switcher with Change Indicators
```typescript
<VersionSwitcher
  versions={versions}
  currentVersion={currentVersion}
  onVersionChange={handleVersionSwitch}
  showChangeIndicators={true}
  changesSummary={changesSummary}
/>
```

### 2. Page List with Version Context
```typescript
<PageList
  pages={versionPages}
  currentVersion={currentVersion}
  onPageSelect={handlePageSelect}
  showChangeTypes={true} // Show added/modified/deleted indicators
/>
```

### 3. Version Comparison View
```typescript
<VersionComparison
  fromVersion={v1}
  toVersion={v2}
  changes={versionChanges}
  onPageDiffView={showPageDiff}
/>
```

## Benefits of This Approach

### 1. **Robust Data Model**
- Clear separation between versions and page snapshots
- Efficient storage with minimal duplication
- Complete audit trail of all changes

### 2. **Excellent User Experience**
- Users see exactly what changed between versions
- Fast version switching with clear visual feedback
- Intuitive understanding of version history

### 3. **Maintainable Architecture**
- Clean separation of concerns
- Easy to extend with new features
- Consistent API patterns

### 4. **Performance Optimized**
- Indexed queries for fast version retrieval
- Minimal data transfer for version switches
- Efficient change detection algorithms

## Migration Strategy

1. **Phase 1**: Implement new schema alongside existing system
2. **Phase 2**: Migrate existing data to new format
3. **Phase 3**: Update UI to use new versioning system
4. **Phase 4**: Remove legacy versioning code

This design provides a much more robust foundation for versioning while delivering the exact user experience you described.
