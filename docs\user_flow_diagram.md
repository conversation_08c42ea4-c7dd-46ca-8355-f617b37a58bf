# JustPrototype User Flow Diagram

This document illustrates the user flow through the JustPrototype application, focusing on the user experience.

## Main User Flow

```mermaid
flowchart TD
    Start[User visits site] --> Auth{Authenticated?}
    
    Auth -->|No| Login[Login Page]
    Login --> OAuth[OAuth Login]
    OAuth --> Dashboard
    
    Auth -->|Yes| Dashboard[Dashboard / My Prototypes]
    
    Dashboard --> Create[Create New Prototype]
    Dashboard --> View[View Existing Prototype]
    
    Create --> PromptScreen[Prompt Screen]
    PromptScreen --> EnterPrompt[Enter Prompt]
    EnterPrompt --> GeneratePlan[Generate Plan]
    GeneratePlan --> PlanGenerated[Plan Generated]
    PlanGenerated --> GenerateCode[Generate Code]
    GenerateCode --> CodeGenerated[Code Generated]
    CodeGenerated --> ModifyElement[Modify Element]
    ModifyElement --> FinalPrototype[Final Prototype]
    
    View --> PrototypeDetails[Prototype Details]
    PrototypeDetails --> Edit[Edit Prototype]
    PrototypeDetails --> Share[Share Prototype]
    
    Edit --> PromptScreen
    
    FinalPrototype --> Save[Save Prototype]
    Save --> Dashboard
```

## Prompt Generation Flow (User Perspective)

```mermaid
flowchart TD
    Start[User enters prompt] --> Submit[Submit prompt]
    Submit --> Loading[Loading indicator]
    
    Loading --> Stream{Streaming enabled?}
    
    Stream -->|Yes| StreamContent[Content streams in real-time]
    StreamContent --> Complete[Generation complete]
    
    Stream -->|No| Wait[Wait for complete response]
    Wait --> DisplayResponse[Display complete response]
    
    Complete --> Options[Show options]
    DisplayResponse --> Options
    
    Options --> GenerateCode[Generate Code]
    Options --> ModifyPrompt[Modify with new prompt]
    Options --> SavePrototype[Save prototype]
    
    GenerateCode --> CodeGeneration[Code generation flow]
    ModifyPrompt --> NewPrompt[New prompt flow]
    SavePrototype --> Dashboard[Return to dashboard]
```

## Prototype Modification Flow

```mermaid
flowchart TD
    Start[User views prototype] --> Select[Select element to modify]
    Select --> EnterPrompt[Enter modification prompt]
    EnterPrompt --> Submit[Submit modification]
    
    Submit --> Loading[Loading indicator]
    Loading --> ModifiedElement[Element modified]
    
    ModifiedElement --> Continue{Continue modifying?}
    
    Continue -->|Yes| Select
    Continue -->|No| Save[Save changes]
    Save --> View[View final prototype]
```

## Authentication Flow

```mermaid
flowchart TD
    Start[User visits site] --> CheckAuth[Check authentication]
    
    CheckAuth --> Auth{Authenticated?}
    
    Auth -->|No| LoginPage[Show login page]
    LoginPage --> ChooseProvider[Choose login provider]
    ChooseProvider --> Google[Google OAuth]
    ChooseProvider --> Other[Other providers]
    
    Google --> OAuthRedirect[Redirect to OAuth]
    Other --> OAuthRedirect
    
    OAuthRedirect --> OAuthCallback[OAuth callback]
    OAuthCallback --> CreateSession[Create user session]
    CreateSession --> CheckQuota[Check user quota]
    CheckQuota --> Dashboard[Show dashboard]
    
    Auth -->|Yes| Dashboard
```

## Quota Management Flow

```mermaid
flowchart TD
    Start[User action] --> CheckQuota[Check user quota]
    
    CheckQuota --> QuotaStatus{Quota status}
    
    QuotaStatus -->|Has quota| AllowAction[Allow action]
    AllowAction --> DecrementQuota[Decrement quota]
    DecrementQuota --> LogUsage[Log usage]
    
    QuotaStatus -->|No quota| CheckPlan{User plan}
    
    CheckPlan -->|Free| ShowUpgrade[Show upgrade modal]
    ShowUpgrade --> UpgradeOption[Offer upgrade to Pro]
    
    CheckPlan -->|Pro| QuotaExceeded[Show quota exceeded]
    QuotaExceeded --> ContactSupport[Offer contact support]
```

## Sharing Flow

```mermaid
flowchart TD
    Start[User views prototype] --> ShareButton[Click Share button]
    
    ShareButton --> ShareModal[Open share modal]
    ShareModal --> CopyLink[Copy public link]
    ShareModal --> PrivateSharing[Set up private sharing]
    
    CopyLink --> ShareLink[Share link externally]
    PrivateSharing --> EnterEmails[Enter recipient emails]
    EnterEmails --> SendInvites[Send invites]
    
    ShareLink --> RecipientAccess[Recipient accesses prototype]
    SendInvites --> RecipientAccess
    
    RecipientAccess --> ViewOnly[View-only access]
```

## Responsive Design Flow

```mermaid
flowchart TD
    Start[User accesses site] --> DetectDevice[Detect device]
    
    DetectDevice --> DeviceType{Device type}
    
    DeviceType -->|Desktop| DesktopLayout[Show desktop layout]
    DesktopLayout --> FullFeatures[All features available]
    
    DeviceType -->|Tablet| TabletLayout[Show tablet layout]
    TabletLayout --> AdaptedUI[Adapted UI for touch]
    
    DeviceType -->|Mobile| MobileLayout[Show mobile layout]
    MobileLayout --> SimplifiedUI[Simplified UI]
    
    FullFeatures --> UserInteraction[User interacts with app]
    AdaptedUI --> UserInteraction
    SimplifiedUI --> UserInteraction
```
