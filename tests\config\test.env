# JustPrototype E2E Test Environment Configuration

# Application URLs
TEST_BASE_URL=http://localhost:5173
TEST_API_BASE_URL=http://localhost:3001/api
PRODUCTION_URL=https://app.justprototype.dev
PRODUCTION_API_URL=https://api.justprototype.dev

# Test Environment
TEST_ENVIRONMENT=local
CI=false

# Authentication
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=test123
GOOGLE_TEST_EMAIL=<EMAIL>
GOOGLE_TEST_PASSWORD=testpassword

# Database (for test data setup)
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_NAME=justprototype_test
TEST_DB_USER=postgres
TEST_DB_PASSWORD=password

# LLM API Configuration (for mocking)
MOCK_LLM_RESPONSES=true
LLM_RESPONSE_DELAY=1000
LITELLM_PROXY_URL=https://litellm-production-744f.up.railway.app

# Test Configuration
HEADLESS=true
SLOW_MO=0
VIDEO_MODE=retain-on-failure
SCREENSHOT_MODE=only-on-failure
TRACE_MODE=on-first-retry

# Timeouts (in milliseconds)
DEFAULT_TIMEOUT=30000
NAVIGATION_TIMEOUT=30000
ACTION_TIMEOUT=15000
GLOBAL_TIMEOUT=3600000

# Test Data
COFFEE_SHOP_PROMPT="Create a modern landing page for a coffee shop with hero section, menu preview, and contact information"
CONTACT_FORM_PROMPT="Add a contact form with name, email, and message fields"
COLOR_CHANGE_PROMPT="Change the color scheme to use blue and white colors"

# Parallel Execution
WORKERS=4
RETRIES=2

# Reporting
REPORT_OUTPUT_DIR=../reports
HTML_REPORT=true
JSON_REPORT=true
JUNIT_REPORT=true

# Debug Settings
DEBUG_MODE=false
VERBOSE_LOGGING=false
SAVE_NETWORK_LOGS=true
SAVE_CONSOLE_LOGS=true
