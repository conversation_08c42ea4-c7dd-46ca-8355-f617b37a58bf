/**
 * Client-side diff utilities for applying HTML diffs
 * Uses diff-match-patch library for efficient HTML updates
 */

import { diff_match_patch } from 'diff-match-patch';

export interface DiffResult {
  success: boolean;
  html?: string;
  error?: string;
  patchResults?: boolean[];
  appliedPatches?: number;
  totalPatches?: number;
}

export interface DiffData {
  shouldUseDiff: boolean;
  patches: string | null;
  stats: {
    additions: number;
    deletions: number;
    unchanged: number;
    totalChanges: number;
    changePercentage: number;
    diffOperations: number;
  } | null;
  metadata: {
    originalSize: number;
    modifiedSize: number;
    patchSize: number;
    compressionRatio: number;
    timestamp: string;
  } | null;
  fallbackHtml: string | null;
}

export interface StreamingDiffResponse {
  type: 'diff' | 'html' | 'error';
  data: DiffData | string;
  timestamp: string;
}

class DiffUtils {
  private dmp: diff_match_patch;

  constructor() {
    this.dmp = new diff_match_patch();
    
    // Configure for optimal HTML diffing
    this.dmp.Diff_Timeout = 1.0;
    this.dmp.Match_Threshold = 0.8;
    this.dmp.Match_Distance = 1000;
    this.dmp.Patch_DeleteThreshold = 0.5;
    this.dmp.Patch_Margin = 4;
  }

  /**
   * Apply diff patches to original HTML content
   * @param originalHtml - Original HTML content
   * @param patchText - Patch text to apply
   * @returns Result with applied HTML or error
   */
  applyDiff(originalHtml: string, patchText: string): DiffResult {
    try {
      if (!originalHtml || !patchText) {
        return {
          success: false,
          error: 'Missing original HTML or patch text'
        };
      }

      const normalizedOriginal = this.normalizeHtml(originalHtml);
      const patches = this.dmp.patch_fromText(patchText);
      
      if (!patches || patches.length === 0) {
        return {
          success: false,
          error: 'Invalid or empty patch text'
        };
      }

      const [modifiedHtml, results] = this.dmp.patch_apply(patches, normalizedOriginal);

      // Check if all patches applied successfully
      const allPatchesApplied = results.every(result => result === true);
      const appliedPatches = results.filter(r => r === true).length;

      return {
        success: allPatchesApplied,
        html: modifiedHtml,
        patchResults: results,
        appliedPatches,
        totalPatches: results.length
      };

    } catch (error) {
      console.error('[DiffUtils] Error applying diff:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error applying diff',
        html: originalHtml // Fallback to original
      };
    }
  }

  /**
   * Process streaming diff response
   * @param originalHtml - Original HTML content
   * @param diffResponse - Streaming diff response from backend
   * @returns Processed HTML content
   */
  processStreamingDiff(originalHtml: string, diffResponse: StreamingDiffResponse): string {
    try {
      if (diffResponse.type === 'error') {
        console.error('[DiffUtils] Received error response:', diffResponse.data);
        return originalHtml;
      }

      if (diffResponse.type === 'html') {
        // Direct HTML response, return as-is
        return diffResponse.data as string;
      }

      if (diffResponse.type === 'diff') {
        const diffData = diffResponse.data as DiffData;

        // If backend says not to use diff, use fallback HTML
        if (!diffData.shouldUseDiff) {
          return diffData.fallbackHtml || originalHtml;
        }

        // Apply diff patches
        if (diffData.patches) {
          const result = this.applyDiff(originalHtml, diffData.patches);
          
          if (result.success && result.html) {
            console.log('[DiffUtils] Successfully applied diff:', {
              appliedPatches: result.appliedPatches,
              totalPatches: result.totalPatches,
              stats: diffData.stats
            });
            return result.html;
          } else {
            console.warn('[DiffUtils] Failed to apply diff, using fallback:', result.error);
            return diffData.fallbackHtml || originalHtml;
          }
        }
      }

      return originalHtml;

    } catch (error) {
      console.error('[DiffUtils] Error processing streaming diff:', error);
      return originalHtml;
    }
  }

  /**
   * Normalize HTML for consistent diffing
   * @param html - HTML content to normalize
   * @returns Normalized HTML
   */
  private normalizeHtml(html: string): string {
    if (!html || typeof html !== 'string') {
      return '';
    }

    return html
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Remove extra spaces around tags
      .replace(/>\s+</g, '><')
      // Normalize line endings
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Trim
      .trim();
  }

  /**
   * Validate if content is suitable for diff processing
   * @param html - HTML content to validate
   * @returns Whether content is valid for diffing
   */
  validateHtmlForDiff(html: string): boolean {
    if (!html || typeof html !== 'string') {
      return false;
    }

    // Check for minimum content length
    if (html.trim().length < 10) {
      return false;
    }

    // Check for basic HTML structure
    const hasHtmlTags = /<[^>]+>/g.test(html);
    return hasHtmlTags;
  }

  /**
   * Calculate compression ratio for diff vs full HTML
   * @param originalSize - Size of original HTML
   * @param patchSize - Size of patch data
   * @returns Compression ratio (0-1, lower is better)
   */
  calculateCompressionRatio(originalSize: number, patchSize: number): number {
    if (originalSize === 0) return 1;
    return patchSize / originalSize;
  }

  /**
   * Create a debounced diff applier for streaming updates
   * @param callback - Callback to call with updated HTML
   * @param delay - Debounce delay in milliseconds
   * @returns Debounced function
   */
  createDebouncedDiffApplier(
    callback: (html: string) => void, 
    delay: number = 100
  ): (originalHtml: string, diffResponse: StreamingDiffResponse) => void {
    let timeoutId: NodeJS.Timeout | null = null;

    return (originalHtml: string, diffResponse: StreamingDiffResponse) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(() => {
        const updatedHtml = this.processStreamingDiff(originalHtml, diffResponse);
        callback(updatedHtml);
      }, delay);
    };
  }

  /**
   * Get diff utility statistics and configuration
   * @returns Utility statistics
   */
  getStats() {
    return {
      version: '1.0.0',
      diffTimeout: this.dmp.Diff_Timeout,
      matchThreshold: this.dmp.Match_Threshold,
      matchDistance: this.dmp.Match_Distance,
      patchDeleteThreshold: this.dmp.Patch_DeleteThreshold,
      patchMargin: this.dmp.Patch_Margin
    };
  }
}

// Export singleton instance
export const diffUtils = new DiffUtils();

// Export class for custom instances
export { DiffUtils };

// Helper function for quick diff application
export function applyHtmlDiff(originalHtml: string, patchText: string): DiffResult {
  return diffUtils.applyDiff(originalHtml, patchText);
}

// Helper function for processing streaming responses
export function processStreamingDiffResponse(
  originalHtml: string, 
  diffResponse: StreamingDiffResponse
): string {
  return diffUtils.processStreamingDiff(originalHtml, diffResponse);
}
