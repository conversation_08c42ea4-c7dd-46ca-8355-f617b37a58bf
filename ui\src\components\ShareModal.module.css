.modal {
  position: relative;
  z-index: 50;
}

.backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.container {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.panel {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.closeButton {
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background-color: #f3f4f6;
}

.closeIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.content {
  padding: 1.5rem;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.tab {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.tab:hover {
  color: #4f46e5;
}

.activeTab {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
}

.section {
  margin-bottom: 1.5rem;
}

.description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.shareLink {
  display: flex;
  margin-bottom: 1rem;
}

.linkContainer {
  display: flex;
  align-items: center;
  flex-grow: 1;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  margin-right: 0.5rem;
}

.linkIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
  margin-right: 0.5rem;
}

.linkInput {
  flex-grow: 1;
  background: transparent;
  border: none;
  font-size: 0.875rem;
  color: #111827;
  outline: none;
  width: 100%;
}

.copyButton {
  display: flex;
  align-items: center;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copyButton:hover {
  background-color: #4338ca;
}

.copyIcon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

.createButton {
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.createButton:hover {
  background-color: #4338ca;
}

.createButton:disabled {
  background-color: #a5b4fc;
  cursor: not-allowed;
}

.formGroup {
  margin-bottom: 1rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.input {
  width: 100%;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.2s;
}

.input:focus {
  outline: none;
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.5);
}

.select {
  width: 100%;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.2s;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

.select:focus {
  outline: none;
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.5);
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.sharesSection {
  margin-top: 2rem;
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.sharesTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.sharesList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.shareItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.shareInfo {
  display: flex;
  flex-direction: column;
}

.publicBadge {
  display: inline-flex;
  align-items: center;
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  margin-bottom: 0.25rem;
}

.accessLevel {
  font-size: 0.75rem;
  color: #6b7280;
}

.shareActions {
  display: flex;
  align-items: center;
}

.copyLinkButton {
  background-color: transparent;
  color: #4f46e5;
  border: none;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  margin-right: 0.5rem;
}

.copyLinkButton:hover {
  text-decoration: underline;
}

.deleteButton {
  background-color: transparent;
  color: #ef4444;
  border: none;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
}

.deleteButton:hover {
  text-decoration: underline;
}

.deleteButton:disabled {
  color: #f87171;
  cursor: not-allowed;
}
