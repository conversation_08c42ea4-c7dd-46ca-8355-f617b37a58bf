import { useState, useCallback } from 'react';
import { UINode } from '../types/uiNode';

type SSEMessage = {
  type: 'start' | 'message' | 'token' | 'content' | 'end' | 'error';
  content?: any;
  text?: string;
  error?: string;
  [key: string]: any;
};

type ASTOperation = 
  | { type: 'addChild'; parentId: string; child: UINode }
  | { type: 'updateProps'; nodeId: string; props: Record<string, any> }
  | { type: 'removeNode'; nodeId: string };

type UseAstReturn = {
  ast: UINode | null;
  snapshotId: string | null;
  error: string | null;
  loading: boolean;
  generateFromPlan: (prompt: string, provider?: string) => Promise<void>;
  addChild: (parentId: string, child: UINode) => void;
  updateNodeProps: (nodeId: string, props: Record<string, any>) => void;
  removeNode: (nodeId: string) => void;
};

export function useAst(): UseAstReturn {
  const [ast, setAst] = useState<UINode | null>(null);
  const [snapshotId, setSnapshotId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  /**
   * Generate AST from plan using LLM
   */
  const generateFromPlan = useCallback(async (prompt: string, provider: string = 'openai'): Promise<void> => {
    setLoading(true);
    setError(null);
    
    // Create a unique ID for this generation request
    const requestId = `ui_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    
    return new Promise<void>((resolve, reject) => {
      let eventSource: EventSource | null = null;
      let isResolved = false;
      let timeoutId: NodeJS.Timeout;

      const cleanup = () => {
        if (eventSource) {
          eventSource.close();
          eventSource = null;
        }
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        setLoading(false);
      };
      
      try {
        // Create the EventSource first to establish the connection
        const eventSourceUrl = new URL('/api/ast/plan', window.location.origin);
        eventSourceUrl.searchParams.append('requestId', requestId);
        
        console.log(`Creating SSE connection to: ${eventSourceUrl.toString()}`);
        
        eventSource = new EventSource(eventSourceUrl.toString(), {
          withCredentials: true
        });
        
        // Set up message handler
        const handleMessage = (event: MessageEvent) => {
          let data: SSEMessage;
          try {
            data = JSON.parse(event.data);
            console.log('SSE message:', data);
          } catch (err) {
            console.error('Failed to parse SSE message:', event.data, err);
            return;
          }
          
          // Handle different event types
          switch (event.type) {
            case 'start':
              console.log('SSE: Generation started');
              
              // Send the generation request
              fetch('/api/ast/plan', {
                method: 'POST',
                headers: { 
                  'Content-Type': 'application/json',
                  'X-Request-ID': requestId,
                  'Accept': 'text/event-stream'
                },
                body: JSON.stringify({ prompt, provider }),
                credentials: 'include'
              }).catch(err => {
                console.error('Failed to start generation:', err);
                if (!isResolved) {
                  isResolved = true;
                  reject(err);
                  cleanup();
                }
              });
              break;
              
            case 'message':
              // Handle informational messages
              if (data.content) {
                console.log('SSE:', data.content);
              }
              break;
              
            case 'token':
              // Handle token streaming if needed
              if (data.text) {
                console.log('SSE: Token received:', data.text);
              }
              break;
              
            case 'content':
              // Handle full content updates
              if (data.content) {
                setAst(data.content);
              }
              break;
              
            case 'end':
              // Handle end of generation
              console.log('SSE: Generation completed');
              if (!isResolved) {
                isResolved = true;
                resolve();
                cleanup();
              }
              break;
              
            case 'error':
              // Handle errors
              console.error('SSE Error:', data.error || 'Unknown error');
              if (!isResolved) {
                isResolved = true;
                reject(new Error(data.error || 'Generation failed'));
                cleanup();
              }
              break;
          }
        };
        
        // Set up error handler
        const handleError = (event: Event) => {
          console.error('SSE connection error:', event);
          if (!isResolved) {
            isResolved = true;
            reject(new Error('SSE connection error'));
            cleanup();
          }
        };
        
        // Set up event listeners
        eventSource.addEventListener('message', handleMessage as EventListener);
        eventSource.addEventListener('error', handleError);
        
        // Set timeout for the entire operation
        timeoutId = setTimeout(() => {
          if (!isResolved) {
            isResolved = true;
            reject(new Error('Generation timed out'));
            cleanup();
          }
        }, 300000); // 5 minutes timeout
        
        // Cleanup function for when the component unmounts
        return () => {
          clearTimeout(timeoutId);
          cleanup();
        };
        
      } catch (err) {
        console.error('Error setting up SSE connection:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to start generation';
        setError(errorMessage);
        setLoading(false);
        reject(new Error(errorMessage));
      }
    });
  }, []);

  // Stub implementations for the AST operations
  const addChild = useCallback((parentId: string, child: UINode) => {
    console.log('Adding child:', { parentId, child });
    // Implementation would go here
  }, []);

  const updateNodeProps = useCallback((nodeId: string, props: Record<string, any>) => {
    console.log('Updating node props:', { nodeId, props });
    // Implementation would go here
  }, []);

  const removeNode = useCallback((nodeId: string) => {
    console.log('Removing node:', nodeId);
    // Implementation would go here
  }, []);

  return {
    ast,
    snapshotId,
    error,
    loading,
    generateFromPlan,
    addChild,
    updateNodeProps,
    removeNode
  };
}
