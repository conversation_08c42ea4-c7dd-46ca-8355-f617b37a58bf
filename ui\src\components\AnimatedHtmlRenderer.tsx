import React, { useEffect, useRef } from 'react';

interface AnimatedHtmlRendererProps {
  html: string;
  delayPerElement?: number;
  onAnimationComplete?: () => void;
}

interface StreamingHtmlRendererProps {
  stream: ReadableStream<Uint8Array>;
  delayPerElement?: number;
  onAnimationComplete?: () => void;
  onError?: (error: Error) => void;
}

/**
 * AnimatedHtmlRenderer - Provides smooth, jitter-free, progressive element-by-element rendering
 *
 * Enhanced to emulate Readdy.ai's seamless rendering by:
 * 1. Parsing HTML string into individual DOM nodes with better chunking
 * 2. Rendering each top-level node one at a time with optimized animations
 * 3. Using CSS transitions for smoother visual effects
 * 4. Implementing readdy.ai-style progressive parsing for better UX
 * 5. Avoiding layout jank by using manual DOM manipulation and optimized timing
 */
const AnimatedHtmlRenderer: React.FC<AnimatedHtmlRendererProps> = ({
  html,
  delayPerElement = 50, // Reduced for more seamless feel like readdy.ai
  onAnimationComplete
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });
  const lastProcessedLengthRef = useRef<number>(0);
  const processingRef = useRef<boolean>(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !html.trim()) {
      console.log('🎬 AnimatedHtmlRenderer: No container or empty HTML', {
        hasContainer: !!container,
        htmlLength: html?.length || 0,
        htmlPreview: html?.substring(0, 100) + '...'
      });
      return;
    }

    // Prevent concurrent processing for smoother experience
    if (processingRef.current) {
      console.log('🎬 Already processing, skipping to avoid jitter');
      return;
    }

    // Reset animation state
    animationRef.current = { cancel: false };
    processingRef.current = true;

    // Enhanced incremental rendering like readdy.ai
    const currentLength = html.length;
    const lastProcessedLength = lastProcessedLengthRef.current;

    // Only process new content for seamless streaming
    const shouldProcessIncremental = currentLength > lastProcessedLength && container.children.length > 0;

    if (!shouldProcessIncremental && container.children.length > 0) {
      // Full replacement - clear with smooth transition
      const fadeOutElements = Array.from(container.children) as HTMLElement[];
      fadeOutElements.forEach(element => {
        element.style.transition = 'opacity 150ms ease-out';
        element.style.opacity = '0';
      });

      setTimeout(() => {
        container.innerHTML = '';
        lastProcessedLengthRef.current = 0;
        processNewContent();
      }, 150);
      return;
    }

    processNewContent();

    async function processNewContent() {
      if (!container) return;

      // Determine content to process
      let contentToProcess = html;
      let isIncremental = false;

      if (shouldProcessIncremental) {
        // Process only new content for seamless streaming
        contentToProcess = html.substring(lastProcessedLength);
        isIncremental = true;
        console.log('🎬 Processing incremental content:', {
          newContentLength: contentToProcess.length,
          totalLength: html.length,
          lastProcessed: lastProcessedLength
        });
      } else {
        console.log('🎬 Processing full content:', {
          contentLength: html.length,
          isReplacement: container.children.length === 0
        });
      }

      // Parse HTML using DOMParser for safe parsing
      const parser = new DOMParser();
      let doc: Document;

      if (isIncremental) {
        // For incremental content, wrap in a container to ensure valid HTML
        doc = parser.parseFromString(`<div>${contentToProcess}</div>`, 'text/html');
      } else {
        doc = parser.parseFromString(html, 'text/html');
      }

      const bodyContent = isIncremental ? doc.body.firstChild : doc.body;

      if (!bodyContent) {
        console.log('🎬 AnimatedHtmlRenderer: No body content found');
        processingRef.current = false;
        return;
      }

      // Get all top-level nodes from the parsed HTML
      const topLevelNodes = Array.from(bodyContent.childNodes).filter(node => {
        // Filter out empty text nodes and comments
        return node.nodeType === Node.ELEMENT_NODE ||
               (node.nodeType === Node.TEXT_NODE && node.textContent?.trim());
      });

      console.log(`🎬 AnimatedHtmlRenderer: Starting animated rendering of ${topLevelNodes.length} elements`, {
        nodeTypes: topLevelNodes.map(node => ({
          type: node.nodeType === Node.ELEMENT_NODE ? 'element' : 'text',
          tagName: node.nodeType === Node.ELEMENT_NODE ? (node as Element).tagName : 'TEXT',
          content: node.textContent?.substring(0, 50) + '...'
        })),
        isIncremental
      });

      // Animate each top-level node progressively with readdy.ai-style smoothness
      await animateNodesSequentially(topLevelNodes, container);

      // Update processed length for incremental updates
      lastProcessedLengthRef.current = html.length;
      processingRef.current = false;

      // Call completion callback if provided
      if (onAnimationComplete && !animationRef.current.cancel) {
        onAnimationComplete();
      }
    }

    // Enhanced animation function for readdy.ai-style smoothness
    async function animateNodesSequentially(nodes: Node[], container: HTMLElement) {
      for (let i = 0; i < nodes.length; i++) {
        // Check if animation was cancelled (component unmounted)
        if (animationRef.current.cancel) {
          console.log('🎬 Animation cancelled');
          return;
        }

        const node = nodes[i];

        // Clone the node to avoid moving it from the original document
        const clonedNode = node.cloneNode(true) as Node;

        // Enhanced animation for elements with readdy.ai-style smoothness
        if (clonedNode.nodeType === Node.ELEMENT_NODE) {
          const element = clonedNode as HTMLElement;

          // Set initial state for smooth animation
          element.style.opacity = '0';
          element.style.transform = 'translateY(8px) scale(0.98)';
          element.style.transition = 'opacity 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94)';

          // Append to container (element is still invisible)
          container.appendChild(clonedNode);

          // Force reflow for smooth animation
          element.offsetHeight;

          // Trigger animation with readdy.ai-style easing
          requestAnimationFrame(() => {
            if (!animationRef.current.cancel) {
              element.style.opacity = '1';
              element.style.transform = 'translateY(0) scale(1)';

              // Clean up transition after animation
              setTimeout(() => {
                if (!animationRef.current.cancel) {
                  element.style.transition = '';
                }
              }, 250);
            }
          });
        } else {
          // Handle text nodes
          container.appendChild(clonedNode);
        }

        // Optimized delay for readdy.ai-style progressive rendering
        if (i < nodes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, Math.max(delayPerElement, 25)));
        }
      }

      console.log(`🎬 Animation completed for ${nodes.length} elements`);
    }

    // Cleanup function to cancel animation if component unmounts
    return () => {
      animationRef.current.cancel = true;
      processingRef.current = false;
    };
  }, [html, delayPerElement, onAnimationComplete]);

  return (
    <div 
      ref={containerRef} 
      className="animated-html-container w-full h-full"
      style={{ minHeight: '100%' }}
    />
  );
};

/**
 * IncrementalHtmlRenderer - Handles incremental HTML content updates without remounting
 *
 * This component provides smooth progressive rendering by:
 * 1. Accepting HTML content as a prop and detecting changes
 * 2. Parsing only new content since last render
 * 3. Appending new elements progressively with animations
 * 4. Maintaining existing rendered content to avoid jitter
 */
interface IncrementalHtmlRendererProps {
  html: string;
  delayPerElement?: number;
  onAnimationComplete?: () => void;
}

const IncrementalHtmlRenderer: React.FC<IncrementalHtmlRendererProps> = ({
  html,
  delayPerElement = 50,
  onAnimationComplete
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const lastProcessedLengthRef = useRef<number>(0);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });
  const isProcessingRef = useRef<boolean>(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !html) return;

    // Only process new content since last render
    const newContentLength = html.length;
    const lastProcessedLength = lastProcessedLengthRef.current;

    if (newContentLength <= lastProcessedLength || isProcessingRef.current) {
      return; // No new content or already processing
    }

    // Extract only the new content
    const newContent = html.substring(lastProcessedLength);
    if (newContent.trim().length === 0) return;

    console.log('🎬 Processing incremental content:', {
      totalLength: newContentLength,
      lastProcessedLength,
      newContentLength: newContent.length,
      newContentPreview: newContent.substring(0, 100) + '...'
    });

    isProcessingRef.current = true;

    const processNewContent = async () => {
      try {
        // Parse new content for complete elements
        const parser = new DOMParser();

        // Try to parse as complete HTML first
        let elementsToRender: Element[] = [];

        // Split new content into lines and process each line
        const lines = newContent.split('\n').filter(line => line.trim().length > 0);

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine) continue;

          // Check if this line contains complete HTML elements
          const elementMatches = trimmedLine.match(/<[^>]+>.*?<\/[^>]+>|<[^>]+\/>/g);

          if (elementMatches) {
            for (const elementHtml of elementMatches) {
              try {
                const doc = parser.parseFromString(`<div>${elementHtml}</div>`, 'text/html');
                const parsedElements = Array.from(doc.body.firstChild?.childNodes || [])
                  .filter(node => node.nodeType === Node.ELEMENT_NODE) as Element[];
                elementsToRender.push(...parsedElements);
              } catch (e) {
                console.warn('🎬 Failed to parse element:', elementHtml);
              }
            }
          } else {
            // Try to parse the line as text content or partial HTML
            try {
              // Wrap in a div to make it valid HTML
              const wrappedContent = `<div class="text-content">${trimmedLine}</div>`;
              const doc = parser.parseFromString(wrappedContent, 'text/html');
              const parsedElements = Array.from(doc.body.childNodes)
                .filter(node => node.nodeType === Node.ELEMENT_NODE) as Element[];
              elementsToRender.push(...parsedElements);
            } catch (e) {
              console.warn('🎬 Failed to parse line as HTML:', trimmedLine);
            }
          }
        }

        // If no elements found, try to parse the entire new content as one block
        if (elementsToRender.length === 0 && newContent.trim().length > 0) {
          try {
            const doc = parser.parseFromString(`<div>${newContent}</div>`, 'text/html');
            const parsedElements = Array.from(doc.body.firstChild?.childNodes || [])
              .filter(node => node.nodeType === Node.ELEMENT_NODE) as Element[];
            elementsToRender.push(...parsedElements);
          } catch (e) {
            console.warn('🎬 Failed to parse new content as HTML');
          }
        }

        // Render elements progressively
        for (let i = 0; i < elementsToRender.length; i++) {
          if (animationRef.current.cancel) break;

          const element = elementsToRender[i];
          await renderElementWithAnimation(element, container);

          if (delayPerElement > 0) {
            await new Promise(resolve => setTimeout(resolve, delayPerElement));
          }
        }

        // Update the last processed length
        lastProcessedLengthRef.current = newContentLength;

        if (onAnimationComplete && !animationRef.current.cancel) {
          onAnimationComplete();
        }
      } catch (error) {
        console.error('🎬 Error processing incremental content:', error);
      } finally {
        isProcessingRef.current = false;
      }
    };

    processNewContent();

    return () => {
      animationRef.current.cancel = true;
    };
  }, [html, delayPerElement, onAnimationComplete]);

  // Helper function to render element with animation
  const renderElementWithAnimation = async (element: Element, container: HTMLElement): Promise<void> => {
    return new Promise((resolve) => {
      if (animationRef.current.cancel) {
        resolve();
        return;
      }

      // Clone the element to avoid modifying the original
      const clonedElement = element.cloneNode(true) as HTMLElement;

      // Set initial animation state
      clonedElement.style.opacity = '0';
      clonedElement.style.transform = 'translateY(8px)';
      clonedElement.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';

      // Append to container
      container.appendChild(clonedElement);

      // Trigger animation on next frame
      requestAnimationFrame(() => {
        if (animationRef.current.cancel) {
          resolve();
          return;
        }

        clonedElement.style.opacity = '1';
        clonedElement.style.transform = 'translateY(0)';

        // Resolve after animation completes
        setTimeout(resolve, 300);
      });
    });
  };

  // Reset when html prop changes significantly (new generation)
  useEffect(() => {
    if (html.length < lastProcessedLengthRef.current) {
      // Content was reset, clear container and reset tracking
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
      lastProcessedLengthRef.current = 0;
      animationRef.current = { cancel: false };
    }
  }, [html]);

  return (
    <div
      ref={containerRef}
      className="space-y-2"
      style={{ minHeight: '20px' }}
    />
  );
};

/**
 * StreamingHtmlRenderer - Provides smooth, jitter-free, progressive element-by-element rendering from a ReadableStream
 *
 * This component handles streamed HTML content by:
 * 1. Reading from a ReadableStream<Uint8Array> progressively
 * 2. Parsing HTML incrementally and safely, element-by-element
 * 3. Appending each parsed top-level element to the DOM progressively using useRef()
 * 4. Animating each appended element using Tailwind CSS for smooth fade-in and slide-up
 * 5. Avoiding full reflows, layout jitter, and scroll jumps
 */
const StreamingHtmlRenderer: React.FC<StreamingHtmlRendererProps> = ({
  stream,
  delayPerElement = 75,
  onAnimationComplete,
  onError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });
  const bufferRef = useRef<string>('');
  const readerRef = useRef<ReadableStreamDefaultReader<Uint8Array> | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !stream) return;

    // Reset animation state
    animationRef.current = { cancel: false };
    bufferRef.current = '';

    // Clear existing content
    container.innerHTML = '';

    console.log('🎬 Starting streaming HTML rendering');

    const processStream = async () => {
      const decoder = new TextDecoder();
      const reader = stream.getReader();
      readerRef.current = reader;

      try {
        while (true) {
          // Check if animation was cancelled
          if (animationRef.current.cancel) {
            console.log('🎬 Streaming animation cancelled');
            await reader.cancel();
            return;
          }

          const { done, value } = await reader.read();

          if (done) {
            console.log('🎬 Stream completed');
            // Process any remaining content in buffer
            await processRemainingBuffer();
            break;
          }

          // Decode the chunk and add to buffer
          const chunk = decoder.decode(value, { stream: true });
          bufferRef.current += chunk;

          // Extract and render complete elements from buffer
          await extractAndRenderElements();
        }

        // Call completion callback if provided
        if (onAnimationComplete && !animationRef.current.cancel) {
          onAnimationComplete();
        }

      } catch (error) {
        console.error('🎬 Streaming error:', error);
        if (onError) {
          onError(error as Error);
        }
      } finally {
        readerRef.current = null;
      }
    };

    // Extract complete HTML elements from buffer and render them
    const extractAndRenderElements = async () => {
      const buffer = bufferRef.current;
      const elementsToRender: string[] = [];
      let processedLength = 0;

      // Use a more sophisticated approach to extract complete elements
      const extractedElements = extractCompleteElements(buffer);

      if (extractedElements.elements.length > 0) {
        elementsToRender.push(...extractedElements.elements);
        processedLength = extractedElements.processedLength;

        // Update buffer to remove processed elements
        bufferRef.current = buffer.substring(processedLength);

        // Render each complete element
        for (const elementHtml of elementsToRender) {
          if (animationRef.current.cancel) return;

          await renderSingleElement(elementHtml);

          // Add delay between elements
          if (delayPerElement > 0) {
            await new Promise(resolve => setTimeout(resolve, delayPerElement));
          }
        }
      }
    };

    // Extract complete HTML elements from a buffer string
    const extractCompleteElements = (buffer: string): { elements: string[], processedLength: number } => {
      const elements: string[] = [];
      let position = 0;

      while (position < buffer.length) {
        // Skip whitespace
        while (position < buffer.length && /\s/.test(buffer[position])) {
          position++;
        }

        if (position >= buffer.length) break;

        // Look for opening tag
        if (buffer[position] === '<') {
          const elementResult = extractSingleElement(buffer, position);

          if (elementResult.element) {
            elements.push(elementResult.element);
            position = elementResult.endPosition;
          } else {
            // Incomplete element, stop processing
            break;
          }
        } else {
          // Text content - extract until next tag or end
          const textStart = position;
          while (position < buffer.length && buffer[position] !== '<') {
            position++;
          }

          const textContent = buffer.substring(textStart, position).trim();
          if (textContent) {
            // Wrap text content in a span for consistent handling
            elements.push(`<span>${textContent}</span>`);
          }
        }
      }

      return { elements, processedLength: position };
    };

    // Extract a single complete HTML element starting at a given position
    const extractSingleElement = (buffer: string, startPos: number): { element: string | null, endPosition: number } => {
      if (buffer[startPos] !== '<') {
        return { element: null, endPosition: startPos };
      }

      // Find the end of the opening tag
      let tagEnd = startPos + 1;
      while (tagEnd < buffer.length && buffer[tagEnd] !== '>') {
        tagEnd++;
      }

      if (tagEnd >= buffer.length) {
        // Incomplete opening tag
        return { element: null, endPosition: startPos };
      }

      tagEnd++; // Include the '>'

      const openingTag = buffer.substring(startPos, tagEnd);

      // Check if it's a self-closing tag
      if (openingTag.endsWith('/>') || openingTag.includes(' />')) {
        return { element: openingTag, endPosition: tagEnd };
      }

      // Extract tag name for finding closing tag
      const tagNameMatch = openingTag.match(/<([a-zA-Z][a-zA-Z0-9]*)/);
      if (!tagNameMatch) {
        return { element: null, endPosition: startPos };
      }

      const tagName = tagNameMatch[1].toLowerCase();

      // Special handling for void elements (no closing tag needed)
      const voidElements = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];
      if (voidElements.includes(tagName)) {
        return { element: openingTag, endPosition: tagEnd };
      }

      // Find the matching closing tag
      const closingTag = `</${tagName}>`;
      let depth = 1;
      let searchPos = tagEnd;

      while (searchPos < buffer.length && depth > 0) {
        const nextOpenTag = buffer.indexOf(`<${tagName}`, searchPos);
        const nextCloseTag = buffer.indexOf(closingTag, searchPos);

        if (nextCloseTag === -1) {
          // No closing tag found
          return { element: null, endPosition: startPos };
        }

        if (nextOpenTag !== -1 && nextOpenTag < nextCloseTag) {
          // Found another opening tag before the closing tag
          depth++;
          searchPos = nextOpenTag + tagName.length + 1;
        } else {
          // Found a closing tag
          depth--;
          if (depth === 0) {
            const endPos = nextCloseTag + closingTag.length;
            const element = buffer.substring(startPos, endPos);
            return { element, endPosition: endPos };
          }
          searchPos = nextCloseTag + closingTag.length;
        }
      }

      // Incomplete element
      return { element: null, endPosition: startPos };
    };

    // Process any remaining content in buffer when stream ends
    const processRemainingBuffer = async () => {
      const remainingContent = bufferRef.current.trim();
      if (remainingContent && !animationRef.current.cancel) {
        // Try to parse remaining content as a complete element
        // If it's not complete, we'll still try to render it
        await renderSingleElement(remainingContent);
      }
    };

    // Render a single HTML element with animation
    const renderSingleElement = async (elementHtml: string): Promise<void> => {
      if (animationRef.current.cancel || !container) return;

      try {
        // Parse the HTML element using DOMParser
        const parser = new DOMParser();
        const doc = parser.parseFromString(elementHtml, 'text/html');
        const bodyContent = doc.body;

        if (!bodyContent || !bodyContent.firstChild) return;

        // Get the first child element
        const element = bodyContent.firstChild.cloneNode(true) as Node;

        // If it's an element node, add animation classes
        if (element.nodeType === Node.ELEMENT_NODE) {
          const htmlElement = element as HTMLElement;

          // Add initial animation classes (hidden and slightly offset)
          htmlElement.classList.add(
            'opacity-0',
            'translate-y-2',
            'transition-opacity',
            'transition-transform',
            'duration-300',
            'ease-out'
          );
        }

        // Append to container (element is still invisible due to opacity-0)
        container.appendChild(element);

        // Trigger animation on next frame
        if (element.nodeType === Node.ELEMENT_NODE) {
          const htmlElement = element as HTMLElement;

          requestAnimationFrame(() => {
            if (animationRef.current.cancel) return;

            // Trigger the fade-in and slide-up animation
            htmlElement.classList.remove('opacity-0', 'translate-y-2');
            htmlElement.classList.add('opacity-100', 'translate-y-0');
          });
        }

        console.log('🎬 Rendered streaming element:', elementHtml.substring(0, 50) + '...');

      } catch (error) {
        console.error('🎬 Error rendering element:', error, elementHtml);
      }
    };

    // Start processing the stream
    processStream();

    // Cleanup function to cancel streaming if component unmounts
    return () => {
      animationRef.current.cancel = true;
      if (readerRef.current) {
        readerRef.current.cancel().catch(console.error);
        readerRef.current = null;
      }
    };
  }, [stream, delayPerElement, onAnimationComplete, onError]);

  return (
    <div
      ref={containerRef}
      className="streaming-html-container w-full h-full"
      style={{ minHeight: '100%' }}
    />
  );
};

/**
 * Utility function to create a ReadableStream from HTML string for testing StreamingHtmlRenderer
 * This simulates streaming HTML content by chunking it and sending it progressively
 *
 * Example usage:
 * ```tsx
 * const htmlContent = '<div>Hello</div><p>World</p>';
 * const stream = createHtmlStream(htmlContent, 50, 100);
 *
 * return <StreamingHtmlRenderer stream={stream} />;
 * ```
 *
 * For real streaming from fetch():
 * ```tsx
 * const response = await fetch('/api/generate-html');
 * const stream = response.body;
 *
 * return <StreamingHtmlRenderer stream={stream} />;
 * ```
 */
/**
 * Enhanced HTML stream creator that mimics readdy.ai's chunking strategy
 * Creates smaller, more frequent chunks for smoother progressive rendering
 */
export const createHtmlStream = (html: string, chunkSize: number = 50, delayMs: number = 30): ReadableStream<Uint8Array> => {
  const encoder = new TextEncoder();
  let position = 0;

  return new ReadableStream({
    start() {
      console.log('🎬 Starting readdy.ai-style HTML stream simulation');
    },

    pull(controller) {
      if (position >= html.length) {
        console.log('🎬 HTML stream completed');
        controller.close();
        return;
      }

      // Smart chunking: try to break at element boundaries for better rendering
      let chunk: string;
      let nextPosition = Math.min(position + chunkSize, html.length);

      // Look for a good breaking point (end of tag or whitespace)
      if (nextPosition < html.length) {
        const searchEnd = Math.min(nextPosition + 20, html.length);
        let bestBreak = nextPosition;

        for (let i = nextPosition; i < searchEnd; i++) {
          if (html[i] === '>' || html[i] === ' ' || html[i] === '\n') {
            bestBreak = i + 1;
            break;
          }
        }
        nextPosition = bestBreak;
      }

      chunk = html.substring(position, nextPosition);
      position = nextPosition;

      // Encode and enqueue the chunk
      const encodedChunk = encoder.encode(chunk);
      controller.enqueue(encodedChunk);

      console.log(`🎬 Streamed smart chunk: ${chunk.length} characters`);

      // Reduced delay for readdy.ai-style smoothness
      if (delayMs > 0) {
        return new Promise(resolve => setTimeout(resolve, delayMs));
      }
    },

    cancel() {
      console.log('🎬 HTML stream cancelled');
    }
  });
};

/**
 * ReadyAI-style Seamless HTML Renderer
 * Provides the smoothest possible progressive rendering experience
 */
interface SeamlessHtmlRendererProps {
  html: string;
  onAnimationComplete?: () => void;
  className?: string;
}

const SeamlessHtmlRenderer: React.FC<SeamlessHtmlRendererProps> = ({
  html,
  onAnimationComplete,
  className = ""
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });
  const lastProcessedRef = useRef<number>(0);
  const isProcessingRef = useRef<boolean>(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !html.trim()) return;

    // Prevent concurrent processing
    if (isProcessingRef.current) return;

    const newContentLength = html.length;
    const lastProcessed = lastProcessedRef.current;

    // Only process new content
    if (newContentLength <= lastProcessed) return;

    isProcessingRef.current = true;
    animationRef.current = { cancel: false };

    const newContent = html.substring(lastProcessed);

    console.log('🎬 SeamlessHtmlRenderer: Processing new content', {
      newLength: newContent.length,
      totalLength: html.length
    });

    processContentSeamlessly(newContent, container);

  }, [html]);

  const processContentSeamlessly = async (content: string, container: HTMLElement) => {
    try {
      // Parse content into renderable chunks
      const chunks = parseIntoRenderableChunks(content);

      for (const chunk of chunks) {
        if (animationRef.current.cancel) break;

        await renderChunkWithAnimation(chunk, container);

        // Minimal delay for ultra-smooth experience
        await new Promise(resolve => setTimeout(resolve, 15));
      }

      lastProcessedRef.current = html.length;

      if (onAnimationComplete && !animationRef.current.cancel) {
        onAnimationComplete();
      }

    } catch (error) {
      console.error('🎬 SeamlessHtmlRenderer error:', error);
    } finally {
      isProcessingRef.current = false;
    }
  };

  const parseIntoRenderableChunks = (content: string): string[] => {
    const chunks: string[] = [];
    let position = 0;

    while (position < content.length) {
      // Find next complete element or meaningful text chunk
      const nextChunk = extractNextRenderableChunk(content, position);
      if (nextChunk.chunk) {
        chunks.push(nextChunk.chunk);
        position = nextChunk.endPosition;
      } else {
        break;
      }
    }

    return chunks;
  };

  const extractNextRenderableChunk = (content: string, startPos: number): { chunk: string | null, endPosition: number } => {
    if (startPos >= content.length) return { chunk: null, endPosition: startPos };

    // Skip whitespace
    while (startPos < content.length && /\s/.test(content[startPos])) {
      startPos++;
    }

    if (startPos >= content.length) return { chunk: null, endPosition: startPos };

    // If we're at a tag, extract the complete element
    if (content[startPos] === '<') {
      return extractCompleteElement(content, startPos);
    }

    // Extract text content until next tag
    let endPos = startPos;
    while (endPos < content.length && content[endPos] !== '<') {
      endPos++;
    }

    const textContent = content.substring(startPos, endPos).trim();
    return textContent ? { chunk: textContent, endPosition: endPos } : { chunk: null, endPosition: endPos };
  };

  const extractCompleteElement = (content: string, startPos: number): { chunk: string | null, endPosition: number } => {
    // Implementation similar to StreamingHtmlRenderer but optimized for seamless rendering
    // This is a simplified version - the full implementation would be more robust
    let depth = 0;
    let pos = startPos;

    while (pos < content.length) {
      const char = content[pos];

      if (char === '<') {
        if (content[pos + 1] === '/') {
          // Closing tag
          const closeTagEnd = content.indexOf('>', pos);
          if (closeTagEnd !== -1) {
            depth--;
            pos = closeTagEnd + 1;
            if (depth === 0) {
              return { chunk: content.substring(startPos, pos), endPosition: pos };
            }
          } else {
            break;
          }
        } else {
          // Opening tag
          const tagEnd = content.indexOf('>', pos);
          if (tagEnd !== -1) {
            const tag = content.substring(pos, tagEnd + 1);
            if (tag.endsWith('/>')) {
              // Self-closing tag
              if (depth === 0) {
                return { chunk: tag, endPosition: tagEnd + 1 };
              }
            } else {
              depth++;
            }
            pos = tagEnd + 1;
          } else {
            break;
          }
        }
      } else {
        pos++;
      }
    }

    // If we couldn't find a complete element, return what we have
    return { chunk: content.substring(startPos, pos), endPosition: pos };
  };

  const renderChunkWithAnimation = async (chunk: string, container: HTMLElement): Promise<void> => {
    return new Promise((resolve) => {
      if (animationRef.current.cancel) {
        resolve();
        return;
      }

      // Create element from chunk
      const parser = new DOMParser();
      let element: HTMLElement;

      if (chunk.startsWith('<')) {
        // HTML element
        const doc = parser.parseFromString(chunk, 'text/html');
        const parsed = doc.body.firstChild as HTMLElement;
        if (!parsed) {
          resolve();
          return;
        }
        element = parsed.cloneNode(true) as HTMLElement;
      } else {
        // Text content
        element = document.createElement('span');
        element.textContent = chunk;
        element.className = 'inline';
      }

      // Apply readdy.ai-style smooth animation
      element.style.opacity = '0';
      element.style.transform = 'translateY(4px)';
      element.style.transition = 'opacity 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94)';

      container.appendChild(element);

      // Trigger animation
      requestAnimationFrame(() => {
        if (!animationRef.current.cancel) {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0)';

          setTimeout(() => {
            if (!animationRef.current.cancel) {
              element.style.transition = '';
            }
            resolve();
          }, 200);
        } else {
          resolve();
        }
      });
    });
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      animationRef.current.cancel = true;
      isProcessingRef.current = false;
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`seamless-html-container ${className}`}
      style={{ minHeight: '100%' }}
    />
  );
};

export default AnimatedHtmlRenderer;
export { StreamingHtmlRenderer, IncrementalHtmlRenderer, SeamlessHtmlRenderer };
