import { ReactNode } from 'react';
import styles from './Hero.module.css';
import '../styles/animations.css';

interface HeroProps {
  title: string;
  subtitle: string;
  children?: ReactNode;
  className?: string;
}

export function Hero({ title, subtitle, children, className = '' }: HeroProps) {
  return (
    <div className={`${styles.hero} ${className}`}>
      <div className={styles.container}>
        <h1 className={`${styles.title} animate-fadeIn`}>{title}</h1>
        <p className={`${styles.subtitle} animate-fadeIn delay-300`}>{subtitle}</p>
        {children && <div className={`${styles.actions} animate-fadeIn delay-500`}>{children}</div>}
      </div>
    </div>
  );
}
