/**
 * Utility functions for authentication
 */

// Define the auth state interface
export interface AuthState {
  isAuthenticated: boolean;
  lastChecked: number;
  user: any;
}

/**
 * Logout the user
 * Clears the authentication state from localStorage and redirects to the logout endpoint
 */
export const logout = () => {
  // Clear the auth state from localStorage
  localStorage.removeItem('authState');

  // Also clear any old format auth state
  localStorage.removeItem('authTimestamp');

  // Clear any global auth state that might be in memory
  if (typeof window !== 'undefined' && (window as any).globalAuthState) {
    (window as any).globalAuthState = null;
  }

  // Redirect to the logout endpoint
  window.location.href = `${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/logout`;
};

/**
 * Check if the user is authenticated
 * @returns {boolean} True if the user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const cachedAuthString = localStorage.getItem('authState');

  if (cachedAuthString) {
    try {
      const cachedAuth = JSON.parse(cachedAuthString) as AuthState;
      const isRecent = (Date.now() - cachedAuth.lastChecked) < 30 * 60 * 1000; // 30 minutes
      return isRecent && cachedAuth.isAuthenticated;
    } catch (e) {
      console.error('Error parsing auth state:', e);
      return false;
    }
  }

  // Check old format as fallback
  const cachedAuth = localStorage.getItem('authState');
  const cachedTimestamp = localStorage.getItem('authTimestamp');

  if (cachedAuth && cachedTimestamp) {
    const isRecent = (Date.now() - parseInt(cachedTimestamp)) < 30 * 60 * 1000; // 30 minutes
    return isRecent && cachedAuth === 'true';
  }

  return false;
};

/**
 * Set the authentication state
 * @param {boolean} isAuthenticated - Whether the user is authenticated
 * @param {any} user - The user object
 */
export const setAuthState = (isAuthenticated: boolean, user?: any): void => {
  const authState: AuthState = {
    isAuthenticated,
    lastChecked: Date.now(),
    user: user || null
  };

  localStorage.setItem('authState', JSON.stringify(authState));

  // Also set in global state if available
  if (typeof window !== 'undefined') {
    (window as any).globalAuthState = authState;
  }
};

/**
 * Clear the authentication state
 */
export const clearAuthState = (): void => {
  localStorage.removeItem('authState');
  localStorage.removeItem('authTimestamp');

  // Also clear global state if available
  if (typeof window !== 'undefined' && (window as any).globalAuthState) {
    (window as any).globalAuthState = null;
  }
};
