-- SQL file to fix token usage function
-- This function will always return true and just log the usage without checking quota

-- Drop the existing function
DROP FUNCTION IF EXISTS use_tokens_and_log;

-- Create a simpler version that always returns true
CREATE OR REPLACE FUNCTION use_tokens_and_log(
    p_user_id INTEGER,
    p_tokens_used INTEGER,
    p_event VARCHAR,
    p_context TEXT DEFAULT NULL,
    p_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    -- Skip token quota check, just log the usage
    INSERT INTO usage_log (user_id, event, tokens_used, context, details)
    VALUES (p_user_id, p_event, p_tokens_used, p_context, p_details);

    -- Always return true to allow the operation to continue
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Test the function with a valid user ID
DO $$
DECLARE
    test_user_id INTEGER;
BEGIN
    -- Get a valid user ID
    SELECT id INTO test_user_id FROM users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE NOTICE 'No users found in the database. Creating a test user...';
        
        -- Create a test user if none exists
        INSERT INTO users (email, display_name, google_id)
        VALUES ('<EMAIL>', 'Test User', 'test123')
        RETURNING id INTO test_user_id;
    END IF;
    
    -- Test the function
    PERFORM use_tokens_and_log(
        test_user_id,
        10,
        'test_event',
        'Test context',
        '{"test": "data"}'
    );
    
    RAISE NOTICE 'Function tested with user ID: %', test_user_id;
END $$;

-- Make sure users have enough quota
UPDATE users SET quota_tokens = 1000000 WHERE quota_tokens < 1000000;

-- Print completion message
DO $$
BEGIN
    RAISE NOTICE 'Token usage function fixed successfully.';
END $$;
