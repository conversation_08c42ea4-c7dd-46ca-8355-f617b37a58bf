import React, { useState, useEffect, useCallback, memo } from 'react';
import { FiZap } from 'react-icons/fi';
import styles from './FunctionalityGenerator.module.css';

export interface UnimplementedElement {
  id: string;
  tagName: string;
  elementId?: string;
  className?: string;
  ref: string;
  description?: string;
  type: string; // button, link, form, etc.
  context?: string; // context or purpose of the element
}

interface FunctionalityGeneratorProps {
  iframeRef: React.RefObject<HTMLIFrameElement>;
  onGenerateFunctionality?: (element: UnimplementedElement, prompt: string) => void;
  disabled?: boolean;
  viewMode?: string;
}

export const FunctionalityGenerator = memo<FunctionalityGeneratorProps>(({
  iframeRef,
  onGenerateFunctionality,
  disabled = false,
  viewMode = 'preview'
}) => {
  const [scriptInjected, setScriptInjected] = useState(false);

  // No need for highlight styles anymore as we won't be highlighting elements automatically

  // JavaScript for checking if an element needs functionality
  const DETECTION_SCRIPT = `
    (function() {
      // Wait for jQuery to be available
      function checkJQuery() {
        if (window.jQuery) {
          initFunctionalityChecker();
        } else {
          setTimeout(checkJQuery, 100);
        }
      }

      function initFunctionalityChecker() {
        // Make the check function available globally
        window.checkElementNeedsFunctionality = function(element) {
          if (!element) return false;

          const $element = $(element);
          const tagName = element.tagName.toLowerCase();

          // Check buttons
          if (tagName === 'button' ||
              (tagName === 'input' &&
               (element.type === 'button' || element.type === 'submit')) ||
              $element.hasClass('btn')) {

            const events = $._data(element, 'events') || {};
            // Check if element has click handlers
            if (!events.click && !$element.attr('onclick')) {
              return {
                needsFunctionality: true,
                type: 'button',
                details: getElementDetails(element, 'button')
              };
            }
          }

          // Check links
          if (tagName === 'a') {
            const href = $element.attr('href');
            if (!href || href === '#' || href === 'javascript:void(0)') {
              return {
                needsFunctionality: true,
                type: 'link',
                details: getElementDetails(element, 'link')
              };
            }
          }

          // Check forms
          if (tagName === 'form') {
            const events = $._data(element, 'events') || {};
            if (!events.submit && !$element.attr('onsubmit')) {
              return {
                needsFunctionality: true,
                type: 'form',
                details: getElementDetails(element, 'form')
              };
            }
          }

          // Check navigation items
          if ($element.hasClass('nav-item') ||
              $element.hasClass('nav-link') ||
              $element.attr('role') === 'tab') {

            const events = $._data(element, 'events') || {};
            if (!events.click && !$element.attr('onclick')) {
              return {
                needsFunctionality: true,
                type: 'navigation',
                details: getElementDetails(element, 'navigation')
              };
            }
          }

          // Element doesn't need functionality
          return {
            needsFunctionality: false
          };
        };

        // Function to get element details
        function getElementDetails(element, type) {
          // Generate a unique selector for the element
          const selector = getUniqueSelector(element);

          // Get element description
          const description = getElementDescription(element);

          // Determine context based on surrounding elements
          const context = getElementContext(element);

          return {
            id: 'unimp-' + Math.random().toString(36).substring(2, 10),
            tagName: element.tagName.toLowerCase(),
            elementId: element.id || undefined,
            className: element.className || undefined,
            ref: selector,
            description: description,
            type: type,
            context: context
          };
        }

        // Function to get a unique selector for an element
        function getUniqueSelector(el) {
          if (!el) return '';
          if (el === document.body) return 'body';
          if (el === document.documentElement) return 'html';

          // Start with the tag name
          const tagName = el.tagName.toLowerCase();

          // If element has an ID, use it (most specific)
          if (el.id) {
            return '#' + el.id;
          }

          // If element has classes, use the first class
          if (el.className && typeof el.className === 'string') {
            const classes = el.className.trim().split(/\\s+/);
            if (classes.length > 0 && classes[0]) {
              // Try with tag name and class
              const selector = tagName + '.' + classes[0];
              // Check if this selector is unique enough
              if (document.querySelectorAll(selector).length === 1) {
                return selector;
              }
            }
          }

          // Try to create a selector based on the element's position
          const parent = el.parentElement;
          if (parent) {
            const siblings = Array.from(parent.children);
            const index = siblings.indexOf(el);

            // If this is the only child of this type, use a simple selector
            const sameTagSiblings = siblings.filter(s => s.tagName.toLowerCase() === tagName);
            if (sameTagSiblings.length === 1) {
              return parent.tagName.toLowerCase() + ' > ' + tagName;
            }

            // Otherwise use nth-child
            return tagName + ':nth-child(' + (index + 1) + ')';
          }

          // Fallback to a generic selector
          return tagName;
        }

        // Function to generate a description of the element
        function getElementDescription(el) {
          if (!el) return '';

          const tagName = el.tagName.toLowerCase();
          const text = el.innerText ? el.innerText.substring(0, 50).trim() : '';

          let description = tagName;
          if (text) description += ' containing text "' + text + '"';

          return description;
        }

        // Function to determine the context of the element
        function getElementContext(el) {
          // Check if element is inside a form
          const form = $(el).closest('form');
          if (form.length) {
            const formId = form.attr('id') || '';
            const formAction = form.attr('action') || '';
            return 'Inside form' + (formId ? ' with ID ' + formId : '') +
                   (formAction ? ' with action ' + formAction : '');
          }

          // Check if element is inside a navigation
          const nav = $(el).closest('nav, [role="navigation"]');
          if (nav.length) {
            return 'Inside navigation element';
          }

          // Check if element is inside a card or panel
          const card = $(el).closest('.card, .panel');
          if (card.length) {
            const cardTitle = card.find('.card-title, .panel-title').text();
            return 'Inside card' + (cardTitle ? ' titled "' + cardTitle + '"' : '');
          }

          return 'General page element';
        }
      }

      // Start checking for jQuery
      checkJQuery();
    })();
  `;

  // Function to inject scripts into iframe
  const injectScripts = useCallback(() => {
    if (!iframeRef.current) return false;

    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return false;

    const doc = iframe.contentDocument;

    // Check if script is already injected to avoid duplicate injection
    if (doc.querySelector('#functionality-generator-script')) return true;

    try {
      // Add detection script
      const script = doc.createElement('script');
      script.id = 'functionality-generator-script';
      script.textContent = DETECTION_SCRIPT;
      doc.body.appendChild(script);

      console.log('Functionality generator scripts injected successfully');
      return true;
    } catch (error) {
      console.error('Failed to inject functionality generator scripts:', error);
      return false;
    }
  }, [iframeRef, DETECTION_SCRIPT]);

  // Add script to iframe when it loads
  useEffect(() => {
    if (!iframeRef.current || disabled || viewMode !== 'preview') return;

    const handleIframeLoad = () => {
      const success = injectScripts();
      setScriptInjected(success);
    };

    // Add load event listener
    const currentIframe = iframeRef.current;
    currentIframe.addEventListener('load', handleIframeLoad);

    // If iframe is already loaded, inject the script immediately
    if (currentIframe.contentDocument?.readyState === 'complete') {
      const success = injectScripts();
      setScriptInjected(success);
    }

    return () => {
      currentIframe.removeEventListener('load', handleIframeLoad);
    };
  }, [iframeRef, injectScripts, disabled, viewMode]);

  // Expose a method to check if an element needs functionality
  const checkElementNeedsFunctionality = useCallback((element: HTMLElement): { needsFunctionality: boolean, details?: UnimplementedElement } => {
    if (!iframeRef.current || !scriptInjected) {
      return { needsFunctionality: false };
    }

    const iframe = iframeRef.current;
    const contentWindow = iframe.contentWindow as any;

    // Check if the checkElementNeedsFunctionality function is available
    if (contentWindow && contentWindow.checkElementNeedsFunctionality) {
      const result = contentWindow.checkElementNeedsFunctionality(element);

      if (result && result.needsFunctionality) {
        return {
          needsFunctionality: true,
          details: result.details
        };
      }
    }

    return { needsFunctionality: false };
  }, [iframeRef, scriptInjected]);

  // Expose the check method to parent components
  useEffect(() => {
    if (iframeRef.current && scriptInjected) {
      // @ts-ignore - Add the method to the component instance
      iframeRef.current.checkElementNeedsFunctionality = checkElementNeedsFunctionality;
      console.log('Functionality checker method attached to iframe');
    }
  }, [iframeRef, scriptInjected, checkElementNeedsFunctionality]);

  // Add a debug listener to check elements on click
  useEffect(() => {
    if (!iframeRef.current || !scriptInjected) return;

    const iframe = iframeRef.current;
    const contentWindow = iframe.contentWindow as any;

    // Add a click listener to the iframe document for debugging
    const handleDebugClick = (e: MouseEvent) => {
      if (!contentWindow.checkElementNeedsFunctionality) return;

      const element = e.target as HTMLElement;
      const result = contentWindow.checkElementNeedsFunctionality(element);

      console.log('Debug - Element clicked:', element);
      console.log('Debug - Needs functionality:', result.needsFunctionality);
      if (result.needsFunctionality) {
        console.log('Debug - Functionality type:', result.type);
        console.log('Debug - Element details:', result.details);
      }
    };

    const doc = iframe.contentDocument;
    if (doc) {
      doc.addEventListener('click', handleDebugClick, true);

      return () => {
        doc.removeEventListener('click', handleDebugClick, true);
      };
    }
  }, [iframeRef, scriptInjected]);

  return null; // This component doesn't render anything visible
});

export default FunctionalityGenerator;
