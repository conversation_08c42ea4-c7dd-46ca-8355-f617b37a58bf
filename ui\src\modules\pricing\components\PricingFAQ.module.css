.faqContainer {
  margin: 4rem 0;
}

.faqTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  text-align: center;
  margin-bottom: 2rem;
}

.faqItems {
  max-width: 768px;
  margin: 0 auto;
}

.faqItem {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.faqQuestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  cursor: pointer;
  font-weight: 600;
  color: #111827;
  font-size: 1rem;
  transition: color 0.2s ease;
}

.faqQuestion:hover {
  color: #4f46e5;
}

.faqToggle {
  font-size: 1.25rem;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.expanded .faqToggle {
  transform: rotate(180deg);
}

.faqAnswer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.6;
}

.expanded .faqAnswer {
  max-height: 500px;
  padding-bottom: 1rem;
}

.faqAnswer p {
  margin: 0;
}

.faqAnswer a {
  color: #4f46e5;
  text-decoration: none;
}

.faqAnswer a:hover {
  text-decoration: underline;
}
