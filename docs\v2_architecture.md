# LLM V2 Architecture Documentation

## Overview

The V2 LLM implementation introduces a modern, modular architecture using LangChain.js with enhanced streaming capabilities and better separation of concerns. This new approach offers improved maintainability, extensibility, and real-time feedback through Server-Sent Events (SSE).

## Key Features

1. Uses LangChain.js for LLM interactions
2. Streams responses using SSE with three event types:
   - `start`: Signals the beginning of generation
   - `data`: Contains the generated tokens
   - `end`: Signals completion
3. Provider-agnostic using factory pattern (OpenAI/Anthropic)
4. Clear separation of concerns
5. Zero-temperature deterministic outputs
6. Modular and extensible architecture

## Architecture

```
backend/
  services/
    llm/
      LLMFactory.js      # Provider factory and configuration
      systemPrompts.js   # Centralized system prompts
    sse/
      SSECallbackHandler.js          # SSE event management
      StreamCaptureMiddleware.js     # Token logging middleware
    llmServiceV2.js     # Main service orchestration
```

### Components

#### 1. LLM Factory
- Handles provider selection and configuration
- Supports OpenAI and Anthropic
- Configurable through environment variables
- Zero-temperature setting for deterministic outputs

```javascript
const llm = LLMFactory.createLLM('openai'); // or 'anthropic'
```

#### 2. SSE Handler
- Manages Server-Sent Events
- Provides real-time streaming
- Three event types: start, data, end
- Clean error handling

```javascript
handler.handleStart();
handler.handleToken(token);
handler.handleEnd();
```

#### 3. Stream Capture
- Middleware for token logging
- Captures streamed content
- Integrates with token tracking system
- Non-intrusive to main flow

#### 4. System Prompts
- Centralized prompt management
- Consistent instructions
- Easy to modify and maintain
- Domain-specific configurations

## API Endpoints

All V2 endpoints use SSE for streaming responses:

### 1. Plan Generation
```http
POST /api/llm/v2/plan
Content-Type: application/json

{
  "prompt": "string",
  "provider": "openai" | "anthropic" (optional)
}
```

### 2. Code Generation
```http
POST /api/llm/v2/generate
Content-Type: application/json

{
  "plan": "string",
  "provider": "openai" | "anthropic" (optional)
}
```

### 3. Element Modification
```http
POST /api/llm/v2/modify-element
Content-Type: application/json

{
  "htmlContent": "string",
  "elementSelector": "string",
  "prompt": "string",
  "provider": "openai" | "anthropic" (optional)
}
```

### 4. Content Modification
```http
POST /api/llm/v2/modify-content
Content-Type: application/json

{
  "htmlContent": "string",
  "prompt": "string",
  "provider": "openai" | "anthropic" (optional)
}
```

## Client-Side Integration

### SSE Event Handling
```javascript
const eventSource = new EventSource('/api/llm/v2/generate');

eventSource.addEventListener('start', () => {
  console.log('Generation started');
});

eventSource.addEventListener('data', (event) => {
  const { token } = JSON.parse(event.data);
  // Process token
});

eventSource.addEventListener('end', () => {
  console.log('Generation complete');
  eventSource.close();
});
```

### Error Handling
```javascript
eventSource.addEventListener('error', (event) => {
  const { error } = JSON.parse(event.data);
  console.error('Generation error:', error);
  eventSource.close();
});
```

## Configuration

### Environment Variables
```env
LLM_PROVIDER=openai    # Default provider (openai or anthropic)
OPENAI_API_KEY=xxx     # OpenAI API key
ANTHROPIC_API_KEY=xxx  # Anthropic API key
```

### Provider Models
```javascript
openai: 'gpt-4-turbo-preview'
anthropic: 'claude-3-sonnet-20240229'
```

## Benefits

1. **Real-time Feedback**: Instant token streaming for better user experience
2. **Modularity**: Easy to maintain and extend
3. **Provider Flexibility**: Simple to add new LLM providers
4. **Consistent Responses**: Zero-temperature for deterministic outputs
5. **Better Error Handling**: Structured error events through SSE
6. **Token Tracking**: Built-in token usage monitoring
7. **Clean Architecture**: Clear separation of concerns

## Backward Compatibility

The V2 endpoints coexist with the original implementation, allowing for gradual migration:
- Original routes remain at `/api/llm/*`
- V2 routes available at `/api/llm/v2/*`

## Future Extensions

The modular architecture makes it easy to:
1. Add new LLM providers
2. Implement additional streaming features
3. Enhance prompt management
4. Add new token tracking capabilities
5. Integrate additional validation steps
