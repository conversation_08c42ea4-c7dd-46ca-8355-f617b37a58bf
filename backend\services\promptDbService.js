const { Pool } = require('pg');
const crypto = require('crypto');

// Use either the connection string or individual parameters
let poolConfig;

if (process.env.DATABASE_URL) {
  console.log('[DB] Using DATABASE_URL for connection');
  poolConfig = {
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.PGSSLMODE === 'require' ? { rejectUnauthorized: false } : false,
  };
} else {
  console.log('[DB] Using individual connection parameters');
  poolConfig = {
    user: process.env.PGUSER,
    host: process.env.PGHOST,
    database: process.env.PGDATABASE,
    password: process.env.PGPASSWORD,
    port: process.env.PGPORT ? parseInt(process.env.PGPORT) : undefined,
    ssl: process.env.PGSSLMODE === 'require' ? { rejectUnauthorized: false } : false,
  };
}

console.log('[DB] Connection config (without password):', {
  ...poolConfig,
  password: poolConfig.password ? '******' : undefined,
  connectionString: poolConfig.connectionString ? '******' : undefined,
});

const pool = new Pool(poolConfig);

// Add error handling for the pool
pool.on('error', (err) => {
  console.error('[DB] Unexpected error on idle client', err);
});

// Test the connection
pool.query('SELECT NOW()', (err, res) => {
  if (err) {
    console.error('[DB] Error connecting to database:', err);
  } else {
    console.log('[DB] Connected to database successfully at', res.rows[0].now);

    // Check if the functions exist
    pool.query(`
      SELECT proname, proargtypes, pronargs
      FROM pg_proc
      WHERE proname IN ('use_tokens_and_log', 'increment_prototype_count')
    `, (err, res) => {
      if (err) {
        console.error('[DB] Error checking for functions:', err);
      } else {
        console.log('[DB] Functions found in database:', res.rows);

        if (res.rows.length === 0) {
          console.error('[DB] WARNING: Token tracking functions not found in database!');
          console.error('[DB] Please run the db_billing_schema.sql script to create them.');
        } else {
          console.log('[DB] Token tracking functions exist in database.');
        }
      }
    });
  }
});

// Save or upsert user (supports multiple auth providers)
async function upsertUser({
  google_id, facebook_id, twitter_id, github_id,
  email, display_name, photo_url,
  auth_provider = 'google',
  email_verified = false,
  password_hash = null
}) {
  console.log(`[DB] Upserting user with ${auth_provider}_id: ${google_id || facebook_id || twitter_id || github_id}`);

  try {
    // Determine which provider ID to use for the query
    let providerIdField, providerId;

    if (google_id) {
      providerIdField = 'google_id';
      providerId = google_id;
    } else if (facebook_id) {
      providerIdField = 'facebook_id';
      providerId = facebook_id;
    } else if (twitter_id) {
      providerIdField = 'twitter_id';
      providerId = twitter_id;
    } else if (github_id) {
      providerIdField = 'github_id';
      providerId = github_id;
    } else if (email && password_hash) {
      // Email/password authentication
      providerIdField = 'email';
      providerId = email;
    } else {
      throw new Error('No valid authentication provider specified');
    }

    // Handle large provider IDs
    if (providerId && providerId.length > 64) {
      console.log(`[DB] Provider ID is too long (${providerId.length} chars), hashing it`);
      providerId = crypto
        .createHash('md5')
        .update(providerId.toString())
        .digest('hex')
        .substring(0, 64); // Ensure it fits in VARCHAR(64)
    }

    // Build the query based on the provider
    let query, params;

    if (providerIdField === 'email') {
      // For email/password auth, use email as the unique identifier
      query = `
        INSERT INTO users (
          email, display_name, photo_url, password_hash,
          auth_provider, email_verified, last_login
        )
        VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
        ON CONFLICT (email) DO UPDATE
        SET display_name = COALESCE(EXCLUDED.display_name, users.display_name),
            photo_url = COALESCE(EXCLUDED.photo_url, users.photo_url),
            password_hash = COALESCE(EXCLUDED.password_hash, users.password_hash),
            auth_provider = COALESCE(EXCLUDED.auth_provider, users.auth_provider),
            email_verified = COALESCE(EXCLUDED.email_verified, users.email_verified),
            last_login = CURRENT_TIMESTAMP
        RETURNING id`;

      params = [email, display_name, photo_url, password_hash, auth_provider, email_verified];
    } else {
      // For social auth, use the provider ID field
      query = `
        INSERT INTO users (
          ${providerIdField}, email, display_name, photo_url,
          auth_provider, email_verified, last_login
        )
        VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
        ON CONFLICT (${providerIdField})
        WHERE ${providerIdField} IS NOT NULL
        DO UPDATE
        SET email = COALESCE(EXCLUDED.email, users.email),
            display_name = COALESCE(EXCLUDED.display_name, users.display_name),
            photo_url = COALESCE(EXCLUDED.photo_url, users.photo_url),
            auth_provider = COALESCE(EXCLUDED.auth_provider, users.auth_provider),
            email_verified = COALESCE(EXCLUDED.email_verified, users.email_verified),
            last_login = CURRENT_TIMESTAMP
        RETURNING id`;

      params = [providerId, email, display_name, photo_url, auth_provider, email_verified];
    }

    // If the user doesn't exist yet, try to find them by email
    try {
      const res = await pool.query(query, params);

      if (res.rows.length > 0) {
        console.log(`[DB] User upserted successfully, id: ${res.rows[0].id}`);
        return res.rows[0].id;
      }
    } catch (error) {
      // If there's a unique constraint violation on the provider ID,
      // the user might already exist with a different provider
      if (error.message.includes('unique constraint') && email) {
        console.log(`[DB] User might exist with a different provider, trying to find by email: ${email}`);

        // Try to find the user by email
        const emailRes = await pool.query(
          `SELECT id FROM users WHERE email = $1`,
          [email]
        );

        if (emailRes.rows.length > 0) {
          const userId = emailRes.rows[0].id;
          console.log(`[DB] Found existing user by email, id: ${userId}`);

          // Update the user with the new provider ID
          await pool.query(
            `UPDATE users
             SET ${providerIdField} = $1,
                 auth_provider = COALESCE($2, auth_provider),
                 display_name = COALESCE($3, display_name),
                 photo_url = COALESCE($4, photo_url),
                 email_verified = COALESCE($5, email_verified),
                 last_login = CURRENT_TIMESTAMP
             WHERE id = $6`,
            [providerId, auth_provider, display_name, photo_url, email_verified, userId]
          );

          console.log(`[DB] Updated existing user with new provider ID, id: ${userId}`);
          return userId;
        }
      }

      // Re-throw the error if we couldn't handle it
      throw error;
    }

    // If we get here, something went wrong
    throw new Error('Failed to upsert user');
  } catch (error) {
    console.error(`[DB] Error upserting user: ${error.message}`);
    throw error;
  }
}

// Save a new prompt
async function savePrompt({ user_id, prompt_text }) {
  console.log(`[DB] Saving prompt for user_id: ${user_id}`);

  try {
    // Check if the database connection is working
    try {
      const pingResult = await pool.query('SELECT 1 as ping');
      console.log(`[DB] Database connection check: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    } catch (pingError) {
      console.error(`[DB] Database connection check FAILED: ${pingError.message}`);
      throw new Error(`Database connection failed: ${pingError.message}`);
    }

    // Check if the prompts table exists
    try {
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'prompts'
        ) as exists
      `);
      console.log(`[DB] Prompts table exists: ${tableCheck.rows[0].exists}`);

      if (!tableCheck.rows[0].exists) {
        console.error('[DB] The prompts table does not exist! Creating it now...');

        // Create the prompts table if it doesn't exist
        await pool.query(`
          CREATE TABLE IF NOT EXISTS prompts (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
            prompt_text TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );

          CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts(user_id);
        `);

        console.log('[DB] Created prompts table');
      }
    } catch (tableError) {
      console.error(`[DB] Error checking if prompts table exists: ${tableError.message}`);
      throw new Error(`Error checking prompts table: ${tableError.message}`);
    }

    // Make sure user_id is a valid integer
    let userIdInt;
    try {
      if (typeof user_id === 'string') {
        // Try to parse as integer first
        userIdInt = parseInt(user_id, 10);
        if (isNaN(userIdInt)) {
          // If not a valid integer, try to find the user by google_id or email
          console.log(`[DB] User ID is not a valid integer: ${user_id}, trying to find by google_id or email`);

          const userLookup = await pool.query(`
            SELECT id FROM users
            WHERE google_id = $1 OR email = $1
            LIMIT 1
          `, [user_id]);

          if (userLookup.rows.length > 0) {
            userIdInt = userLookup.rows[0].id;
            console.log(`[DB] Found user with ID ${userIdInt} by google_id or email lookup`);
          } else {
            throw new Error(`Could not find user with ID ${user_id}`);
          }
        }
      } else if (typeof user_id === 'number') {
        userIdInt = user_id;
      } else {
        throw new Error(`Invalid user_id type: ${typeof user_id}`);
      }

      console.log(`[DB] Using user ID: ${userIdInt}`);
    } catch (parseError) {
      console.error(`[DB] Error parsing user_id: ${parseError.message}`);
      throw new Error(`Invalid user ID: ${parseError.message}`);
    }

    // Check if the user exists in the users table
    try {
      const userCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM users
          WHERE id = $1
        ) as exists
      `, [userIdInt]);

      console.log(`[DB] User with ID ${userIdInt} exists: ${userCheck.rows[0].exists}`);

      if (!userCheck.rows[0].exists) {
        console.error(`[DB] User with ID ${userIdInt} does not exist in the users table!`);
        throw new Error(`User with ID ${userIdInt} does not exist in the users table`);
      }
    } catch (userError) {
      console.error(`[DB] Error checking if user exists: ${userError.message}`);
      throw new Error(`Error checking user: ${userError.message}`);
    }

    console.log(`[DB] Executing INSERT INTO prompts with user_id=${userIdInt}`);

    const res = await pool.query(
      `INSERT INTO prompts (user_id, prompt_text)
       VALUES ($1, $2)
       RETURNING id`,
      [userIdInt, prompt_text]
    );

    if (res.rows.length === 0) {
      throw new Error('Insert succeeded but no ID was returned');
    }

    console.log(`[DB] Prompt saved successfully, id: ${res.rows[0].id}`);
    return res.rows[0].id;
  } catch (error) {
    console.error(`[DB] Error saving prompt: ${error.message}`);
    console.error(`[DB] Error details:`, error);

    // Check for specific database errors
    if (error.code === '42P01') {
      console.error('[DB] Error: Table "prompts" does not exist. Please run the schema SQL files.');
    } else if (error.code === '23503') {
      console.error('[DB] Error: Foreign key violation. The user_id may not exist in the users table.');
    }

    // Rethrow the error to be handled by the caller
    throw error;
  }
}

// Save a new prompt iteration
async function savePromptIteration({ prompt_id, iteration_number, input_text, output_text }) {
  console.log(`[DB] Saving prompt iteration for prompt_id: ${prompt_id}, iteration: ${iteration_number}`);

  // Skip if prompt_id is invalid (e.g., -1 from a failed prompt save)
  if (!prompt_id || prompt_id < 0) {
    console.log(`[DB] Skipping prompt iteration save because prompt_id is invalid: ${prompt_id}`);
    throw new Error(`Invalid prompt_id: ${prompt_id}`);
  }

  try {
    // Check if the database connection is working
    try {
      const pingResult = await pool.query('SELECT 1 as ping');
      console.log(`[DB] Database connection check: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    } catch (pingError) {
      console.error(`[DB] Database connection check FAILED: ${pingError.message}`);
      throw new Error(`Database connection failed: ${pingError.message}`);
    }

    // Check if the prompt_iterations table exists
    try {
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'prompt_iterations'
        ) as exists
      `);
      console.log(`[DB] prompt_iterations table exists: ${tableCheck.rows[0].exists}`);

      if (!tableCheck.rows[0].exists) {
        console.error('[DB] The prompt_iterations table does not exist! Creating it now...');

        // Create the prompt_iterations table if it doesn't exist
        await pool.query(`
          CREATE TABLE IF NOT EXISTS prompt_iterations (
            id SERIAL PRIMARY KEY,
            prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
            iteration_number INTEGER NOT NULL,
            input_text TEXT NOT NULL,
            output_text TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );

          CREATE INDEX IF NOT EXISTS idx_prompt_iterations_prompt_id ON prompt_iterations(prompt_id);
        `);

        console.log('[DB] Created prompt_iterations table');
      }
    } catch (tableError) {
      console.error(`[DB] Error checking if prompt_iterations table exists: ${tableError.message}`);
      throw new Error(`Error checking prompt_iterations table: ${tableError.message}`);
    }

    // Check if the prompt exists in the prompts table
    try {
      const promptCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM prompts
          WHERE id = $1
        ) as exists
      `, [prompt_id]);

      console.log(`[DB] Prompt with ID ${prompt_id} exists: ${promptCheck.rows[0].exists}`);

      if (!promptCheck.rows[0].exists) {
        console.error(`[DB] Prompt with ID ${prompt_id} does not exist in the prompts table!`);
        throw new Error(`Prompt with ID ${prompt_id} does not exist in the prompts table`);
      }
    } catch (promptError) {
      console.error(`[DB] Error checking if prompt exists: ${promptError.message}`);
      throw new Error(`Error checking prompt: ${promptError.message}`);
    }

    // Make sure iteration_number is a valid integer
    let iterationNumberInt;
    try {
      iterationNumberInt = parseInt(iteration_number, 10);
      if (isNaN(iterationNumberInt) || iterationNumberInt < 1) {
        throw new Error(`Invalid iteration number: ${iteration_number}`);
      }
    } catch (parseError) {
      console.error(`[DB] Error parsing iteration_number: ${parseError.message}`);
      throw new Error(`Invalid iteration number: ${parseError.message}`);
    }

    console.log(`[DB] Executing INSERT INTO prompt_iterations with prompt_id=${prompt_id}, iteration=${iterationNumberInt}`);

    const res = await pool.query(
      `INSERT INTO prompt_iterations (prompt_id, iteration_number, input_text, output_text)
       VALUES ($1, $2, $3, $4)
       RETURNING id`,
      [prompt_id, iterationNumberInt, input_text, output_text]
    );

    if (res.rows.length === 0) {
      throw new Error('Insert succeeded but no ID was returned');
    }

    console.log(`[DB] Prompt iteration saved successfully, id: ${res.rows[0].id}`);
    return res.rows[0].id;
  } catch (error) {
    console.error(`[DB] Error saving prompt iteration: ${error.message}`);
    console.error(`[DB] Error details:`, error);

    // Check for specific database errors
    if (error.code === '42P01') {
      console.error('[DB] Error: Table "prompt_iterations" does not exist. Please run the schema SQL files.');
    } else if (error.code === '23503') {
      console.error('[DB] Error: Foreign key violation. The prompt_id may not exist in the prompts table.');
    }

    // Rethrow the error to be handled by the caller
    throw error;
  }
}

module.exports = {
  pool,
  upsertUser,
  savePrompt,
  savePromptIteration,
};
