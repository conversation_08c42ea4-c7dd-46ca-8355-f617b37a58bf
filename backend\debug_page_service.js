/**
 * Debug the prototypePageService to see what's causing the 500 error
 */

const { pool } = require('./services/promptDbService');

async function debugPageService() {
  try {
    console.log('🔍 Debugging Page Service...\n');

    // 1. Check database connection
    console.log('1. Testing database connection...');
    try {
      const result = await pool.query('SELECT 1 as test');
      console.log('✅ Database connection working');
    } catch (dbError) {
      console.log('❌ Database connection failed:', dbError.message);
      return;
    }

    // 2. Check if prototype_pages table exists
    console.log('\n2. Checking if prototype_pages table exists...');
    try {
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'prototype_pages'
        );
      `);
      
      if (tableCheck.rows[0].exists) {
        console.log('✅ prototype_pages table exists');
        
        // Check table structure
        const columns = await pool.query(`
          SELECT column_name, data_type, is_nullable 
          FROM information_schema.columns 
          WHERE table_name = 'prototype_pages' 
          ORDER BY ordinal_position;
        `);
        
        console.log('📋 Table structure:');
        columns.rows.forEach(col => {
          console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
        
      } else {
        console.log('❌ prototype_pages table does not exist');
        console.log('🔧 Need to run migration first!');
        return;
      }
    } catch (error) {
      console.log('❌ Error checking table:', error.message);
      return;
    }

    // 3. Check if prototype 228 exists
    console.log('\n3. Checking if prototype 228 exists...');
    try {
      const prototypeCheck = await pool.query('SELECT id, title FROM prototypes WHERE id = $1', [228]);
      if (prototypeCheck.rows.length > 0) {
        console.log('✅ Prototype 228 exists:', prototypeCheck.rows[0].title);
      } else {
        console.log('❌ Prototype 228 does not exist');
        
        // Show available prototypes
        const allPrototypes = await pool.query('SELECT id, title FROM prototypes ORDER BY id LIMIT 5');
        console.log('📋 Available prototypes:');
        allPrototypes.rows.forEach(p => {
          console.log(`  - ID ${p.id}: ${p.title}`);
        });
      }
    } catch (error) {
      console.log('❌ Error checking prototype:', error.message);
    }

    // 4. Test the prototypePageService directly
    console.log('\n4. Testing prototypePageService...');
    try {
      const prototypePageService = require('./services/prototypePageService');
      
      // Test with prototype 228 and user 1
      const pages = await prototypePageService.getPagesByPrototypePaginated(228, 1, 30, 0);
      console.log('✅ getPagesByPrototypePaginated works, returned:', pages.length, 'pages');
      
      const count = await prototypePageService.getPagesCountByPrototype(228, 1);
      console.log('✅ getPagesCountByPrototype works, count:', count);
      
    } catch (serviceError) {
      console.log('❌ prototypePageService error:', serviceError.message);
      console.log('Stack trace:', serviceError.stack);
    }

    // 5. Test the actual API logic
    console.log('\n5. Testing API logic manually...');
    try {
      const prototypeService = require('./services/prototypeService');
      const prototypePageService = require('./services/prototypePageService');
      
      // Simulate the API call logic
      const projectId = 228;
      const userId = 1; // Assuming user 1
      const pageSize = 30;
      const offset = 0;
      
      console.log('🔍 Checking if prototype belongs to user...');
      const project = await prototypeService.getPrototypeById(projectId, userId);
      if (!project) {
        console.log('❌ Project not found or access denied for user', userId);
        
        // Try to find the actual owner
        const projectInfo = await pool.query('SELECT id, user_id, title FROM prototypes WHERE id = $1', [projectId]);
        if (projectInfo.rows.length > 0) {
          console.log('📋 Project info:', projectInfo.rows[0]);
          console.log('🔍 Trying with correct user ID...');
          
          const correctUserId = projectInfo.rows[0].user_id;
          const pages = await prototypePageService.getPagesByPrototypePaginated(projectId, correctUserId, pageSize, offset);
          const totalCount = await prototypePageService.getPagesCountByPrototype(projectId, correctUserId);
          
          console.log('✅ With correct user ID:', correctUserId);
          console.log('✅ Pages found:', pages.length);
          console.log('✅ Total count:', totalCount);
        }
      } else {
        console.log('✅ Project found:', project.title);
        
        const pages = await prototypePageService.getPagesByPrototypePaginated(projectId, userId, pageSize, offset);
        const totalCount = await prototypePageService.getPagesCountByPrototype(projectId, userId);
        
        console.log('✅ Pages found:', pages.length);
        console.log('✅ Total count:', totalCount);
      }
      
    } catch (apiError) {
      console.log('❌ API logic error:', apiError.message);
      console.log('Stack trace:', apiError.stack);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

debugPageService();
