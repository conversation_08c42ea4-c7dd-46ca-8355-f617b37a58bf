.errorContainer {
  padding: 1.5rem;
  max-width: 28rem;
  margin: 0 auto;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.errorTitle {
  color: #dc2626;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.errorMessageContainer {
  color: #4b5563;
  margin-bottom: 1rem;
}

.errorMessageLabel {
  margin-bottom: 0.5rem;
}

.errorMessageContent {
  background-color: #f3f4f6;
  padding: 0.5rem;
  border-radius: 0.375rem;
  overflow: auto;
  font-size: 0.875rem;
}

.resetButton {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.resetButton:hover {
  background-color: #1d4ed8;
}

/* Dark mode styles */
.darkErrorContainer {
  background-color: #1f2937;
}

.darkErrorTitle {
  color: #ef4444;
}

.darkErrorMessageContainer {
  color: #d1d5db;
}

.darkErrorMessageContent {
  background-color: #111827;
}
