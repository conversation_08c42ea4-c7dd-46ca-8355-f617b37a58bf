<
div
 
id
=
"
app
"
>

  
<!-- Main Navigation -->

  
<
nav
 
class
=
"
bg-navy-900 text-white p-4 flex items-center justify-between
"
>

    
<
div
 
class
=
"
flex items-center space-x-4
"
>

      
<
h1
 
class
=
"
text-xl font-bold
"
>
Dashing CRM
</
h1
>

      
<
div
 
class
=
"
hidden md:flex space-x-6
"
>

        
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showPage
(
'home'
)
"
 
class
=
"
hover:text-teal-300
"
>
Dashboard
</
a
>

        
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showPage
(
'pipeline'
)
"
 
class
=
"
hover:text-teal-300
"
>
Pipeline
</
a
>

        
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showPage
(
'performance'
)
"
 
class
=
"
hover:text-teal-300
"
>
Performance
</
a
>

        
<
div
 
class
=
"
unimplemented
"
 
data-feature
=
"
navigation-to-contacts
"
>

          
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showUnimplementedFeature
(
this
)
"
 
class
=
"
hover:text-teal-300
"
>
Contacts
</
a
>

        
</
div
>

      
</
div
>

    
</
div
>

    
<
div
 
class
=
"
flex items-center space-x-4
"
>

      
<
button
 
class
=
"
bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded-lg
"
>
Quick Action
</
button
>

      
<
div
 
class
=
"
w-8 h-8 bg-gray-300 rounded-full
"
>
</
div
>

    
</
div
>

  
</
nav
>

  
<!-- Home Page -->

  
<
div
 
class
=
"
page
"
 
id
=
"
page-home
"
 
style
=
"
display
:
 none
;
"
>

    
<
div
 
class
=
"
container mx-auto p-4
"
>

      
<!-- KPI Cards -->

      
<
div
 
class
=
"
grid grid-cols-1 md:grid-cols-4 gap-4 mb-6
"
>

        
<
kpi-card
 
title
=
"
Open Deals
"
 
value
=
"
24
"
 
trend
=
"
up
"
 
change
=
"
12%
"
>
</
kpi-card
>

        
<
kpi-card
 
title
=
"
Win Rate
"
 
value
=
"
68%
"
 
trend
=
"
up
"
 
change
=
"
5%
"
>
</
kpi-card
>

        
<
kpi-card
 
title
=
"
Avg. Deal Size
"
 
value
=
"
$12.4k
"
 
trend
=
"
down
"
 
change
=
"
3%
"
>
</
kpi-card
>

        
<
kpi-card
 
title
=
"
Response Time
"
 
value
=
"
2.4h
"
 
trend
=
"
down
"
 
change
=
"
15%
"
>
</
kpi-card
>

      
</
div
>

      
<!-- Main Content -->

      
<
div
 
class
=
"
grid grid-cols-1 lg:grid-cols-3 gap-6
"
>

        
<!-- Activity Feed -->

        
<
div
 
class
=
"
lg:col-span-2 bg-white rounded-xl shadow p-4
"
>

          
<
h2
 
class
=
"
text-lg font-semibold mb-4
"
>
Smart Activity Feed
</
h2
>

          
<
div
 
class
=
"
space-y-4
"
>

            
<
timeline-item
 
              
title
=
"
New lead from website
"
 
              
time
=
"
15 min ago
"
 
              
urgency
=
"
high
"
 
              
content
=
"
John Smith from Acme Corp requested pricing info
"
>

            
</
timeline-item
>

            
<
timeline-item
 
              
title
=
"
Meeting scheduled
"
 
              
time
=
"
2 hours ago
"
 
              
urgency
=
"
medium
"
 
              
content
=
"
Demo call with Sarah Johnson at TechStart
"
>

            
</
timeline-item
>

            
<
timeline-item
 
              
title
=
"
Contract signed
"
 
              
time
=
"
1 day ago
"
 
              
urgency
=
"
low
"
 
              
content
=
"
Enterprise deal with Global Solutions
"
>

            
</
timeline-item
>

          
</
div
>

        
</
div
>

        
<!-- Performance Chart -->

        
<
div
 
class
=
"
bg-white rounded-xl shadow p-4
"
>

          
<
h2
 
class
=
"
text-lg font-semibold mb-4
"
>
Team Performance
</
h2
>

          
<
div
 
id
=
"
chart1
"
 
class
=
"
h-64 w-full
"
>
</
div
>

        
</
div
>

      
</
div
>

      
<!-- Bottom Row -->

      
<
div
 
class
=
"
grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6
"
>

        
<!-- Deal Stages -->

        
<
div
 
class
=
"
bg-white rounded-xl shadow p-4
"
>

          <2 class="text-lg font-semibold mb-4">Deal Stages
</
h2
>

          
<
div
 
id
=
"
chart2
"
 
class
=
"
h-64 w-full
"
>
</
div
>

        
</
div
>

        
<!-- Recent Activity -->

        
<
div
 
class
=
"
bg-white rounded-xl shadow p-4
"
>

          
<
h2
 
class
=
"
text-lg font-semibold mb-4
"
>
Recent Wins
</
h2
>

          
<
div
 
id
=
"
chart3
"
 
class
=
"
h-64 w-full
"
>
</
div
>

        
</
div
>

      
</
div
>

    
</
div
>

  
</
div
>

  
<!-- Pipeline Page -->

  
<
div
 
class
=
"
page
"
 
id
=
"
page-pipeline
"
 
style
=
"
display
:
 none
;
"
>

    
<
div
 
class
=
"
container mx-auto p-4
"
>

      
<
div
 
class
=
"
flex justify-between items-center mb-6
"
>

        
<
h2
 
class
=
"
text-2xl font-bold
"
>
Deal Pipeline
</
h2
>

        
<
div
 
class
=
"
flex space-x-2
"
>

          
<
button
 
class
=
"
bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded-lg
"
>
Add Deal
</
button
>

          
<
button
 
class
=
"
bg-white border border-gray-300 hover:bg-gray-50 px-4 py-2 rounded-lg
"
>
Filter
</
button
>

        
</
div
>

      
</
div
>

      
<!-- Pipeline Canvas -->

      
<
div
 
class
=
"
bg-white rounded-xl shadow p-4 overflow-x-auto
"
>

        
<
div
 
class
=
"
flex space-x-4 min-w-max
"
>

          
<!-- Pipeline Stage Columns -->

          
<
div
 
class
=
"
w-64 flex-shrink-0
"
>

            
<
h3
 
class
=
"
font-semibold mb-2
"
>
Prospecting (8)
</
h3
>

            
<
div
 
class
=
"
space-y-3
"
>

              
<
tool-card
 
                
title
=
"
Acme Corp
"
 
                
value
=
"
$15k
"
 
                
stage
=
"
prospecting
"
 
                
days
=
"
5
"
>

              
</
tool-card
>

              
<
tool-card
 
                
title
=
"
TechStart
"
 
                
value
=
"
$8k
"
 
                
stage
=
"
prospecting
"
 
                
days
=
"
2
"
>

              
</
tool-card
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
w-64 flex-shrink-0
"
>

            
<
h3
 
class
=
"
font-semibold mb-2
"
>
Qualified (6)
</
h3
>

            
<
div
 
class
=
"
space-y-3
"
>

              
<
tool-card
 
                
title
=
"
Global Solutions
"
 
                
value
=
"
$45k
"
 
                
stage
=
"
qualified
"
 
                
days
=
"
12
"
>

              
</
tool-card
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
w-64 flex-shrink-0
"
>

            
<
h3
 
class
=
"
font-semibold mb-2
"
>
Proposal (4)
</
h3
>

            
<
div
 
class
=
"
space-y-3
"
>

              
<
tool-card
 
                
title
=
"
Innovate Inc
"
 
                
value
=
"
$32k
"
 
                
stage
=
"
proposal
"
 
                
days
=
"
7
"
>

              
</
tool-card
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
w-64 flex-shrink-0
"
>

            
<
h3
 
class
=
"
font-semibold mb-2
"
>
Negotiation (3)
</
h3
>

            
<
div
 
class
=
"
space-y-3
"
>

              
<
tool-card
 
                
title
=
"
FutureTech
"
 
                
value
=
"
$28k
"
 
                
stage
=
"
negotiation
"
 
                
days
=
"
3
"
>

              
</
tool-card
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
w-64 flex-shrink-0
"
>

            
<
h3
 
class
=
"
font-semibold mb-2
"
>
Closed Won (3)
</
h3
>

            
<
div
 
class
=
"
space-y-3
"
>

              
<
tool-card
 
                
title
=
"
DataSystems
"
 
                
value
=
"
$52k
"
 
                
stage
=
"
closed-won
"
 
                
days
=
"
0
"
>

              
</
tool-card
>

            
</
div
>

          
</
div
>

        
</
div
>

      
</
div
>

    
</
div
>

    
<!-- Floating Back to Home Button -->

    
<
button
 
class
=
"
fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 z-50
"

      
onclick
=
"
showPage
(
'home'
)
"

      
title
=
"
Back to Home
"
>

      ⬅️
    
</
button
>

  
</
div
>

  
<!-- Performance Page -->

  
<
div
 
class
=
"
page
"
 
id
=
"
page-performance
"
 
style
=
"
display
:
 none
;
"
>

    
<
div
 
class
=
"
container mx-auto p-4
"
>

      
<
h2
 
class
=
"
text-2xl font-bold mb-6
"
>
Team Performance
</
h2
>

      
<
div
 
class
=
"
grid grid-cols-1 lg:grid-cols-2 gap-6
"
>

        
<!-- Heatmap -->

        
<
div
 
class
=
"
bg-white rounded-xl shadow p-4
"
>

          
<
h3
 
class
=
"
text-lg font-semibold mb-4
"
>
Activity Heatmap
</
h3
>

          
<
div
 
class
=
"
h-64 w-full bg-gray-100 rounded flex items-center justify-center
"
>

            
<
p
 
class
=
"
text-gray-500
"
>
Performance heatmap visualization
</
p
>

          
</
div
>

        
</
div
>

        
<!-- Leaderboard -->

        
<
div
 
class
=
"
bg-white rounded-xl shadow p-4
"
>

          
<
h3
 
class
=
"
text-lg font-semibold mb-4
"
>
Top Performers
</
h3
>

          
<
div
 
class
=
"
space-y-4
"
>

            
<
div
 
class
=
"
flex items-center justify-between p-2 hover:bg-gray-50 rounded
"
>

              
<
div
 
class
=
"
flex items-center space-x-3
"
>

                
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center text-teal-600
"
>
1
</
div
>

                
<
span
>
Alex Johnson
</
span
>

              
</
div
>

              
<
span
 
class
=
"
font-semibold
"
>
$124k
</
span
>

            
</
div
>

            
<
div
 
class
=
"
flex items-center justify-between p-2 hover:bg-gray-50 rounded
"
>

              
<
div
 
class
=
"
flex items-center space-x-3
"
>

                
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center text-teal-600
"
>
2
</
div
>

                
<
span
>
Sam Wilson
</
span
>

              
</
div
>

              
<
span
 
class
=
"
font-semibold
"
>
$98k
</
span
>

            
</
div
>

            
<
div
 
class
=
"
flex items-center justify-between p-2 hover:bg-gray-50 rounded
"
>

              
<
div
 
class
=
"
flex items-center space-x-3
"
>

                
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center text-teal-600
"
>
3
</
div
>

                
<
span
>
Taylor Smith
</
span
>

              
</
div
>

              
<
span
 
class
=
"
font-semibold
"
>
$87k
</
span
>

            
</
div
>

          
</
div
>

        
</
div
>

      
</
div
>

      
<!-- Individual Stats -->

      
<
div
 
class
=
"
mt-6 bg-white rounded-xl shadow p-4
"
>

        
<
h3
 
class
=
"
text-lg font-semibold mb-4
"
>
Your Metrics
</
h3
>

        
<
div
 
class
=
"
grid grid-cols-2 md:grid-cols-4 gap-4
"
>

          
<
div
 
class
=
"
p-4 border border-gray-200 rounded-lg
"
>

            
<
p
 
class
=
"
text-sm text-gray-500
"
>
Calls Made
</
p
>

            
<
p
 
class
=
"
text-2xl font-bold
"
>
42
</
p
>

          
</
div
>

          
<
div
 
class
=
"
p-4 border border-gray-200 rounded-lg
"
>

            
<
p
 
class
=
"
text-sm text-gray-500
"
>
Emails Sent
</
p
>

            
<
p
 
class
=
"
text-2xl font-bold
"
>
76
</
p
>

          
</
div
>

          
<
div
 
class
=
"
p-4 border border-gray-200 rounded-lg
"
>

            
<
p
 
class
=
"
text-sm text-gray-500
"
>
Meetings
</
p
>

            
<
p
 
class
=
"
text-2xl font-bold
"
>
15
</
p
>

          
</
div
>

          
<
div
 
class
=
"
p-4 border border-gray-200 rounded-lg
"
>

            
<
p
 
class
=
"
text-sm text-gray-500
"
>
Deals Closed
</
p
>

            
<
p
 
class
=
"
text-2xl font-bold
"
>
7
</
p
>

          
</
div
>

        
</
div
>

      
</
div
>

    
</
div
>

    
<!-- Floating Back to Home Button -->

    
<
button
 
class
=
"
fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 z-50
"

      
onclick
=
"
showPage
(
'home'
)
"

      
title
=
"
Back to Home
"
>

      ⬅️
    
</
button
>

  
</
div
>

  
<!-- Quick Action Bar -->

  
<
div
 
class
=
"
fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-2 flex justify-center space-x-4 z-40
"
>

    
<
button
 
class
=
"
flex flex-col items-center text-xs text-gray-600 hover:text-teal-500
"
>

      
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mb-1
"
>

        
<
span
 
class
=
"
text-teal-600
"
>
📞
</
span
>

      
</
div
>

      Log Call
    
</
button
>

    
<
button
 
class
=
"
flex flex-col items-center text-xs text-gray-600 hover:text-teal-500
"
>

      
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mb-1
"
>

        
<
span
 
class
=
"
text-teal-600
"
>
✉️
</
span
>

      
</
div
>

      Send Email
    
</
button
>

    
<
button
 
class
=
"
flex flex-col items-center text-xs text-gray-600 hover:text-teal-500
"
>

      
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mb-1
"
>

        
<
span
 
class
=
"
text-teal-600
"
>
📅
</
span
>

      
</
div
>

      Schedule
    
</
button
>

    
<
button
 
class
=
"
flex flex-col items-center text-xs text-gray-600 hover:text-teal-500
"
>

      
<
div
 
class
=
"
w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mb-1
"
>

        
<
span
 
class
=
"
text-teal-600
"
>
➕
</
span
>

      
</
div
>

      Add Task
    
</
button
>

  
</
div
>

  
<
script
 
data-exec
=
"
inline
"
>

    
// SPA Navigation

    
const
 pages 
=
 
document
.
querySelectorAll
(
'.page'
)
;

    
function
 
showPage
(
pageId
)
 
{

      
const
 trigger 
=
 event
?.
target 
||
 
null
;

      
const
 target 
=
 
document
.
getElementById
(
'page-'
 
+
 pageId
)
;

      
if
 
(
target
)
 
{

        pages
.
forEach
(
p
 
=>
 p
.
style
.
display
 
=
 
'none'
)
;

        target
.
style
.
display
 
=
 
'block'
;

      
}
 
else
 
if
 
(
trigger
)
 
{

        
try
 
{

          
const
 wrapper 
=
 
document
.
createElement
(
'div'
)
;

          wrapper
.
className
 
=
 
'unimplemented border-2 border-dashed border-red-500 p-2'
;

          wrapper
.
setAttribute
(
'data-feature'
,
 
'navigation-to-'
 
+
 pageId
)
;

          
const
 button 
=
 
document
.
createElement
(
'button'
)
;

          button
.
textContent
 
=
 
'Implement: navigation-to-'
 
+
 pageId
;

          button
.
className
 
=
 
'mt-2 text-red-600 underline block'
;

          trigger
.
parentNode
.
replaceChild
(
wrapper
,
 trigger
)
;

          wrapper
.
appendChild
(
trigger
)
;

          wrapper
.
appendChild
(
button
)
;

          
setTimeout
(
(
)
 
=>
 
{

            wrapper
.
replaceWith
(
trigger
)
;

          
}
,
 
3000
)
;

        
}
 
catch
 
(
e
)
 
{

          
console
.
error
(
'Failed to mark unimplemented navigation:'
,
 e
)
;

        
}

      
}

    
}

    
function
 
showUnimplementedFeature
(
element
)
 
{

      
try
 
{

        
const
 wrapper 
=
 element
.
closest
(
'.unimplemented'
)
;

        
if
 
(
wrapper
)
 
{

          wrapper
.
classList
.
add
(
'border-2'
,
 
'border-dashed'
,
 
'border-red-500'
,
 
'p-2'
)
;

          
const
 button 
=
 
document
.
createElement
(
'button'
)
;

          button
.
textContent
 
=
 
'Implement: '
 
+
 wrapper
.
dataset
.
feature
;

          button
.
className
 
=
 
'mt-2 text-red-600 underline block'
;

          wrapper
.
appendChild
(
button
)
;

          
setTimeout
(
(
)
 
=>
 
{

            wrapper
.
classList
.
remove
(
'border-2'
,
 
'border-dashed'
,
 
'border-red-500'
,
 
'p-2'
)
;

            
if
 
(
wrapper
.
contains
(
button
)
)
 
{

              wrapper
.
removeChild
(
button
)
;

            
}

          
}
,
 
3000
)
;

        
}

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Failed to show unimplemented feature:'
,
 e
)
;

      
}

    
}

    
// Initialize Charts

    
document
.
addEventListener
(
'DOMContentLoaded'
,
 
(
)
 
=>
 
{

      
showPage
(
'home'
)
;

      
try
 
{

        
// Team Performance Pie Chart

        echarts
.
init
(
document
.
getElementById
(
'chart1'
)
)
.
setOption
(
{

          
tooltip
:
 
{

            
trigger
:
 
'item'

          
}
,

          
legend
:
 
{

            
orient
:
 
'vertical'
,

            
right
:
 
10
,

            
top
:
 
'center'

          
}
,

          
series
:
 
[
{

            
name
:
 
'Team Performance'
,

            
type
:
 
'pie'
,

            
radius
:
 
[
'40%'
,
 
'70%'
]
,

            
avoidLabelOverlap
:
 
false
,

            
itemStyle
:
 
{

              
borderRadius
:
 
10
,

              
borderColor
:
 
'#fff'
,

              
borderWidth
:
 
2

            
}
,

            
label
:
 
{

              
show
:
 
false
,

              
position
:
 
'center'

            
}
,

            
emphasis
:
 
{

              
label
:
 
{

                
show
:
 
true
,

                
fontSize
:
 
'18'
,

                
fontWeight
:
 
'bold'

              
}

            
}
,

            
labelLine
:
 
{

              
show
:
 
false

            
}
,

            
data
:
 
[

              
{
 
value
:
 
42
,
 
name
:
 
'Alex J.'
 
}
,

              
{
 
value
:
 
35
,
 
name
:
 
'Sam W.'
 
}
,

              
{
 
value
:
 
28
,
 
name
:
 
'Taylor S.'
 
}
,

              
{
 
value
:
 
22
,
 
name
:
 
'Jordan M.'
 
}

            
]
,

            
color
:
 
[
'#0d9488'
,
 
'#14b8a6'
,
 
'#2dd4bf'
,
 
'#99f6e4'
]

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart1 failed:'
,
 e
)
;

      
}

      
try
 
{

        
// Deal Stages Bar Chart

        echarts
.
init
(
document
.
getElementById
(
'chart2'
)
)
.
setOption
(
{

          
tooltip
:
 
{

            
trigger
:
 
'axis'
,

            
axisPointer
:
 
{

              
type
:
 
'shadow'

            
}

          
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
data
:
 
[
'Prospecting'
,
 
'Qualified'
,
 
'Proposal'
,
 
'Negotiation'
,
 
'Closed Won'
]

          
}
,

          
yAxis
:
 
{

            
type
:
 
'value'

          
}
,

          
series
:
 
[
{

            
data
:
 
[
8
,
 
6
,
 
4
,
 
3
,
 
3
]
,

            
type
:
 
'bar'
,

            
showBackground
:
 
true
,

            
backgroundStyle
:
 
{

              
color
:
 
'rgba(180, 180, 180, 0.2)'

            
}
,

            
itemStyle
:
 
{

              
color
:
 
'#0d9488'

            
}

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart2 failed:'
,
 e
)
;

      
}

      
try
 
{

        
// Recent Wins Line Chart

        echarts
.
init
(
document
.
getElementById
(
'chart3'
)
)
.
setOption
(
{

          
tooltip
:
 
{

            
trigger
:
 
'axis'

          
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
boundaryGap
:
 
false
,

            
data
:
 
[
'Jan'
,
 
'Feb'
,
 
'Mar'
,
 
'Apr'
,
 
'May'
,
 
'Jun'
,
 
'Jul'
]

          
}
,

          
yAxis
:
 
{

            
type
:
 
'value'

          
}
,

          
series
:
 
[
{

            
data
:
 
[
12000
,
 
19000
,
 
15000
,
 
28000
,
 
22000
,
 
32000
,
 
45000
]
,

            
type
:
 
'line'
,

            
areaStyle
:
 
{

              
color
:
 
'color: '
#0d9488'
            
}
,

            
smooth
:
 
true

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart3 failed:'
,
 e
)
;

      
}

    
}
)
;

    
// Quick action handlers

    
function
 
showQuickActionMenu
(
)
 
{

      
const
 menu 
=
 
document
.
getElementById
(
'quick-action-menu'
)
;

      menu
.
classList
.
toggle
(
'hidden'
)
;

    
}

    
function
 
logCall
(
)
 
{

      
alert
(
'Call logged to CRM'
)
;

      
document
.
getElementById
(
'quick-action-menu'
)
.
classList
.
add
(
'hidden'
)
;

    
}

    
function
 
createTask
(
)
 
{

      
alert
(
'Task creation form opened'
)
;

      
document
.
getElementById
(
'quick-action-menu'
)
.
classList
.
add
(
'hidden'
)
;

    
}

    
function
 
generateReport
(
)
 
{

      
alert
(
'Report generation started'
)
;

      
document
.
getElementById
(
'quick-action-menu'
)
.
classList
.
add
(
'hidden'
)
;

    
}

  
</
script
>

</
div
>
JustPrototype
Contact
Discord
Privacy Policy
© 2025 JustPrototype. All rights reserved.