import { ReactNode } from 'react';
import styles from './FeatureCard.module.css';
import '../styles/animations.css';

interface FeatureCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  className?: string;
  animationDelay?: number;
}

export function FeatureCard({ 
  icon, 
  title, 
  description, 
  className = '',
  animationDelay = 0
}: FeatureCardProps) {
  const delayClass = animationDelay > 0 ? `delay-${animationDelay}` : '';
  
  return (
    <div className={`${styles.card} animate-float ${delayClass} ${className}`}>
      <div className={`${styles.iconContainer} animate-pulse`}>{icon}</div>
      <h3 className={styles.title}>{title}</h3>
      <p className={styles.description}>{description}</p>
    </div>
  );
}
