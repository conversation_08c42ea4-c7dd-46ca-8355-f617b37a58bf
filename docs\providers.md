# LLM Provider Configuration

## Supported Providers

The V2 LLM implementation supports the following providers:

1. **OpenAI (Default)**
   - Model: gpt-4-turbo-preview
   - Environment Variable: `OPENAI_API_KEY`
   - Features: Zero-temperature streaming
   - Requires API key

2. **Anthropic**
   - Model: claude-3-sonnet-20240229
   - Environment Variable: `ANTHROPIC_API_KEY`
   - Features: Zero-temperature streaming
   - Requires API key

## Configuration

### Environment Variables

```env
# Default provider (optional, defaults to 'openai')
LLM_PROVIDER=openai

# Provider API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
```

### Using Different Providers

1. **Via API Request**
```json
{
  "prompt": "your prompt",
  "provider": "openai|anthropic"
}
```

2. **Via Environment Variable**
```bash
LLM_PROVIDER=openai npm start
```

3. **Default Behavior**
- If no provider is specified in the request or environment
- Uses OpenAI as the default provider
- Falls back to available providers if OpenAI API key is not set

## Provider Selection Logic

1. Check request body for `provider` parameter
2. Check `LLM_PROVIDER` environment variable
3. Use 'openai' as default
4. Validate API key availability
5. Throw error if configuration is invalid

## Error Handling

```javascript
// Missing API Key
{
  "error": "API key not set for provider openai (OPENAI_API_KEY)"
}

// Invalid Provider
{
  "error": "Unsupported LLM provider: invalid-provider"
}

// Streaming Not Supported
{
  "error": "Provider does not support streaming"
}
```

## Streaming Configuration

All providers are configured with:
- `temperature`: 0 (deterministic outputs)
- `streaming`: true (SSE support)
- `verbose`: true (detailed logging)

## Provider Details

### OpenAI
- Default provider
- High-quality responses
- Reliable streaming
- Requires API key

### Anthropic
- Alternative provider
- High-quality responses
- Reliable streaming
- Requires API key

## SSE Event Format

```javascript
// Start Event
event: start
data: { "message": "I'll help you create..." }

// Token Event
event: token
data: { "text": "generated content chunk" }

// End Event
event: end
data: { 
  "message": "I've completed...",
  "content": "complete generated content"
}

// Error Event
event: error
data: { "error": "error message" }
```

## Adding New Providers

To add a new provider:

1. Update modelConfigs in LLMFactory:
```javascript
const modelConfigs = {
  newProvider: {
    modelName: 'provider-model-name',
    apiKeyEnvVar: 'PROVIDER_API_KEY',
    supportsStreaming: true
  },
  // ... existing providers
};
```

2. Add provider initialization in createLLM:
```javascript
case 'newProvider':
  return new ProviderChat({
    ...commonConfig,
    apiKey,
    modelName: config.modelName
  });
```

3. Update documentation and environment variables
