.card {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.25rem; /* Reduced from 1.5rem to 1.25rem */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.card:hover {
  transform: translateY(-3px); /* Reduced from -5px to -3px */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.iconContainer {
  font-size: 2.25rem; /* Reduced from 2.5rem to 2.25rem */
  margin-bottom: 0.75rem; /* Reduced from 1rem to 0.75rem */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem; /* Reduced from 4rem to 3.5rem */
  height: 3.5rem; /* Reduced from 4rem to 3.5rem */
  border-radius: 50%;
  background-color: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.375rem; /* Reduced from 0.5rem to 0.375rem */
  color: #111827;
}

.description {
  color: #6b7280;
  font-size: 0.95rem; /* Reduced from 1rem to 0.95rem */
  line-height: 1.5;
}

/* Dark mode styles */
:global(.dark) .card {
  background-color: #1f2937;
}

:global(.dark) .title {
  color: #f9fafb;
}

:global(.dark) .description {
  color: #d1d5db;
}

:global(.dark) .iconContainer {
  background-color: rgba(99, 102, 241, 0.2);
  color: #818cf8;
}
