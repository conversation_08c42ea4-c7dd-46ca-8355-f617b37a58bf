/**
 * Tests for useEditorV3 hook
 * Tests the core business logic and state management
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useEditorV3 } from '../hooks/useEditorV3';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock EventSource for streaming
class MockEventSource {
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onopen: ((event: Event) => void) | null = null;
  readyState = 1;
  url = '';
  
  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 0);
  }
  
  close() {
    this.readyState = 2;
  }
  
  // Helper method to simulate receiving messages
  simulateMessage(data: string) {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data }));
    }
  }
  
  // Helper method to simulate errors
  simulateError() {
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }
}

global.EventSource = MockEventSource as any;

describe('useEditorV3', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful fetch responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ sessionId: 'test-session-123' }),
    });
  });

  describe('Initial State', () => {
    test('should initialize with correct default state', () => {
      const { result } = renderHook(() => useEditorV3());

      expect(result.current.state).toEqual({
        isGenerating: false,
        htmlContent: '',
        stableIframeContent: '',
        streamingContent: '',
        currentPageId: null,
        selectedElement: null,
        pages: [],
      });
    });

    test('should provide all required actions', () => {
      const { result } = renderHook(() => useEditorV3());

      expect(result.current.actions).toHaveProperty('generateFromPrompt');
      expect(result.current.actions).toHaveProperty('setStreamingContent');
      expect(result.current.actions).toHaveProperty('setHtmlContent');
      expect(result.current.actions).toHaveProperty('setCurrentPageId');
      expect(result.current.actions).toHaveProperty('setIsGenerating');
    });
  });

  describe('generateFromPrompt', () => {
    test('should start generation and set loading state', async () => {
      const { result } = renderHook(() => useEditorV3());

      await act(async () => {
        await result.current.actions.generateFromPrompt('Create a landing page');
      });

      expect(result.current.state.isGenerating).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/llm/v3/generate-html'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: expect.stringContaining('Create a landing page'),
        })
      );
    });

    test('should handle streaming response', async () => {
      const { result } = renderHook(() => useEditorV3());
      
      // Mock streaming response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ sessionId: 'test-session-123' }),
      });

      await act(async () => {
        await result.current.actions.generateFromPrompt('Create a landing page');
      });

      // Simulate streaming content
      const eventSource = new MockEventSource('test-url');
      
      act(() => {
        eventSource.simulateMessage('data: <div>Partial content</div>');
      });

      expect(result.current.state.streamingContent).toContain('<div>Partial content</div>');
    });

    test('should handle generation completion', async () => {
      const { result } = renderHook(() => useEditorV3());

      await act(async () => {
        await result.current.actions.generateFromPrompt('Create a landing page');
      });

      // Simulate completion
      act(() => {
        result.current.actions.setIsGenerating(false);
        result.current.actions.setHtmlContent('<div>Complete content</div>');
      });

      expect(result.current.state.isGenerating).toBe(false);
      expect(result.current.state.htmlContent).toBe('<div>Complete content</div>');
    });

    test('should handle generation errors', async () => {
      const { result } = renderHook(() => useEditorV3());

      // Mock API error
      mockFetch.mockRejectedValueOnce(new Error('Generation failed'));

      await act(async () => {
        try {
          await result.current.actions.generateFromPrompt('Create a landing page');
        } catch (error) {
          // Error should be handled gracefully
        }
      });

      expect(result.current.state.isGenerating).toBe(false);
    });
  });

  describe('State Management', () => {
    test('should update streaming content', () => {
      const { result } = renderHook(() => useEditorV3());

      act(() => {
        result.current.actions.setStreamingContent('<div>Streaming content</div>');
      });

      expect(result.current.state.streamingContent).toBe('<div>Streaming content</div>');
    });

    test('should update HTML content', () => {
      const { result } = renderHook(() => useEditorV3());

      act(() => {
        result.current.actions.setHtmlContent('<div>Final content</div>');
      });

      expect(result.current.state.htmlContent).toBe('<div>Final content</div>');
    });

    test('should update current page ID', () => {
      const { result } = renderHook(() => useEditorV3());

      act(() => {
        result.current.actions.setCurrentPageId('page-123');
      });

      expect(result.current.state.currentPageId).toBe('page-123');
    });

    test('should update generation state', () => {
      const { result } = renderHook(() => useEditorV3());

      act(() => {
        result.current.actions.setIsGenerating(true);
      });

      expect(result.current.state.isGenerating).toBe(true);

      act(() => {
        result.current.actions.setIsGenerating(false);
      });

      expect(result.current.state.isGenerating).toBe(false);
    });
  });

  describe('Page Management', () => {
    test('should handle page creation with proper title', async () => {
      const { result } = renderHook(() => useEditorV3());

      // Mock page creation response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          sessionId: 'new-page-session',
          pageTitle: 'Landing Page'
        }),
      });

      await act(async () => {
        await result.current.actions.generateFromPrompt('Create a landing page', 'Landing Page');
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/llm/v3/generate-html'),
        expect.objectContaining({
          body: expect.stringContaining('Landing Page'),
        })
      );
    });

    test('should handle multiple pages', () => {
      const { result } = renderHook(() => useEditorV3());

      const mockPages = [
        { id: '1', name: 'Page 1', content: '<div>Page 1</div>' },
        { id: '2', name: 'Page 2', content: '<div>Page 2</div>' },
      ];

      act(() => {
        // Simulate pages being loaded
        result.current.state.pages = mockPages;
      });

      expect(result.current.state.pages).toHaveLength(2);
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      const { result } = renderHook(() => useEditorV3());

      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await act(async () => {
        try {
          await result.current.actions.generateFromPrompt('Create a landing page');
        } catch (error) {
          // Should handle error without crashing
        }
      });

      // State should remain stable
      expect(result.current.state.isGenerating).toBe(false);
    });

    test('should handle invalid API responses', async () => {
      const { result } = renderHook(() => useEditorV3());

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Server error' }),
      });

      await act(async () => {
        try {
          await result.current.actions.generateFromPrompt('Create a landing page');
        } catch (error) {
          // Should handle error gracefully
        }
      });

      expect(result.current.state.isGenerating).toBe(false);
    });
  });
});
