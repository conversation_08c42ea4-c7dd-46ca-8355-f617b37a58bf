import React, { memo } from 'react';

interface DirectionIconProps {
  className?: string;
  direction: 'horizontal' | 'vertical';
}

// Using memo to prevent unnecessary re-renders
export const DirectionIcon = memo<DirectionIconProps>(({ className, direction }) => {
  // Path data for each direction
  const pathData = direction === 'horizontal'
    ? "M2 8H14M4 5L1 8L4 11M12 5L15 8L12 11"
    : "M8 2V14M5 4L8 1L11 4M5 12L8 15L11 12";

  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      aria-label={`${direction} direction`}
    >
      <path
        d={pathData}
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
});

// For backward compatibility
export const HorizontalIcon = memo<{className?: string}>(({ className }) => (
  <DirectionIcon direction="horizontal" className={className} />
));

export const VerticalIcon = memo<{className?: string}>(({ className }) => (
  <DirectionIcon direction="vertical" className={className} />
));
