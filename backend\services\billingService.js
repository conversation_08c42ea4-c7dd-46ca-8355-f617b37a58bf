// Import required modules
const { pool } = require('./promptDbService');
const crypto = require('crypto');

/**
 * Get the database user ID from the provider ID
 * This ensures we're using the correct user ID for token tracking
 */
async function getDatabaseUserId(userId, provider = 'google') {
  try {
    // If userId is already a database ID (numeric), return it directly
    if (!isNaN(parseInt(userId)) && parseInt(userId) > 0 && parseInt(userId) < **********) {
      console.log(`[TokenLog] userId appears to be a database ID already: ${userId}`);
      return parseInt(userId);
    }

    console.log(`[TokenLog] Looking up database user ID for ${provider} ID: ${userId}`);

    // Determine which provider field to query
    let providerField;
    switch (provider) {
      case 'google':
        providerField = 'google_id';
        break;
      case 'facebook':
        providerField = 'facebook_id';
        break;
      case 'twitter':
        providerField = 'twitter_id';
        break;
      case 'github':
        providerField = 'github_id';
        break;
      default:
        providerField = 'google_id'; // Default to Google
    }

    const res = await pool.query(
      `SELECT id FROM users WHERE ${providerField} = $1`,
      [userId.toString()]
    );

    if (res.rows.length === 0) {
      console.error(`[TokenLog] No user found with ${provider} ID: ${userId}`);
      return null;
    }

    const dbUserId = res.rows[0].id;
    console.log(`[TokenLog] Found database user ID: ${dbUserId} for ${provider} ID: ${userId}`);
    return dbUserId;
  } catch (error) {
    console.error(`[TokenLog] Error looking up database user ID: ${error.message}`);
    return null;
  }
}

/**
 * Atomically decrement tokens and log usage for a user.
 * Returns true if successful, false if quota exceeded.
 * @param {Object} params - The parameters object
 * @param {string|number} params.userId - The user ID (can be provider ID or database ID)
 * @param {number} params.tokensUsed - Number of tokens to log
 * @param {string} params.event - Event type
 * @param {string} [params.context] - Context information
 * @param {Object} [params.details] - Additional details
 * @param {Object} [req] - Express request object (optional)
 */
async function useTokensAndLog({ userId, tokensUsed, event, context = null, details = null }, req = null) {
  // Check if userId is a string starting with 'anonymous-'
  if (typeof userId === 'string' && userId.startsWith('anonymous-')) {
    console.log(`[TokenLog] Logging anonymous token usage: ${tokensUsed} tokens, event=${event}`);
    // For anonymous users, just log the usage but don't decrement tokens
    // We could store this in a separate table if needed
    return true;
  }

  try {
    console.log(`[TokenLog] Attempting to log token usage for user ${userId}: ${tokensUsed} tokens, event=${event}`);

    // Validate inputs
    if (!userId) {
      console.error(`[TokenLog] Invalid userId: ${userId}`);
      return true; // Continue operation even if logging fails
    }

    if (!tokensUsed || isNaN(parseInt(tokensUsed))) {
      console.error(`[TokenLog] Invalid tokensUsed: ${tokensUsed}`);
      return true; // Continue operation even if logging fails
    }

    // Get the actual database user ID
    // If userId is already a database ID (from req.user.dbId), it will be used directly
    // Otherwise, we'll look it up by the provider ID
    const provider = req && req.user && req.user.provider ? req.user.provider : 'google';
    const dbUserId = await getDatabaseUserId(userId, provider);

    if (!dbUserId) {
      console.error(`[TokenLog] Could not find database user ID for user: ${userId}`);
      return true; // Continue operation even if logging fails
    }

    // Use the database user ID for token tracking
    const userIdInt = dbUserId;

    const tokensUsedInt = parseInt(tokensUsed);

    // Convert details to a JSON string if it's an object
    const detailsJson = details ? JSON.stringify(details) : null;

    // Log that we're about to call the database function
    console.log(`[TokenLog] About to call use_tokens_and_log database function`);

    // If there are any issues with the database schema, they should be fixed by running
    // the appropriate SQL scripts, not by trying to create tables/functions on the fly

    // Log the SQL query and parameters for debugging
    console.log(`[TokenLog] SQL: SELECT use_tokens_and_log($1::integer, $2::integer, $3::varchar, $4::text, $5::jsonb) AS success`);
    console.log(`[TokenLog] Params: [${userIdInt}, ${tokensUsedInt}, ${event}, ${context}, ${detailsJson}]`);

    let res;
    try {
      // Call the database function
      res = await pool.query(
        `SELECT use_tokens_and_log($1::integer, $2::integer, $3::varchar, $4::text, $5::jsonb) AS success`,
        [userIdInt, tokensUsedInt, event, context, detailsJson]
      );
      console.log(`[TokenLog] Function call successful: ${res.rows[0].success}`);

      // If the database function returns false, log it but don't create a fake success
      if (!res.rows[0].success) {
        console.error(`[TokenLog] Database function returned false. This might indicate a quota issue.`);
      }
    } catch (error) {
      console.error(`[TokenLog] Error calling use_tokens_and_log: ${error.message}`);

      // Rethrow the error to properly handle it
      throw error;
    }

    console.log(`[TokenLog] Token usage logged successfully: ${res.rows[0].success}`);
    return res.rows[0].success;
  } catch (error) {
    console.error(`[TokenLog] Database error logging token usage:`, error);
    // Log the error but don't hide it
    throw error;
  }
}

/**
 * Atomically increment prototype count for a user, enforcing free plan limit.
 * Returns true if successful, false if quota exceeded.
 * @param {string|number} userId - The user ID (can be provider ID or database ID)
 * @param {Object} [req] - Express request object (optional)
 */
async function incrementPrototypeCount(userId, req = null) {
  // Check if userId is a string starting with 'anonymous-'
  if (typeof userId === 'string' && userId.startsWith('anonymous-')) {
    console.log(`[TokenLog] Anonymous user prototype count not tracked`);
    // For anonymous users, always allow
    return true;
  }

  try {
    console.log(`[TokenLog] Attempting to increment prototype count for user ${userId}`);

    // Validate inputs
    if (!userId) {
      console.error(`[TokenLog] Invalid userId: ${userId}`);
      return false; // Don't allow operation with invalid user ID
    }

    // Get the actual database user ID
    // If userId is already a database ID (from req.user.dbId), it will be used directly
    // Otherwise, we'll look it up by the provider ID
    const provider = req && req.user && req.user.provider ? req.user.provider : 'google';
    const dbUserId = await getDatabaseUserId(userId, provider);

    if (!dbUserId) {
      console.error(`[TokenLog] Could not find database user ID for user: ${userId}`);
      return false; // Don't allow operation if we can't find the user
    }

    // Use the database user ID for prototype count tracking
    const userIdInt = dbUserId;

    // Log the SQL query and parameters for debugging
    console.log(`[TokenLog] SQL: SELECT increment_prototype_count($1::integer) AS success`);
    console.log(`[TokenLog] Params: [${userIdInt}]`);

    let res;
    try {
      // Call the database function
      res = await pool.query(
        `SELECT increment_prototype_count($1::integer) AS success`,
        [userIdInt]
      );
      console.log(`[TokenLog] Function call successful: ${res.rows[0].success}`);

      // If the database function returns false, log it
      if (!res.rows[0].success) {
        console.error(`[TokenLog] Quota exceeded: User ${userIdInt} has reached their prototype limit.`);
      }
    } catch (error) {
      console.error(`[TokenLog] Error calling increment_prototype_count: ${error.message}`);

      // Rethrow the error to properly handle it
      throw error;
    }

    console.log(`[TokenLog] Prototype count incremented successfully: ${res.rows[0].success}`);
    return res.rows[0].success;
  } catch (error) {
    console.error(`[TokenLog] Database error incrementing prototype count:`, error);
    // Log the error but don't hide it
    throw error;
  }
}

module.exports = {
  useTokensAndLog,
  incrementPrototypeCount,
};
