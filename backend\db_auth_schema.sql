-- Update users table to support multiple authentication providers and email-based signup
ALTER TABLE users 
DROP CONSTRAINT IF EXISTS users_google_id_key;

-- Make google_id nullable to support other auth methods
ALTER TABLE users 
ALTER COLUMN google_id DROP NOT NULL;

-- Add columns for other auth providers and password-based auth
ALTER TABLE users
ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255),
ADD COLUMN IF NOT EXISTS facebook_id VARCHAR(64),
ADD COLUMN IF NOT EXISTS twitter_id VARCHAR(64),
ADD COLUMN IF NOT EXISTS github_id VARCHAR(64),
ADD COLUMN IF NOT EXISTS auth_provider VARCHAR(32),
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_token VARCHAR(128),
ADD COLUMN IF NOT EXISTS reset_token VARCHAR(128),
ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP WITH TIME ZONE;

-- Create unique indexes for social provider IDs
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id) WHERE google_id IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_facebook_id ON users(facebook_id) WHERE facebook_id IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_twitter_id ON users(twitter_id) WHERE twitter_id IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_github_id ON users(github_id) WHERE github_id IS NOT NULL;

-- Set default prototype_count to 3 for new users
ALTER TABLE users
ALTER COLUMN prototype_count SET DEFAULT 3;
