import React from 'react';
import styles from './QuotaModal.module.css';
import { QuotaInfo } from '../QuotaModule';
import { QuotaBar } from './QuotaBar';

interface QuotaModalProps {
  /**
   * Whether the modal is open
   */
  open: boolean;
  
  /**
   * Callback when the modal is closed
   */
  onClose: () => void;
  
  /**
   * Callback when upgrade is clicked
   */
  onUpgrade: () => void;
  
  /**
   * Quota information
   */
  quota: QuotaInfo;
  
  /**
   * Item name (e.g., "prototype", "credit")
   */
  itemName?: string;
  
  /**
   * Whether the upgrade button is loading
   */
  loading?: boolean;
  
  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * A modal component that displays quota information and upgrade options
 */
export const QuotaModal: React.FC<QuotaModalProps> = ({
  open,
  onClose,
  onUpgrade,
  quota,
  itemName = 'prototype',
  loading = false,
  className = ''
}) => {
  if (!open) return null;
  
  const { plan, usedCount, totalCount, remainingCount } = quota;
  const isPro = plan.toLowerCase() !== 'free';
  const isExceeded = remainingCount <= 0;
  
  return (
    <div className={styles.modalOverlay}>
      <div className={`${styles.modalContent} ${className}`}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>
            {isExceeded ? `${isPro ? 'Pro' : 'Free'} Limit Reached` : 'Quota Status'}
          </h2>
          <button className={styles.closeButton} onClick={onClose}>×</button>
        </div>
        
        <div className={styles.modalBody}>
          <p className={styles.modalText}>
            {isExceeded
              ? `You've reached your ${isPro ? 'pro' : 'free'} ${itemName} limit. ${!isPro ? 'Upgrade to create more and unlock additional features.' : 'Contact support to increase your limit.'}`
              : `You have ${remainingCount} ${remainingCount === 1 ? itemName : `${itemName}s`} remaining on your ${isPro ? 'pro' : 'free'} plan.`
            }
          </p>
          
          <div className={styles.quotaBarContainer}>
            <QuotaBar quota={quota} itemName={itemName} showLabels={true} />
          </div>
          
          {!isPro && (
            <div className={styles.benefitsBox}>
              <h3 className={styles.benefitsTitle}>
                Pro Plan Benefits
              </h3>
              <ul className={styles.benefitsList}>
                <li className={styles.benefitItem}>
                  Up to 50 {itemName}s (vs. 3 on free plan)
                </li>
                <li className={styles.benefitItem}>
                  Advanced design options
                </li>
                <li className={styles.benefitItem}>
                  Export to code
                </li>
                <li className={styles.benefitItem}>
                  Priority support
                </li>
              </ul>
            </div>
          )}
        </div>
        
        <div className={styles.modalFooter}>
          <button
            className={styles.secondaryButton}
            onClick={onClose}
          >
            {remainingCount > 0 ? 'Close' : 'Maybe Later'}
          </button>
          
          {!isPro && (
            <button
              className={`${styles.primaryButton} ${loading ? styles.loadingButton : ''}`}
              onClick={onUpgrade}
              disabled={loading}
            >
              {loading ? 'Redirecting...' : 'Upgrade to Pro'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
