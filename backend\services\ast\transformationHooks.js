const codeAstService = require('./codeAstService');

/**
 * Built-in transformation hooks for code modification
 */
class TransformationHooks {
  constructor() {
    this.registerBuiltInHooks();
  }

  /**
   * Register all built-in transformation hooks
   */
  registerBuiltInHooks() {
    // Code formatting hook
    codeAstService.registerTransformationHook('format', this.formatHook);
    
    // Variable renaming hook
    codeAstService.registerTransformationHook('renameVariable', this.renameVariableHook);
    
    // Function extraction hook
    codeAstService.registerTransformationHook('extractFunction', this.extractFunctionHook);
    
    // Import optimization hook
    codeAstService.registerTransformationHook('optimizeImports', this.optimizeImportsHook);
    
    // Dead code elimination hook
    codeAstService.registerTransformationHook('removeDeadCode', this.removeDeadCodeHook);
    
    // CSS optimization hook
    codeAstService.registerTransformationHook('optimizeCSS', this.optimizeCSSHook);
    
    // HTML attribute sanitization hook
    codeAstService.registerTransformationHook('sanitizeHTML', this.sanitizeHTMLHook);
    
    // Accessibility improvements hook
    codeAstService.registerTransformationHook('improveAccessibility', this.improveAccessibilityHook);
  }

  /**
   * Format code hook
   */
  formatHook = async (ast, config = {}, language = 'javascript') => {
    // This hook doesn't modify the AST but triggers formatting during print
    return ast;
  };

  /**
   * Rename variable hook
   */
  renameVariableHook = async (ast, config = {}, language = 'javascript') => {
    const { oldName, newName } = config;
    
    if (!oldName || !newName) {
      throw new Error('Both oldName and newName are required for variable renaming');
    }

    if (language === 'javascript' || language === 'typescript') {
      codeAstService.walkAST(ast, (node) => {
        // Rename variable declarations
        if (node.type === 'VariableDeclarator' && node.id?.name === oldName) {
          node.id.name = newName;
        }
        
        // Rename variable references
        if (node.type === 'Identifier' && node.name === oldName) {
          node.name = newName;
        }
        
        // Rename function parameters
        if (node.type === 'FunctionDeclaration' || node.type === 'FunctionExpression' || node.type === 'ArrowFunctionExpression') {
          node.params?.forEach(param => {
            if (param.type === 'Identifier' && param.name === oldName) {
              param.name = newName;
            }
          });
        }
      });
    }

    return ast;
  };

  /**
   * Extract function hook
   */
  extractFunctionHook = async (ast, config = {}, language = 'javascript') => {
    const { functionName, startLine, endLine } = config;
    
    if (!functionName) {
      throw new Error('Function name is required for function extraction');
    }

    // This is a complex transformation that would extract code between startLine and endLine
    // into a new function. For now, we'll return the AST unchanged as a placeholder.
    console.log(`Function extraction hook called for ${functionName} (lines ${startLine}-${endLine})`);
    return ast;
  };

  /**
   * Optimize imports hook
   */
  optimizeImportsHook = async (ast, config = {}, language = 'javascript') => {
    if (language !== 'javascript' && language !== 'typescript') {
      return ast;
    }

    const usedImports = new Set();
    const importNodes = [];

    // First pass: collect all import statements
    codeAstService.walkAST(ast, (node) => {
      if (node.type === 'ImportDeclaration') {
        importNodes.push(node);
      }
    });

    // Second pass: find which imports are actually used
    codeAstService.walkAST(ast, (node) => {
      if (node.type === 'Identifier') {
        usedImports.add(node.name);
      }
    });

    // Remove unused imports
    importNodes.forEach(importNode => {
      const usedSpecifiers = importNode.specifiers.filter(spec => {
        const localName = spec.local.name;
        return usedImports.has(localName);
      });

      if (usedSpecifiers.length === 0) {
        // Remove the entire import if no specifiers are used
        const parent = importNode.parent;
        if (parent && parent.body) {
          const index = parent.body.indexOf(importNode);
          if (index > -1) {
            parent.body.splice(index, 1);
          }
        }
      } else {
        // Keep only used specifiers
        importNode.specifiers = usedSpecifiers;
      }
    });

    return ast;
  };

  /**
   * Remove dead code hook
   */
  removeDeadCodeHook = async (ast, config = {}, language = 'javascript') => {
    if (language !== 'javascript' && language !== 'typescript') {
      return ast;
    }

    const referencedFunctions = new Set();
    const functionDeclarations = new Map();

    // First pass: collect all function declarations
    codeAstService.walkAST(ast, (node) => {
      if (node.type === 'FunctionDeclaration' && node.id?.name) {
        functionDeclarations.set(node.id.name, node);
      }
    });

    // Second pass: find referenced functions
    codeAstService.walkAST(ast, (node) => {
      if (node.type === 'CallExpression' && node.callee?.name) {
        referencedFunctions.add(node.callee.name);
      }
      if (node.type === 'Identifier' && functionDeclarations.has(node.name)) {
        referencedFunctions.add(node.name);
      }
    });

    // Remove unreferenced functions
    functionDeclarations.forEach((node, name) => {
      if (!referencedFunctions.has(name) && !config.keepFunctions?.includes(name)) {
        const parent = node.parent;
        if (parent && parent.body) {
          const index = parent.body.indexOf(node);
          if (index > -1) {
            parent.body.splice(index, 1);
          }
        }
      }
    });

    return ast;
  };

  /**
   * Optimize CSS hook
   */
  optimizeCSSHook = async (ast, config = {}, language = 'css') => {
    if (language !== 'css') {
      return ast;
    }

    // Remove duplicate rules, optimize selectors, etc.
    // This is a placeholder for CSS optimization logic
    console.log('CSS optimization hook called');
    return ast;
  };

  /**
   * Sanitize HTML hook
   */
  sanitizeHTMLHook = async (ast, config = {}, language = 'html') => {
    if (language !== 'html' || ast.type !== 'html') {
      return ast;
    }

    const dangerousAttributes = config.dangerousAttributes || [
      'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout'
    ];

    const allowedTags = config.allowedTags || [
      'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'a', 'img', 'strong', 'em', 'br', 'hr',
      'table', 'tr', 'td', 'th', 'thead', 'tbody', 'section',
      'article', 'header', 'footer', 'nav', 'main', 'aside',
      'button', 'input', 'form', 'label', 'select', 'option',
      'textarea', 'script', 'style'
    ];

    // Walk through HTML nodes and sanitize
    const sanitizeNode = (node) => {
      if (!node || typeof node !== 'object') return;

      // Remove dangerous attributes
      if (node.attrs) {
        dangerousAttributes.forEach(attr => {
          delete node.attrs[attr];
        });
      }

      // Check if tag is allowed
      if (node.tagName && !allowedTags.includes(node.tagName.toLowerCase())) {
        console.warn(`Removing disallowed tag: ${node.tagName}`);
        // In a real implementation, we would remove this node
      }

      // Recursively sanitize children
      if (node.childNodes) {
        node.childNodes.forEach(sanitizeNode);
      }
    };

    if (ast.root) {
      sanitizeNode(ast.root);
    }

    return ast;
  };

  /**
   * Improve accessibility hook
   */
  improveAccessibilityHook = async (ast, config = {}, language = 'html') => {
    if (language !== 'html' || ast.type !== 'html') {
      return ast;
    }

    // Add accessibility improvements
    const improveNode = (node) => {
      if (!node || typeof node !== 'object') return;

      // Add alt attributes to images
      if (node.tagName === 'img' && (!node.attrs || !node.attrs.alt)) {
        if (!node.attrs) node.attrs = {};
        node.attrs.alt = config.defaultAltText || 'Image';
      }

      // Add labels to form inputs
      if (node.tagName === 'input' && (!node.attrs || !node.attrs['aria-label'])) {
        if (!node.attrs) node.attrs = {};
        const type = node.attrs.type || 'text';
        node.attrs['aria-label'] = config.defaultInputLabel || `${type} input`;
      }

      // Add role attributes where appropriate
      if (node.tagName === 'button' && (!node.attrs || !node.attrs.role)) {
        if (!node.attrs) node.attrs = {};
        node.attrs.role = 'button';
      }

      // Recursively improve children
      if (node.childNodes) {
        node.childNodes.forEach(improveNode);
      }
    };

    if (ast.root) {
      improveNode(ast.root);
    }

    return ast;
  };

  /**
   * Register a custom transformation hook
   */
  registerCustomHook(name, hook) {
    if (typeof hook !== 'function') {
      throw new Error('Hook must be a function');
    }
    codeAstService.registerTransformationHook(name, hook);
  }

  /**
   * Get all available transformation hooks
   */
  getAvailableHooks() {
    return codeAstService.getTransformationHooks();
  }

  /**
   * Create a transformation pipeline
   */
  createPipeline(transformations) {
    return async (ast, language) => {
      let result = ast;
      for (const transformation of transformations) {
        result = await codeAstService.transformAST(result, [transformation], language);
      }
      return result;
    };
  }

  /**
   * Apply common transformations for web development
   */
  applyWebOptimizations = async (ast, language) => {
    const transformations = [
      { type: 'optimizeImports', config: {} },
      { type: 'removeDeadCode', config: { keepFunctions: ['main', 'init'] } },
      { type: 'format', config: {} }
    ];

    if (language === 'html') {
      transformations.unshift(
        { type: 'sanitizeHTML', config: {} },
        { type: 'improveAccessibility', config: {} }
      );
    }

    if (language === 'css') {
      transformations.push({ type: 'optimizeCSS', config: {} });
    }

    return await codeAstService.transformAST(ast, transformations, language);
  };
}

// Export singleton instance
module.exports = new TransformationHooks();
