import React, { useState } from "react";
import styles from "./PricingPage.module.css";
import { useNavigate } from "react-router-dom";
import RequireAuth from "../components/RequireAuth";

const features = [
  "Up to 100 prototypes per month",
  "Visual editing included",
  "Clean HTML/CSS output",
  "No token tracking",
  "Priority build speed",
];

export const CheckoutPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleCheckout = async () => {
    setLoading(true);
    setError(null);
    try {
      // Optionally: window.analytics?.track("stripe_checkout_started");
      const res = await fetch("/api/stripe/create-checkout-session", {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
      });
      if (!res.ok) throw new Error("Failed to create Stripe session");
      const data = await res.json();
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error("No Stripe URL returned");
      }
    } catch (e: any) {
      setError(e.message || "Something went wrong. Please try again.");
      setLoading(false);
    }
  };

  return (
    <RequireAuth>
      <div className={styles.pricingRoot}>
        <header className={styles.header}>
          <h1 className={styles.title}>Upgrade to Pro</h1>
          <div className={styles.subtitle}>
            Access up to 100 prototypes/month for just $5.
          </div>
        </header>
        <section className={styles.cardSection}>
          <div className={styles.pricingCard}>
            <div className={styles.planName}>Pro Plan</div>
            <div className={styles.price}>
              <span className={styles.priceValue}>$5</span>
              <span className={styles.pricePeriod}>/ month</span>
            </div>
            <ul className={styles.featuresList}>
              {features.map((f) => (
                <li key={f}>✔ {f}</li>
              ))}
            </ul>
            <button
              className={styles.ctaButton}
              onClick={handleCheckout}
              disabled={loading}
              style={loading ? { opacity: 0.7, pointerEvents: "none" } : {}}
            >
              {loading ? "Redirecting..." : "Proceed to Payment"}
            </button>
            {error && (
              <div style={{ color: "#dc2626", marginTop: "1em", fontWeight: 500 }}>
                {error}
              </div>
            )}
          </div>
        </section>
      </div>
    </RequireAuth>
  );
};

export default CheckoutPage;
