const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';

export interface Prototype {
  id: number;
  title: string;
  description: string;
  html: string;
  css?: string;
  preview_image_url?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get all prototypes for the current user
 * @returns Promise<Prototype[]> - Array of prototypes
 */
export async function getPrototypes(): Promise<Prototype[]> {
  try {
    console.log('Fetching prototypes from API');

    // Check if user is logged in via cookie
    const isLoggedIn = document.cookie.includes('isLoggedIn=true');
    if (!isLoggedIn) {
      console.log('User not logged in according to cookie, cannot fetch prototypes');
      throw new Error('Not authenticated');
    }

    // Log all cookies for debugging
    console.log('Cookies being sent:', document.cookie);
    console.log('API Base URL:', API_BASE);

    // Use a simple fetch with minimal headers
    const response = await fetch(`${API_BASE}/prototype`, {
      method: 'GET',
      credentials: 'include', // Include cookies for authentication
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log('Prototype API response status:', response.status);

    if (!response.ok) {
      // Create a custom error object with status code
      const errorText = await response.text();
      console.error('Error response text:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (e) {
        errorData = { error: 'Failed to parse error response' };
      }

      const error: any = new Error(errorData.error || 'Failed to get prototypes');
      error.status = response.status;
      throw error;
    }

    // Try to parse the response as JSON
    let data;
    try {
      const responseText = await response.text();
      //console.log('Response text:', responseText);
      data = JSON.parse(responseText);
    } catch (e) {
      console.error('Error parsing JSON response:', e);
      throw new Error('Invalid response format');
    }

    // Validate the response structure
    if (!data || !Array.isArray(data.prototypes)) {
      console.error('Invalid response structure:', data);
      return [];
    }

    console.log(`Fetched ${data.prototypes.length} prototypes:`, data.prototypes);
    return data.prototypes;
  } catch (error: any) {
    console.error('Error getting prototypes:', error);

    // Check if this is a CORS error
    if (error.message && (error.message.includes('CORS') || error.message.includes('cross-origin'))) {
      console.error('CORS error detected:', error.message);
    }

    // Add authentication check
    if (error.status === 401) {
      console.log('Authentication error in getPrototypes');
      // Clear any stale auth state
      localStorage.removeItem('authState');
    }
    throw error;
  }
}

/**
 * Get a single prototype by ID
 * @param id - The prototype ID
 * @returns Promise<Prototype> - The prototype
 */
export async function getPrototypeById(id: number): Promise<Prototype> {
  try {
    const response = await fetch(`${API_BASE}/prototype/${id}`, {
      method: 'GET',
      credentials: 'include', // Include cookies for authentication
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get prototype');
    }

    const data = await response.json();
    return data.prototype;
  } catch (error) {
    console.error(`Error getting prototype ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a prototype
 * @param id - The prototype ID
 * @returns Promise<boolean> - True if deleted successfully
 */
export async function deletePrototype(id: number): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE}/prototype/${id}`, {
      method: 'DELETE',
      credentials: 'include', // Include cookies for authentication
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete prototype');
    }

    const data = await response.json();
    return data.success || false;
  } catch (error) {
    console.error(`Error deleting prototype ${id}:`, error);
    throw error;
  }
}

/**
 * Update a prototype
 * @param id - The prototype ID
 * @param updates - The fields to update
 * @returns Promise<Prototype> - The updated prototype
 */
export async function updatePrototype(
  id: number,
  updates: Partial<Omit<Prototype, 'id' | 'created_at' | 'updated_at'>>
): Promise<Prototype> {
  try {
    // Using POST instead of PUT to avoid CORS issues
    const response = await fetch(`${API_BASE}/prototype/update/${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
      credentials: 'include', // Include cookies for authentication
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update prototype');
    }

    const data = await response.json();
    return data.prototype;
  } catch (error) {
    console.error(`Error updating prototype ${id}:`, error);
    throw error;
  }
}

/**
 * Generate a thumbnail for a prototype
 * @param title - The prototype title
 * @param description - The prototype description
 * @returns string - A data URL for the thumbnail
 */
export function generateThumbnail(title: string, description: string): string {
  // Safely escape text for SVG
  const escapeXml = (unsafe: string): string => {
    return unsafe.replace(/[<>&'"]/g, (c) => {
      switch (c) {
        case '<': return '&lt;';
        case '>': return '&gt;';
        case '&': return '&amp;';
        case "'": return '&apos;';
        case '"': return '&quot;';
        default: return c;
      }
    });
  };

  // Prepare safe text content
  const safeTitle = escapeXml(title || 'Untitled Prototype');
  const safeDescription = escapeXml(description || 'No description');

  // Create a simple SVG thumbnail with the title and description
  const svgContent = `
    <svg xmlns="http://www.w3.org/2000/svg" width="300" height="200" viewBox="0 0 300 200">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <rect x="10" y="10" width="280" height="180" rx="5" fill="#ffffff" stroke="#e5e7eb" stroke-width="1"/>
      <text x="20" y="40" font-family="Arial" font-size="16" font-weight="bold" fill="#111827">${safeTitle.substring(0, 20)}${safeTitle.length > 20 ? '...' : ''}</text>
      <text x="20" y="70" font-family="Arial" font-size="12" fill="#6b7280">${safeDescription.substring(0, 50)}${safeDescription.length > 50 ? '...' : ''}</text>
    </svg>
  `;

  // Convert SVG to a data URL
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent.trim())}`;
}
