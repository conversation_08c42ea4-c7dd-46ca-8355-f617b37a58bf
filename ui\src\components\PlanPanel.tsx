import styles from './PlanPanel.module.css';

interface PlanPanelProps {
  features: string[];
  countdown?: number;
  onStart?: () => void;
}

export function PlanPanel({ features, countdown, onStart }: PlanPanelProps) {
  return (
    <div className={styles.planPanel}>
      <div className={styles.title}>Here’s what we’re building for you...</div>
      <ul className={styles.featureList}>
        {features.map((f, i) => (
          <li key={i} className={styles.featureItem}>• {f}</li>
        ))}
      </ul>
      {typeof countdown === 'number' && countdown > 0 ? (
        <div className={styles.countdown}>
          <span className={styles.spinner} />
          <span>Starting in {countdown}s...</span>
        </div>
      ) : onStart ? (
        <button className={styles.startBtn} onClick={onStart}>Start</button>
      ) : null}
    </div>
  );
}
