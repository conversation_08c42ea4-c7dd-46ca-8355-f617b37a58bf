.container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 56px);
  width: 100%;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  height: 64px;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s;
}

.backLink:hover {
  color: #111827;
}

.shareButton {
  margin-right: 0.5rem;
}

.editButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #4f46e5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  transition: background-color 0.2s;
}

.editButton:hover {
  background-color: #4338ca;
}

.previewContainer {
  flex: 1;
  overflow: hidden;
  background-color: #f9fafb;
  position: relative;
}

.previewFrame {
  width: 100%;
  height: 100%;
  border: none;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 56px);
  gap: 1rem;
  color: #6b7280;
}

.loadingIcon {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 56px);
  gap: 1.5rem;
  padding: 2rem;
}

.error {
  padding: 1.5rem;
  background-color: #fee2e2;
  color: #b91c1c;
  border-radius: 0.5rem;
  max-width: 500px;
  text-align: center;
}
