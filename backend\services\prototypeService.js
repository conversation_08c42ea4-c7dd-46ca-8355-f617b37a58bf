const { pool } = require('./promptDbService');

/**
 * Create a new prototype in the database.
 * @param {Object} params
 * @param {number} params.user_id - DB user ID
 * @param {string} params.title
 * @param {string} [params.description]
 * @param {string} params.html
 * @param {string} [params.css]
 * @param {string} [params.preview_image_url]
 * @param {number} [params.prompt_id]
 * @returns {Promise<Object>} The created prototype row
 */
async function createPrototype({ user_id, title, description, html, css, preview_image_url, prompt_id = null }) {
  const res = await pool.query(
    `INSERT INTO prototypes (user_id, title, description, html, css, preview_image_url, prompt_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *`,
    [user_id, title, description, html, css, preview_image_url, prompt_id]
  );
  return res.rows[0];
}

/**
 * Retrieve all prototypes for a given user.
 * @param {number} user_id
 * @returns {Promise<Array<Object>>}
 */
async function getPrototypesByUser(user_id) {
  const res = await pool.query(
    `SELECT * FROM prototypes WHERE user_id = $1 ORDER BY created_at DESC`,
    [user_id]
  );
  return res.rows;
}

/**
 * Get paginated prototypes for a user.
 * @param {number} user_id
 * @param {number} limit
 * @param {number} offset
 * @returns {Promise<Array>}
 */
async function getPrototypesByUserPaginated(user_id, limit = 50, offset = 0) {
  const res = await pool.query(
    `SELECT * FROM prototypes WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3`,
    [user_id, limit, offset]
  );
  return res.rows;
}

/**
 * Get total count of prototypes for a user.
 * @param {number} user_id
 * @returns {Promise<number>}
 */
async function getPrototypesCountByUser(user_id) {
  const res = await pool.query(
    `SELECT COUNT(*) as count FROM prototypes WHERE user_id = $1`,
    [user_id]
  );
  return parseInt(res.rows[0].count);
}

/**
 * Retrieve a single prototype by its ID and owner.
 * @param {number} prototype_id
 * @param {number} user_id
 * @returns {Promise<Object|null>}
 */
async function getPrototypeById(prototype_id, user_id) {
  const res = await pool.query(
    `SELECT * FROM prototypes WHERE id = $1 AND user_id = $2 LIMIT 1`,
    [prototype_id, user_id]
  );
  return res.rows[0] || null;
}

/**
 * Update a prototype (only if it belongs to the user).
 * @param {number} prototype_id
 * @param {number} user_id
 * @param {Object} fields - Fields to update
 * @returns {Promise<Object|null>}
 */
async function updatePrototype(prototype_id, user_id, fields) {
  const allowed = ['title', 'description', 'html', 'css', 'preview_image_url'];
  const keys = Object.keys(fields).filter(key => allowed.includes(key));
  if (keys.length === 0) return null;

  const setClause = keys.map((k, i) => `${k} = $${i + 3}`).join(', ');
  const values = keys.map(k => fields[k]);

  const res = await pool.query(
    `UPDATE prototypes SET ${setClause}, updated_at = NOW()
      WHERE id = $1 AND user_id = $2
      RETURNING *`,
    [prototype_id, user_id, ...values]
  );
  return res.rows[0] || null;
}

/**
 * Delete a prototype (only if it belongs to the user).
 * @param {number} prototype_id
 * @param {number} user_id
 * @returns {Promise<boolean>} true if deleted, false otherwise
 */
async function deletePrototype(prototype_id, user_id) {
  const res = await pool.query(
    `DELETE FROM prototypes WHERE id = $1 AND user_id = $2`,
    [prototype_id, user_id]
  );
  return res.rowCount > 0;
}

module.exports = {
  createPrototype,
  getPrototypesByUser,
  getPrototypesByUserPaginated,
  getPrototypesCountByUser,
  getPrototypeById,
  updatePrototype,
  deletePrototype
};