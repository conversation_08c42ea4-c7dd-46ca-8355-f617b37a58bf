import React, { useState, useRef, useEffect, memo, useCallback } from 'react';
import styles from './ColorPicker.module.css';

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  onClose: () => void;
}

// Predefined color palettes - moved outside component to prevent recreation
const COLOR_PALETTES = {
  Slate: [
    '#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1', '#94a3b8',
    '#64748b', '#475569', '#334155', '#1e293b', '#0f172a'
  ],
  Gray: [
    '#f9fafb', '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af',
    '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827'
  ],
  Zinc: [
    '#fafafa', '#f4f4f5', '#e4e4e7', '#d4d4d8', '#a1a1aa',
    '#71717a', '#52525b', '#3f3f46', '#27272a', '#18181b'
  ],
  Red: [
    '#fef2f2', '#fee2e2', '#fecaca', '#fca5a5', '#f87171',
    '#ef4444', '#dc2626', '#b91c1c', '#991b1b', '#7f1d1d'
  ],
  Blue: [
    '#eff6ff', '#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa',
    '#3b82f6', '#2563eb', '#1d4ed8', '#1e40af', '#1e3a8a'
  ],
  Green: [
    '#f0fdf4', '#dcfce7', '#bbf7d0', '#86efac', '#4ade80',
    '#22c55e', '#16a34a', '#15803d', '#166534', '#14532d'
  ]
};

// Memoized color swatch component to prevent unnecessary re-renders
const ColorSwatch = memo<{
  color: string;
  onClick: (color: string) => void;
}>(({ color, onClick }) => (
  <button
    className={styles.colorSwatch}
    style={{ backgroundColor: color }}
    onClick={() => onClick(color)}
    title={color}
  />
));

// Memoized palette component
const ColorPalette = memo<{
  name: string;
  colors: string[];
  onColorSelect: (color: string) => void;
}>(({ name, colors, onColorSelect }) => (
  <div className={styles.palette}>
    <div className={styles.paletteName}>{name}</div>
    <div className={styles.colorGrid}>
      {colors.map((color, index) => (
        <ColorSwatch key={`${name}-${index}`} color={color} onClick={onColorSelect} />
      ))}
    </div>
  </div>
));

export const ColorPicker = memo<ColorPickerProps>(({ value, onChange, onClose }) => {
  const [activeTab, setActiveTab] = useState<'styles' | 'custom'>('styles');
  const [customColor, setCustomColor] = useState(value);
  const pickerRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close the picker - memoized to prevent recreation
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Memoized callbacks to prevent recreation on each render
  const handleColorSelect = useCallback((color: string) => {
    onChange(color);
  }, [onChange]);

  const handleCustomColorChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setCustomColor(newColor);

    // Apply the color immediately if it's a valid color
    if (/^#([0-9A-F]{3}){1,2}$/i.test(newColor) ||
        /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/i.test(newColor) ||
        /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/i.test(newColor)) {
      onChange(newColor);
    }
  }, [onChange]);

  const handleTabChange = useCallback((tab: 'styles' | 'custom') => {
    setActiveTab(tab);
  }, []);

  return (
    <div className={styles.colorPickerContainer} ref={pickerRef}>
      <div className={styles.header}>
        <div className={styles.tabs}>
          <button
            className={`${styles.tab} ${activeTab === 'styles' ? styles.activeTab : ''}`}
            onClick={() => handleTabChange('styles')}
          >
            Styles
          </button>
          <button
            className={`${styles.tab} ${activeTab === 'custom' ? styles.activeTab : ''}`}
            onClick={() => handleTabChange('custom')}
          >
            Custom
          </button>
        </div>
        <button className={styles.closeButton} onClick={onClose}>×</button>
      </div>

      <div className={styles.content}>
        {activeTab === 'styles' ? (
          <div className={styles.paletteContainer}>
            {Object.entries(COLOR_PALETTES).map(([paletteName, colors]) => (
              <ColorPalette
                key={paletteName}
                name={paletteName}
                colors={colors}
                onColorSelect={handleColorSelect}
              />
            ))}
          </div>
        ) : (
          <div className={styles.customColorContainer}>
            <div className={styles.customColorPreview} style={{ backgroundColor: customColor }} />
            <div className={styles.customColorInputs}>
              <input
                type="color"
                value={customColor}
                onChange={handleCustomColorChange}
                className={styles.colorInput}
              />
              <input
                type="text"
                value={customColor}
                onChange={handleCustomColorChange}
                onBlur={handleCustomColorChange}
                className={styles.colorText}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
});
