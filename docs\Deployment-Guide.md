# Deployment Guide - LiteLLM Integration

## Overview

This guide covers deploying the LiteLLM integration for JustPrototype in various environments, from development to production.

## Prerequisites

### System Requirements
- **Docker:** Version 20.10+
- **Docker Compose:** Version 2.0+
- **Node.js:** Version 18+
- **Memory:** Minimum 2GB RAM for LiteLLM proxy
- **Storage:** 1GB for Docker images and logs

### API Keys Required
- **DeepSeek API Key** (Primary) - Get from https://platform.deepseek.com/
- **OpenRouter API Key** (For Qwen) - Get from https://openrouter.ai/
- **OpenAI API Key** (Optional fallback) - Get from https://platform.openai.com/
- **Anthropic API Key** (Optional fallback) - Get from https://console.anthropic.com/

## Development Environment

### 1. Local Setup

#### Step 1: Clone and Configure
```bash
# Navigate to project directory
cd /path/to/justprototype

# Create environment file
cp .env.example .env
```

#### Step 2: Environment Configuration
Add to `.env`:
```bash
# LiteLLM Configuration
LITELLM_BASE_URL=http://localhost:4000
LITELLM_API_KEY=sk-1234

# Primary Provider (DeepSeek)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Secondary Provider (OpenRouter for Qwen)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional Fallbacks
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

#### Step 3: Start LiteLLM Proxy
```bash
# Start LiteLLM proxy
docker-compose -f docker-compose.litellm.yml up -d

# Verify it's running
curl http://localhost:4000/health
```

#### Step 4: Start Application
```bash
# Start backend
cd backend
npm install
npm start

# Start frontend (in another terminal)
cd ui
npm install
npm start
```

### 2. Development Verification

#### Test LiteLLM Proxy
```bash
# Test DeepSeek model
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-1234" \
  -d '{
    "model": "deepseek/deepseek-chat",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

#### Test Application Integration
```bash
# Test intent generation (requires authentication)
curl -X POST http://localhost:3000/api/llm/v3/generate-intent \
  -H "Content-Type: application/json" \
  -H "Cookie: your_session_cookie" \
  -d '{
    "elementCode": "<button>Test Button</button>",
    "htmlContent": "<html><body><button>Test Button</button></body></html>"
  }'
```

## Production Deployment

### 1. Docker Production Setup

#### Production Docker Compose
Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  litellm:
    image: ghcr.io/berriai/litellm:main-latest
    ports:
      - "4000:4000"
    volumes:
      - ./backend/litellm_config.yaml:/app/config.yaml
      - litellm_data:/app/data
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - LITELLM_MASTER_KEY=${LITELLM_MASTER_KEY}
      - LITELLM_LOG=INFO
    command: ["--config", "/app/config.yaml", "--port", "4000", "--num_workers", "4"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - LITELLM_BASE_URL=http://litellm:4000
      - LITELLM_API_KEY=${LITELLM_MASTER_KEY}
    depends_on:
      litellm:
        condition: service_healthy
    restart: unless-stopped

volumes:
  litellm_data:
```

#### Production Environment Variables
```bash
# Production .env
NODE_ENV=production

# LiteLLM Configuration
LITELLM_BASE_URL=http://litellm:4000
LITELLM_MASTER_KEY=your_secure_master_key_here

# API Keys
DEEPSEEK_API_KEY=your_production_deepseek_key
OPENROUTER_API_KEY=your_production_openrouter_key
OPENAI_API_KEY=your_production_openai_key
ANTHROPIC_API_KEY=your_production_anthropic_key

# Database and other configs
DATABASE_URL=your_production_db_url
SESSION_SECRET=your_secure_session_secret
```

### 2. Cloud Deployment

#### AWS ECS Deployment

**Task Definition:**
```json
{
  "family": "justprototype-litellm",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "containerDefinitions": [
    {
      "name": "litellm",
      "image": "ghcr.io/berriai/litellm:main-latest",
      "portMappings": [
        {
          "containerPort": 4000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DEEPSEEK_API_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:deepseek-key"
        }
      ],
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:4000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

#### Google Cloud Run

**Deploy LiteLLM:**
```bash
# Build and deploy LiteLLM
gcloud run deploy litellm-proxy \
  --image=ghcr.io/berriai/litellm:main-latest \
  --platform=managed \
  --region=us-central1 \
  --allow-unauthenticated \
  --port=4000 \
  --memory=2Gi \
  --cpu=1 \
  --set-env-vars="DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}" \
  --set-env-vars="OPENROUTER_API_KEY=${OPENROUTER_API_KEY}"
```

#### DigitalOcean App Platform

**App Spec (`app.yaml`):**
```yaml
name: justprototype-litellm
services:
- name: litellm
  source_dir: /
  github:
    repo: your-repo/justprototype
    branch: main
  run_command: litellm --config backend/litellm_config.yaml --port 4000
  environment_slug: docker
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: DEEPSEEK_API_KEY
    value: your_key_here
    type: SECRET
  - key: OPENROUTER_API_KEY
    value: your_key_here
    type: SECRET
  http_port: 4000
  health_check:
    http_path: /health
```

### 3. Kubernetes Deployment

#### Deployment Manifest
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: litellm-proxy
spec:
  replicas: 2
  selector:
    matchLabels:
      app: litellm-proxy
  template:
    metadata:
      labels:
        app: litellm-proxy
    spec:
      containers:
      - name: litellm
        image: ghcr.io/berriai/litellm:main-latest
        ports:
        - containerPort: 4000
        env:
        - name: DEEPSEEK_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-secrets
              key: deepseek-key
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-secrets
              key: openrouter-key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: litellm-service
spec:
  selector:
    app: litellm-proxy
  ports:
  - port: 4000
    targetPort: 4000
  type: ClusterIP
```

## Monitoring and Logging

### 1. Health Monitoring

#### Health Check Endpoints
```bash
# LiteLLM health
curl http://your-domain:4000/health

# Application health
curl http://your-domain:3000/api/health
```

#### Monitoring Script
```bash
#!/bin/bash
# monitor.sh

check_litellm() {
  if curl -f -s http://localhost:4000/health > /dev/null; then
    echo "✅ LiteLLM is healthy"
  else
    echo "❌ LiteLLM is down"
    # Restart if needed
    docker-compose -f docker-compose.litellm.yml restart litellm
  fi
}

check_app() {
  if curl -f -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Application is healthy"
  else
    echo "❌ Application is down"
  fi
}

check_litellm
check_app
```

### 2. Logging Configuration

#### LiteLLM Logging
```yaml
# In litellm_config.yaml
litellm_settings:
  set_verbose: true
  json_logs: true
  log_level: "INFO"
  success_callback: ["langfuse"] # Optional
  failure_callback: ["langfuse"]
```

#### Application Logging
```javascript
// In your app
console.log(`🤖 Using ${providerKey} provider with model ${modelName} for task: ${taskType}`);
console.log(`💰 Estimated cost: $${estimatedCost}`);
console.log(`⏱️ Response time: ${responseTime}ms`);
```

### 3. Metrics Collection

#### Prometheus Metrics (Optional)
```yaml
# Add to docker-compose.prod.yml
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## Security Considerations

### 1. API Key Management
- Use environment variables only
- Implement key rotation procedures
- Monitor for key exposure in logs
- Use cloud secret management services

### 2. Network Security
```yaml
# Docker network isolation
networks:
  internal:
    driver: bridge
    internal: true
  external:
    driver: bridge

services:
  litellm:
    networks:
      - internal
      - external
  app:
    networks:
      - internal
```

### 3. Rate Limiting
```yaml
# In litellm_config.yaml
router_settings:
  routing_strategy: "least-busy"
  cooldown_time: 1
  retry_policy:
    max_retries: 3
    base_delay: 1
```

## Backup and Recovery

### 1. Configuration Backup
```bash
# Backup configuration
tar -czf backup-$(date +%Y%m%d).tar.gz \
  backend/litellm_config.yaml \
  docker-compose.litellm.yml \
  .env.production
```

### 2. Disaster Recovery
```bash
# Quick recovery script
#!/bin/bash
# restore.sh

# Stop services
docker-compose -f docker-compose.litellm.yml down

# Restore configuration
tar -xzf backup-latest.tar.gz

# Restart services
docker-compose -f docker-compose.litellm.yml up -d

# Verify health
sleep 30
curl http://localhost:4000/health
```

## Performance Optimization

### 1. Scaling
- **Horizontal:** Multiple LiteLLM instances behind load balancer
- **Vertical:** Increase CPU/memory for single instance
- **Model-specific:** Different instances for different model types

### 2. Caching
```yaml
# Redis caching (optional)
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
```

### 3. Load Balancing
```nginx
# Nginx configuration
upstream litellm_backend {
    server litellm1:4000;
    server litellm2:4000;
    server litellm3:4000;
}

server {
    listen 80;
    location / {
        proxy_pass http://litellm_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Troubleshooting

### Common Issues

1. **LiteLLM won't start**
   - Check Docker daemon
   - Verify config file syntax
   - Ensure API keys are set

2. **Models not responding**
   - Test API keys directly
   - Check model names in config
   - Verify network connectivity

3. **High latency**
   - Monitor resource usage
   - Check network latency
   - Consider model optimization

### Debug Commands
```bash
# Check container logs
docker-compose -f docker-compose.litellm.yml logs -f litellm

# Test model directly
curl -X POST http://localhost:4000/v1/models

# Check resource usage
docker stats
```

## Cost Monitoring

### 1. Usage Tracking
```bash
# Monitor API usage
curl -H "Authorization: Bearer sk-1234" \
  http://localhost:4000/v1/usage
```

### 2. Budget Alerts
Set up monitoring for:
- Daily/monthly spend limits
- Unusual usage patterns
- Model performance metrics

This deployment guide ensures a robust, scalable, and secure LiteLLM integration for production use.
