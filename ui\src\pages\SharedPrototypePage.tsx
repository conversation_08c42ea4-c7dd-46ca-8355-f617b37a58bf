import React from 'react';
import { Container, Typography, Box, CircularProgress } from '@mui/material';
import { useParams } from 'react-router-dom';

const SharedPrototypePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [loading] = React.useState(true);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>Prototype Viewer</Typography>

      <Box sx={{ mt: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Viewing prototype: {id}
            </Typography>
            {/* Add prototype content */}
          </Box>
        )}
      </Box>
    </Container>
  );
};

export default SharedPrototypePage;
