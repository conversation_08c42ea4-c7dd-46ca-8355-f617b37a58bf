const { v4: uuidv4 } = require('uuid');
const { schemaValidator } = require('./schemaValidator');

/**
 * Service for managing the UI AST (Abstract Syntax Tree) state
 */
class ASTStore {
  constructor() {
    this.currentAST = this.createEmptyPage();
    this.history = {};
    this.latestVersion = 0;
    this.saveSnapshot('Initial empty page');
  }

  createEmptyPage() {
    return {
      id: uuidv4(),
      type: 'Page',
      props: {
        title: 'Untitled Page',
        meta: {},
        styles: {}
      },
      children: []
    };
  }

  deepCloneAST(ast) {
    return JSON.parse(JSON.stringify(ast));
  }

  generateSnapshotId() {
    this.latestVersion++;
    return `v${this.latestVersion}`;
  }

  getCurrentAST() {
    return this.deepCloneAST(this.currentAST);
  }

  getCurrentSnapshot() {
    return {
      snapshotId: `v${this.latestVersion}`,
      ast: this.getCurrentAST()
    };
  }

  setCurrentAST(ast, saveSnapshot = true) {
    const validation = schemaValidator.validateTree(ast);
    if (!validation.valid) {
      throw new Error(`Invalid AST: ${validation.errors.join(', ')}`);
    }

    this.currentAST = this.deepCloneAST(ast);
    if (saveSnapshot) {
      return this.saveSnapshot('AST updated');
    }
    return `v${this.latestVersion}`;
  }

  saveSnapshot(message) {
    const id = this.generateSnapshotId();
    const version = {
      id,
      ast: this.deepCloneAST(this.currentAST),
      timestamp: new Date(),
      message
    };
    this.history[id] = version;
    return id;
  }

  getSnapshot(id) {
    const version = this.history[id];
    if (version) {
      return {
        ...version,
        ast: this.deepCloneAST(version.ast)
      };
    }
    return undefined;
  }

  listSnapshots() {
    return Object.keys(this.history).sort();
  }

  restoreSnapshot(id) {
    const version = this.getSnapshot(id);
    if (version) {
      this.setCurrentAST(version.ast, false);
      return true;
    }
    return false;
  }

  initializeFromPlan(plan) {
    // Convert plan text into initial AST structure
    const ast = {
      id: uuidv4(),
      type: 'Page',
      props: {
        title: 'Generated Page',
        meta: { generatedFrom: plan }
      },
      children: []
    };

    // Save and return new snapshot
    this.setCurrentAST(ast);
    return this.getCurrentSnapshot();
  }

  applyPatch(snapshotId, patch) {
    // Verify snapshot exists
    const baseVersion = this.getSnapshot(snapshotId);
    if (!baseVersion) {
      throw new Error(`Invalid snapshot ID: ${snapshotId}`);
    }

    // Apply patch and validate
    const newAst = this.deepCloneAST(baseVersion.ast);
    this.applyPatchOperations(newAst, patch);
    
    // Save and return new version
    this.setCurrentAST(newAst);
    return this.getCurrentSnapshot();
  }

  applyPatchOperations(ast, patch) {
    for (const op of patch) {
      switch (op.type) {
        case 'addChild':
          this.addChild(ast, op.parentId, op.child);
          break;
        case 'updateProps':
          this.updateNodeProps(ast, op.nodeId, op.props);
          break;
        case 'removeNode':
          this.removeNode(ast, op.nodeId);
          break;
        default:
          throw new Error(`Unknown patch operation: ${op.type}`);
      }
    }
  }

  findNodeById(ast, id) {
    if (ast.id === id) {
      return ast;
    }
    if (ast.children) {
      for (const child of ast.children) {
        const found = this.findNodeById(child, id);
        if (found) {
          return found;
        }
      }
    }
    return undefined;
  }

  addChild(ast, parentId, child) {
    const parent = this.findNodeById(ast, parentId);
    if (!parent) {
      throw new Error(`Parent node not found: ${parentId}`);
    }
    
    child.id = child.id || uuidv4();
    if (!parent.children) {
      parent.children = [];
    }
    parent.children.push(this.deepCloneAST(child));
  }

  updateNodeProps(ast, nodeId, props) {
    const node = this.findNodeById(ast, nodeId);
    if (!node) {
      throw new Error(`Node not found: ${nodeId}`);
    }
    node.props = { ...node.props, ...props };
  }

  removeNode(ast, nodeId) {
    const removeFromChildren = (children) => {
      const index = children.findIndex(child => child.id === nodeId);
      if (index >= 0) {
        children.splice(index, 1);
        return true;
      }
      for (const child of children) {
        if (child.children && removeFromChildren(child.children)) {
          return true;
        }
      }
      return false;
    };

    if (ast.children && !removeFromChildren(ast.children)) {
      throw new Error(`Node not found: ${nodeId}`);
    }
  }
}

// Export singleton instance
module.exports = new ASTStore();
