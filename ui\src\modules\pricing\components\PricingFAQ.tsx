import React, { useState } from 'react';
import styles from './PricingFAQ.module.css';
import { FAQItem } from '../types';

interface PricingFAQProps {
  /**
   * Array of FAQ items
   */
  items: Array<FAQItem | { question: string; answer: string }>;
  
  /**
   * Optional title override
   */
  title?: string;
  
  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * FAQ component for the pricing module
 */
export const PricingFAQ: React.FC<PricingFAQProps> = ({
  items,
  title = 'Frequently Asked Questions',
  className = ''
}) => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  
  const toggleItem = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };
  
  return (
    <div className={`${styles.faqContainer} ${className}`}>
      <h2 className={styles.faqTitle}>{title}</h2>
      
      <div className={styles.faqItems}>
        {items.map((item, index) => (
          <div 
            key={index} 
            className={`${styles.faqItem} ${expandedIndex === index ? styles.expanded : ''}`}
          >
            <div 
              className={styles.faqQuestion}
              onClick={() => toggleItem(index)}
            >
              <span>{item.question}</span>
              <span className={styles.faqToggle}>
                {expandedIndex === index ? '−' : '+'}
              </span>
            </div>
            
            <div className={styles.faqAnswer}>
              {typeof item.answer === 'string' ? (
                <p dangerouslySetInnerHTML={{ __html: item.answer }} />
              ) : (
                item.answer
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
