console.log('🧪 Starting LLMServiceV3 Tests...\n');

try {
  const service = require('../services/llmServiceV3');
  console.log('✅ LLMServiceV3 loaded successfully\n');

  const sampleHTML = `
<div id="app">
  <header class="bg-blue-600">
    <nav class="nav">
      <h1>Dashboard</h1>
      <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
      </ul>
    </nav>
  </header>
  <main>
    <div id="chart-container">Chart here</div>
  </main>
</div>
`;

  // Test fragment extraction (synchronous)
  console.log('🔍 Testing Fragment Extraction:\n');
  
  const selectors = ['header', '.nav', '#chart-container', 'h1', '.bg-blue-600'];
  selectors.forEach(selector => {
    console.log(`Testing selector: ${selector}`);
    try {
      const fragment = service.extractFragment(sampleHTML, selector);
      if (fragment) {
        console.log(`✅ SUCCESS - Length: ${fragment.length}`);
        console.log(`📄 Preview: ${fragment.substring(0, 80)}...`);
      } else {
        console.log('❌ FAILED - No fragment extracted');
      }
    } catch (e) {
      console.log(`❌ ERROR: ${e.message}`);
    }
    console.log('');
  });

  // Test prompt analysis patterns (synchronous part)
  console.log('🎯 Testing Prompt Patterns:\n');
  
  const testPrompts = [
    'change the header background color to red',
    'add a new menu item called Services', 
    'add a bar chart showing monthly sales',
    'change the title to Admin Panel',
    'redesign the entire layout'
  ];

  testPrompts.forEach((prompt, i) => {
    console.log(`${i+1}. "${prompt}"`);
    
    // Test pattern matching logic manually
    const promptLower = prompt.toLowerCase().trim();
    
    const targetedPatterns = [
      { pattern: /change.*color.*to|make.*color|color.*to/i, selector: '[class*="color"], [style*="color"], .text-', type: 'color' },
      { pattern: /add.*menu.*item|new.*menu|menu.*item/i, selector: 'nav, .nav, .menu, [role="navigation"]', type: 'menu' },
      { pattern: /add.*chart|new.*chart|chart.*show|display.*chart/i, selector: '.chart, [id*="chart"], canvas, svg', type: 'chart' },
      { pattern: /change.*text.*to|update.*text|text.*to/i, selector: 'h1, h2, h3, h4, h5, h6, p, span, div', type: 'text' },
      { pattern: /redesign|complete.*change|overhaul|rebuild/i, selector: null, type: 'global' }
    ];

    let matched = false;
    for (const { pattern, selector, type } of targetedPatterns) {
      if (pattern.test(prompt)) {
        console.log(`   ✅ Matched pattern: ${type}`);
        console.log(`   🎯 Strategy: ${type === 'global' ? 'FULL' : 'FRAGMENT'}`);
        console.log(`   🔧 Selector: ${selector || 'none'}`);
        matched = true;
        break;
      }
    }
    
    if (!matched) {
      console.log('   ❓ No pattern matched - would use FULL strategy');
    }
    console.log('');
  });

} catch (error) {
  console.error('❌ Error loading or testing service:', error.message);
  console.error(error.stack);
}

console.log('🏁 Test completed');
