// ElementSelector.tsx
import React, { useRef } from "react";
import useElementSelection from "../hooks/useElementSelection";
import "../styles/elementSelection.css";

interface ElementSelectorProps {
  html: string;
  onSelect: (selector: string) => void;
}

const ElementSelector: React.FC<ElementSelectorProps> = ({ html, onSelect }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  useElementSelection(containerRef, onSelect);

  return (
    <div
      ref={containerRef}
      className="element-selector-container"
      dangerouslySetInnerHTML={{ __html: html }}
      tabIndex={0}
    />
  );
};

export default ElementSelector;
