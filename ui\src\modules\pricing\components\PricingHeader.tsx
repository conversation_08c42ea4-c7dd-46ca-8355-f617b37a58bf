import React from 'react';
import styles from './PricingHeader.module.css';

interface PricingHeaderProps {
  /**
   * Current billing cycle
   */
  billingCycle: 'monthly' | 'yearly';
  
  /**
   * Callback when billing cycle changes
   */
  onBillingCycleChange: (cycle: 'monthly' | 'yearly') => void;
  
  /**
   * Optional yearly discount percentage
   */
  yearlyDiscountPercentage?: number;
  
  /**
   * Optional title override
   */
  title?: string;
  
  /**
   * Optional subtitle override
   */
  subtitle?: string;
  
  /**
   * Optional description override
   */
  description?: string;
  
  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * Header component for the pricing module with billing cycle toggle
 */
export const PricingHeader: React.FC<PricingHeaderProps> = ({
  billingCycle,
  onBillingCycleChange,
  yearlyDiscountPercentage = 0,
  title = 'Simple Pricing',
  subtitle = 'Choose the plan that works for you',
  description = 'All plans include a 14-day free trial. No credit card required.',
  className = ''
}) => {
  return (
    <div className={`${styles.pricingHeader} ${className}`}>
      <h1 className={styles.title}>{title}</h1>
      <h2 className={styles.subtitle}>{subtitle}</h2>
      <p className={styles.description}>{description}</p>
      
      <div className={styles.billingToggle}>
        <span 
          className={`${styles.billingOption} ${billingCycle === 'monthly' ? styles.active : ''}`}
          onClick={() => onBillingCycleChange('monthly')}
        >
          Monthly
        </span>
        
        <label className={styles.toggleSwitch}>
          <input 
            type="checkbox" 
            checked={billingCycle === 'yearly'} 
            onChange={() => onBillingCycleChange(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
          />
          <span className={styles.toggleSlider}></span>
        </label>
        
        <span 
          className={`${styles.billingOption} ${billingCycle === 'yearly' ? styles.active : ''}`}
          onClick={() => onBillingCycleChange('yearly')}
        >
          Yearly
          {yearlyDiscountPercentage > 0 && (
            <span className={styles.saveBadge}>
              Save {yearlyDiscountPercentage}%
            </span>
          )}
        </span>
      </div>
    </div>
  );
};
