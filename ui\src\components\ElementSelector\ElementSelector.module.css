.elementSelector {
  width: 280px;
  min-width: 240px;
  max-width: 340px;
  background: #f8f8fa;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.selectorHeader {
  padding: 14px 18px 10px 18px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f8fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selectorHeader h3 {
  margin: 0;
  font-size: 1.08em;
  font-weight: 700;
  letter-spacing: 0.01em;
}

.selectorButton {
  background: #f0f0f5;
  border: 1px solid #ddd;
  color: #333;
  font-size: 0.85em;
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.selectorButton:hover {
  background: #e3e8f0;
  border-color: #ccc;
}

.selectorButton.active {
  background: #2196f3;
  color: white;
  border-color: #1976d2;
}

/* Selected Elements Pills */
.selectedElementsPills {
  padding: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  border-bottom: 1px solid #e0e0e0;
  max-height: 120px;
  overflow-y: auto;
}

.elementPill {
  display: flex;
  align-items: center;
  background: #f0f0f5;
  border: 1px solid #ddd;
  border-radius: 16px;
  padding: 4px 8px 4px 12px;
  font-size: 0.85em;
  cursor: pointer;
  transition: all 0.2s;
}

.elementPill:hover {
  background: #e3e8f0;
  border-color: #ccc;
}

.elementPill.activePill {
  background: #e3f2fd;
  border-color: #2196f3;
}

.pillText {
  margin-right: 6px;
  font-family: monospace;
  white-space: nowrap;
}

.pillCloseBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s;
}

.pillCloseBtn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.elementInfo {
  padding: 12px 18px;
  overflow-y: auto;
  flex: 1;
}

.elementPath {
  background: #eee;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  font-family: monospace;
  font-size: 0.9em;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.tagName {
  color: #e91e63;
  font-weight: bold;
}

.elementId {
  color: #9c27b0;
}

.elementClass {
  color: #2196f3;
}

.actionButtons {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.actionButton {
  background: #f0f0f5;
  border: 1px solid #ddd;
  color: #555;
  font-size: 1em;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.actionButton:hover {
  background: #e3e8f0;
  color: #1976d2;
  border-color: #ccc;
}

.styleEditor {
  margin-top: 16px;
}

.styleEditor h4 {
  margin: 0 0 8px 0;
  font-size: 1em;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.styleGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.styleRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  align-items: center;
}

.styleRow label {
  font-size: 0.85em;
  color: #666;
  font-family: monospace;
}

.styleRow input {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.85em;
  font-family: monospace;
}

.noSelection, .selectionMode {
  padding: 20px;
  text-align: center;
  color: #888;
  font-size: 0.9em;
}

.selectionMode {
  color: #2196f3;
  font-weight: 500;
}

.selectionMode p {
  margin: 8px 0;
}

.selectorOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 1000;
  display: none;
  pointer-events: none;
}
