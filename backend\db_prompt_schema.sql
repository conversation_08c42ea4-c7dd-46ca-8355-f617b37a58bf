-- SQL file to create or update the prompts and prompt_iterations tables

-- Create the prompts table if it doesn't exist
CREATE TABLE IF NOT EXISTS prompts (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  prompt_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on user_id if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'prompts' AND indexname = 'idx_prompts_user_id'
  ) THEN
    CREATE INDEX idx_prompts_user_id ON prompts(user_id);
  END IF;
END $$;

-- Create the prompt_iterations table if it doesn't exist
CREATE TABLE IF NOT EXISTS prompt_iterations (
  id SERIAL PRIMARY KEY,
  prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
  iteration_number INTEGER NOT NULL,
  input_text TEXT NOT NULL,
  output_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on prompt_id if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'prompt_iterations' AND indexname = 'idx_prompt_iterations_prompt_id'
  ) THEN
    CREATE INDEX idx_prompt_iterations_prompt_id ON prompt_iterations(prompt_id);
  END IF;
END $$;

-- Add a function to get the latest iteration number for a prompt
CREATE OR REPLACE FUNCTION get_latest_iteration_number(p_prompt_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
  latest_iteration INTEGER;
BEGIN
  SELECT COALESCE(MAX(iteration_number), 0) INTO latest_iteration
  FROM prompt_iterations
  WHERE prompt_id = p_prompt_id;
  
  RETURN latest_iteration;
END;
$$ LANGUAGE plpgsql;

-- Print completion message
DO $$
BEGIN
  RAISE NOTICE 'Prompt schema created or updated successfully.';
END $$;
