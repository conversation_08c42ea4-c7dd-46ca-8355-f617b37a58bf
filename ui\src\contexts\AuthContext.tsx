import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

// Define the auth state interface
export interface AuthState {
  isAuthenticated: boolean;
  user: any;
  lastChecked: number;
}

// Define the context interface
interface AuthContextType {
  authState: AuthState;
  setAuthState: (state: AuthState) => void;
  refreshAuth: () => Promise<boolean | undefined>;
  logout: () => void;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  authState: {
    isAuthenticated: false,
    user: null,
    lastChecked: 0
  },
  setAuthState: () => {},
  refreshAuth: async () => false,
  logout: () => {}
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    lastChecked: 0
  });

  // Global variable to track if we've already initialized auth
  // This ensures we only do it once across all instances
  const hasInitializedRef = useRef(false);

  // Initialize auth state from cookies and localStorage
  useEffect(() => {
    // This function will only run once on component mount
    const initializeAuth = async () => {
      // Skip if we've already initialized
      if (hasInitializedRef.current) {
        console.log("AuthContext: Auth already initialized, skipping");
        return;
      }

      // Mark as initialized
      hasInitializedRef.current = true;

      // Check for the isLoggedIn cookie first
      const isLoggedIn = document.cookie.includes('isLoggedIn=true');
      console.log("AuthContext: isLoggedIn cookie present:", isLoggedIn);

      if (isLoggedIn) {
        console.log("AuthContext: User is logged in according to cookie");

        // Check if we have a cached auth state that's recent
        const cachedAuthString = localStorage.getItem('authState');
        if (cachedAuthString) {
          try {
            const cachedAuth = JSON.parse(cachedAuthString) as AuthState;
            const isRecent = (Date.now() - cachedAuth.lastChecked) < 30 * 60 * 1000; // 30 minutes

            if (isRecent && cachedAuth.user) {
              console.log("AuthContext: Using cached auth state");
              setAuthState(cachedAuth);

              // No need to refresh immediately if we have recent data
              return;
            }
          } catch (e) {
            console.error("AuthContext: Error parsing cached auth state:", e);
          }
        }

        // Create a minimal auth state based on the cookie
        // This avoids making an API call if we just need to know if the user is authenticated
        const minimalAuthState: AuthState = {
          isAuthenticated: true,
          user: { id: 'unknown', displayName: 'User' },
          lastChecked: Date.now()
        };

        setAuthState(minimalAuthState);
        localStorage.setItem('authState', JSON.stringify(minimalAuthState));

        // We'll let individual components refresh auth if they need specific user data
        return;
      }

      // Fall back to localStorage if no cookie
      const cachedAuthString = localStorage.getItem('authState');
      if (cachedAuthString) {
        try {
          const cachedAuth = JSON.parse(cachedAuthString) as AuthState;
          const isRecent = (Date.now() - cachedAuth.lastChecked) < 30 * 60 * 1000; // 30 minutes

          if (isRecent && cachedAuth.user) {
            console.log("AuthContext: Using cached auth state");
            setAuthState(cachedAuth);

            // No need to refresh immediately if we have recent data
            return;
          }

          // Don't refresh automatically, let components do it if needed
          console.log("AuthContext: Cached auth state expired, but not refreshing automatically");
        } catch (e) {
          console.error("AuthContext: Error parsing cached auth state:", e);
          localStorage.removeItem('authState');
        }
      } else {
        console.log("AuthContext: No cached auth state, but not refreshing automatically");
      }
    };

    // Call the initialization function
    initializeAuth();

    // No dependencies means this effect runs only once on mount
  }, []);

  // Track the last time we refreshed auth to avoid too many calls
  const lastRefreshRef = useRef<number>(0);

  // Debug counter to track how many times refreshAuth is called
  const refreshCountRef = useRef<number>(0);

  // Function to refresh authentication state
  const refreshAuth = async () => {
    // Increment the debug counter
    refreshCountRef.current += 1;
    console.log(`AuthContext: refreshAuth called ${refreshCountRef.current} times`);

    // Throttle refresh calls to at most once every 30 seconds
    const now = Date.now();
    if (now - lastRefreshRef.current < 30000) { // 30 seconds
      console.log("AuthContext: Skipping refresh, last refresh was too recent");

      // If we have a valid auth state, return true
      if (authState.isAuthenticated && authState.user) {
        return true;
      }

      // Otherwise, check if we have a cached state
      const cachedAuthString = localStorage.getItem('authState');
      if (cachedAuthString) {
        try {
          const cachedAuth = JSON.parse(cachedAuthString) as AuthState;
          const isRecent = (now - cachedAuth.lastChecked) < 30 * 60 * 1000; // 30 minutes

          if (isRecent && cachedAuth.user) {
            console.log("AuthContext: Using cached auth state instead of refreshing");
            setAuthState(cachedAuth);
            return true;
          }
        } catch (e) {
          console.error("AuthContext: Error parsing cached auth state:", e);
        }
      }
    }

    // Update the last refresh time
    lastRefreshRef.current = now;

    try {
      // Use the same approach as other services that don't have CORS issues
      const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';
      console.log("AuthContext: Refreshing auth state, API URL:", `${API_BASE}/auth/me`);

      const response = await fetch(`${API_BASE}/auth/me`, {
        method: 'GET',
        credentials: 'include', // Include cookies for authentication
      });

      console.log("AuthContext: Auth refresh response status:", response.status);

      if (response.ok) {
        const data = await response.json();
        console.log("AuthContext: Auth refresh successful");

        if (!data.user) {
          console.error("AuthContext: Auth refresh returned OK but no user data");
          handleLogout(false);
          return false;
        }

        const newAuthState = {
          isAuthenticated: true,
          user: data.user,
          lastChecked: now
        };

        // Set a cookie directly from the frontend as well
        document.cookie = `isLoggedIn=true; path=/; max-age=${30 * 24 * 60 * 60}; SameSite=Lax;`;

        setAuthState(newAuthState);
        localStorage.setItem('authState', JSON.stringify(newAuthState));

        return true;
      } else {
        console.log("AuthContext: Auth refresh failed");
        handleLogout(false);
        return false;
      }
    } catch (error) {
      console.error("AuthContext: Error refreshing auth:", error);

      // If there's an error, try to use cached data if available
      const cachedAuthString = localStorage.getItem('authState');
      if (cachedAuthString) {
        try {
          const cachedAuth = JSON.parse(cachedAuthString) as AuthState;
          if (cachedAuth.user) {
            console.log("AuthContext: Using cached user as fallback");
            setAuthState({
              isAuthenticated: true,
              user: cachedAuth.user,
              lastChecked: now
            });
            return true;
          }
        } catch (e) {
          console.error("AuthContext: Error parsing cached auth state:", e);
        }
      }

      // Check if the isLoggedIn cookie is present
      const isLoggedIn = document.cookie.includes('isLoggedIn=true');
      if (isLoggedIn) {
        console.log("AuthContext: Using isLoggedIn cookie as fallback");
        setAuthState({
          isAuthenticated: true,
          user: { id: 'unknown', displayName: 'User' },
          lastChecked: now
        });
        return true;
      }

      handleLogout(false);
      return false;
    }
  };

  // Function to handle logout
  const handleLogout = async (redirect = true) => {
    console.log("AuthContext: Logging out");

    // First, update local state
    setAuthState({
      isAuthenticated: false,
      user: null,
      lastChecked: 0
    });

    // Clear localStorage
    localStorage.removeItem('authState');

    // Clear all other auth-related items from localStorage
    localStorage.removeItem('authTimestamp');
    if (typeof window !== 'undefined' && (window as any).globalAuthState) {
      (window as any).globalAuthState = null;
    }

    // Clear the isLoggedIn cookie (multiple variants to ensure it's cleared)
    document.cookie = 'isLoggedIn=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT;';
    document.cookie = 'isLoggedIn=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=None; Secure;';
    document.cookie = 'isLoggedIn=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax;';
    document.cookie = 'isLoggedIn=; path=/; domain=.justprototype.dev; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=None; Secure;';

    console.log("AuthContext: Cookies after local logout:", document.cookie);

    if (redirect) {
      try {
        // Try to use the API logout endpoint first
        console.log("AuthContext: Calling API logout endpoint");
        const apiUrl = `${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/logout`;

        const response = await fetch(apiUrl, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          console.log("AuthContext: API logout successful");
          // Force reload to clear any remaining state
          window.location.href = '/';
        } else {
          console.log("AuthContext: API logout failed, falling back to redirect");
          // Fall back to the redirect method
          window.location.href = `${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/logout`;
        }
      } catch (error) {
        console.error("AuthContext: Error during API logout:", error);
        // Fall back to the redirect method
        window.location.href = `${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/logout`;
      }
    }
  };

  return (
    <AuthContext.Provider value={{
      authState,
      setAuthState,
      refreshAuth,
      logout: () => handleLogout(true)
    }}>
      {children}
    </AuthContext.Provider>
  );
};
