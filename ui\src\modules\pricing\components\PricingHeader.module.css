.pricingHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #111827;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #4b5563;
  margin: 0 0 1rem 0;
}

.description {
  font-size: 1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto 2rem auto;
  line-height: 1.6;
}

.billingToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem 0;
}

.billingOption {
  font-size: 0.875rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
}

.billingOption:hover {
  color: #111827;
}

.billingOption.active {
  color: #4f46e5;
  font-weight: 600;
}

.toggleSwitch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  margin: 0 0.75rem;
}

.toggleSwitch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggleSlider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: 0.4s;
  border-radius: 24px;
}

.toggleSlider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggleSlider {
  background-color: #4f46e5;
}

input:focus + .toggleSlider {
  box-shadow: 0 0 1px #4f46e5;
}

input:checked + .toggleSlider:before {
  transform: translateX(24px);
}

.saveBadge {
  display: inline-block;
  background-color: #fef3c7;
  color: #92400e;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 20px;
  margin-left: 0.5rem;
}

@media (max-width: 640px) {
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
}
