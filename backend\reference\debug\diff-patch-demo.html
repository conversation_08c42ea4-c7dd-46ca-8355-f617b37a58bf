<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Diff Match Patch Demo (HTML5)</title>
  <script src="https://unpkg.com/diff-match-patch@1.0.5/index.js"></script>
  <style>
    body { font-family: sans-serif; margin: 2em; }
    textarea { width: 100%; height: 80px; margin-bottom: 1em; }
    pre { background: #f4f4f4; padding: 1em; border-radius: 4px; }
    .result { background: #e6ffe6; }
    .error { color: red; }
  </style>
</head>
<body>
  <h1>Diff Match Patch Demo</h1>

  <label>Original HTML:</label>
  <textarea id="originalHtml"><div>Hello <b>World</b>!</div></textarea>

  <label>Patch (diff-match-patch format):</label>
  <textarea id="patchText">@@ -1,31 +1,32 @@
-<div>Hello <b>World</b>!</div>
+<div>Hello <b>Universe</b>!</div>
</textarea>

  <button onclick="applyPatch()">Apply Patch</button>

  <h2>Patched Result:</h2>
  <pre id="result" class="result"></pre>
  <div id="error" class="error"></div>

  <script>
    function applyPatch() {
      document.getElementById('error').textContent = '';
      try {
        const originalHtml = document.getElementById('originalHtml').value;
        const patchText    = document.getElementById('patchText').value;
        const dmp = new window.diff_match_patch();
        const patches = dmp.patch_fromText(patchText);
        const [patched, results] = dmp.patch_apply(patches, originalHtml);

        document.getElementById('result').textContent = patched;
        if (!results.every(Boolean)) {
          document.getElementById('error').textContent = '⚠️ Warning: Some patches failed to apply.';
        }
      } catch (e) {
        document.getElementById('result').textContent = '';
        document.getElementById('error').textContent = '❌ Error: ' + e.message;
      }
    }
  </script>
</body>
</html>
