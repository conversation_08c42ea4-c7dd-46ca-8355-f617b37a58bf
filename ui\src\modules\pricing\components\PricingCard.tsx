import React, { useState } from 'react';
import { PricingPlan } from '../types';
import styles from './PricingCard.module.css';

interface PricingCardProps {
  /**
   * Plan data to display
   */
  plan: PricingPlan;

  /**
   * Current billing cycle
   */
  billingCycle: 'monthly' | 'yearly';

  /**
   * Whether this is the user's current plan
   */
  isCurrentPlan?: boolean;

  /**
   * Callback when plan is selected
   */
  onSelect?: () => void;

  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * A card component that displays pricing plan information
 */
export const PricingCard: React.FC<PricingCardProps> = ({
  plan,
  billingCycle,
  isCurrentPlan = false,
  onSelect,
  className = ''
}) => {
  const [loading, setLoading] = useState(false);

  const handleSelect = () => {
    // Don't do anything if the plan is disabled
    if (plan.disabled) {
      return;
    }

    setLoading(true);

    if (plan.onSelect) {
      plan.onSelect();
    } else if (onSelect) {
      onSelect();
    } else if (plan.checkoutUrl) {
      window.open(plan.checkoutUrl, '_blank', 'noopener,noreferrer');
    } else {
      // Default payment URL if none provided
      window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');
    }
  };

  // Determine price to display based on billing cycle
  const displayPrice = billingCycle === 'yearly' && plan.yearlyPricePerMonth
    ? plan.yearlyPricePerMonth
    : plan.price;

  // Determine total price for yearly billing
  const yearlyTotal = billingCycle === 'yearly' && plan.yearlyPrice
    ? plan.yearlyPrice
    : plan.price * 12;

  return (
    <div
      className={`
        ${styles.pricingCard}
        ${plan.isFeatured ? styles.featured : ''}
        ${isCurrentPlan ? styles.currentPlan : ''}
        ${className}
      `}
    >
      {plan.badge && (
        <div className={styles.badge}>
          {plan.badge}
        </div>
      )}

      <div className={styles.planHeader}>
        <h3 className={styles.planName}>{plan.name}</h3>
        {plan.description && (
          <p className={styles.planDescription}>{plan.description}</p>
        )}
      </div>

      <div className={styles.pricing}>
        <div className={styles.priceContainer}>
          <span className={styles.currency}>$</span>
          <span className={styles.price}>{displayPrice}</span>
          <span className={styles.period}>
            /{billingCycle === 'yearly' ? 'mo' : 'month'}
          </span>
        </div>

        {billingCycle === 'yearly' && (
          <div className={styles.billedYearly}>
            Billed yearly (${yearlyTotal.toFixed(2)})
          </div>
        )}
      </div>

      {plan.quota && (
        <div className={styles.quota}>
          <div className={styles.quotaItem}>
            <span className={styles.quotaValue}>{plan.quota.prototypeLimit}</span>
            <span className={styles.quotaLabel}>
              prototype{plan.quota.prototypeLimit !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
      )}

      <ul className={styles.featuresList}>
        {plan.features.map((feature, index) => (
          <li
            key={index}
            className={`
              ${styles.featureItem}
              ${feature.isHighlighted ? styles.highlightedFeature : ''}
            `}
          >
            {typeof feature.icon === 'string' ? (
              <span className={styles.featureIcon}>{feature.icon}</span>
            ) : feature.icon ? (
              <span className={styles.featureIconComponent}>{feature.icon}</span>
            ) : (
              <span className={styles.featureCheck}>✓</span>
            )}
            <span className={styles.featureName}>
              {feature.name}
              {feature.description && (
                <span className={styles.featureDescription}>
                  {feature.description}
                </span>
              )}
            </span>
          </li>
        ))}
      </ul>

      <div className={styles.cardFooter}>
        <button
          className={`
            ${styles.actionButton}
            ${isCurrentPlan ? styles.currentPlanButton : ''}
            ${loading ? styles.loadingButton : ''}
            ${plan.disabled ? styles.disabledButton : ''}
          `}
          onClick={handleSelect}
          disabled={loading || isCurrentPlan || plan.disabled}
        >
          {loading ? 'Redirecting...' :
           isCurrentPlan ? 'Current Plan' :
           plan.disabled ? 'Coming Soon' :
           plan.buttonText || 'Get Started'}
        </button>
      </div>
    </div>
  );
};
