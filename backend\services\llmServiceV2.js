const { LL<PERSON>hain } = require('langchain/chains');
const { ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate } = require('@langchain/core/prompts');
const LLMFactory = require('./llm/LLMFactory');
const SSECallbackHandler = require('./sse/SSECallbackHandler');
const systemPrompts = require('./llm/systemPrompts');
const codeAstService = require('./ast/codeAstService');
const transformationHooks = require('./ast/transformationHooks');

class LLMServiceV2 {
  constructor() {
    this.llmFactory = LLMFactory;
  }

  createChain(systemPrompt, llm) {
    const chatPrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(systemPrompt),
      HumanMessagePromptTemplate.fromTemplate('{input}')
    ]);

    return new LL<PERSON>hain({
      prompt: chatPrompt,
      llm
    });
  }

  async generateWithSSE(systemPrompt, userPrompt, res, provider = undefined, task = 'implementation', sseOptions = {}) {
    const handler = new SSECallbackHandler(res, { 
      task,
      streamTokens: sseOptions.streamTokens ?? true,  // Enable token streaming by default
      tokenBatchSize: sseOptions.tokenBatchSize ?? 0,
      tokenBatchTimeout: sseOptions.tokenBatchTimeout ?? 50
    });
    
    try {
      provider = this.llmFactory.validateProvider(provider);
      if (!this.llmFactory.supportsStreaming(provider)) {
        throw new Error(`Provider ${provider} does not support streaming`);
      }

      const callbacks = handler.getCallbacks();
      const llm = this.llmFactory.createLLM(provider, callbacks);
      const chain = this.createChain(systemPrompt, llm);
      
      handler.handleStart();
      await chain.call({ input: userPrompt });
      handler.handleEnd();
    } catch (error) {
      handler.handleError(error);
    }
  }

  async generatePlan(prompt, res, provider) {
    if (!prompt || typeof prompt !== 'string') {
      throw new Error('Prompt is required and must be a string');
    }

    await this.generateWithSSE(
      systemPrompts.plan,
      prompt,
      res,
      provider,
      'plan',
      { streamTokens: true }
    );
  }

  async generateCodeWithAST(plan, res, provider, options = {}) {
    if (!plan || typeof plan !== 'string') {
      throw new Error('Plan is required and must be a string');
    }

    const {
      language = 'html',
      transformations = [],
      format = true,
      parseAST = true,
      streamTokens = true
    } = options;

    const handler = new SSECallbackHandler(res, { 
      task: 'codeGeneration',
      streamTokens,
      tokenBatchSize: 100,
      tokenBatchTimeout: 50
    });

    try {
      handler.handleStart();

      handler.sendEvent('ast_metadata', {
        type: 'initial',
        language,
        metadata: {
          plan,
          options: { format, parseAST, language }
        }
      });

      provider = this.llmFactory.validateProvider(provider);
      if (!this.llmFactory.supportsStreaming(provider)) {
        throw new Error(`Provider ${provider} does not support streaming`);
      }

      const callbacks = handler.getCallbacks();
      const llm = this.llmFactory.createLLM(provider, callbacks);
      const chain = this.createChain(systemPrompts.code, llm);
      
      await chain.call({ input: plan });

      // Parse AST if requested (will be handled after complete code generation)
      if (parseAST) {
        try {
          // Get the final code from the handler's buffer
          const code = handler.extractCode(handler.contentBuffer);
          if (!code) {
            throw new Error('No valid code generated');
          }

          const ast = await codeAstService.parseCode(code, language);
          
          handler.sendEvent('ast_metadata', {
            type: 'parsed',
            language,
            metadata: ast
          });

          // Apply transformations if any
          if (transformations && transformations.length > 0) {
            const transformedAST = await codeAstService.transformAST(ast, transformations, language);
            const transformedCode = await codeAstService.printCode(transformedAST, language);
            
            handler.sendEvent('ast_metadata', {
              type: 'transformed',
              language,
              metadata: transformedAST,
              transformationsApplied: transformations.map(t => t.type)
            });

            handler.sendEvent('formatted_code', {
              type: 'final',
              code: transformedCode
            });
          }
        } catch (astError) {
          console.error('AST processing error:', astError);
          handler.sendEvent('ast_metadata', {
            type: 'error',
            language,
            error: astError.message
          });
        }
      }

      handler.handleEnd();
    } catch (error) {
      console.error('Error in generateCodeWithAST:', error);
      handler.handleError(error);
    }
  }

  getTransformationHooks() {
    return transformationHooks.getAvailableHooks();
  }

  getSupportedLanguages() {
    return codeAstService.getSupportedLanguages();
  }
}

module.exports = new LLMServiceV2();
