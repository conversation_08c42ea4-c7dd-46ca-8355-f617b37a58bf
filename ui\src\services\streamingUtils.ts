/**
 * Utility functions for handling streaming responses from LLM services
 */

/**
 * A wrapper for AsyncGenerator that adds a callback for when the final chunk is received
 * @param generator The original AsyncGenerator
 * @param onFinalChunk Callback function to be called when the final chunk is received
 * @returns A new AsyncGenerator that yields the same chunks but calls onFinalChunk when done
 */
export async function* withFinalChunkCallback<T>(
  generator: AsyncGenerator<T, void, unknown>,
  onFinalChunk: () => void
): AsyncGenerator<T, void, unknown> {
  let lastChunk: T | null = null;
  let isFirstChunk = true;
  let lastChunkTime = Date.now();
  let noNewChunksTimeout: ReturnType<typeof setTimeout> | null = null;
  let callbackCalled = false;

  // Function to safely call the callback only once
  const callFinalChunkCallback = () => {
    if (!callbackCalled) {
      callbackCalled = true;
      console.log('Calling final chunk callback');
      onFinalChunk();
    }
  };

  // Set up a timeout to detect when no new chunks are received for a while
  // This helps handle cases where the LLM provider (like Deepseek) stops responding
  // without properly closing the connection
  const setupNoNewChunksTimeout = () => {
    // Clear any existing timeout
    if (noNewChunksTimeout) {
      clearTimeout(noNewChunksTimeout);
    }

    // Set a new timeout
    noNewChunksTimeout = setTimeout(() => {
      const timeSinceLastChunk = Date.now() - lastChunkTime;
      console.log(`No new chunks received for ${timeSinceLastChunk}ms`);

      // If it's been more than 3 seconds since the last chunk, consider the stream complete
      if (timeSinceLastChunk > 3000 && lastChunk !== null) {
        console.log('Stream appears to be stalled. Treating as complete.');
        // Call the final chunk callback directly
        callFinalChunkCallback();

        // We'll also call it in the finally block as a safety measure
        // But our flag will prevent it from being called twice
      }
    }, 3000); // Check after 3 seconds of no activity
  };

  try {
    setupNoNewChunksTimeout();

    for await (const chunk of generator) {
      // Update the last chunk time
      lastChunkTime = Date.now();

      // Reset the timeout since we received a new chunk
      setupNoNewChunksTimeout();

      // If this is not the first chunk, yield the previous chunk
      if (!isFirstChunk) {
        yield lastChunk as T;
      } else {
        isFirstChunk = false;
      }

      // Store the current chunk as the last chunk
      lastChunk = chunk;
    }

    // Yield the final chunk
    if (lastChunk !== null) {
      yield lastChunk;
    }

    // Call the onFinalChunk callback
    callFinalChunkCallback();
  } catch (error) {
    console.error('Error in streaming with final chunk callback:', error);

    // If we have a last chunk, yield it before throwing
    if (lastChunk !== null) {
      yield lastChunk;
    }

    throw error;
  } finally {
    // Clean up the timeout
    if (noNewChunksTimeout) {
      clearTimeout(noNewChunksTimeout);
    }

    // If we have a last chunk but haven't called onFinalChunk yet, call it now
    // This ensures the UI is updated properly even if there was an error or the stream stalled
    if (lastChunk !== null && !callbackCalled) {
      callFinalChunkCallback();
    }
  }
}
