export type UINodeType = 'Section' | 'Container' | 'Component' | 'Element' | 'Page';
export type LayoutType = 'row' | 'column' | 'grid';

export interface BaseProps {
  className?: string;
  style?: Record<string, string>;
  layout?: LayoutType;
  title?: string;
  meta?: Record<string, string>;
  styles?: Record<string, string>;
}

export interface SectionProps extends BaseProps {
  title: string;
}

export interface ContainerProps extends BaseProps {
  layout: LayoutType;
}

export interface ComponentProps extends BaseProps {
  name: string;
  variant?: string;
}

export interface ElementProps extends BaseProps {
  tag: string;
  content?: string;
}

export interface PageProps extends BaseProps {
  title: string;
  meta?: Record<string, string>;
}

export interface UINode {
  id: string;
  type: UINodeType;
  children?: UINode[];
  props: SectionProps | ContainerProps | ComponentProps | ElementProps | PageProps;
}

export interface ASTVersion {
  id: string;
  timestamp: number;
  node: UINode;
  changes: Array<{
    type: 'add' | 'update' | 'delete';
    path: string[];
    node?: UINode;
    props?: Partial<UINode['props']>;
  }>;
}

export interface ASTHistory {
  currentVersion: string;
  versions: Record<string, ASTVersion>;
}

// Type guards
export const isSectionProps = (props: UINode['props']): props is SectionProps => {
  return props.hasOwnProperty('title') && !props.hasOwnProperty('tag') && !props.hasOwnProperty('name');
};

export const isContainerProps = (props: UINode['props']): props is ContainerProps => {
  return props.hasOwnProperty('layout') && !props.hasOwnProperty('tag') && !props.hasOwnProperty('name');
};

export const isComponentProps = (props: UINode['props']): props is ComponentProps => {
  return props.hasOwnProperty('name');
};

export const isElementProps = (props: UINode['props']): props is ElementProps => {
  return props.hasOwnProperty('tag');
};

export const isPageProps = (props: UINode['props']): props is PageProps => {
  return props.hasOwnProperty('title') && !props.hasOwnProperty('tag') && !props.hasOwnProperty('layout');
};

export const isPageNode = (node: UINode): boolean => {
  return node.type === 'Page' && isPageProps(node.props);
};
