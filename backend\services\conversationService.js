// Conversation service for managing conversation history
const crypto = require('crypto');

// In-memory store for conversations (in production, use a database)
const conversations = new Map();

// Maximum number of messages to keep in a conversation
const MAX_CONVERSATION_LENGTH = 10;

// Conversation expiry time (in milliseconds) - 24 hours
const CONVERSATION_EXPIRY = 24 * 60 * 60 * 1000;

/**
 * Generate a unique conversation ID
 * @returns {string} A unique conversation ID
 */
function generateConversationId() {
  return crypto.randomBytes(16).toString('hex');
}

/**
 * Create a new conversation
 * @returns {Object} The new conversation object with ID
 */
function createConversation() {
  const conversationId = generateConversationId();
  const conversation = {
    id: conversationId,
    messages: [],
    currentHtml: '',
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };
  
  conversations.set(conversationId, conversation);
  return conversation;
}

/**
 * Get a conversation by ID
 * @param {string} conversationId - The conversation ID
 * @returns {Object|null} The conversation object or null if not found
 */
function getConversation(conversationId) {
  if (!conversationId) return null;
  
  const conversation = conversations.get(conversationId);
  if (!conversation) return null;
  
  // Check if the conversation has expired
  if (Date.now() - conversation.updatedAt > CONVERSATION_EXPIRY) {
    conversations.delete(conversationId);
    return null;
  }
  
  return conversation;
}

/**
 * Add a message to a conversation
 * @param {string} conversationId - The conversation ID
 * @param {string} role - The role of the message sender (user or assistant)
 * @param {string} content - The message content
 * @returns {Object|null} The updated conversation or null if not found
 */
function addMessage(conversationId, role, content) {
  const conversation = getConversation(conversationId);
  if (!conversation) return null;
  
  // Add the message to the conversation
  conversation.messages.push({
    role,
    content,
    timestamp: Date.now(),
  });
  
  // Limit the number of messages in the conversation
  if (conversation.messages.length > MAX_CONVERSATION_LENGTH) {
    conversation.messages = conversation.messages.slice(-MAX_CONVERSATION_LENGTH);
  }
  
  // Update the conversation timestamp
  conversation.updatedAt = Date.now();
  
  return conversation;
}

/**
 * Update the current HTML in a conversation
 * @param {string} conversationId - The conversation ID
 * @param {string} html - The HTML content
 * @returns {Object|null} The updated conversation or null if not found
 */
function updateHtml(conversationId, html) {
  const conversation = getConversation(conversationId);
  if (!conversation) return null;
  
  conversation.currentHtml = html;
  conversation.updatedAt = Date.now();
  
  return conversation;
}

/**
 * Get all messages in a conversation
 * @param {string} conversationId - The conversation ID
 * @returns {Array|null} The conversation messages or null if not found
 */
function getMessages(conversationId) {
  const conversation = getConversation(conversationId);
  if (!conversation) return null;
  
  return conversation.messages;
}

/**
 * Clean up expired conversations
 */
function cleanupExpiredConversations() {
  const now = Date.now();
  
  for (const [id, conversation] of conversations.entries()) {
    if (now - conversation.updatedAt > CONVERSATION_EXPIRY) {
      conversations.delete(id);
    }
  }
}

// Run cleanup every hour
setInterval(cleanupExpiredConversations, 60 * 60 * 1000);

module.exports = {
  createConversation,
  getConversation,
  addMessage,
  updateHtml,
  getMessages,
};
