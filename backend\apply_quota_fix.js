/**
 * <PERSON><PERSON><PERSON> to apply quota function fixes
 * 
 * This script applies the fixes to the increment_prototype_count function
 * to properly enforce quotas and adds a new get_user_quota function.
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.PGSSLMODE === 'require' ? { rejectUnauthorized: false } : false,
});

async function applyQuotaFixes() {
  try {
    console.log('Applying quota function fixes...');
    
    // Read the quota fix SQL file
    const fixPath = path.join(__dirname, 'db_quota_fix.sql');
    const fixSql = fs.readFileSync(fixPath, 'utf8');
    
    // Execute the quota fixes
    await pool.query(fixSql);
    console.log('Quota function fixes applied successfully!');
    
    // Test the functions
    console.log('Testing get_user_quota function...');
    const quotaResult = await pool.query(`
      SELECT * FROM get_user_quota(1)
    `);
    
    if (quotaResult.rows.length > 0) {
      console.log('Sample quota result:', quotaResult.rows[0]);
    } else {
      console.log('No users found to test quota function.');
    }
    
    console.log('Quota fix complete!');
  } catch (error) {
    console.error('Error applying quota fixes:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the function
applyQuotaFixes().catch(console.error);
