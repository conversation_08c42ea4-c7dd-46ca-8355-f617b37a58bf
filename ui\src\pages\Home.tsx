import { useEffect, useState } from "react";

export function Home() {
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    fetch(`${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/me`, {
      credentials: "include",
    })
      .then(async (res) => {
        if (res.ok) {
          const data = await res.json();
          setUser(data.user);
        } else {
          setUser(null);
        }
      })
      .catch(() => setUser(null));
  }, []);

  return (
    <div className="flex flex-col items-center justify-center py-8">
      <h1 className="text-3xl font-bold mb-4">Welcome to JustPrototype!</h1>
      <p className="mb-6">Rapid prototyping with AI-powered code generation.</p>
      {!user ? (
        <a
          href={`${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/google`}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded flex items-center"
        >
          <svg className="w-5 h-5 mr-2" width="20" height="20" viewBox="0 0 48 48" style={{ minWidth: 20, minHeight: 20 }}>
            <g>
              <path fill="#4285F4" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.23l6.9-6.9C36.68 2.36 30.7 0 24 0 14.82 0 6.71 5.06 2.69 12.44l8.06 6.26C12.5 13.13 17.79 9.5 24 9.5z"/>
              <path fill="#34A853" d="M46.1 24.55c0-1.64-.15-3.22-.42-4.74H24v9.01h12.42c-.54 2.9-2.17 5.36-4.63 7.01l7.19 5.6C43.94 37.13 46.1 31.33 46.1 24.55z"/>
              <path fill="#FBBC05" d="M10.75 28.7c-1.18-3.54-1.18-7.36 0-10.9l-8.06-6.26C.9 15.44 0 19.59 0 24s.9 8.56 2.69 12.46l8.06-6.26z"/>
              <path fill="#EA4335" d="M24 48c6.7 0 12.68-2.21 16.9-6.02l-7.19-5.6c-2.01 1.35-4.59 2.15-7.71 2.15-6.21 0-11.5-3.63-13.25-8.7l-8.06 6.26C6.71 42.94 14.82 48 24 48z"/>
              <path fill="none" d="M0 0h48v48H0z"/>
            </g>
          </svg>
          Sign in with Google
        </a>
      ) : (
        <div className="flex items-center gap-2">
          <span className="font-semibold">Signed in as {user.displayName || user.emails?.[0]?.value}</span>
          <a
            href={`${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/logout`}
            className="text-blue-600 underline ml-2"
          >
            Logout
          </a>
        </div>
      )}
    </div>
  );
}
