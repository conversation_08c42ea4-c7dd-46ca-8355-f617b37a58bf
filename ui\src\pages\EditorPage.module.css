/* Global box-sizing for all elements */
:global(*) {
  box-sizing: border-box;
}

.editorPage {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: calc(100vh - 56px); /* Subtract nav bar height */
  overflow: hidden;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: fixed;
  top: 56px; /* Position below the nav bar */
  left: 0;
}

/* Top navigation bar */
.topActionsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  height: 56px;
}

.leftActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.centerActions {
  display: flex;
  justify-content: center;
}

.rightActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.iconBtn {
  background: none;
  border: none;
  color: #4b5563;
  font-size: 1.25em;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconBtn:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.projectName {
  font-weight: 600;
  font-size: 16px;
  color: #111827;
}

.viewModeToggle {
  display: flex;
  position: relative;
  background-color: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
  border: 1px solid #e5e7eb;
  width: 220px;
}

.toggleBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
  flex: 1;
}

.toggleBtn:hover {
  color: #111827;
}

.toggleBtn.active {
  color: #111827;
}

.toggleSlider {
  position: absolute;
  top: 4px;
  height: calc(100% - 8px);
  width: calc(50% - 4px);
  background-color: white;
  border-radius: 4px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggleSlider.left {
  left: 4px;
}

.toggleSlider.right {
  left: calc(50% + 0px);
}

.modeBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modeBtn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.modeBtn.active {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #111827;
}

.modeBtn span {
  font-size: 14px;
}

.editModeBtn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editModeBtn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.editModeBtn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: #ffffff;
}

.shareBtn {
  margin-right: 8px;
}

.publishBtn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  background: #4f46e5;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.publishBtn:hover {
  background: #4338ca;
}

.publishBtn:disabled {
  background: #a5b4fc;
  cursor: not-allowed;
}

.publishBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.loadingIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main content area */
.mainContent {
  display: flex;
  flex: 1;
  height: calc(100vh - 56px);
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Sidebar */
.sidebarCol {
  width: 30%;
  min-width: 300px;
  max-width: 30%;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

/* Sidebar header removed */

.sidebarItem {
  padding: 12px 16px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
  border-bottom: 1px solid #f3f4f6;
}

.bulletPoint {
  width: 6px;
  height: 6px;
  background: #4f46e5;
  border-radius: 50%;
  margin-top: 8px;
  flex-shrink: 0;
}

/* Chat container */
.chatContainer {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  border-top: 1px solid #e5e7eb;
  height: 300px;
}

.chatHeader {
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.chatMessages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 12px; /* Increased top padding for better spacing */
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: absolute;
  top: 0; /* Start from the top since header is removed */
  left: 0;
  right: 0;
  bottom: 134px; /* Increased to account for potentially taller input */
  width: 100%;
  box-sizing: border-box;
}

.chatPropertiesContainer {
  position: absolute;
  bottom: 134px; /* Match the chat messages */
  left: 0;
  right: 0;
  z-index: 20;
  max-height: 70%;
  overflow-y: auto;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

.userMessage, .assistantMessage {
  padding: 10px 12px;
  border-radius: 8px;
  max-width: 90%;
}

.userMessage {
  align-self: flex-end;
  background-color: #4f46e5;
  color: white;
}

.assistantMessage {
  align-self: flex-start;
  background-color: #f3f4f6;
  color: #111827;
  border: 1px solid #e5e7eb;
}

.messageRole {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  opacity: 0.8;
}

.messageContent {
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Style for plan content in messages */
.assistantMessage .messageContent pre,
.assistantMessage .messageContent code {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 2px 4px;
  font-family: monospace;
  font-size: 13px;
}

.assistantMessage .messageContent ul,
.assistantMessage .messageContent ol {
  margin: 8px 0;
  padding-left: 20px;
}

.assistantMessage .messageContent li {
  margin-bottom: 4px;
}

.chatInputContainer {
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  min-height: 110px; /* Minimum height instead of fixed height */
  max-height: 200px; /* Maximum height to prevent it from taking too much space */
  display: flex; /* Use flexbox for full height */
  padding: 0; /* Remove padding to allow wrapper to take full space */
}

.chatInputWrapper {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 0;
  gap: 8px;
  flex: 1; /* Take up all available space */
  border: 1px solid #e5e7eb;
  overflow: hidden; /* Ensure content doesn't overflow */
  position: relative; /* For proper positioning of children */
}

.chatInput {
  width: 100%;
  padding: 10px 12px;
  border: none;
  background-color: transparent;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
  min-height: 24px;
  max-height: 100px;
  resize: none; /* Remove resize handle */
  overflow-y: auto;
  line-height: 1.5;
  font-family: inherit;
  flex: 1; /* Take available space */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #d1d5db transparent; /* Firefox */
  box-sizing: border-box;
  display: block; /* Ensure proper block display */
  height: auto; /* Allow height to adjust with content */
}

/* Webkit scrollbar styling */
.chatInput::-webkit-scrollbar {
  width: 6px;
  height: auto; /* Allow height to adjust with content */
}

.chatInput::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0; /* Add margin to align with textarea content */
}

.chatInput::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
  min-height: 40px; /* Minimum thumb size */
}

.chatInput::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.chatInput::placeholder {
  color: #333;
  font-weight: 500;
}

.chatInput:focus {
  border-color: transparent;
  box-shadow: none;
}

.chatInputActions {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px; /* Fixed height to ensure proper alignment */
  justify-content: space-between;
  padding: 4px 12px; /* Reduced bottom padding */
  border-top: 1px solid rgba(0, 0, 0, 0.05); /* Light separator */
  margin-right: 6px; /* Make room for scrollbar */
}

.selectorButtonContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 4px;
  padding: 4px 8px;
  height: 32px;
}

.selectorButtonContainer > div {
  display: flex; /* Show the selector component */
  align-items: center;
  justify-content: center;
}

.selectorButtonContainer.active {
  color: #4f46e5;
  background-color: rgba(79, 70, 229, 0.1);
}

.selectorButtonContainer span {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.selectorButtonContainer.active span {
  color: #4f46e5;
}

.selectorButtonContainer.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.sendButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.sendButton:hover {
  background-color: #4338ca;
}

.sendButton:disabled {
  background-color: #a5b4fc;
  cursor: not-allowed;
}

.loadingIcon {
  animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  transform-origin: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: rotate(360deg);
    opacity: 1;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.loadingPulse {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loadingPulse div {
  position: absolute;
  border: 4px solid #4f46e5;
  opacity: 1;
  border-radius: 50%;
  animation: loadingPulse 1.5s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.loadingPulse div:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes loadingPulse {
  0% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    top: 0px;
    left: 0px;
    width: 72px;
    height: 72px;
    opacity: 0;
  }
}

/* Content column */
.contentCol {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  width: 70%;
  max-width: 70%; /* Use percentage instead of calculation */
  box-sizing: border-box;
}

.contentContainer {
  flex: 1;
  display: flex;
  overflow: hidden;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.codeContainer {
  flex: 1;
  background: #1e1e1e;
  height: 100%;
  max-width: 100%;
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.previewContainer {
  flex: 1;
  background: #ffffff;
  height: 100%;
  max-width: 100%;
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.previewIframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Error and streaming notices */
.error {
  color: #b91c1c;
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px;
  font-size: 14px;
  flex-shrink: 0;
}

.streamingNotice {
  padding: 10px;
  text-align: center;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  background: rgba(243, 244, 246, 0.9);
  border-top: 1px solid #e5e7eb;
  flex-shrink: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

/* Editor overlay for element selection */
.editorOverlay {
  position: absolute;
  top: 0; /* Relative to the editorPage which is already positioned below the nav */
  left: 30%; /* Match the sidebar width */
  right: 0;
  bottom: 0;
  z-index: 100;
  pointer-events: none;
}

.editorOverlay > * {
  pointer-events: auto;
}

/* Properties panel */
.propertiesPanel {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 320px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 200;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* Generation complete overlay */
.generationCompleteOverlay {
  position: absolute; /* Changed from fixed to absolute */
  bottom: 16px;
  right: 16px;
  z-index: 1000;
}

.generationComplete {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  color: #166534;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 150px;
}
