/**
 * Error Boundary Component
 * Catches React DOM errors and provides fallback UI
 */

import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    console.warn('🛡️ ErrorBoundary caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.warn('🛡️ ErrorBoundary componentDidCatch:', error, errorInfo);
    
    // Don't crash the app for DOM manipulation errors
    if (error.message.includes('removeChild') || error.message.includes('Node')) {
      console.warn('🛡️ Suppressing DOM manipulation error (non-critical)');
      // Reset error state after a brief delay
      setTimeout(() => {
        this.setState({ hasError: false, error: undefined });
      }, 100);
    }
  }

  render() {
    if (this.state.hasError) {
      // For DOM errors, show a minimal fallback that auto-recovers
      if (this.state.error?.message.includes('removeChild') || this.state.error?.message.includes('Node')) {
        return (
          <div className="h-full flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Refreshing view...</p>
            </div>
          </div>
        );
      }

      // For other errors, show a proper error message
      return (
        <div className="h-full flex items-center justify-center bg-red-50">
          <div className="text-center">
            <h3 className="text-lg font-medium text-red-800 mb-2">Something went wrong</h3>
            <p className="text-red-600 text-sm mb-4">{this.state.error?.message}</p>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
