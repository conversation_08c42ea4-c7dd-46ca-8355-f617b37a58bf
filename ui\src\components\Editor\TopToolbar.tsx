/**
 * Top Toolbar Component for EditorPageV3
 * Contains view mode toggles, edit mode controls, and page alignment options
 */

import React from 'react';
import { FiEye, FiCode, FiEdit3, FiAlignLeft, FiAlignCenter, FiAlignRight } from 'react-icons/fi';

interface TopToolbarProps {
  // View mode state
  viewMode: 'preview' | 'code';
  onViewModeChange: (mode: 'preview' | 'code') => void;

  // Edit mode state
  editMode: boolean;
  onEditModeToggle: () => void;







  // Page alignment state
  pageAlignment?: 'left' | 'center' | 'right';
  onPageAlignmentChange?: (alignment: 'left' | 'center' | 'right') => void;

  // Generation state
  isGenerating: boolean;
  hasContent: boolean;

  // Project info
  projectName?: string;
}

export const TopToolbar: React.FC<TopToolbarProps> = ({
  viewMode,
  onViewModeChange,
  editMode,
  onEditModeToggle,
  pageAlignment = 'center',
  onPageAlignmentChange,
  isGenerating,
  hasContent,
  projectName,
}) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-3 flex-shrink-0">
      <div className="flex items-center justify-between">
        {/* Left Section - Project Name */}
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold text-gray-900">
            {projectName || 'Design Editor'}
          </h1>
        </div>

        {/* Center Section - View Mode Toggle & SPA Mode Toggle */}
        <div className="flex items-center space-x-4">
          {hasContent && (
            <>
              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1 border border-gray-200">
                <button
                  onClick={() => onViewModeChange('preview')}
                  disabled={isGenerating}
                  className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-all ${
                    viewMode === 'preview'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <FiEye className="w-4 h-4 mr-1.5" />
                  Preview
                </button>
                <button
                  onClick={() => onViewModeChange('code')}
                  disabled={isGenerating}
                  className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-all ${
                    viewMode === 'code'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <FiCode className="w-4 h-4 mr-1.5" />
                  Code
                </button>
              </div>


            </>
          )}
        </div>

        {/* Right Section - Edit Mode & Alignment Controls */}
        <div className="flex items-center space-x-3">
          {/* Page Alignment Controls */}
          {hasContent && viewMode === 'preview' && onPageAlignmentChange && (
            <div className="flex bg-gray-100 rounded-lg p-1 border border-gray-200">
              <button
                onClick={() => onPageAlignmentChange('left')}
                disabled={isGenerating}
                className={`p-1.5 rounded-md transition-all ${
                  pageAlignment === 'left'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Align Left"
              >
                <FiAlignLeft className="w-4 h-4" />
              </button>
              <button
                onClick={() => onPageAlignmentChange('center')}
                disabled={isGenerating}
                className={`p-1.5 rounded-md transition-all ${
                  pageAlignment === 'center'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Align Center"
              >
                <FiAlignCenter className="w-4 h-4" />
              </button>
              <button
                onClick={() => onPageAlignmentChange('right')}
                disabled={isGenerating}
                className={`p-1.5 rounded-md transition-all ${
                  pageAlignment === 'right'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Align Right"
              >
                <FiAlignRight className="w-4 h-4" />
              </button>
            </div>
          )}

          {/* Edit Mode Toggle - Only show in preview mode */}
          {hasContent && viewMode === 'preview' && (
            <button
              onClick={onEditModeToggle}
              disabled={isGenerating}
              className={`flex items-center px-4 py-1.5 rounded-lg text-sm font-medium transition-all ${
                editMode
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
              } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={editMode ? 'Exit Edit Mode' : 'Enter Edit Mode'}
            >
              <FiEdit3 className="w-4 h-4 mr-1.5" />
              {editMode ? 'Exit Edit' : 'Edit Mode'}
            </button>
          )}

          {/* Generation Status Indicator */}
          {isGenerating && (
            <div className="flex items-center px-3 py-1.5 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
              <span className="text-sm text-blue-700 font-medium">Generating...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopToolbar;
