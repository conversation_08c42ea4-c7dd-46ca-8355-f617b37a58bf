
# Build stage
FROM node:20-alpine AS builder
WORKDIR /app

# Install dependencies using lockfile (for reliable, repeatable builds)
COPY package.json package-lock.json ./
RUN npm ci

# Copy rest of the source code
COPY . .

# Copy environment files
COPY .env.production .env.production

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine
WORKDIR /app

# Install serve globally
RUN npm install -g serve

# Copy built files from previous stage
COPY --from=builder /app/dist ./dist

# Railway expects port 3000 by default, so we'll use that
ENV PORT=3000
EXPOSE 3000
CMD ["serve", "-s", "dist"]


