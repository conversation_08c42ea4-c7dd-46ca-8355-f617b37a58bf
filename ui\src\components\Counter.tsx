import { useCounterContext } from '../contexts/CounterContext';
import { useTheme } from '../contexts/ThemeContext';
import styles from './Counter.module.css';

export function Counter() {
  const { count, increment, decrement, reset } = useCounterContext();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className={`${styles.container} ${isDark ? styles.darkContainer : ''}`}>
      <h2 className={`${styles.title} ${isDark ? styles.darkTitle : ''}`}>Counter Example</h2>
      
      <div className={`${styles.count} ${isDark ? styles.darkCount : ''}`}>
        {count}
      </div>
      
      <div className={styles.buttonGroup}>
        <button
          onClick={decrement}
          className={styles.decrementButton}
          aria-label="Decrement"
        >
          -
        </button>
        
        <button
          onClick={increment}
          className={styles.incrementButton}
          aria-label="Increment"
        >
          +
        </button>
      </div>
      
      <button
        onClick={reset}
        className={`${styles.resetButton} ${isDark ? styles.darkResetButton : ''}`}
      >
        Reset
      </button>
      
      <div className={`${styles.themeInfo} ${isDark ? styles.darkThemeInfo : ''}`}>
        Current theme: {theme}
      </div>
    </div>
  );
}
