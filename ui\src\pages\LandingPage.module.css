.landingRoot {
  background: #fff;
  min-height: 100vh;
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

.nav {
  background: #fff;
  border-bottom: 2px solid #2563eb;
  color: #18181b;
  box-shadow: none;
}

.heroSection {
  background: linear-gradient(120deg, #f8fafc 0%, #e0e7ef 100%);
  padding: 3.5rem 1rem 2.5rem 1rem;
  text-align: center;
  border-bottom: none;
  box-shadow: none;
  position: relative;
}
.heroSection::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: #2563eb;
  border-radius: 2px;
  opacity: 0.12;
}

.heroContent {
  max-width: 600px;
  margin: 0 auto;
}

.heroHeadline {
  font-size: 2.4rem;
  font-weight: 900;
  margin-bottom: 1.1rem;
  color: #18181b;
  letter-spacing: -1px;
  line-height: 1.1;
  position: relative;
}

.heroSubheading {
  font-size: 1.15rem;
  color: #64748b;
  margin-bottom: 1.1rem;
  font-weight: 400;
}

.heroPriceLine {
  font-size: 1.05rem;
  color: #2563eb;
  font-weight: 600;
  margin-bottom: 2rem;
}

.heroCtas {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.primaryCta {
  background: #2563eb;
  color: #fff;
  font-weight: 600;
  padding: 0.65rem 1.7rem;
  border-radius: 999px;
  text-decoration: none;
  font-size: 1.05rem;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  border: none;
  letter-spacing: 0.01em;
}

.primaryCta:hover {
  background: #1e40af;
  color: #fff;
  box-shadow: 0 4px 16px 0 rgba(37,99,235,0.13);
}

.secondaryCta {
  background: none;
  color: #2563eb;
  font-weight: 600;
  margin-left: 1.2rem;
  text-decoration: underline;
  font-size: 1.05rem;
  border: none;
  cursor: pointer;
  padding: 0.65rem 1.2rem;
  border-radius: 999px;
  transition: background 0.15s, color 0.15s;
}

.secondaryCta:hover {
  background: #e0e7ef;
  color: #1e40af;
}

.heroHighlight {
  color: #2563eb;
  background: none;
  font-weight: 900;
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
}

.featuresSection {
  background: #f8fafc;
  padding: 3rem 1rem 3rem 1rem;
  border-top: none;
  border-bottom: none;
}

.featuresGrid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.featureCol {
  flex: 1 1 200px;
  max-width: 260px;
  background: #fff;
  border-radius: 1.1rem;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.06);
  padding: 1.5rem 1rem 1.2rem 1rem;
  text-align: center;
  min-width: 160px;
  border: 1px solid #e5e7eb;
  transition: transform 0.16s cubic-bezier(.4,0,.2,1), box-shadow 0.16s cubic-bezier(.4,0,.2,1);
  cursor: pointer;
}

.featureCol:hover {
  transform: translateY(-4px) scale(1.025);
  box-shadow: 0 6px 24px 0 rgba(37,99,235,0.10);
  border: 1.5px solid #2563eb33;
}

.featureIcon {
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
}

.featureIconCircle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 0.5rem auto;
  box-shadow: 0 1px 4px 0 rgba(37,99,235,0.07);
}

.iconFast {
  background: #e0e7ef;
  color: #2563eb;
}

.iconClean {
  background: #fef9c3;
  color: #eab308;
}

.iconSimple {
  background: #ede9fe;
  color: #7c3aed;
}

.featureTitle {
  font-size: 1.13rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  color: #18181b;
}

.featureDesc {
  color: #64748b;
  font-size: 0.98rem;
  font-weight: 400;
}

.featureCard {
  transition: transform 0.18s cubic-bezier(.4,0,.2,1), box-shadow 0.18s cubic-bezier(.4,0,.2,1);
  cursor: pointer;
}

.featureCard:hover {
  transform: translateY(-6px) scale(1.035);
  box-shadow: 0 8px 32px 0 rgba(37,99,235,0.10);
  border: 1.5px solid #2563eb33;
}

.pricingSection {
  background: #fff;
  padding: 3rem 1rem 2.5rem 1rem;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  text-align: center;
}

.pricingContent {
  max-width: 420px;
  margin: 0 auto;
}

.pricingTitle {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: #23272f;
}

.pricingSubtitle {
  font-size: 1.2rem;
  color: #2563eb;
  margin-bottom: 1.5rem;
}

.pricingBullets {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  color: #23272f;
  font-size: 1.1rem;
}

.pricingBullets li {
  margin-bottom: 0.5rem;
  text-align: left;
  padding-left: 1.5em;
  position: relative;
}

.pricingBullets li:before {
  content: "✔";
  color: #22c55e;
  position: absolute;
  left: 0;
  font-size: 1.1em;
}

.pricingCta {
  display: inline-block;
  background: #2563eb;
  color: #fff;
  font-weight: 600;
  padding: 0.75rem 2.5rem;
  border-radius: 999px;
  text-decoration: none;
  font-size: 1.1rem;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
  transition: background 0.2s;
}

.pricingCta:hover {
  background: #1e40af;
}

.howItWorksSection {
  background: #fff;
  padding: 3rem 1rem 3rem 1rem;
  text-align: center;
  border-top: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
}

.howItWorksTitle {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #18181b;
}

.howSteps {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.howStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1.01rem;
  font-weight: 500;
  color: #23272f;
  min-width: 120px;
}

.howStepIcon {
  font-size: 1.7rem;
  margin-bottom: 0.4rem;
}

.howStepText {
  font-size: 1.01rem;
}

/* Modal styles for feedback/support */
.modalOverlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(24, 24, 27, 0.18);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modalContent {
  background: #fff;
  border-radius: 1.2rem;
  box-shadow: 0 8px 32px 0 rgba(37,99,235,0.13);
  padding: 2.2rem 1.5rem 1.5rem 1.5rem;
  max-width: 380px;
  width: 95vw;
  position: relative;
  animation: fadein 0.3s;
}
.modalClose {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  transition: color 0.15s;
}
.modalClose:hover {
  color: #18181b;
}

/* Animations */
@keyframes fadein {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideup {
  from { opacity: 0; transform: translateY(32px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadein {
  animation: fadein 1.1s cubic-bezier(.4,0,.2,1);
}

.animate-slideup {
  animation: slideup 1.1s cubic-bezier(.4,0,.2,1);
}

@media (max-width: 700px) {
  .heroHeadline {
    font-size: 1.5rem;
  }
  .featuresGrid {
    flex-direction: column;
    gap: 1rem;
  }
  .featureCol {
    max-width: 100%;
    min-width: 0;
  }
  .howSteps {
    flex-direction: column;
    gap: 1rem;
  }
  .pricingContent {
    max-width: 100%;
  }
}
