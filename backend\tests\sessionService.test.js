const sessionService = require('../services/sessionService');
const { pool } = require('../services/promptDbService');

// Mock the database pool for testing
jest.mock('../services/promptDbService', () => ({
  pool: {
    query: jest.fn()
  }
}));

describe('SessionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Stop automatic cleanup for tests
    sessionService.stopAutomaticCleanup();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSession', () => {
    const validSessionData = {
      prototype_id: 1,
      user_id: 1,
      page_url: 'https://example.com',
      page_html: '<html><body>Test</body></html>'
    };

    it('should create a session successfully', async () => {
      // Mock validation queries
      pool.query
        .mockResolvedValueOnce({ rows: [{ id: 1 }] }) // User exists
        .mockResolvedValueOnce({ rows: [{ id: 1 }] }) // Prototype exists
        .mockResolvedValueOnce({ // Session creation
          rows: [{
            id: 'test-session-id',
            prototype_id: 1,
            user_id: 1,
            page_url: 'https://example.com',
            page_html: '<html><body>Test</body></html>',
            session_state: 'active',
            created_at: new Date(),
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000)
          }]
        });

      const result = await sessionService.createSession(validSessionData);

      expect(result).toBeDefined();
      expect(result.id).toBe('test-session-id');
      expect(result.session_state).toBe('active');
      expect(pool.query).toHaveBeenCalledTimes(3);
    });

    it('should throw error for missing required parameters', async () => {
      await expect(sessionService.createSession({})).rejects.toThrow('Missing required parameters');
    });

    it('should throw error for invalid session state', async () => {
      const invalidData = { ...validSessionData, session_state: 'invalid' };
      await expect(sessionService.createSession(invalidData)).rejects.toThrow('Invalid session state');
    });

    it('should throw error if user does not exist', async () => {
      pool.query.mockResolvedValueOnce({ rows: [] }); // User doesn't exist

      await expect(sessionService.createSession(validSessionData)).rejects.toThrow('User with ID 1 does not exist');
    });

    it('should throw error if prototype does not exist', async () => {
      pool.query
        .mockResolvedValueOnce({ rows: [{ id: 1 }] }) // User exists
        .mockResolvedValueOnce({ rows: [] }); // Prototype doesn't exist

      await expect(sessionService.createSession(validSessionData)).rejects.toThrow('Prototype with ID 1 does not exist');
    });
  });

  describe('getSession', () => {
    it('should return session when found and not expired', async () => {
      const mockSession = {
        id: 'test-session-id',
        user_id: 1,
        expires_at: new Date(Date.now() + 60 * 60 * 1000) // 1 hour from now
      };

      pool.query
        .mockResolvedValueOnce({ rows: [mockSession] }) // Get session
        .mockResolvedValueOnce({ rows: [] }); // Update last accessed

      const result = await sessionService.getSession('test-session-id');

      expect(result).toEqual(mockSession);
      expect(pool.query).toHaveBeenCalledTimes(2);
    });

    it('should return null when session not found', async () => {
      pool.query.mockResolvedValueOnce({ rows: [] });

      const result = await sessionService.getSession('non-existent-id');

      expect(result).toBeNull();
    });

    it('should expire and return null for expired session', async () => {
      const expiredSession = {
        id: 'expired-session-id',
        expires_at: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
      };

      pool.query
        .mockResolvedValueOnce({ rows: [expiredSession] }) // Get expired session
        .mockResolvedValueOnce({ rows: [{ ...expiredSession, session_state: 'expired' }] }); // Expire session

      const result = await sessionService.getSession('expired-session-id');

      expect(result).toBeNull();
    });

    it('should validate user ownership when userId provided', async () => {
      const mockSession = {
        id: 'test-session-id',
        user_id: 1,
        expires_at: new Date(Date.now() + 60 * 60 * 1000)
      };

      pool.query
        .mockResolvedValueOnce({ rows: [mockSession] })
        .mockResolvedValueOnce({ rows: [] });

      await sessionService.getSession('test-session-id', 1);

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM prototype_sessions WHERE id = $1 AND user_id = $2',
        ['test-session-id', 1]
      );
    });
  });

  describe('getActiveSession', () => {
    it('should return active session for user and prototype', async () => {
      const mockSession = {
        id: 'active-session-id',
        user_id: 1,
        prototype_id: 1,
        session_state: 'active'
      };

      pool.query
        .mockResolvedValueOnce({ rows: [mockSession] })
        .mockResolvedValueOnce({ rows: [] });

      const result = await sessionService.getActiveSession(1, 1);

      expect(result).toEqual(mockSession);
    });

    it('should return null when no active session found', async () => {
      pool.query.mockResolvedValueOnce({ rows: [] });

      const result = await sessionService.getActiveSession(1, 1);

      expect(result).toBeNull();
    });
  });

  describe('updateSessionState', () => {
    it('should update session state successfully', async () => {
      const updatedSession = {
        id: 'test-session-id',
        session_state: 'completed'
      };

      pool.query.mockResolvedValueOnce({ rows: [updatedSession] });

      const result = await sessionService.updateSessionState('test-session-id', 'completed');

      expect(result).toEqual(updatedSession);
    });

    it('should throw error for invalid session state', async () => {
      await expect(sessionService.updateSessionState('test-id', 'invalid')).rejects.toThrow('Invalid session state');
    });

    it('should return null when session not found', async () => {
      pool.query.mockResolvedValueOnce({ rows: [] });

      const result = await sessionService.updateSessionState('non-existent-id', 'completed');

      expect(result).toBeNull();
    });
  });

  describe('deleteSession', () => {
    it('should delete session successfully', async () => {
      pool.query.mockResolvedValueOnce({ rowCount: 1 });

      const result = await sessionService.deleteSession('test-session-id');

      expect(result).toBe(true);
    });

    it('should return false when session not found', async () => {
      pool.query.mockResolvedValueOnce({ rowCount: 0 });

      const result = await sessionService.deleteSession('non-existent-id');

      expect(result).toBe(false);
    });

    it('should validate user ownership when userId provided', async () => {
      pool.query.mockResolvedValueOnce({ rowCount: 1 });

      await sessionService.deleteSession('test-session-id', 1);

      expect(pool.query).toHaveBeenCalledWith(
        'DELETE FROM prototype_sessions WHERE id = $1 AND user_id = $2',
        ['test-session-id', 1]
      );
    });
  });

  describe('getUserSessions', () => {
    it('should return user sessions with default options', async () => {
      const mockSessions = [
        { id: 'session-1', user_id: 1 },
        { id: 'session-2', user_id: 1 }
      ];

      pool.query.mockResolvedValueOnce({ rows: mockSessions });

      const result = await sessionService.getUserSessions(1);

      expect(result).toEqual(mockSessions);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('WHERE user_id = $1'),
        [1, 10]
      );
    });

    it('should filter by state when provided', async () => {
      pool.query.mockResolvedValueOnce({ rows: [] });

      await sessionService.getUserSessions(1, { state: 'active' });

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('AND session_state = $2'),
        [1, 'active', 10]
      );
    });
  });

  describe('cleanupExpiredSessions', () => {
    it('should clean up expired sessions', async () => {
      pool.query.mockResolvedValueOnce({ rowCount: 5 });

      const result = await sessionService.cleanupExpiredSessions();

      expect(result).toBe(5);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM prototype_sessions')
      );
    });

    it('should force cleanup when force option is true', async () => {
      pool.query.mockResolvedValueOnce({ rowCount: 3 });

      const result = await sessionService.cleanupExpiredSessions({ force: true });

      expect(result).toBe(3);
    });
  });

  describe('getSessionStats', () => {
    it('should return session statistics', async () => {
      const mockStats = [
        { session_state: 'active', count: '5', avg_age_hours: '2.5' },
        { session_state: 'completed', count: '3', avg_age_hours: '10.0' }
      ];

      pool.query.mockResolvedValueOnce({ rows: mockStats });

      const result = await sessionService.getSessionStats();

      expect(result.total).toBe(8);
      expect(result.by_state.active.count).toBe(5);
      expect(result.by_state.completed.count).toBe(3);
    });

    it('should filter by user when userId provided', async () => {
      pool.query.mockResolvedValueOnce({ rows: [] });

      await sessionService.getSessionStats(1);

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('WHERE user_id = $1'),
        [1]
      );
    });
  });
});
