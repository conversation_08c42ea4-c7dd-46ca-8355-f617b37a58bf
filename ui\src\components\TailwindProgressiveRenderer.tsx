import React, { useEffect, useRef, useState, useCallback } from 'react';

interface ReadyAIStyleRendererProps {
  htmlContent?: string;
  streamingContent?: string;
  isGenerating?: boolean;
  onElementClick?: (element: any) => void;
  className?: string;
  maxElements?: number;
  customCSS?: string;
  animationDuration?: number;
}

const TailwindProgressiveRenderer: React.FC<ReadyAIStyleRendererProps> = ({
  htmlContent = '',
  streamingContent = '',
  isGenerating = false,
  onElementClick,
  className = '',
  maxElements = 100,
  customCSS = '',
  animationDuration = 400,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const lastProcessedLengthRef = useRef<number>(0);
  const isProcessingRef = useRef<boolean>(false);
  const processedChunksRef = useRef<Set<string>>(new Set());
  const [stylesLoaded, setStylesLoaded] = useState(false);
  const [hasRenderedFirstElement, setHasRenderedFirstElement] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  // Minimal CSS setup - only progressive rendering styles
  useEffect(() => {
    if (!document.querySelector('#tailwind-progressive-renderer-css')) {
      const progressiveStyles = document.createElement('style');
      progressiveStyles.id = 'tailwind-progressive-renderer-css';
      progressiveStyles.textContent = `
        /* Progressive Animation System */
        .progressive-chunk {
          opacity: 0;
          transform: translateY(12px) scale(0.98);
          transition: all ${animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1);
          will-change: opacity, transform;
        }

        .progressive-chunk.visible {
          opacity: 1;
          transform: translateY(0) scale(1);
        }

        .progressive-chunk.slide-in {
          animation: slideInUp ${animationDuration}ms ease-out forwards;
        }

        /* Smooth wrapper transitions */
        .progressive-renderer.fade-in {
          animation: fadeInWrapper 600ms ease-out forwards;
        }

        /* Enhanced loading skeleton */
        .skeleton-shimmer {
          background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
          background-size: 200% 100%;
          animation: shimmer 1.8s infinite ease-in-out;
        }

        /* Animation keyframes */
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.96);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes fadeInWrapper {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes shimmer {
          0% { background-position: 200% 0; }
          100% { background-position: -200% 0; }
        }

        /* Ensure proper text rendering */
        .progressive-renderer {
          text-rendering: optimizeLegibility;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        /* Custom CSS injection */
        ${customCSS}
      `;
      document.head.appendChild(progressiveStyles);
    }
    setStylesLoaded(true);
  }, [customCSS, animationDuration]);

  // Smart HTML parsing with Tailwind-aware chunking
  const parseHTMLIntoChunks = useCallback((content: string): string[] => {
  const chunks: string[] = [];
  const parser = new DOMParser();

  try {
    const cleanContent = content.trim();
    if (!cleanContent) return [];

    const wrappedContent = `<div>${cleanContent}</div>`;
    const doc = parser.parseFromString(wrappedContent, 'text/html');
    const container = doc.body.firstChild as HTMLElement;

    if (!container) return [];

    const processNode = (node: ChildNode, parentLayoutId?: string) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          chunks.push(`<div class="text-gray-800">${text}</div>`);
        }
        return;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;
        const tagName = element.tagName.toLowerCase();

        const isLayout = ['flex', 'grid', 'container', 'card', 'navbar'].some(cls =>
          element.className.includes(cls)
        );

        // Inject layout shell first
        if (isLayout) {
          const id = element.id || `layout-${Math.random().toString(36).substring(2, 9)}`;
          element.id = id;
          const emptyClone = element.cloneNode(false) as HTMLElement;
          chunks.push(emptyClone.outerHTML);

          // Process children separately
          Array.from(element.childNodes).forEach((child) => processNode(child, id));
        } else {
          // Adjust element styling
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
            const headingClasses = {
              h1: 'text-4xl font-bold text-gray-900 mb-6 mt-8',
              h2: 'text-3xl font-semibold text-gray-800 mb-5 mt-7',
              h3: 'text-2xl font-semibold text-gray-800 mb-4 mt-6',
              h4: 'text-xl font-medium text-gray-700 mb-3 mt-5',
              h5: 'text-lg font-medium text-gray-700 mb-2 mt-4',
              h6: 'text-base font-medium text-gray-600 mb-2 mt-3',
            };
            if (!element.className.includes('text-')) {
              element.className += ' ' + headingClasses[tagName as keyof typeof headingClasses];
            }
          } else if (tagName === 'p' && !element.className.includes('mb-')) {
            element.className += ' mb-4 text-gray-700 leading-relaxed';
          } else if (tagName === 'blockquote') {
            element.className += ' border-l-4 border-blue-500 pl-4 italic text-gray-600 my-4';
          }

          // If the parent layout exists, insert into that wrapper
          if (parentLayoutId) {
            const wrapper = document.createElement('div');
            wrapper.setAttribute('data-parent', parentLayoutId);
            wrapper.innerHTML = element.outerHTML;
            chunks.push(wrapper.innerHTML);
          } else {
            chunks.push(element.outerHTML);
          }

          // Recursively process children if needed
          Array.from(element.childNodes).forEach((child) => processNode(child));
        }
      }
    };

    Array.from(container.childNodes).forEach((child) => processNode(child));

  } catch (err) {
    console.warn('Fallback HTML parser used due to error:', err);
    chunks.push(content);
  }

  return chunks.filter(chunk => chunk.trim().length > 0);
}, []);


  // Enhanced rendering with Tailwind-optimized animations
  const renderChunk = useCallback(async (htmlChunk: string, container: HTMLDivElement): Promise<void> => {
    return new Promise((resolve) => {
      try {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlChunk;
        
        // Create wrapper with Tailwind classes
        const wrapper = document.createElement('div');
        wrapper.className = 'progressive-chunk mb-2';
        
        // Move content to wrapper while preserving structure
        while (tempDiv.firstChild) {
          wrapper.appendChild(tempDiv.firstChild);
        }

        // Enhanced click handling
        if (onElementClick) {
          wrapper.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            
            // Don't interfere with form inputs and links
            if (['input', 'textarea', 'select', 'button', 'a'].includes(target.tagName.toLowerCase())) {
              return;
            }
            
            e.preventDefault();
            onElementClick({
              tagName: target.tagName?.toLowerCase() || '',
              textContent: target.textContent?.trim() || '',
              className: target.className || '',
              id: target.id || '',
              outerHTML: target.outerHTML || '',
              boundingRect: target.getBoundingClientRect(),
            });
          });
        }

        // Add to container
        container.appendChild(wrapper);
        
        // Force reflow for animation
        wrapper.offsetHeight;
        
        // Trigger animation
        requestAnimationFrame(() => {
          wrapper.classList.add('visible', 'slide-in');
          setTimeout(resolve, Math.min(animationDuration * 0.6, 300));
        });
        
      } catch (error) {
        console.error('Error rendering chunk:', error);
        resolve();
      }
    });
  }, [onElementClick, animationDuration]);

  // Main content processing effect
  useEffect(() => {
    if (!stylesLoaded || isProcessingRef.current) return;

    const container = containerRef.current;
    if (!container) return;

    const currentContent = isGenerating ? streamingContent : htmlContent;
    if (!currentContent?.trim()) return;

    const currentLength = currentContent.length;
    if (currentLength <= lastProcessedLengthRef.current) return;

    isProcessingRef.current = true;

    const processContent = async () => {
      try {
        const newContent = currentContent.substring(lastProcessedLengthRef.current);
        const newChunks = parseHTMLIntoChunks(newContent);

        for (const chunk of newChunks) {
          if (!container.isConnected) break;
          
          // Create a simple hash for deduplication
          const chunkHash = chunk.substring(0, 50) + chunk.length;
          if (processedChunksRef.current.has(chunkHash)) continue;

          processedChunksRef.current.add(chunkHash);
          await renderChunk(chunk, container);

          if (!hasRenderedFirstElement) {
            setHasRenderedFirstElement(true);
          }

          // Manage container size efficiently
          if (container.children.length > maxElements) {
            const firstChild = container.firstChild;
            if (firstChild) {
              container.removeChild(firstChild);
            }
          }
        }

        lastProcessedLengthRef.current = currentLength;
        
        if (!isGenerating) {
          setTimeout(() => setIsComplete(true), 500);
        }
      } catch (error) {
        console.error('Error processing content:', error);
      } finally {
        isProcessingRef.current = false;
      }
    };

    processContent();
  }, [htmlContent, streamingContent, isGenerating, stylesLoaded, hasRenderedFirstElement, maxElements, parseHTMLIntoChunks, renderChunk]);

  // Content reset handler
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const currentContent = isGenerating ? streamingContent : htmlContent;
    
    // Reset if content is significantly shorter (likely new content)
    if (currentContent.length < lastProcessedLengthRef.current * 0.3) {
      container.innerHTML = '';
      lastProcessedLengthRef.current = 0;
      processedChunksRef.current.clear();
      setHasRenderedFirstElement(false);
      setIsComplete(false);
    }
  }, [htmlContent, streamingContent, isGenerating]);

  // Loading state with Tailwind skeleton
  if (!stylesLoaded) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-sm text-gray-500">Preparing renderer...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      {/* Enhanced skeleton loader */}
      {!hasRenderedFirstElement && (
        <div className="space-y-6 p-4 animate-pulse">
          <div className="space-y-3">
            <div className="h-8 bg-gray-200 rounded-lg w-3/4 skeleton-shimmer"></div>
            <div className="h-4 bg-gray-200 rounded w-full skeleton-shimmer"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6 skeleton-shimmer"></div>
            <div className="h-4 bg-gray-200 rounded w-4/5 skeleton-shimmer"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-2/3 skeleton-shimmer"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 skeleton-shimmer"></div>
          </div>
          <div className="flex space-x-4">
            <div className="h-20 w-20 bg-gray-200 rounded-lg skeleton-shimmer"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded skeleton-shimmer"></div>
              <div className="h-4 bg-gray-200 rounded w-4/5 skeleton-shimmer"></div>
            </div>
          </div>
        </div>
      )}
      
      {/* Main renderer container */}
      <div
        ref={containerRef}
        className={`progressive-renderer ${hasRenderedFirstElement ? 'fade-in' : ''} ${className}`}
        style={{ 
          minHeight: hasRenderedFirstElement ? 'auto' : '300px',
          opacity: hasRenderedFirstElement ? 1 : 0,
          transition: 'opacity 600ms ease-out'
        }}
      />
      
      {/* Completion indicator */}
      {isComplete && (
        <div className="flex items-center justify-center mt-6 p-4">
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-2 rounded-full text-sm font-medium flex items-center shadow-sm">
            <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Content rendered successfully
          </div>
        </div>
      )}
    </div>
  );
};

export default TailwindProgressiveRenderer;