/**
 * Component Registry
 * Manages component lifecycle and instantiation
 */

interface ComponentInstance {
  id: string;
  type: string;
  instance: any;
  element: HTMLElement;
  config: any;
}

export class ComponentRegistry {
  private components = new Map<string, any>();
  private instances = new Map<string, ComponentInstance>();
  private instanceCounter = 0;

  /**
   * Register a component class
   */
  register(name: string, componentClass: any): void {
    this.components.set(name, componentClass);
    console.log(`🔧 Component registered: ${name}`);
  }

  /**
   * Initialize component on element
   */
  initialize(type: string, element: HTMLElement, config: any = {}): string | null {
    const ComponentClass = this.components.get(type);
    if (!ComponentClass) {
      console.warn(`⚠️ Component type '${type}' not registered`);
      return null;
    }

    try {
      // Generate unique instance ID
      const instanceId = `${type}_${++this.instanceCounter}`;
      element.setAttribute('data-component-id', instanceId);

      // Create component instance
      const instance = new ComponentClass(element, config);

      // Store instance
      this.instances.set(instanceId, {
        id: instanceId,
        type,
        instance,
        element,
        config
      });

      console.log(`✅ Component initialized: ${type} (${instanceId})`);
      return instanceId;

    } catch (error) {
      console.error(`❌ Error initializing component ${type}:`, error);
      return null;
    }
  }

  /**
   * Get component instance by ID
   */
  getInstance(id: string): ComponentInstance | null {
    return this.instances.get(id) || null;
  }

  /**
   * Get component instance by element
   */
  getInstanceByElement(element: HTMLElement): ComponentInstance | null {
    const instanceId = element.getAttribute('data-component-id');
    return instanceId ? this.getInstance(instanceId) : null;
  }

  /**
   * Destroy component instance
   */
  destroy(id: string): boolean {
    const componentInstance = this.instances.get(id);
    if (!componentInstance) {
      return false;
    }

    try {
      // Call destroy method if available
      if (typeof componentInstance.instance.destroy === 'function') {
        componentInstance.instance.destroy();
      }

      // Remove from registry
      this.instances.delete(id);

      // Remove data attribute
      componentInstance.element.removeAttribute('data-component-id');

      console.log(`🗑️ Component destroyed: ${componentInstance.type} (${id})`);
      return true;

    } catch (error) {
      console.error(`❌ Error destroying component ${id}:`, error);
      return false;
    }
  }

  /**
   * Destroy all components in an element tree
   */
  destroyInElement(element: HTMLElement): void {
    const componentsInElement = element.querySelectorAll('[data-component-id]');
    componentsInElement.forEach(el => {
      const instanceId = el.getAttribute('data-component-id');
      if (instanceId) {
        this.destroy(instanceId);
      }
    });
  }

  /**
   * Reinitialize components in an element tree
   */
  reinitialize(element: HTMLElement): void {
    // Find all elements with data-component attribute
    const components = element.querySelectorAll('[data-component]');
    
    components.forEach(componentEl => {
      const componentType = componentEl.getAttribute('data-component');
      if (componentType) {
        const config = this.extractDataParams(componentEl as HTMLElement);
        this.initialize(componentType, componentEl as HTMLElement, config);
      }
    });
  }

  /**
   * Update component configuration
   */
  updateConfig(id: string, newConfig: any): boolean {
    const componentInstance = this.instances.get(id);
    if (!componentInstance) {
      return false;
    }

    try {
      // Merge configurations
      componentInstance.config = { ...componentInstance.config, ...newConfig };

      // Call update method if available
      if (typeof componentInstance.instance.updateConfig === 'function') {
        componentInstance.instance.updateConfig(componentInstance.config);
      }

      console.log(`🔄 Component config updated: ${componentInstance.type} (${id})`);
      return true;

    } catch (error) {
      console.error(`❌ Error updating component config ${id}:`, error);
      return false;
    }
  }

  /**
   * Extract data parameters from element
   */
  private extractDataParams(element: HTMLElement): any {
    const params: any = {};
    
    Array.from(element.attributes).forEach(attr => {
      if (attr.name.startsWith('data-') && 
          !['data-component', 'data-component-id'].includes(attr.name)) {
        const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
        
        // Try to parse as JSON, fallback to string
        try {
          params[key] = JSON.parse(attr.value);
        } catch {
          params[key] = attr.value;
        }
      }
    });
    
    return params;
  }

  /**
   * Get all instances of a component type
   */
  getInstancesByType(type: string): ComponentInstance[] {
    return Array.from(this.instances.values()).filter(instance => instance.type === type);
  }

  /**
   * Get all registered component types
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.components.keys());
  }

  /**
   * Get total instance count
   */
  getInstanceCount(): number {
    return this.instances.size;
  }

  /**
   * Get registry statistics
   */
  getStats(): any {
    const typeStats: { [key: string]: number } = {};
    
    this.instances.forEach(instance => {
      typeStats[instance.type] = (typeStats[instance.type] || 0) + 1;
    });

    return {
      totalInstances: this.instances.size,
      registeredTypes: this.components.size,
      typeDistribution: typeStats
    };
  }

  /**
   * Clear all instances (for cleanup)
   */
  clear(): void {
    // Destroy all instances
    const instanceIds = Array.from(this.instances.keys());
    instanceIds.forEach(id => this.destroy(id));

    console.log('🧹 Component registry cleared');
  }

  /**
   * Check if component type is registered
   */
  isRegistered(type: string): boolean {
    return this.components.has(type);
  }

  /**
   * Refresh component (destroy and recreate)
   */
  refresh(id: string): boolean {
    const componentInstance = this.instances.get(id);
    if (!componentInstance) {
      return false;
    }

    const { type, element, config } = componentInstance;
    
    // Destroy current instance
    this.destroy(id);
    
    // Recreate with same configuration
    const newId = this.initialize(type, element, config);
    
    console.log(`🔄 Component refreshed: ${type} (${id} -> ${newId})`);
    return newId !== null;
  }
}
