// intent.test.js
// Integration tests for the Intent Generation API

const express = require('express');
const bodyParser = require('body-parser');
const request = require('supertest');
const intentRoute = require('../routes/intent');

const app = express();
app.use(bodyParser.json());
app.use(intentRoute);

describe('POST /api/llm/v3/intent', () => {
  it('should return prompt and intent for valid HTML', async () => {
    const html = '<button id="saveBtn" class="btn">Save</button>';
    const res = await request(app)
      .post('/api/llm/v3/intent')
      .send({ html });
    expect(res.statusCode).toBe(200);
    expect(res.body.prompt).toContain('Selector:');
    expect(Array.isArray(res.body.intent)).toBe(true);
    expect(res.body.intent[0]).toHaveProperty('selector');
    expect(res.body.intent[0]).toHaveProperty('intent');
  });

  it('should return 400 for missing HTML', async () => {
    const res = await request(app)
      .post('/api/llm/v3/intent')
      .send({});
    expect(res.statusCode).toBe(400);
    expect(res.body.error).toBe('Missing HTML input');
  });
});
