/**
 * Implementation Choice Modal Component
 * Modal for choosing how to implement selected features
 */

import React from 'react';

interface ImplementationModalProps {
  showImplementModal: boolean;
  selectedElement: any;
  customFunctionality: string;
  isGeneratingIntent: boolean;
  onCloseImplementModal: () => void;
  onSetCustomFunctionality: (value: string) => void;
  onImplementChoice: (choice: 'inline' | 'modal' | 'page') => void;
}

export const ImplementationModal: React.FC<ImplementationModalProps> = ({
  showImplementModal,
  selectedElement,
  customFunctionality,
  isGeneratingIntent,
  onCloseImplementModal,
  onSetCustomFunctionality,
  onImplementChoice,
}) => {
  if (!showImplementModal || !selectedElement) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-900">Implement Feature</h3>
            <button
              onClick={onCloseImplementModal}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Selected Element Info */}
          <div className="flex items-start space-x-3 mb-6 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-900">"{selectedElement.textContent}"</p>
              <p className="text-sm text-gray-500">{selectedElement.implementationReason}</p>
            </div>
          </div>

          {/* Intent Generation Progress */}
          {isGeneratingIntent && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                <div>
                  <p className="text-sm font-medium text-blue-800">Analyzing your request...</p>
                  <p className="text-xs text-blue-600">This helps me understand exactly what you want to implement</p>
                </div>
              </div>
            </div>
          )}

          {/* Custom Functionality Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Describe the functionality you want to implement:
            </label>
            <textarea
              value={customFunctionality}
              onChange={(e) => onSetCustomFunctionality(e.target.value)}
              placeholder={`Examples:
• "Rename this to Submit to SSO"
• "Change to Google Login button"
• "Add form validation"
• "Make this redirect to dashboard"`}
              className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
            />
          </div>

          {/* Implementation Options */}
          <div className="space-y-3 mb-6">
            <h4 className="text-sm font-medium text-gray-700">Choose implementation method:</h4>

            {/* Inline Implementation */}
            <button
              onClick={() => onImplementChoice('inline')}
              disabled={!customFunctionality.trim()}
              className={`w-full p-4 text-left border rounded-lg transition-colors group ${
                customFunctionality.trim()
                  ? 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                  : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="font-medium text-gray-900">Modify Current Page</h5>
                  <p className="text-sm text-gray-500 mt-1">Update this element directly on the current page</p>
                </div>
                <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </button>

            {/* Modal Implementation */}
            <button
              onClick={() => onImplementChoice('modal')}
              disabled={!customFunctionality.trim()}
              className={`w-full p-4 text-left border rounded-lg transition-colors group ${
                customFunctionality.trim()
                  ? 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                  : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="font-medium text-gray-900">Add Modal/Popup</h5>
                  <p className="text-sm text-gray-500 mt-1">Create a modal or popup for this functionality</p>
                </div>
                <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </button>

            {/* New Page Implementation */}
            <button
              onClick={() => onImplementChoice('page')}
              disabled={!customFunctionality.trim()}
              className={`w-full p-4 text-left border rounded-lg transition-colors group ${
                customFunctionality.trim()
                  ? 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                  : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="font-medium text-gray-900">Create New Page</h5>
                  <p className="text-sm text-gray-500 mt-1">Build a dedicated page for this functionality</p>
                </div>
                <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </button>
          </div>

          <div className="flex justify-end">
            <button
              onClick={onCloseImplementModal}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImplementationModal;
