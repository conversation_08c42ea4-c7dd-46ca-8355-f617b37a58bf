const express = require('express');
const router = express.Router();
const { generatePlan, generateCodeWithAST } = require('../controllers/llmControllerV2');

/**
 * @swagger
 * /api/llm/v2/plan:
 *   get:
 *     summary: Generate a plan from a prompt using LLMv2
 *     parameters:
 *       - name: prompt
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *       - name: provider
 *         in: query
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successfully generated plan (SSE stream)
 */
router.get('/plan', generatePlan);

/**
 * @swagger
 * /api/llm/v2/generate-with-ast:
 *   post:
 *     summary: Generate code with AST from a plan using LLMv2
 *     parameters:
 *       - name: plan
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *       - name: provider
 *         in: query
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               language:
 *                 type: string
 *               transformations:
 *                 type: array
 *               format:
 *                 type: boolean
 *               parseAST:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Successfully generated code with AST (SSE stream)
 */
router.post('/generate-with-ast', express.json(), generateCodeWithAST);

// Also support GET for testing
router.get('/generate-with-ast', generateCodeWithAST);

module.exports = router;
