const codeAstService = require('../services/ast/codeAstService');
const transformationHooks = require('../services/ast/transformationHooks');

/**
 * Example demonstrating the Code AST Pipeline functionality
 */
async function demonstrateCodeASTPipeline() {
  console.log('🚀 Code AST Pipeline Demonstration\n');

  // Example 1: Parse HTML with embedded JavaScript
  console.log('1️⃣ Parsing HTML with embedded JavaScript...');
  const htmlCode = `
<!DOCTYPE html>
<html>
<head>
    <title>Sample Page</title>
    <style>
        .header { background-color: blue; padding: 20px; }
        .content { margin: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome</h1>
    </div>
    <div class="content">
        <p>This is a sample page.</p>
        <button onclick="showAlert()">Click me</button>
    </div>
    <script>
        function showAlert() {
            alert('Hello World!');
        }
        
        const userName = 'John';
        console.log('User:', userName);
    </script>
</body>
</html>
  `;

  try {
    const htmlAst = codeAstService.parseCode(htmlCode, 'html');
    console.log('✅ HTML parsed successfully');
    console.log(`   - Found ${htmlAst.scripts.length} script tags`);
    console.log(`   - Found ${htmlAst.styles.length} style tags`);
    
    if (htmlAst.scripts.length > 0) {
      const firstScript = htmlAst.scripts[0];
      if (firstScript.ast) {
        const metadata = codeAstService.extractMetadata(firstScript.content, 'javascript');
        console.log(`   - JavaScript functions: ${metadata.functions.map(f => f.name).join(', ')}`);
        console.log(`   - JavaScript variables: ${metadata.variables.map(v => v.name).join(', ')}`);
      }
    }
  } catch (error) {
    console.error('❌ HTML parsing failed:', error.message);
  }

  console.log('\n');

  // Example 2: Parse and transform JavaScript code
  console.log('2️⃣ Parsing and transforming JavaScript code...');
  const jsCode = `
import React from 'react';
import { useState, useEffect } from 'react';
import lodash from 'lodash';

function MyComponent() {
    const [count, setCount] = useState(0);
    const [unused, setUnused] = useState('');
    
    function incrementCount() {
        setCount(count + 1);
    }
    
    function unusedFunction() {
        console.log('This function is never called');
    }
    
    useEffect(() => {
        console.log('Component mounted');
    }, []);
    
    return (
        <div>
            <h1>Count: {count}</h1>
            <button onClick={incrementCount}>Increment</button>
        </div>
    );
}

export default MyComponent;
  `;

  try {
    const jsAst = codeAstService.parseCode(jsCode, 'javascript');
    console.log('✅ JavaScript parsed successfully');
    
    const metadata = codeAstService.extractMetadata(jsCode, 'javascript');
    console.log(`   - Imports: ${metadata.imports.map(i => i.source).join(', ')}`);
    console.log(`   - Functions: ${metadata.functions.map(f => f.name || 'anonymous').join(', ')}`);
    console.log(`   - Variables: ${metadata.variables.map(v => v.name).join(', ')}`);

    // Apply transformations
    console.log('\n   Applying transformations...');
    const transformations = [
      { type: 'optimizeImports', config: {} },
      { type: 'removeDeadCode', config: { keepFunctions: ['MyComponent'] } },
      { type: 'renameVariable', config: { oldName: 'count', newName: 'counter' } }
    ];

    const transformedAst = await codeAstService.transformAST(jsAst, transformations, 'javascript');
    const transformedCode = codeAstService.printCode(transformedAst, 'javascript', { format: true });
    
    console.log('✅ Transformations applied successfully');
    console.log('   - Optimized imports');
    console.log('   - Removed dead code');
    console.log('   - Renamed variable: count → counter');
    
  } catch (error) {
    console.error('❌ JavaScript transformation failed:', error.message);
  }

  console.log('\n');

  // Example 3: CSS parsing and optimization
  console.log('3️⃣ Parsing CSS code...');
  const cssCode = `
.header {
    background-color: #3498db;
    padding: 20px;
    margin: 0;
    font-size: 24px;
}

.content {
    margin: 20px;
    padding: 15px;
    border: 1px solid #ccc;
}

.unused-class {
    color: red;
    font-weight: bold;
}

@media (max-width: 768px) {
    .header {
        padding: 10px;
        font-size: 18px;
    }
}
  `;

  try {
    const cssAst = codeAstService.parseCode(cssCode, 'css');
    console.log('✅ CSS parsed successfully');
    
    const formattedCss = codeAstService.printCode(cssAst, 'css', { format: true });
    console.log('✅ CSS formatted successfully');
    
  } catch (error) {
    console.error('❌ CSS parsing failed:', error.message);
  }

  console.log('\n');

  // Example 4: Web optimizations
  console.log('4️⃣ Applying web optimizations...');
  const webCode = `
<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <img src="example.jpg">
    <input type="text">
    <button>Click me</button>
    <div onclick="handleClick()">Clickable div</div>
    
    <script>
        function handleClick() {
            console.log('Clicked');
        }
        
        function anotherUnusedFunction() {
            return 'unused';
        }
    </script>
</body>
</html>
  `;

  try {
    const webAst = codeAstService.parseCode(webCode, 'html');
    const optimizedAst = await transformationHooks.applyWebOptimizations(webAst, 'html');
    const optimizedCode = codeAstService.printCode(optimizedAst, 'html', { format: true });
    
    console.log('✅ Web optimizations applied successfully');
    console.log('   - HTML sanitized (removed dangerous attributes)');
    console.log('   - Accessibility improvements added');
    console.log('   - JavaScript optimized');
    
  } catch (error) {
    console.error('❌ Web optimization failed:', error.message);
  }

  console.log('\n');

  // Example 5: Syntax validation
  console.log('5️⃣ Validating syntax...');
  const invalidJsCode = `
function broken() {
    console.log('Missing closing brace'
}
  `;

  const validationResult = codeAstService.validateSyntax(invalidJsCode, 'javascript');
  if (validationResult.valid) {
    console.log('✅ Code syntax is valid');
  } else {
    console.log('❌ Code syntax is invalid:');
    validationResult.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }

  console.log('\n');

  // Example 6: Available transformation hooks
  console.log('6️⃣ Available transformation hooks:');
  const availableHooks = transformationHooks.getAvailableHooks();
  availableHooks.forEach(hook => {
    console.log(`   - ${hook}`);
  });

  console.log('\n');

  // Example 7: Supported languages
  console.log('7️⃣ Supported languages:');
  const supportedLanguages = codeAstService.getSupportedLanguages();
  supportedLanguages.forEach(lang => {
    console.log(`   - ${lang}`);
  });

  console.log('\n🎉 Code AST Pipeline demonstration completed!');
}

// Run the demonstration if this file is executed directly
if (require.main === module) {
  demonstrateCodeASTPipeline()
    .then(() => {
      console.log('\n✨ All examples completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Demonstration failed:', error);
      process.exit(1);
    });
}

module.exports = { demonstrateCodeASTPipeline };
