const express = require('express');
const router = express.Router();
const astController = require('../controllers/astController');
const { ensureAuthenticated } = require('../auth/googleAuth');
const createStreamCapture = require('../services/sse/StreamCaptureMiddleware');

// Add stream capture for plan generation
const planStreamCapture = (req, res, next) => {
  const capture = createStreamCapture(req, 'ast_plan', req.body.prompt);
  capture.middleware(req, res, next);
};

/**
 * @swagger
 * /api/ast/current:
 *   get:
 *     summary: Get current AST state
 *     tags: [AST]
 *     responses:
 *       200:
 *         description: Current AST state
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 snapshotId:
 *                   type: string
 *                 ast:
 *                   $ref: '#/components/schemas/UINode'
 */
router.get('/current', ensureAuthenticated, astController.getCurrentAST);

/**
 * @swagger
 * /api/ast/plan:
 *   post:
 *     summary: Generate AST from plan using LLM
 *     tags: [AST]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               prompt:
 *                 type: string
 *               provider:
 *                 type: string
 *     responses:
 *       200:
 *         description: SSE stream of plan generation
 *         content:
 *           text/event-stream:
 *             schema:
 *               type: object
 */
router.post('/plan', ensureAuthenticated, planStreamCapture, astController.generatePlan);

/**
 * @swagger
 * /api/ast/patch:
 *   post:
 *     summary: Apply patch to AST
 *     tags: [AST]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               snapshotId:
 *                 type: string
 *               patch:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       200:
 *         description: Updated AST state
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 snapshotId:
 *                   type: string
 *                 ast:
 *                   $ref: '#/components/schemas/UINode'
 */
router.post('/patch', ensureAuthenticated, astController.patchAST);

/**
 * @swagger
 * /api/ast/snapshots:
 *   get:
 *     summary: List available snapshots
 *     tags: [AST]
 *     responses:
 *       200:
 *         description: List of snapshot IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 */
router.get('/snapshots', ensureAuthenticated, astController.listSnapshots);

/**
 * @swagger
 * /api/ast/snapshots/{id}:
 *   get:
 *     summary: Get specific snapshot
 *     tags: [AST]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Snapshot details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 ast:
 *                   $ref: '#/components/schemas/UINode'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 message:
 *                   type: string
 */
router.get('/snapshots/:id', ensureAuthenticated, astController.getSnapshot);

module.exports = router;
