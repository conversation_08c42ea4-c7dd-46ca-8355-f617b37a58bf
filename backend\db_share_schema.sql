-- PostgreSQL schema for JustPrototype sharing functionality

-- Create shares table for tracking prototype sharing
CREATE TABLE IF NOT EXISTS shares (
    id SERIAL PRIMARY KEY,
    prototype_id INTEGER NOT NULL, -- References the prototype being shared
    owner_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    shared_with_email VARCHAR(255), -- Email of the person the prototype is shared with (optional for public shares)
    shared_with_user_id INTEGER REFERENCES users(id) ON DELETE SET NULL, -- User ID if the shared person has an account
    access_level VARCHAR(20) NOT NULL DEFAULT 'view' CHECK (access_level IN ('view', 'comment', 'edit')),
    access_token VARCHAR(64) NOT NULL UNIQUE, -- Unique token for accessing the shared prototype
    is_public BOOLEAN NOT NULL DEFAULT FALSE, -- Whether this is a public share (anyone with the link)
    is_active BOOLEAN NOT NULL DEFAULT TRUE, -- Whether this share is active
    expires_at TIMESTAMP WITH TIME ZONE, -- Optional expiration date
    has_accessed BOOLEAN NOT NULL DEFAULT FALSE, -- Whether the shared user has accessed the prototype
    first_accessed_at TIMESTAMP WITH TIME ZONE, -- When the shared user first accessed the prototype
    last_accessed_at TIMESTAMP WITH TIME ZONE, -- When the shared user last accessed the prototype
    access_count INTEGER NOT NULL DEFAULT 0, -- Number of times the shared user has accessed the prototype
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_shares_prototype_id ON shares(prototype_id);
CREATE INDEX IF NOT EXISTS idx_shares_owner_id ON shares(owner_id);
CREATE INDEX IF NOT EXISTS idx_shares_shared_with_email ON shares(shared_with_email);
CREATE INDEX IF NOT EXISTS idx_shares_shared_with_user_id ON shares(shared_with_user_id);
CREATE INDEX IF NOT EXISTS idx_shares_access_token ON shares(access_token);

-- Create compound index for prototype + owner for quick lookups
CREATE INDEX IF NOT EXISTS idx_shares_owner_prototype ON shares(owner_id, prototype_id);

-- Create unique constraint for prototype + shared email (only for private shares)
CREATE UNIQUE INDEX IF NOT EXISTS idx_shares_prototype_email_unique 
ON shares(prototype_id, shared_with_email) 
WHERE is_public = FALSE AND shared_with_email IS NOT NULL;

-- Trigger to auto-update updated_at on shares row update
CREATE OR REPLACE FUNCTION update_shares_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_shares_updated_at ON shares;
CREATE TRIGGER set_shares_updated_at
BEFORE UPDATE ON shares
FOR EACH ROW
EXECUTE FUNCTION update_shares_updated_at();

-- Function to record an access to a shared prototype
CREATE OR REPLACE FUNCTION record_share_access(p_access_token VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
    share_record RECORD;
BEGIN
    -- Get the share record
    SELECT * INTO share_record FROM shares 
    WHERE access_token = p_access_token AND is_active = TRUE
    FOR UPDATE;
    
    -- Check if share exists and is not expired
    IF share_record IS NULL OR (share_record.expires_at IS NOT NULL AND share_record.expires_at < NOW()) THEN
        RETURN FALSE;
    END IF;
    
    -- Update access information
    UPDATE shares SET
        has_accessed = TRUE,
        first_accessed_at = CASE WHEN first_accessed_at IS NULL THEN NOW() ELSE first_accessed_at END,
        last_accessed_at = NOW(),
        access_count = access_count + 1
    WHERE access_token = p_access_token;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to create a new share
CREATE OR REPLACE FUNCTION create_share(
    p_prototype_id INTEGER,
    p_owner_id INTEGER,
    p_shared_with_email VARCHAR DEFAULT NULL,
    p_is_public BOOLEAN DEFAULT FALSE,
    p_access_level VARCHAR DEFAULT 'view',
    p_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
) RETURNS TABLE (
    share_id INTEGER,
    access_token VARCHAR
) AS $$
DECLARE
    v_access_token VARCHAR;
    v_shared_with_user_id INTEGER;
    v_share_id INTEGER;
BEGIN
    -- Validate owner
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = p_owner_id) THEN
        RAISE EXCEPTION 'Owner does not exist';
    END IF;
    
    -- For private shares, email is required
    IF NOT p_is_public AND p_shared_with_email IS NULL THEN
        RAISE EXCEPTION 'Email is required for private shares';
    END IF;
    
    -- Check if the user being shared with has an account
    IF p_shared_with_email IS NOT NULL THEN
        SELECT id INTO v_shared_with_user_id FROM users WHERE email = p_shared_with_email LIMIT 1;
    END IF;
    
    -- Generate a secure random access token (32 bytes hex = 64 chars)
    v_access_token := encode(gen_random_bytes(32), 'hex');
    
    -- Create the share
    INSERT INTO shares (
        prototype_id,
        owner_id,
        shared_with_email,
        shared_with_user_id,
        is_public,
        access_level,
        access_token,
        expires_at
    ) VALUES (
        p_prototype_id,
        p_owner_id,
        p_shared_with_email,
        v_shared_with_user_id,
        p_is_public,
        p_access_level,
        v_access_token,
        p_expires_at
    ) RETURNING id INTO v_share_id;
    
    RETURN QUERY SELECT v_share_id, v_access_token;
END;
$$ LANGUAGE plpgsql;

-- Function to validate access to a shared prototype
CREATE OR REPLACE FUNCTION validate_share_access(
    p_prototype_id INTEGER,
    p_email VARCHAR DEFAULT NULL,
    p_access_token VARCHAR DEFAULT NULL
) RETURNS TABLE (
    has_access BOOLEAN,
    access_level VARCHAR,
    is_public BOOLEAN
) AS $$
BEGIN
    -- First try to find by access token
    IF p_access_token IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            TRUE as has_access,
            s.access_level,
            s.is_public
        FROM shares s
        WHERE 
            s.access_token = p_access_token
            AND s.is_active = TRUE
            AND s.prototype_id = p_prototype_id
            AND (s.expires_at IS NULL OR s.expires_at > NOW())
        LIMIT 1;
        
        -- If we found a match, record the access and return
        IF FOUND THEN
            PERFORM record_share_access(p_access_token);
            RETURN;
        END IF;
    END IF;
    
    -- If no valid token or token doesn't match, check by email
    IF p_email IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            TRUE as has_access,
            s.access_level,
            s.is_public
        FROM shares s
        WHERE 
            s.shared_with_email = p_email
            AND s.is_active = TRUE
            AND s.prototype_id = p_prototype_id
            AND (s.expires_at IS NULL OR s.expires_at > NOW())
        LIMIT 1;
        
        -- If we found a match, return
        IF FOUND THEN
            RETURN;
        END IF;
    END IF;
    
    -- No access found
    RETURN QUERY SELECT FALSE as has_access, NULL::VARCHAR as access_level, NULL::BOOLEAN as is_public;
END;
$$ LANGUAGE plpgsql;
