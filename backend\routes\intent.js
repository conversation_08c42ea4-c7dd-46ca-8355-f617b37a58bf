/**
 * Intent Generation API Routes
 * Implements Readdy.ai-style intent generation with session support
 */

const express = require('express');
const router = express.Router();
const llmServiceV3 = require('../services/llmServiceV3');
const sessionService = require('../services/sessionService');

/**
 * POST /api/intent/generate
 * Generate intent from element selection (Readdy.ai Phase 1)
 */
router.post('/generate', async (req, res) => {
  try {
    const { sessionId, elementCode, elementSelector, interactionType } = req.body;

    // Validate required fields
    if (!sessionId || !elementCode || !elementSelector) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'Missing required fields: sessionId, elementCode, elementSelector'
        }
      });
    }

    // Generate intent using session context
    const result = await llmServiceV3.generateIntentFromSession(
      sessionId,
      elementCode,
      elementSelector
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INTENT_GENERATION_FAILED',
          message: result.error
        }
      });
    }

    res.json({
      success: true,
      intent: result.intent
    });

  } catch (error) {
    console.error('Error generating intent:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/intent/batch
 * Generate intents for multiple elements
 */
router.post('/batch', async (req, res) => {
  try {
    const { sessionId, elements } = req.body;

    // Validate required fields
    if (!sessionId || !elements || !Array.isArray(elements)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'Missing required fields: sessionId, elements (array)'
        }
      });
    }

    // Generate batch intents
    const result = await llmServiceV3.generateBatchIntents(sessionId, elements);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'BATCH_INTENT_GENERATION_FAILED',
          message: result.error
        }
      });
    }

    res.json({
      success: true,
      results: result.results,
      sessionId: result.sessionId
    });

  } catch (error) {
    console.error('Error generating batch intents:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * Legacy endpoint for backward compatibility
 * POST /api/llm/v3/intent
 */
router.post('/api/llm/v3/intent', async (req, res) => {
  try {
    const { html, elementCode } = req.body;

    if (!html) {
      return res.status(400).json({
        error: 'Missing HTML input',
        suggestion: 'Use /api/intent/generate with sessionId for better performance'
      });
    }

    // Use legacy approach for backward compatibility
    const result = await llmServiceV3.generateIntent(
      elementCode || '<div>No element specified</div>',
      html
    );

    if (result.success) {
      res.json({
        success: true,
        intent: result.intent,
        deprecated: true,
        migration: 'Consider using session-based /api/intent/generate endpoint'
      });
    } else {
      res.status(400).json({
        error: result.error,
        deprecated: true
      });
    }

  } catch (error) {
    console.error('Error in legacy intent endpoint:', error);
    res.status(500).json({
      error: error.message,
      deprecated: true
    });
  }
});

module.exports = router;
