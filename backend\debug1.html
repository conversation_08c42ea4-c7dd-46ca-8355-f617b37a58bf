<div id="app">
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-blue-600">Customer Support Portal</h1>
        <nav class="flex space-x-8">
          <button onclick="showPage('dashboard')" class="text-gray-600 hover:text-blue-600">Dashboard</button>
          <button onclick="showPage('tickets')" class="text-gray-600 hover:text-blue-600">My Tickets</button>
          <button onclick="showPage('knowledge')" class="text-gray-600 hover:text-blue-600">Knowledge Base</button>
          <button onclick="showPage('status')" class="text-gray-600 hover:text-blue-600">Service Status</button>
        </nav>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
      <!-- Dashboard Page -->
      <div id="page-dashboard" class="page space-y-6" style="display:none">
        <h2 class="text-xl font-semibold text-gray-800">Support Overview</h2>
        
        <!-- KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <kpi-card title="Active Tickets" value="3" trend="up" color="blue"></kpi-card>
          <kpi-card title="Avg. Response Time" value="2h 15m" trend="down" color="green"></kpi-card>
          <kpi-card title="Satisfaction Rate" value="92%" trend="steady" color="amber"></kpi-card>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-lg font-medium text-gray-800 mb-4">Recent Activity</h3>
          <div class="space-y-4">
            <timeline-item 
              date="Today, 10:30 AM" 
              title="Ticket #4567 updated" 
              description="Your ticket has been assigned to support agent"
              status="in-progress"
            ></timeline-item>
            <timeline-item 
              date="Yesterday, 3:45 PM" 
              title="Ticket #4212 resolved" 
              description="Your issue has been marked as resolved"
              status="completed"
            ></timeline-item>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-medium text-gray-800 mb-4">Need Help?</h3>
            <div class="space-y-3">
              <button onclick="showPage('new-ticket')" class="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg text-blue-600 font-medium">
                Create New Ticket
              </button>
              <button onclick="startChat()" class="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg text-green-600 font-medium">
                Start Live Chat
              </button>
              <button onclick="showPage('callback')" class="w-full text-left p-3 bg-amber-50 hover:bg-amber-100 rounded-lg text-amber-600 font-medium">
                Schedule Callback
              </button>
            </div>
          </div>

          <!-- Service Status Summary -->
          <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-medium text-gray-800 mb-4">Service Status</h3>
            <div class="space-y-2">
              <div class="flex items-center">
                <div class="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                <span class="text-sm">All systems operational</span>
              </div>
              <button onclick="showPage('status')" class="text-sm text-blue-600 hover:underline mt-2">
                View detailed status
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- New Ticket Page -->
      <div id="page-new-ticket" class="page" style="display:none">
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">Create New Support Ticket</h2>
          
          <form class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Issue Type</label>
              <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option>Select issue type</option>
                <option>Technical Support</option>
                <option>Billing Inquiry</option>
                <option>Account Help</option>
                <option>Feature Request</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
              <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Attachments</label>
              <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div class="space-y-1 text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div class="flex text-sm text-gray-600">
                    <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                      <span>Upload files</span>
                      <input type="file" class="sr-only">
                    </label>
                    <p class="pl-1">or drag and drop</p>
                  </div>
                  <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3">
              <button type="button" onclick="showPage('dashboard')" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                Cancel
              </button>
              <button type="button" onclick="submitTicket()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                Submit Ticket
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Chat Interface -->
      <div id="page-chat" class="page" style="display:none">
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-4 border-b border-gray-200 bg-blue-600 text-white">
            <h2 class="text-lg font-medium">Live Support Chat</h2>
          </div>
          
          <div class="h-96 overflow-y-auto p-4 space-y-4" id="chat-messages">
            <div class="flex justify-start">
              <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg rounded-tl-none bg-gray-100">
                <p class="text-sm text-gray-800">Hello! I'm your support assistant. How can I help you today?</p>
              </div>
            </div>
          </div>

          <div class="p-4 border-t border-gray-200">
            <div class="flex space-x-2">
              <input type="text" placeholder="Type your message..." class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <button onclick="sendMessage()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                Send
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Other Pages (simplified for brevity) -->
      <div id="page-tickets" class="page" style="display:none">
        <div class="unimplemented" data-feature="tickets-page">
          <h2 class="text-xl font-semibold text-gray-800">My Tickets</h2>
          <button class="mt-2 text-red-600 underline">Implement: tickets-page</button>
        </div>
      </div>

      <div id="page-knowledge" class="page" style="display:none">
        <div class="unimplemented" data-feature="knowledge-base">
          <h2 class="text-xl font-semibold text-gray-800">Knowledge Base</h2>
          <button class="mt-2 text-red-600 underline">Implement: knowledge-base</button>
        </div>
      </div>

      <div id="page-status" class="page" style="display:none">
        <div class="unimplemented" data-feature="service-status">
          <h2 class="text-xl font-semibold text-gray-800">Service Status</h2>
          <button class="mt-2 text-red-600 underline">Implement: service-status</button>
        </div>
      </div>

      <div id="page-callback" class="page" style="display:none">
        <div class="unimplemented" data-feature="callback-scheduler">
          <h2 class="text-xl font-semibold text-gray-800">Schedule Callback</h2>
          <button class="mt-2 text-red-600 underline">Implement: callback-scheduler</button>
        </div>
      </div>
    </main>
  </div>

  <script data-exec="inline">
    // SPA Router
    const pages = document.querySelectorAll('.page');
    function showPage(pageId) {
      const trigger = event?.target || null;
      const target = document.getElementById('page-' + pageId);
      if (target) {
        pages.forEach(p => p.style.display = 'none');
        target.style.display = 'block';
      } else if (trigger) {
        try {
          const wrapper = document.createElement('div');
          wrapper.className = 'unimplemented border-2 border-dashed border-red-500 p-2';
          wrapper.setAttribute('data-feature', 'navigation-to-' + pageId);

          const button = document.createElement('button');
          button.textContent = 'Implement: navigation-to-' + pageId;
          button.className = 'mt-2 text-red-600 underline block';

          trigger.parentNode.replaceChild(wrapper, trigger);
          wrapper.appendChild(trigger);
          wrapper.appendChild(button);

          setTimeout(() => {
            wrapper.replaceWith(trigger);
          }, 3000);
        } catch (e) {
          console.error('Failed to mark unimplemented navigation:', e);
        }
      }
    }
    document.addEventListener('DOMContentLoaded', () => {
      showPage('dashboard');
    });

    // Chat functions
    function startChat() {
      showPage('chat');
      // In a real implementation, this would initialize the chat connection
    }

    function sendMessage() {
      const input = document.querySelector('#page-chat input');
      const message = input.value.trim();
      if (message) {
        const messagesContainer = document.getElementById('chat-messages');
        
        // Add user message
        const userMsg = document.createElement('div');
        userMsg.className = 'flex justify-end';
        userMsg.innerHTML = `
          <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg rounded-tr-none bg-blue-100">
            <p class="text-sm text-blue-800">${message}</p>
          </div>
        `;
        messagesContainer.appendChild(userMsg);
        
        // Simulate bot response
        setTimeout(() => {
          const botMsg = document.createElement('div');
          botMsg.className = 'flex justify-start';
          botMsg.innerHTML = `
            <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg rounded-tl-none bg-gray-100">
              <p class="text-sm text-gray-800">Thanks for your message. Let me check that for you...</p>
            </div>
          `;
          messagesContainer.appendChild(botMsg);
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 1000);
        
        input.value = '';
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }

    // Ticket submission
    function submitTicket() {
      // In a real implementation, this would submit the form data
      alert('Ticket submitted successfully!');
      showPage('dashboard');
    }

    // Handle unimplemented features
    document.querySelectorAll('.unimplemented').forEach(el => {
      el.classList.add('border-2', 'border-dashed', 'border-red-500', 'p-4');
      
      const feature = el.getAttribute('data-feature');
      const button = document.createElement('button');
      button.textContent = `Implement: ${feature}`;
      button.className = 'mt-2 text-red-600 underline';
      button.onclick = function() {
        this.remove();
        el.classList.remove('border-2', 'border-dashed', 'border-red-500');
      };
      
      el.appendChild(button);
      setTimeout(() => {
        if (button.parentNode) {
          button.remove();
          el.classList.remove('border-2', 'border-dashed', 'border-red-500');
        }
      }, 3000);
    });
  </script>
</div>