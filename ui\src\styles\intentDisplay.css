/* Intent Display Styles */
.intent-display {
  margin: 16px 0;
}

.intent-display.loading {
  min-height: 120px;
}

.intent-display.empty {
  min-height: 160px;
}

/* Loading Animation */
.intent-display .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Intent Card Styles */
.intent-display .bg-white {
  transition: box-shadow 0.2s ease-in-out;
}

.intent-display .bg-white:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Confidence Indicators */
.intent-display .text-green-600 {
  color: #059669;
}

.intent-display .text-yellow-600 {
  color: #d97706;
}

.intent-display .text-red-600 {
  color: #dc2626;
}

/* Intent Content Boxes */
.intent-display .bg-blue-50 {
  background-color: #eff6ff;
  border-left-color: #3b82f6;
}

.intent-display .bg-green-50 {
  background-color: #f0fdf4;
  border-left-color: #10b981;
}

/* <PERSON><PERSON> Styles */
.intent-display button {
  transition: all 0.2s ease-in-out;
}

.intent-display button:hover {
  transform: translateY(-1px);
}

.intent-display button:active {
  transform: translateY(0);
}

.intent-display button:disabled {
  transform: none;
  cursor: not-allowed;
}

/* Refinement Section */
.intent-display textarea {
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.intent-display textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Edit Confirmation Styles */
.edit-confirmation {
  margin: 16px 0;
}

.edit-confirmation.processing {
  position: relative;
}

.edit-confirmation.success .border-green-200 {
  border-color: #bbf7d0;
}

.edit-confirmation.error .border-red-200 {
  border-color: #fecaca;
}

/* Progress Bar */
.edit-confirmation .bg-blue-600 {
  background-color: #2563eb;
}

.edit-confirmation .bg-gray-200 {
  background-color: #e5e7eb;
}

/* Status Icons */
.edit-confirmation .text-green-600 {
  color: #059669;
}

.edit-confirmation .text-red-600 {
  color: #dc2626;
}

/* Performance Metrics */
.edit-confirmation .bg-blue-400 {
  background-color: #60a5fa;
}

/* Details Section */
.edit-confirmation .bg-gray-50 {
  background-color: #f9fafb;
}

.edit-confirmation .font-mono {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* Action Buttons */
.edit-confirmation .bg-green-600:hover {
  background-color: #059669;
}

.edit-confirmation .bg-red-600:hover {
  background-color: #dc2626;
}

.edit-confirmation .bg-white:hover {
  background-color: #f9fafb;
}

/* Responsive Design */
@media (max-width: 640px) {
  .intent-display .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .intent-display .py-4 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .intent-display .flex.space-x-2 {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .intent-display .flex.space-x-2 > * {
    margin-left: 0;
  }
  
  .edit-confirmation .flex.space-x-2 {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .edit-confirmation .flex.space-x-2 > * {
    margin-left: 0;
  }
}

/* Focus States */
.intent-display button:focus,
.edit-confirmation button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.intent-display button:focus-visible,
.edit-confirmation button:focus-visible {
  box-shadow: 0 0 0 2px #fff, 0 0 0 4px #3b82f6;
}

/* Animation for state changes */
.intent-display,
.edit-confirmation {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state specific styles */
.intent-display.loading .animate-spin {
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: #3b82f6;
  border-left-color: transparent;
}

/* Empty state styles */
.intent-display.empty .text-gray-300 {
  color: #d1d5db;
}

.intent-display.empty .text-gray-500 {
  color: #6b7280;
}

/* Accessibility improvements */
.intent-display [role="button"],
.edit-confirmation [role="button"] {
  cursor: pointer;
}

.intent-display [aria-disabled="true"],
.edit-confirmation [aria-disabled="true"] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .intent-display .border-gray-200 {
    border-color: #000;
  }
  
  .edit-confirmation .border-gray-200 {
    border-color: #000;
  }
  
  .intent-display .text-gray-600 {
    color: #000;
  }
  
  .edit-confirmation .text-gray-600 {
    color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .intent-display,
  .edit-confirmation,
  .intent-display button,
  .edit-confirmation button,
  .intent-display textarea {
    transition: none;
    animation: none;
  }
  
  .intent-display .animate-spin {
    animation: none;
  }
}
