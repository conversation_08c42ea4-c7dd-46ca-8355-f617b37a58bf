import { useState, useCallback } from 'react';

interface UseCounterProps {
  initialValue?: number;
  step?: number;
  min?: number;
  max?: number;
}

interface UseCounterReturn {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  setValue: (value: number) => void;
}

export const useCounter = ({
  initialValue = 0,
  step = 1,
  min = Number.MIN_SAFE_INTEGER,
  max = Number.MAX_SAFE_INTEGER,
}: UseCounterProps = {}): UseCounterReturn => {
  const [count, setCount] = useState<number>(initialValue);

  const increment = useCallback(() => {
    setCount((prevCount) => {
      const newValue = prevCount + step;
      return newValue <= max ? newValue : prevCount;
    });
  }, [step, max]);

  const decrement = useCallback(() => {
    setCount((prevCount) => {
      const newValue = prevCount - step;
      return newValue >= min ? newValue : prevCount;
    });
  }, [step, min]);

  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);

  const setValue = useCallback((value: number) => {
    if (value >= min && value <= max) {
      setCount(value);
    }
  }, [min, max]);

  return {
    count,
    increment,
    decrement,
    reset,
    setValue,
  };
};
