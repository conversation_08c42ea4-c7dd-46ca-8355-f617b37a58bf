import React, { useState, useEffect } from 'react';
import {
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface EditResult {
  success: boolean;
  html?: string;
  error?: string;
  tokensUsed?: number;
  processingTime?: number;
}

interface EditConfirmationProps {
  isProcessing: boolean;
  result: EditResult | null;
  onAccept: () => void;
  onReject: () => void;
  onRetry: () => void;
  streamingProgress?: {
    stage: string;
    progress: number;
    message?: string;
  };
}

const EditConfirmation: React.FC<EditConfirmationProps> = ({
  isProcessing,
  result,
  onAccept,
  onReject,
  onRetry,
  streamingProgress
}) => {
  const [showDetails, setShowDetails] = useState(false);

  // Auto-hide details after success
  useEffect(() => {
    if (result?.success) {
      const timer = setTimeout(() => setShowDetails(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [result?.success]);

  if (isProcessing) {
    return (
      <div className="edit-confirmation processing">
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">
                  Generating Your Changes
                </h3>
                {streamingProgress && (
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                      <span>{streamingProgress.stage}</span>
                      <span>{Math.round(streamingProgress.progress)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${streamingProgress.progress}%` }}
                      ></div>
                    </div>
                    {streamingProgress.message && (
                      <p className="text-xs text-gray-500 mt-1">
                        {streamingProgress.message}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!result) {
    return null;
  }

  if (result.success) {
    return (
      <div className="edit-confirmation success">
        <div className="bg-white rounded-lg border border-green-200 shadow-sm">
          <div className="px-6 py-4">
            <div className="flex items-start space-x-3">
              <CheckCircleIcon className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">
                  Implementation Complete!
                </h3>
                <p className="text-gray-600 mt-1">
                  The feature has been successfully implemented. Review the changes and choose to keep or undo them.
                </p>

                {/* Performance Metrics */}
                {(result.tokensUsed || result.processingTime) && (
                  <div className="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                    {result.tokensUsed && (
                      <span className="flex items-center">
                        <span className="w-2 h-2 bg-blue-400 rounded-full mr-1"></span>
                        {result.tokensUsed.toLocaleString()} tokens
                      </span>
                    )}
                    {result.processingTime && (
                      <span className="flex items-center">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        {(result.processingTime / 1000).toFixed(1)}s
                      </span>
                    )}
                  </div>
                )}

                {/* Details Toggle */}
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                >
                  {showDetails ? 'Hide details' : 'Show details'}
                </button>

                {/* Detailed Information */}
                {showDetails && result.html && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-md">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                      Generated HTML Preview:
                    </h4>
                    <div className="text-xs text-gray-600 font-mono bg-white p-2 rounded border max-h-32 overflow-y-auto">
                      {result.html.substring(0, 500)}
                      {result.html.length > 500 && '...'}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="px-6 py-4 bg-green-50 border-t border-green-200 rounded-b-lg">
            <div className="flex justify-end space-x-2">
              <button
                onClick={onReject}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <XCircleIcon className="h-4 w-4 mr-1" />
                Undo Changes
              </button>

              <button
                onClick={onAccept}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                Keep Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  return (
    <div className="edit-confirmation error">
      <div className="bg-white rounded-lg border border-red-200 shadow-sm">
        <div className="px-6 py-4">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">
                Generation Failed
              </h3>
              <p className="text-gray-600 mt-1">
                We encountered an error while generating your changes.
              </p>

              {result.error && (
                <div className="mt-3 p-3 bg-red-50 rounded-md">
                  <h4 className="text-sm font-medium text-red-800 mb-1">
                    Error Details:
                  </h4>
                  <p className="text-sm text-red-700">
                    {result.error}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="px-6 py-4 bg-red-50 border-t border-red-200 rounded-b-lg">
          <div className="flex justify-end space-x-2">
            <button
              onClick={onReject}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <XCircleIcon className="h-4 w-4 mr-1" />
              Cancel
            </button>

            <button
              onClick={onRetry}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditConfirmation;
