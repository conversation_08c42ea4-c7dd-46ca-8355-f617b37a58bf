<div id="task-2" class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 flex items-center justify-between" style="">
          <div class="flex items-center">
            <input id="task-2-checkbox" type="checkbox" class="h-5 w-5 text-blue-600 rounded focus:ring-blue-500">
            <div class="ml-3">
              <label for="task-2-checkbox" class="text-gray-800 font-medium">Buy groceries</label>
              <div class="flex items-center mt-1 text-sm text-gray-500">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">Shopping</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medium</span>
              </div>
            </div>
          </div>
          <div class="flex space-x-2">
            <button id="edit-task-2-btn" data-action="openModal" data-target="edit-task-modal" class="text-gray-500 hover:text-blue-600">
              <i class="fas fa-pencil-alt"></i>
            </button>
            <button id="delete-task-2-btn" data-action="openModal" data-target="delete-task-modal" class="text-gray-500 hover:text-red-600">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>