/**
 * HTML validation and sanitization service
 */

const sanitizeConfig = {
  // Tags that should be preserved (everything else will be stripped)
  allowedTags: [
    'main', 'section', 'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'form', 'input', 'label', 'button', 'a', 'img', 'header', 'footer',
    'nav', 'ul', 'ol', 'li', 'blockquote', 'br', 'hr', 'pre', 'code', 'em',
    'strong', 'i', 'b', 'small', 'style', 'script'
  ],

  // Attributes that should be preserved
  allowedAttributes: {
    '*': ['id', 'class', 'name', 'style', 'aria-*', 'data-*', 'role', 'tabindex'],
    'a': ['href', 'target', 'rel'],
    'img': ['src', 'alt', 'width', 'height'],
    'input': ['type', 'value', 'placeholder', 'required', 'disabled', 'checked', 'pattern', 'autocomplete', 'inputmode'],
    'button': ['type', 'disabled'],
    'form': ['action', 'method', 'novalidate', 'autocomplete'],
  },

  // Ensure proper attribute values
  allowedSchemes: ['http', 'https', 'mailto', 'tel'],
};

/**
 * Sanitize HTML content by removing disallowed tags and attributes
 * @param {string} html - Raw HTML content
 * @returns {string} - Sanitized HTML
 */
function sanitizeHTML(html) {
  try {
    // Basic validation
    if (!html || typeof html !== 'string') {
      console.warn('Invalid HTML input for sanitization:', typeof html);
      return '';
    }

    // Process HTML to preserve allowed elements and attributes
    let processedHtml = html;
    
    // Remove DOCTYPE declaration (optional)
    processedHtml = processedHtml.replace(/<!DOCTYPE[^>]*>/i, '');
    
    // Remove HTML comments
    processedHtml = processedHtml.replace(/<!--[\s\S]*?-->/g, '');

    // Remove disallowed tags
    const tagPattern = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;
    processedHtml = processedHtml.replace(tagPattern, (match, tag) => {
      tag = tag.toLowerCase();
      if (!sanitizeConfig.allowedTags.includes(tag)) {
        console.warn('Removing disallowed tag:', tag);
        return '';
      }
      return match;
    });

    // Process attributes
    const attrPattern = /\s([a-zA-Z\-]+)(?:=(?:"([^"]*)"|'([^']*)'|([^\s>]+)))?/g;
    processedHtml = processedHtml.replace(/<([a-z][a-z0-9]*)\b([^>]*)>/gi, (match, tag, attrs) => {
      tag = tag.toLowerCase();
      
      // If tag is not allowed, return empty string
      if (!sanitizeConfig.allowedTags.includes(tag)) {
        return '';
      }

      // Process attributes
      let newAttrs = attrs.replace(attrPattern, (_, attr, val1, val2, val3) => {
        attr = attr.toLowerCase();
        const value = val1 || val2 || val3 || '';

        // Check if attribute is allowed
        const allowedForTag = sanitizeConfig.allowedAttributes[tag] || [];
        const allowedForAll = sanitizeConfig.allowedAttributes['*'] || [];
        
        const isAllowed = 
          allowedForTag.includes(attr) || 
          allowedForAll.includes(attr) ||
          (attr.startsWith('aria-') && allowedForAll.includes('aria-*')) ||
          (attr.startsWith('data-') && allowedForAll.includes('data-*'));

        if (!isAllowed) {
          return '';
        }

        // For href/src attributes, validate URL scheme
        if ((attr === 'href' || attr === 'src') && value) {
          const scheme = value.split(':')[0].toLowerCase();
          if (!sanitizeConfig.allowedSchemes.includes(scheme)) {
            return '';
          }
        }

        return ` ${attr}="${value.replace(/"/g, '&quot;')}"`;
      });

      return `<${tag}${newAttrs}>`;
    });

    return processedHtml;
  } catch (error) {
    console.error('HTML sanitization error:', error);
    return '';
  }
}

module.exports = {
  sanitizeHTML,
  sanitizeConfig
};
