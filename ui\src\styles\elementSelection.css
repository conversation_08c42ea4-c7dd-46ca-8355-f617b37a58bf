.element-selector-container {
  outline: none;
  position: relative;
  min-height: 100px;
  background: #fafbfc;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 12px;
  overflow: auto;
}

.element-highlight {
  outline: 2px solid #2563eb !important;
  background: rgba(37, 99, 235, 0.08) !important;
  cursor: pointer !important;
  transition: outline 0.1s, background 0.1s;
  z-index: 10;
}
