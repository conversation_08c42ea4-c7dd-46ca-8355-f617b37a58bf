import React, { useState } from "react";
import styles from "./PricingPage.module.css";

export const PricingPage: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handleUpgrade = (e: React.MouseEvent) => {
    setLoading(true);
    // Optionally: window.analytics?.track("Upgrade Clicked");
    window.location.href = "https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa";
  };

  return (
    <div className={styles.pricingRoot}>
      {/* Header */}
      <header className={styles.header}>
        <h1 className={styles.title}>Simple Pricing</h1>
        <div className={styles.subtitle}>
          Just $5/month — create dozens of production-ready UI prototypes
        </div>
        <div className={styles.subtext}>
          No tokens. No usage surprises. No complex plans.
        </div>
      </header>

      {/* Pricing Card */}
      <section className={styles.cardSection}>
        <div className={styles.pricingCard}>
          <div className={styles.planName}>Pro Plan</div>
          <div className={styles.price}>
            <span className={styles.priceValue}>$5</span>
            <span className={styles.pricePeriod}>/ month</span>
          </div>
          <ul className={styles.featuresList}>
            <li>✔ Up to 100 prototypes per month</li>
            <li>✔ Visual editing included</li>
            <li>✔ Clean HTML/CSS output</li>
            <li>✔ No token tracking</li>
            <li>✔ Priority build speed</li>
          </ul>
          <button
            className={styles.ctaButton}
            onClick={handleUpgrade}
            disabled={loading}
            style={loading ? { opacity: 0.7, pointerEvents: "none" } : {}}
          >
            {loading ? "Redirecting..." : "Upgrade to Pro"}
          </button>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={styles.faqSection}>
        <h2 className={styles.faqTitle}>FAQ</h2>
        <div className={styles.faqList}>
          <div className={styles.faqItem}>
            <div className={styles.faqQ}>Can I cancel anytime?</div>
            <div className={styles.faqA}>Yes, subscriptions are month-to-month.</div>
          </div>
          <div className={styles.faqItem}>
            <div className={styles.faqQ}>Do you support teams?</div>
            <div className={styles.faqA}>Team plans coming soon.</div>
          </div>
          <div className={styles.faqItem}>
            <div className={styles.faqQ}>Is there a free trial?</div>
            <div className={styles.faqA}>Yes — 3 free prototypes to get started.</div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingPage;
