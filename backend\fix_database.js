// Script to fix database issues
const fs = require('fs');
const path = require('path');
const { pool } = require('./services/promptDbService');

async function fixDatabase() {
  console.log('Starting database fix process...');
  
  try {
    // Check if the database is accessible
    const pingResult = await pool.query('SELECT 1 as ping');
    console.log(`Database connection: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    
    // Read and execute the fix_db_schema.sql file
    const schemaPath = path.join(__dirname, 'fix_db_schema.sql');
    if (fs.existsSync(schemaPath)) {
      console.log('Executing fix_db_schema.sql...');
      const schemaSql = fs.readFileSync(schemaPath, 'utf8');
      await pool.query(schemaSql);
      console.log('Successfully executed fix_db_schema.sql');
    } else {
      console.error('fix_db_schema.sql not found!');
    }
    
    // Read and execute the db_prompt_prototype_linkage.sql file
    const linkagePath = path.join(__dirname, 'db_prompt_prototype_linkage.sql');
    if (fs.existsSync(linkagePath)) {
      console.log('Executing db_prompt_prototype_linkage.sql...');
      const linkageSql = fs.readFileSync(linkagePath, 'utf8');
      await pool.query(linkageSql);
      console.log('Successfully executed db_prompt_prototype_linkage.sql');
    } else {
      console.error('db_prompt_prototype_linkage.sql not found!');
    }
    
    // Check if tables exist now
    const tables = ['users', 'prompts', 'prompt_iterations', 'prototypes', 'usage_log'];
    for (const table of tables) {
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        ) as exists
      `, [table]);
      
      console.log(`Table '${table}' exists: ${tableCheck.rows[0].exists}`);
    }
    
    // Check if functions exist now
    const functions = ['use_tokens_and_log', 'increment_prototype_count', 'get_user_quota'];
    for (const func of functions) {
      const funcCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM pg_proc
          WHERE proname = $1
        ) as exists
      `, [func]);
      
      console.log(`Function '${func}' exists: ${funcCheck.rows[0].exists}`);
    }
    
    console.log('Database fix process completed successfully!');
  } catch (error) {
    console.error('Error fixing database:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the fix
fixDatabase().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
