curl ^"https://readdy.ai/api/page_gen/edit^" ^
  -H ^"accept: text/event-stream^" ^
  -H ^"accept-language: en-US,en;q=0.9^" ^
  -H ^"authorization: Bearer eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDIyMkFBQSIsImtpZCI6Imluc18ycWtRbmFmNnRNcW9DQVZRMjRsWEwzRzRDcnQiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************.ep7h5kRPdzc4SVyIytF0ifF1O3X_3klDJjzazTMR6vviGSJIv17JE5QFbp4oBo2V9V8FSpRa_rYwRdv41yGykvkCIx_GDBInPkSL4kNfA-MZ_CTlajej0EQlv9wLgu76ssnrd1-Xs9ditdnpPq2X81hlUb9PC-w4pEpXs6fEGjH28EwvzY3D242RUPJeXRe9ZCq3Jw3o0eZeYjJ2siIcFg05v1TQ9jLqqSE-pLPMwY4OhiQUn2Pj6z0WZ1ob3J_qLhLQJTB2bqRJZMABm0Ko6W_EriJ5yWZSSD9w-TxV6XFffe3htO6BLSat_sycGoBgUwSHCGD4fZbIy-s38ckXWg^" ^
  -H ^"cache-control: no-cache^" ^
  -H ^"content-type: application/json^" ^
  -b ^"_ga=GA1.1.1465294481.1745988234; __client_uat=1745988267; __client_uat_ABo4F7rL=1745988267; featurebase-anon-id=01968502-cb3b-71da-a7b3-8040ad5e954f; _ga_MHDZ46BL5J=GS2.1.s1746973142^$o2^$g0^$t1746973142^$j0^$l0^$h0; featurebase-access-67554c8c97bd6ab5bb7e9290=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODBiMTcxZjY3MTA2MTVkODQwOGE0NTIiLCJ1dCI6ImN1c3RvbWVyIiwiaWF0IjoxNzQ3ODM4MjI5LCJleHAiOjE3NTA0MzAyMjksIm5iZiI6MTc0NzgzODEwOSwiaXNzIjoicHJvZHVjdGlvbjpiYWNrZW5kIiwiYXVkIjoic2VzcyIsInR0IjoiYWNjZXNzIiwidHYiOjAsIm9pZCI6IjY3NTU0YzhjOTdiZDZhYjViYjdlOTI5MCIsInNyYyI6InNzbyJ9.08q33i4ZBZN2ZefVIyXNdydjwQLlaUD9_mhiA7koxx4; ph_phc_V7JMHB0fVJGRu8UHyrsj6pSL1BS76P5zD8qCi7lrTTV_posthog=^%^7B^%^22distinct_id^%^22^%^3A^%^22user_2wCtEse7SEtbCnPCarydaaRSgfU^%^22^%^2C^%^22^%^24sesid^%^22^%^3A^%^5B1747839096229^%^2C^%^220196f336-be80-7a71-8103-d301f3c1ca28^%^22^%^2C1747837173376^%^5D^%^2C^%^22^%^24epp^%^22^%^3Atrue^%^2C^%^22^%^24initial_person_info^%^22^%^3A^%^7B^%^22r^%^22^%^3A^%^22https^%^3A^%^2F^%^2Freaddy.ai^%^2Fsignin^%^22^%^2C^%^22u^%^22^%^3A^%^22https^%^3A^%^2F^%^2Freaddy.ai^%^2Fhome^%^22^%^7D^%^7D; __session=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18ycWtRbmFmNnRNcW9DQVZRMjRsWEwzRzRDcnQiLCJ0eXAiOiJKV1QifQ.eyJhenAiOiJodHRwczovL3JlYWRkeS5haSIsImV4cCI6MTc0NzgzOTE0MCwiZnZhIjpbMzA4NDYsLTFdLCJpYXQiOjE3NDc4MzkwODAsImlzcyI6Imh0dHBzOi8vY2xlcmsucmVhZGR5LmFpIiwibmJmIjoxNzQ3ODM5MDcwLCJzaWQiOiJzZXNzXzJ3UXlvWWM1T3o4NU04dDFyd3JpaU1SQXpoWiIsInN1YiI6InVzZXJfMndDdEVzZTdTRXRiQ25QQ2FyeWRhYVJTZ2ZVIn0.O3Zo8TBwpY0Cr2w_H3SynbSskYwbHJ-ThBEZuWZBfC6ZDG7MtKm3yqHlsTnvy8w70a8G2SQQ0_JBi2YHJ8rERMBA1jwBKhXeb5e1pjaoUTUBB86Vw1CehMK_sa_bIRJ8EimTStMgJKiepxa00--k-jitLF2W6bnmMYT2Zd0oL9qkpPcTrj2vUX0RxEUXL4DX1rUIINlE-8f_pdVdpiPZ17NN0OEuts7Q16l0twmjbuZxeOoHNLRuVAQrB6PP-eYCIyW5x84cuzFMPDYhyO9M5rLdRgM5eEfffA_T0EfI_M1zFkFiCINVrsAfBlobxwERdQSTl2GiOzWQNZ0q77kf1g; __session_ABo4F7rL=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18ycWtRbmFmNnRNcW9DQVZRMjRsWEwzRzRDcnQiLCJ0eXAiOiJKV1QifQ.eyJhenAiOiJodHRwczovL3JlYWRkeS5haSIsImV4cCI6MTc0NzgzOTE0MCwiZnZhIjpbMzA4NDYsLTFdLCJpYXQiOjE3NDc4MzkwODAsImlzcyI6Imh0dHBzOi8vY2xlcmsucmVhZGR5LmFpIiwibmJmIjoxNzQ3ODM5MDcwLCJzaWQiOiJzZXNzXzJ3UXlvWWM1T3o4NU04dDFyd3JpaU1SQXpoWiIsInN1YiI6InVzZXJfMndDdEVzZTdTRXRiQ25QQ2FyeWRhYVJTZ2ZVIn0.O3Zo8TBwpY0Cr2w_H3SynbSskYwbHJ-ThBEZuWZBfC6ZDG7MtKm3yqHlsTnvy8w70a8G2SQQ0_JBi2YHJ8rERMBA1jwBKhXeb5e1pjaoUTUBB86Vw1CehMK_sa_bIRJ8EimTStMgJKiepxa00--k-jitLF2W6bnmMYT2Zd0oL9qkpPcTrj2vUX0RxEUXL4DX1rUIINlE-8f_pdVdpiPZ17NN0OEuts7Q16l0twmjbuZxeOoHNLRuVAQrB6PP-eYCIyW5x84cuzFMPDYhyO9M5rLdRgM5eEfffA_T0EfI_M1zFkFiCINVrsAfBlobxwERdQSTl2GiOzWQNZ0q77kf1g^" ^
  -H ^"origin: https://readdy.ai^" ^
  -H ^"pragma: no-cache^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://readdy.ai/home/<USER>/a8e50c70-ab3d-4919-ba44-bc4357801ad1^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"134^\^", ^\^"Not:A-Brand^\^";v=^\^"24^\^", ^\^"Google Chrome^\^";v=^\^"134^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?0^" ^
  -H ^"sec-ch-ua-platform: ^\^"Windows^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36^" ^
  --data-raw ^"^{^\^"sessionKey^\^":^\^"a8e50c70-ab3d-4919-ba44-bc4357801ad1^\^",^\^"desc^\^":^\^"**1. Header Section**^\^\n^\^\n- Maintain the consistent header from the original page with logo, search bar, notifications, and user profile^\^\n^\^\n- Keep the secondary navigation with ^\^\^\^"Investments^\^\^\^" tab now highlighted as active^\^\n^\^\n- Add a back button linking to the original page in the header for easy navigation^\^\n^\^\n**2. Investment Overview**^\^\n^\^\n- Portfolio value card showing total investment amount, returns, and YTD performance^\^\n^\^\n- Interactive chart displaying portfolio performance over time with customizable time periods^\^\n^\^\n- Quick stats showing key metrics like total value, return percentage, and risk level^\^\n^\^\n**3. Asset Allocation**^\^\n^\^\n- Donut chart showing investment distribution across different asset classes^\^\n^\^\n- Breakdown of investments by category (Stocks, Bonds, ETFs, etc.)^\^\n^\^\n- Color-coded legend with percentages and amounts^\^\n^\^\n- Quick actions to rebalance or adjust allocation**4. Investment Accounts**^\^\n^\^\n- List of investment accounts with account numbers, balances, and performance^\^\n^\^\n- Individual account cards showing:  ^\^\n^\^\n- Account type and name^\^\n^\^\n- Current balance  ^\^\n^\^\n- Performance metrics  ^\^\n^\^\n- Quick actions (deposit, withdraw, trade)^\^\n^\^\n**5. Recent Activities**^\^\n^\^\n- Timeline of recent investment transactions^\^\n^\^\n- Each activity shows:^\^\n^\^\n- Transaction type (buy/sell/dividend)^\^\n^\^\n- Asset name^\^\n^\^\n- Amount  ^\^\n^\^\n- Date and time^\^\n^\^\n- Status**6. Investment Opportunities**^\^\n^\^\n- Recommended investment options based on user profile^\^\n^\^\n- Each opportunity card displays:^\^\n^\^\n- Investment type^\^\n^\^\n- Expected returns  ^\^\n^\^\n- Risk level  ^\^\n^\^\n- Minimum investment amount^\^\n^\^\n- Quick action to invest^\^\n^\^\n**7. Action Buttons**^\^\n^\^\n- Primary actions:^\^\n^\^\n- Buy/Sell Securities^\^\n^\^\n- Transfer Funds^\^\n^\^\n- Set up Auto-Invest^\^\n^\^\n- Schedule Advisor Meeting^\^\n^\^\n**8. Footer**^\^\n^\^\n- Maintain consistent footer from original page^\^\n^\^\n- Add investment-specific links and disclaimers^\^\n^\^\n- Include market data attribution and legal notices^\^",^\^"recordId^\^":2208333,^\^"query^\^":^\^"Remove risk level^\^",^\^"style^\^":^\^"light^\^",^\^"color^\^":^\^"^\^",^\^"borderRadius^\^":^\^"medium^\^",^\^"language^\^":^\^"English^\^",^\^"framework^\^":^\^"html^\^",^\^"lib^\^":^\^"^\^",^\^"messages^\^":^[^{^\^"role^\^":^\^"user^\^",^\^"content^\^":^\^"I already have a original page: The user is clicking on the ^\^\^\^"Investments^\^\^\^" navigation link in the header section. They likely want to navigate to an investments page to view their investment accounts, portfolio details, or investment opportunities.Now your task is: Create an Investments page that displays the user's investment portfolio details, including account balances, asset allocation, performance metrics, investment history, and available investment opportunities. The page should include interactive charts showing portfolio performance over time, a breakdown of investments by category, and options to manage existing investments or explore new ones.Important: When generating the new page, ensure that users can click on a page element to access the original page. The Original page link is: https://readdy.ai/home/<USER>/d47de6a6-a5fe-4661-8513-b349d01fa833. Add the original page link to a suitable UI element (such as a navigation bar item, tab bar option, or back button). And the link must be added on a ^<a^>^</a^>, such as ^<a href=^\^\^\^"https://readdy.ai/home/<USER>/d47de6a6-a5fe-4661-8513-b349d01fa833^\^\^\^" data-readdy=^\^\^\^"true^\^\^\^"^>^</a^>^\^"^},^{^\^"role^\^":^\^"assistant^\^",^\^"content^\^":^\^"**1. Header Section**^\^\n^\^\n- Maintain the consistent header from the original page with logo, search bar, notifications, and user profile^\^\n^\^\n- Keep the secondary navigation with ^\^\^\^"Investments^\^\^\^" tab now highlighted as active^\^\n^\^\n- Add a back button linking to the original page in the header for easy navigation^\^\n^\^\n**2. Investment Overview**^\^\n^\^\n- Portfolio value card showing total investment amount, returns, and YTD performance^\^\n^\^\n- Interactive chart displaying portfolio performance over time with customizable time periods^\^\n^\^\n- Quick stats showing key metrics like total value, return percentage, and risk level^\^\n^\^\n**3. Asset Allocation**^\^\n^\^\n- Donut chart showing investment distribution across different asset classes^\^\n^\^\n- Breakdown of investments by category (Stocks, Bonds, ETFs, etc.)^\^\n^\^\n- Color-coded legend with percentages and amounts^\^\n^\^\n- Quick actions to rebalance or adjust allocation**4. Investment Accounts**^\^\n^\^\n- List of investment accounts with account numbers, balances, and performance^\^\n^\^\n- Individual account cards showing:  ^\^\n^\^\n- Account type and name^\^\n^\^\n- Current balance  ^\^\n^\^\n- Performance metrics  ^\^\n^\^\n- Quick actions (deposit, withdraw, trade)^\^\n^\^\n**5. Recent Activities**^\^\n^\^\n- Timeline of recent investment transactions^\^\n^\^\n- Each activity shows:^\^\n^\^\n- Transaction type (buy/sell/dividend)^\^\n^\^\n- Asset name^\^\n^\^\n- Amount  ^\^\n^\^\n- Date and time^\^\n^\^\n- Status**6. Investment Opportunities**^\^\n^\^\n- Recommended investment options based on user profile^\^\n^\^\n- Each opportunity card displays:^\^\n^\^\n- Investment type^\^\n^\^\n- Expected returns  ^\^\n^\^\n- Risk level  ^\^\n^\^\n- Minimum investment amount^\^\n^\^\n- Quick action to invest^\^\n^\^\n**7. Action Buttons**^\^\n^\^\n- Primary actions:^\^\n^\^\n- Buy/Sell Securities^\^\n^\^\n- Transfer Funds^\^\n^\^\n- Set up Auto-Invest^\^\n^\^\n- Schedule Advisor Meeting^\^\n^\^\n**8. Footer**^\^\n^\^\n- Maintain consistent footer from original page^\^\n^\^\n- Add investment-specific links and disclaimers^\^\n^\^\n- Include market data attribution and legal notices^\^"^}^],^\^"force^\^":false,^\^"seq^\^":1^}^"