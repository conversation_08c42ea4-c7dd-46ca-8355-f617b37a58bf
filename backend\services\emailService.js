const nodemailer = require('nodemailer');
const config = require('../config/config');

// Create a transporter based on configuration
let transporter;

// Initialize the email transporter if email is configured
if (config.email && config.email.host) {
  transporter = nodemailer.createTransport({
    host: config.email.host,
    port: config.email.port || 587,
    secure: config.email.secure || false,
    auth: {
      user: config.email.user,
      pass: config.email.password
    }
  });
}

/**
 * Service for sending emails
 */
const emailService = {
  /**
   * Send an email notification for a shared prototype
   * @param {Object} shareData - Data for the share email
   * @param {string} shareData.ownerEmail - Email of the prototype owner
   * @param {string} shareData.sharedWithEmail - Email of the person receiving access
   * @param {string} shareData.prototypeName - Name of the prototype
   * @param {string} shareData.accessToken - Access token for the share
   * @param {string} shareData.accessLevel - Access level granted
   * @param {Date} [shareData.expiresAt] - Optional expiration date
   * @returns {Promise<Object>} - Result of the email sending operation
   */
  async sendShareEmail(shareData) {
    // Skip if email is not configured
    if (!transporter) {
      console.log('Email service not configured, skipping share notification');
      return { skipped: true };
    }
    
    const shareUrl = `${config.appUrl}/shared/${shareData.accessToken}`;
    
    // Format expiration date if present
    let expirationText = '';
    if (shareData.expiresAt) {
      const expirationDate = new Date(shareData.expiresAt);
      expirationText = `This link will expire on ${expirationDate.toLocaleDateString()} at ${expirationDate.toLocaleTimeString()}.`;
    }
    
    // Format access level for display
    const accessLevelText = {
      'view': 'view only',
      'comment': 'view and comment',
      'edit': 'view and edit'
    }[shareData.accessLevel] || 'view only';
    
    // Email content
    const mailOptions = {
      from: `"JustPrototype" <${config.email.from || config.email.user}>`,
      to: shareData.sharedWithEmail,
      subject: `${shareData.ownerEmail} shared a prototype with you: ${shareData.prototypeName}`,
      text: `
Hello,

${shareData.ownerEmail} has shared a prototype with you: "${shareData.prototypeName}".

You have ${accessLevelText} access to this prototype.
${expirationText}

Access the prototype here: ${shareUrl}

Thank you,
JustPrototype Team
      `,
      html: `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
    .content { padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px; }
    .button { display: inline-block; background-color: #4f46e5; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 20px 0; }
    .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Prototype Shared With You</h1>
    </div>
    <div class="content">
      <p>Hello,</p>
      <p><strong>${shareData.ownerEmail}</strong> has shared a prototype with you: <strong>"${shareData.prototypeName}"</strong>.</p>
      <p>You have <strong>${accessLevelText}</strong> access to this prototype.</p>
      ${expirationText ? `<p>${expirationText}</p>` : ''}
      <p><a href="${shareUrl}" class="button">View Prototype</a></p>
      <p>Or copy this link: ${shareUrl}</p>
    </div>
    <div class="footer">
      <p>Thank you,<br>JustPrototype Team</p>
    </div>
  </div>
</body>
</html>
      `
    };
    
    return transporter.sendMail(mailOptions);
  }
};

module.exports = emailService;
