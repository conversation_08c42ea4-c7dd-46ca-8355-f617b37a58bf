/**
 * Test script for LLMServiceV3 edit strategy
 * Tests various edit scenarios to ensure we avoid full HTML regeneration
 */

const LLMServiceV3 = require('../services/llmServiceV3');

// Mock HTML content for testing
const sampleHTML = `
<div id="app" class="min-h-screen bg-gray-100">
  <header class="bg-blue-600 text-white p-4">
    <nav class="flex justify-between items-center">
      <h1 class="text-2xl font-bold">My Dashboard</h1>
      <ul class="flex space-x-4">
        <li><a href="#home" class="hover:text-blue-200">Home</a></li>
        <li><a href="#about" class="hover:text-blue-200">About</a></li>
        <li><a href="#contact" class="hover:text-blue-200">Contact</a></li>
      </ul>
    </nav>
  </header>
  
  <main class="container mx-auto p-6">
    <section class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">Analytics Overview</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 p-4 rounded">
          <h3 class="text-lg font-medium text-blue-800">Users</h3>
          <p class="text-2xl font-bold text-blue-600">1,234</p>
        </div>
        <div class="bg-green-50 p-4 rounded">
          <h3 class="text-lg font-medium text-green-800">Revenue</h3>
          <p class="text-2xl font-bold text-green-600">$12,345</p>
        </div>
        <div class="bg-purple-50 p-4 rounded">
          <h3 class="text-lg font-medium text-purple-800">Orders</h3>
          <p class="text-2xl font-bold text-purple-600">567</p>
        </div>
      </div>
    </section>
    
    <section class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">Chart Section</h2>
      <div id="chart-container" class="h-64 bg-gray-50 rounded flex items-center justify-center">
        <p class="text-gray-500">Chart placeholder</p>
      </div>
    </section>
  </main>
  
  <footer class="bg-gray-800 text-white p-4 mt-8">
    <p class="text-center">&copy; 2024 My Dashboard. All rights reserved.</p>
  </footer>
</div>
`;

// Test scenarios
const testScenarios = [
  {
    name: "Change color test",
    prompt: "change the header background color to red",
    expectedStrategy: "fragment",
    expectedSelector: "header, .header, h1, h2, h3"
  },
  {
    name: "Add menu item test",
    prompt: "add a new menu item called 'Services'",
    expectedStrategy: "fragment", 
    expectedSelector: "nav, .nav, .menu, [role=\"navigation\"]"
  },
  {
    name: "Add chart test",
    prompt: "add a bar chart showing monthly sales",
    expectedStrategy: "fragment",
    expectedSelector: ".chart, [id*=\"chart\"], canvas, svg"
  },
  {
    name: "Change text test",
    prompt: "change the title to 'Admin Panel'",
    expectedStrategy: "fragment",
    expectedSelector: "h1, h2, h3, h4, h5, h6, p, span, div"
  },
  {
    name: "Add button test",
    prompt: "add a download button",
    expectedStrategy: "fragment",
    expectedSelector: ".btn, button, [role=\"button\"]"
  },
  {
    name: "Global change test",
    prompt: "redesign the entire layout with a modern look",
    expectedStrategy: "full",
    expectedSelector: null
  },
  {
    name: "Multiple changes test",
    prompt: "add several new sections and reorganize the layout",
    expectedStrategy: "full",
    expectedSelector: null
  }
];

// Mock response object for testing
class MockResponse {
  constructor() {
    this.events = [];
    this.ended = false;
  }
  
  write(data) {
    this.events.push(data);
    console.log('📡 SSE Event:', data.trim());
  }
  
  end() {
    this.ended = true;
    console.log('🏁 Response ended');
  }
}

// Test function
async function testEditStrategy() {
  console.log('🧪 Starting LLMServiceV3 Edit Strategy Tests\n');
  
  const llmService = LLMServiceV3;
  let passedTests = 0;
  let totalTests = testScenarios.length;
  
  for (const scenario of testScenarios) {
    console.log(`\n🔬 Testing: ${scenario.name}`);
    console.log(`📝 Prompt: "${scenario.prompt}"`);
    
    try {
      // Test prompt analysis
      const analysis = await llmService.analyzePromptIntent(scenario.prompt, sampleHTML);
      
      console.log('📊 Analysis Result:', {
        isTargeted: analysis.isTargeted,
        elementSelector: analysis.elementSelector,
        changeType: analysis.changeType,
        confidence: analysis.confidence
      });
      
      // Validate strategy
      const actualStrategy = analysis.isTargeted && analysis.elementSelector ? "fragment" : "full";
      const strategyMatch = actualStrategy === scenario.expectedStrategy;
      
      console.log(`🎯 Expected Strategy: ${scenario.expectedStrategy}`);
      console.log(`🎯 Actual Strategy: ${actualStrategy}`);
      console.log(`✅ Strategy Match: ${strategyMatch ? 'PASS' : 'FAIL'}`);
      
      // Test fragment extraction if targeted
      if (analysis.isTargeted && analysis.elementSelector) {
        const fragment = llmService.extractFragment(sampleHTML, analysis.elementSelector);
        console.log(`📦 Fragment Extracted: ${fragment ? 'YES' : 'NO'}`);
        if (fragment) {
          console.log(`📏 Fragment Length: ${fragment.length} characters`);
          console.log(`📄 Fragment Preview: ${fragment.substring(0, 100)}...`);
        }
      }
      
      if (strategyMatch) {
        passedTests++;
        console.log('🎉 Test PASSED');
      } else {
        console.log('❌ Test FAILED');
      }
      
    } catch (error) {
      console.error('❌ Test Error:', error.message);
    }
    
    console.log('─'.repeat(80));
  }
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  console.log(`📈 Success Rate: ${Math.round((passedTests/totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Edit strategy is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Review the edit strategy implementation.');
  }
}

// Test fragment extraction specifically
async function testFragmentExtraction() {
  console.log('\n🧪 Testing Fragment Extraction\n');
  
  const llmService = LLMServiceV3;
  
  const extractionTests = [
    { selector: 'header', description: 'Header tag' },
    { selector: '.bg-blue-600', description: 'Blue background class' },
    { selector: 'nav', description: 'Navigation tag' },
    { selector: '#chart-container', description: 'Chart container ID' },
    { selector: 'footer', description: 'Footer tag' },
    { selector: '.nonexistent', description: 'Non-existent class' }
  ];
  
  for (const test of extractionTests) {
    console.log(`🔍 Testing extraction: ${test.description} (${test.selector})`);
    
    const fragment = llmService.extractFragment(sampleHTML, test.selector);
    
    if (fragment) {
      console.log(`✅ Extracted ${fragment.length} characters`);
      console.log(`📄 Preview: ${fragment.substring(0, 150)}...`);
    } else {
      console.log('❌ No fragment extracted');
    }
    console.log('');
  }
}

// Run tests
if (require.main === module) {
  (async () => {
    try {
      await testEditStrategy();
      await testFragmentExtraction();
    } catch (error) {
      console.error('Test execution error:', error);
    }
  })();
}

module.exports = { testEditStrategy, testFragmentExtraction };
