/**
 * Production-ready Page Manager Component
 * Handles multi-page functionality, navigation, and page operations
 */

import React, { useState } from 'react';
import {
  FiPlus,
  FiTrash2,
  FiEdit3,
  FiLink,
  FiLoader,
  FiFile,
  FiHome,
  FiMoreVertical
} from 'react-icons/fi';
import { Page } from '../../hooks/useEditorV3';

// ============================================================================
// TYPES
// ============================================================================

interface PageManagerProps {
  pages: Page[];
  currentPageId: string;
  isLinking: boolean;
  onPageSwitch: (pageId: string) => void;
  onPageAdd: (page: Omit<Page, 'lastUpdated'>) => void;
  onPageUpdate: (pageId: string, updates: Partial<Page>) => void;
  onPageDelete?: (pageId: string) => void;
  onLinkPages: () => void;
  className?: string;
}

interface PageItemProps {
  page: Page;
  isActive: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<Page>) => void;
  onDelete?: () => void;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatLastUpdated = (date?: Date): string => {
  if (!date) return 'Never';

  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;

  return date.toLocaleDateString();
};

const getPageIcon = (pageName: string): React.ReactNode => {
  const name = pageName.toLowerCase();

  if (name.includes('main') || name.includes('home')) {
    return <FiHome className="w-4 h-4" />;
  }

  return <FiFile className="w-4 h-4" />;
};

const generatePageId = (name: string): string => {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// ============================================================================
// SUB-COMPONENTS
// ============================================================================

const PageItem: React.FC<PageItemProps> = ({
  page,
  isActive,
  onSelect,
  onUpdate,
  onDelete
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(page.name);
  const [showMenu, setShowMenu] = useState(false);

  const handleNameSubmit = () => {
    if (editName.trim() && editName !== page.name) {
      onUpdate({ name: editName.trim() });
    }
    setIsEditing(false);
    setEditName(page.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNameSubmit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditName(page.name);
    }
  };

  const hasContent = page.content && page.content.length > 50; // More meaningful content threshold

  return (
    <div
      className={`group relative p-3 rounded-lg border transition-all cursor-pointer ${
        isActive
          ? 'bg-blue-50 border-blue-200 shadow-sm'
          : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
      }`}
      onClick={!isEditing ? onSelect : undefined}
    >
      {/* Page Icon and Name */}
      <div className="flex items-center space-x-3">
        <div className={`flex-shrink-0 ${isActive ? 'text-blue-600' : 'text-gray-400'}`}>
          {getPageIcon(page.name)}
        </div>

        <div className="flex-1 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleNameSubmit}
              onKeyDown={handleKeyDown}
              className="w-full px-2 py-1 text-sm font-medium bg-white border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
            />
          ) : (
            <h3 className={`text-sm font-medium truncate ${
              isActive ? 'text-blue-900' : 'text-gray-900'
            }`}>
              {page.name}
            </h3>
          )}

          <div className="flex items-center space-x-2 mt-1">
            <span className={`text-xs ${
              hasContent
                ? (isActive ? 'text-blue-600' : 'text-green-600')
                : (isActive ? 'text-blue-400' : 'text-gray-400')
            }`}>
              {hasContent ? 'Ready' : 'Empty'}
            </span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-400">
              {formatLastUpdated(page.lastUpdated)}
            </span>
          </div>
        </div>

        {/* Menu Button */}
        <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowMenu(!showMenu);
            }}
            className={`p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity ${
              showMenu ? 'opacity-100' : ''
            } ${isActive ? 'text-blue-600 hover:bg-blue-100' : 'text-gray-400 hover:bg-gray-100'}`}
          >
            <FiMoreVertical className="w-4 h-4" />
          </button>

          {/* Dropdown Menu */}
          {showMenu && (
            <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(true);
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
              >
                <FiEdit3 className="w-3 h-3" />
                <span>Rename</span>
              </button>

              {onDelete && page.id !== 'main' && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                    setShowMenu(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                >
                  <FiTrash2 className="w-3 h-3" />
                  <span>Delete</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
};

const AddPageForm: React.FC<{
  onAdd: (page: Omit<Page, 'lastUpdated'>) => void;
  onCancel: () => void;
}> = ({ onAdd, onCancel }) => {
  const [name, setName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      const pageId = generatePageId(name.trim());
      onAdd({
        id: pageId,
        name: name.trim(),
        content: '',
        isActive: false
      });
      setName('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
      <input
        type="text"
        value={name}
        onChange={(e) => setName(e.target.value)}
        placeholder="Enter page name..."
        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        autoFocus
      />
      <div className="flex justify-end space-x-2 mt-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={!name.trim()}
          className="px-3 py-1.5 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Add Page
        </button>
      </div>
    </form>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const PageManager: React.FC<PageManagerProps> = ({
  pages,
  currentPageId,
  isLinking,
  onPageSwitch,
  onPageAdd,
  onPageUpdate,
  onPageDelete,
  onLinkPages,
  className = ''
}) => {
  const [showAddForm, setShowAddForm] = useState(false);

  const handlePageAdd = (page: Omit<Page, 'lastUpdated'>) => {
    onPageAdd(page);
    setShowAddForm(false);
  };

  const pagesWithContent = pages.filter(p => p.content && p.content.length > 50);

  // Check if pages actually need linking (don't already have navigation)
  const checkIfPagesNeedLinking = () => {
    if (pagesWithContent.length < 2) return false;

    // Check if any page is missing navigation links to other pages
    for (const page of pagesWithContent) {
      const otherPageNames = pagesWithContent
        .filter(p => p.id !== page.id)
        .map(p => p.name.toLowerCase());

      // Check if this page's content includes links to other pages
      const pageContent = page.content.toLowerCase();
      const hasAllLinks = otherPageNames.every(pageName =>
        pageContent.includes(pageName) ||
        pageContent.includes(pageName.replace(/\s+/g, '-')) ||
        pageContent.includes(pageName.replace(/\s+/g, ''))
      );

      if (!hasAllLinks) {
        return true; // This page needs linking
      }
    }

    return false; // All pages already have proper navigation
  };

  const canLinkPages = checkIfPagesNeedLinking();

  return (
    <div className={`bg-white border-r border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-4 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Pages</h2>
          <span className="text-sm text-gray-500">
            {pages.length} page{pages.length !== 1 ? 's' : ''}
          </span>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAddForm(true)}
            disabled={showAddForm}
            className="flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FiPlus className="w-4 h-4 mr-1.5" />
            Add Page
          </button>

          <button
            onClick={onLinkPages}
            disabled={!canLinkPages || isLinking}
            className="flex items-center justify-center px-3 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={
              pagesWithContent.length < 2
                ? 'Need at least 2 pages with content'
                : !canLinkPages
                  ? 'All pages already have navigation links'
                  : 'Link all pages with navigation'
            }
          >
            {isLinking ? (
              <FiLoader className="w-4 h-4 animate-spin" />
            ) : (
              <FiLink className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Pages List */}
      <div className="p-4 space-y-3 overflow-y-auto">
        {/* Add Page Form */}
        {showAddForm && (
          <AddPageForm
            onAdd={handlePageAdd}
            onCancel={() => setShowAddForm(false)}
          />
        )}

        {/* Page Items */}
        {pages.map((page) => (
          <PageItem
            key={page.id}
            page={page}
            isActive={page.id === currentPageId}
            onSelect={() => onPageSwitch(page.id)}
            onUpdate={(updates) => onPageUpdate(page.id, updates)}
            onDelete={onPageDelete ? () => onPageDelete(page.id) : undefined}
          />
        ))}

        {/* Empty State */}
        {pages.length === 0 && (
          <div className="text-center py-8">
            <FiFile className="w-8 h-8 text-gray-300 mx-auto mb-3" />
            <p className="text-sm text-gray-500">No pages yet</p>
            <p className="text-xs text-gray-400 mt-1">
              Add your first page to get started
            </p>
          </div>
        )}
      </div>

      {/* Footer Info */}
      {isLinking && (
        <div className="px-4 py-3 border-t border-gray-200 bg-blue-50">
          <div className="flex items-center space-x-2 text-sm text-blue-700">
            <FiLoader className="w-4 h-4 animate-spin" />
            <span>Linking pages...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PageManager;
