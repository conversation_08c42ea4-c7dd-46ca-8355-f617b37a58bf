.planPanel {
  background: #fff;
  border-radius: 1rem;
  box-shadow: 0 4px 24px rgba(79, 70, 229, 0.10);
  padding: 2.5rem 2rem 2rem 2rem;
  max-width: 480px;
  margin: 2.5rem auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.6s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px);}
  to { opacity: 1; transform: translateY(0);}
}

.title {
  font-size: 1.35rem;
  font-weight: 700;
  color: #4f46e5;
  margin-bottom: 1.25rem;
  text-align: center;
}

.featureList {
  width: 100%;
  margin-bottom: 1.5rem;
  padding-left: 0;
}

.featureItem {
  font-size: 1.08rem;
  color: #111827;
  margin-bottom: 0.5rem;
  list-style: none;
  padding-left: 0.5rem;
  transition: color 0.2s;
}

.countdown {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.08rem;
  color: #6366f1;
  font-weight: 600;
}

.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 3px solid #e0e7ff;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  to { transform: rotate(360deg);}
}

.startBtn {
  background: #4f46e5;
  color: #fff;
  border: none;
  border-radius: 0.5rem;
  padding: 0.85rem 1.5rem;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: background 0.2s;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.08);
}

.startBtn:hover,
.startBtn:focus {
  background: #6366f1;
}

/* Dark mode styles */
:global(.dark) .planPanel {
  background: #1f2937;
  color: #f9fafb;
}

:global(.dark) .title {
  color: #a5b4fc;
}

:global(.dark) .featureItem {
  color: #f9fafb;
}

:global(.dark) .countdown {
  color: #a5b4fc;
}

:global(.dark) .startBtn {
  background: #6366f1;
  color: #fff;
}

:global(.dark) .startBtn:hover,
:global(.dark) .startBtn:focus {
  background: #818cf8;
}

/* Responsive styles */
@media (max-width: 600px) {
  .planPanel {
    padding: 1.25rem 0.5rem 1rem 0.5rem;
    max-width: 98vw;
  }
  .title {
    font-size: 1.05rem;
  }
}
