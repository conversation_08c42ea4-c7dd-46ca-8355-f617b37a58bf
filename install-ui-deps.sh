#!/bin/bash
cd ui
echo "Cleaning previous installations..."
rm -rf node_modules package-lock.json dist

echo "Installing core dependencies..."
npm install --save \
  @mui/material@5.13.0 \
  @mui/icons-material@5.13.0 \
  @mui/system@5.13.0 \
  @emotion/react@11.11.0 \
  @emotion/styled@11.11.0 \
  @emotion/cache@11.11.0 \
  react@18.2.0 \
  react-dom@18.2.0 \
  react-router-dom@6.11.0

echo "Installing dev dependencies..."
npm install --save-dev \
  @types/react@18.0.28 \
  @types/react-dom@18.0.11 \
  @types/node@18.16.3 \
  @emotion/babel-plugin@11.11.0 \
  @vitejs/plugin-react@4.0.0 \
  typescript@5.0.2 \
  vite@4.3.2

echo "Creating necessary directories..."
mkdir -p src/types

echo "Setting up environment..."
cat > .env << EOL
VITE_API_URL=http://localhost:3001
EOL

echo "Building project..."
npm run build

echo "Installation complete!"
echo "You can now run: npm run dev"

echo "Note: If you still see TypeScript errors, try:"
echo "1. Delete node_modules/.vite"
echo "2. Restart VS Code"
echo "3. Run: npm run dev"
