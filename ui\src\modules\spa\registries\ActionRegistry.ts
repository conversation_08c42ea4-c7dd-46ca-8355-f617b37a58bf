// ActionRegistry.ts
// SPA Action Registry - Handles all data-action triggers in the SPA

export class ActionRegistry {
  private actions: Record<string, Function>;

  constructor() {
    this.actions = {};
    this.registerDefaultActions();
  }

  registerDefaultActions() {
    // Modal Actions
    this.actions['openModal'] = (targetId: string, params: any, el: HTMLElement) => {
      // Prevent modal opening in edit mode
      if (window && (window as any).spaCore?.isEditMode) {
        console.log('🔒 Modal opening prevented in edit mode');
        return;
      }
      const modal = document.getElementById(targetId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        // Add smooth transition
        modal.style.opacity = '0';
        modal.style.transition = 'opacity 0.3s ease';
        setTimeout(() => modal.style.opacity = '1', 10);
        console.log('✅ Modal opened:', targetId);
      }
    };

    this.actions['closeModal'] = (targetId: string, params: any, el: HTMLElement) => {
      const modal = document.getElementById(targetId);
      if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
          modal.style.display = 'none';
          document.body.style.overflow = 'auto';
          console.log('✅ Modal closed:', targetId);
        }, 300);
      }
    };

    // Chart Actions
    this.actions['initChart'] = (targetId: string, params: any, el: HTMLElement) => {
      const chartId = el.getAttribute('data-chart-id');
      const chartType = el.getAttribute('data-chart-type') || 'line';
      const chartData = JSON.parse(el.getAttribute('data-chart-data') || '[]');
      const chartLabels = JSON.parse(el.getAttribute('data-chart-labels') || '[]');

      if (chartId && (window as any).echarts) {
        const chartContainer = document.getElementById(chartId);
        if (chartContainer) {
          const chart = (window as any).echarts.init(chartContainer);
          const option = {
            title: { text: el.getAttribute('data-chart-title') || 'Chart' },
            tooltip: { trigger: 'axis' },
            xAxis: { type: 'category', data: chartLabels },
            yAxis: { type: 'value' },
            series: [{
              type: chartType,
              data: chartData,
              smooth: chartType === 'line',
              itemStyle: { color: '#3b82f6' }
            }]
          };
          chart.setOption(option);

          // Make chart responsive
          window.addEventListener('resize', () => chart.resize());
          console.log('📊 Chart initialized:', chartId);
        }
      }
    };

    // Form Actions
    this.actions['submitForm'] = (targetId: string, params: any, el: HTMLElement) => {
      console.log('📝 Form submitted:', targetId, params);
      // Add form submission logic here
    };

    // Navigation Actions (handled by router)
    this.actions['navigate'] = (targetId: string, params: any, el: HTMLElement) => {
      const viewName = el.getAttribute('data-nav');
      if (viewName && (window as any).spaCore?.router) {
        (window as any).spaCore.router.navigateToView(viewName);
      }
    };

    // Generic button actions
    this.actions['showAlert'] = (targetId: string, params: any, el: HTMLElement) => {
      const message = el.getAttribute('data-message') || 'Action completed!';
      alert(message);
    };

    // ESC key handler for modals
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const openModals = document.querySelectorAll('[id$="Modal"][style*="flex"]');
        openModals.forEach(modal => {
          this.actions['closeModal'](modal.id, {}, modal as HTMLElement);
        });
      }
    });

    console.log('🎯 Default actions registered: openModal, closeModal, initChart, submitForm, navigate, showAlert');
  }

  execute(action: string, target: string | null, params: any, el: HTMLElement) {
    if (this.actions[action]) {
      this.actions[action](target, params, el);
    }
  }

  // Allow registering custom actions
  register(action: string, handler: Function) {
    this.actions[action] = handler;
  }

  clear() {
    this.actions = {};
  }
}

export default ActionRegistry;
