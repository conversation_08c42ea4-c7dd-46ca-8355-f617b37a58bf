/**
 * Production-ready type definitions for the Editor system
 * Comprehensive types for state management, operations, and data flow
 */

// ============================================================================
// CORE EDITOR TYPES
// ============================================================================

export interface EditorState {
  content: {
    html: string;
    css?: string;
    javascript?: string;
  };
  metadata: {
    title: string;
    description?: string;
    lastModified: Date;
    version: string;
  };
  ui: {
    isGenerating: boolean;
    isLinking: boolean;
    viewMode: ViewMode;
    selectedElement?: ElementInfo;
  };
}

export type ViewMode = 'preview' | 'code' | 'split';

export interface ElementInfo {
  selector: string;
  tagName: string;
  textContent: string;
  attributes: Record<string, string>;
  boundingRect: DOMRect;
  isNavigation: boolean;
  isInteractive: boolean;
}

// ============================================================================
// PAGE MANAGEMENT TYPES
// ============================================================================

export interface Page {
  id: string;
  name: string;
  content: PageContent;
  metadata: PageMetadata;
  status: PageStatus;
}

export interface PageContent {
  html: string;
  css?: string;
  javascript?: string;
  assets?: Asset[];
}

export interface PageMetadata {
  title: string;
  description?: string;
  keywords?: string[];
  createdAt: Date;
  updatedAt: Date;
  version: string;
}

export type PageStatus = 'draft' | 'generating' | 'ready' | 'error';

export interface Asset {
  id: string;
  type: 'image' | 'icon' | 'font' | 'script' | 'style';
  url: string;
  name: string;
  size?: number;
}

// ============================================================================
// EDIT OPERATION TYPES
// ============================================================================

export interface EditOperation {
  id: string;
  type: EditType;
  target: EditTarget;
  payload: EditPayload;
  metadata: OperationMetadata;
}

export type EditType = 
  | 'create' 
  | 'update' 
  | 'delete' 
  | 'style' 
  | 'content' 
  | 'structure' 
  | 'navigation';

export interface EditTarget {
  pageId: string;
  elementSelector?: string;
  scope: 'element' | 'section' | 'page' | 'global';
}

export interface EditPayload {
  prompt: string;
  changes?: Record<string, any>;
  context?: EditContext;
}

export interface EditContext {
  userIntent: string;
  relatedElements?: string[];
  preserveElements?: string[];
  designConstraints?: DesignConstraint[];
}

export interface DesignConstraint {
  type: 'color' | 'typography' | 'spacing' | 'layout' | 'accessibility';
  rules: Record<string, any>;
}

export interface OperationMetadata {
  timestamp: Date;
  userId?: string;
  sessionId: string;
  estimatedDuration?: number;
  priority: 'low' | 'medium' | 'high';
}

// ============================================================================
// API COMMUNICATION TYPES
// ============================================================================

export interface EditRequest {
  operation: EditOperation;
  prototypeId?: string;
  options?: EditOptions;
}

export interface EditOptions {
  streaming?: boolean;
  validateOutput?: boolean;
  preserveFormatting?: boolean;
  generatePreview?: boolean;
}

export interface EditResponse {
  success: boolean;
  result?: EditResult;
  error?: ApiError;
  metadata: ResponseMetadata;
}

export interface EditResult {
  content: PageContent;
  changes: ChangeSet[];
  preview?: string;
  suggestions?: Suggestion[];
}

export interface ChangeSet {
  type: 'addition' | 'modification' | 'deletion';
  target: string;
  before?: string;
  after?: string;
  description: string;
}

export interface Suggestion {
  type: 'improvement' | 'optimization' | 'accessibility' | 'performance';
  description: string;
  implementation?: string;
  impact: 'low' | 'medium' | 'high';
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
}

export interface ResponseMetadata {
  requestId: string;
  duration: number;
  tokensUsed?: number;
  model?: string;
  timestamp: Date;
}

// ============================================================================
// CHAT & CONVERSATION TYPES
// ============================================================================

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  editOperation?: EditOperation;
  attachments?: Attachment[];
  reactions?: Reaction[];
  status: 'sending' | 'sent' | 'delivered' | 'error';
}

export interface Attachment {
  type: 'image' | 'code' | 'link' | 'file';
  content: string;
  metadata?: Record<string, any>;
}

export interface Reaction {
  type: 'like' | 'dislike' | 'helpful' | 'unclear';
  timestamp: Date;
}

export interface Conversation {
  id: string;
  messages: ChatMessage[];
  context: ConversationContext;
  metadata: ConversationMetadata;
}

export interface ConversationContext {
  prototypeId?: string;
  currentPageId: string;
  recentOperations: EditOperation[];
  userPreferences: UserPreferences;
}

export interface UserPreferences {
  designStyle?: 'modern' | 'classic' | 'minimal' | 'bold';
  colorScheme?: 'light' | 'dark' | 'auto';
  codeStyle?: 'compact' | 'verbose' | 'commented';
  assistanceLevel?: 'minimal' | 'guided' | 'detailed';
}

export interface ConversationMetadata {
  startedAt: Date;
  lastActivity: Date;
  messageCount: number;
  operationCount: number;
}

// ============================================================================
// STATE MANAGEMENT TYPES
// ============================================================================

export interface EditorStore {
  // Core state
  editor: EditorState;
  pages: Record<string, Page>;
  currentPageId: string;
  conversation: Conversation;
  
  // UI state
  ui: {
    sidebarOpen: boolean;
    chatOpen: boolean;
    previewMode: ViewMode;
    loading: LoadingState;
  };
  
  // Operations
  operations: {
    pending: EditOperation[];
    history: EditOperation[];
    undoStack: EditOperation[];
    redoStack: EditOperation[];
  };
}

export interface LoadingState {
  isGenerating: boolean;
  isLinking: boolean;
  isSaving: boolean;
  operationId?: string;
  progress?: number;
  message?: string;
}

// ============================================================================
// EVENT TYPES
// ============================================================================

export interface EditorEvent {
  type: EditorEventType;
  payload: any;
  timestamp: Date;
  source: 'user' | 'system' | 'api';
}

export type EditorEventType =
  | 'page.created'
  | 'page.updated'
  | 'page.deleted'
  | 'page.switched'
  | 'edit.started'
  | 'edit.completed'
  | 'edit.failed'
  | 'chat.message'
  | 'element.selected'
  | 'element.deselected'
  | 'navigation.updated'
  | 'preview.refreshed';

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// ============================================================================
// VALIDATION TYPES
// ============================================================================

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type {
  // Re-export commonly used types for convenience
  EditorState as State,
  EditOperation as Operation,
  ChatMessage as Message,
  Page as EditorPage,
  EditRequest as Request,
  EditResponse as Response
};
