.fullPage {
  min-height: 100vh;
  width: 100vw;
  background: var(--bg-color-light, #f9fafb);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.centerBox {
  background: #fff;
  border-radius: 1.25rem;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.10);
  padding: 2.5rem 2rem 2rem 2rem;
  max-width: 540px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.heading {
  font-size: 1.7rem;
  font-weight: 700;
  color: #4f46e5;
  margin-bottom: 1.5rem;
  text-align: center;
}

.input {
  width: 100%;
  min-height: 140px;
  font-size: 1.15rem;
  padding: 1.25rem 1rem;
  border-radius: 0.75rem;
  border: 1.5px solid #e5e7eb;
  margin-bottom: 1.5rem;
  resize: vertical;
  outline: none;
  transition: border 0.2s;
  background: #f9fafb;
  color: #111827;
}

.input:focus {
  border: 1.5px solid #4f46e5;
  background: #fff;
}

.examples {
  margin-bottom: 1.5rem;
}

.examplesLabel {
  font-size: 1rem;
  font-weight: 600;
  color: #6366f1;
  margin-bottom: 0.5rem;
}

.examples ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.example {
  background: #f3f4f6;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  color: #4f46e5;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.example:hover {
  background: #e0e7ff;
  color: #3730a3;
}

.generateBtn {
  margin-top: 0.5rem;
  font-size: 1.15rem;
  font-weight: 700;
  width: 100%;
  padding: 1rem 0;
  border-radius: 0.75rem;
}

/* Dark mode styles */
:global(.dark) .centerBox {
  background: #1f2937;
  color: #f9fafb;
}

:global(.dark) .input {
  background: #374151;
  color: #f9fafb;
  border: 1.5px solid #374151;
}

:global(.dark) .input:focus {
  background: #1f2937;
  border: 1.5px solid #6366f1;
}

:global(.dark) .example {
  background: #374151;
  color: #a5b4fc;
}

:global(.dark) .example:hover {
  background: #6366f1;
  color: #fff;
}

/* Responsive styles */
@media (max-width: 600px) {
  .centerBox {
    padding: 1.25rem 0.5rem 1rem 0.5rem;
    max-width: 98vw;
  }
  .heading {
    font-size: 1.15rem;
  }
}
