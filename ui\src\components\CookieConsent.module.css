.cookieBanner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(17, 24, 39, 0.95);
  color: white;
  z-index: 1000;
  padding: 1rem;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.5s ease-out;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

@media (min-width: 768px) {
  .content {
    flex-direction: row;
    justify-content: space-between;
  }
}

.message {
  margin: 0;
  line-height: 1.5;
  font-size: 0.875rem;
  flex: 1;
}

.link {
  color: #60a5fa;
  margin-left: 0.5rem;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

.buttons {
  display: flex;
  gap: 0.75rem;
}

.button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.acceptButton {
  background-color: #2563eb;
  color: white;
}

.acceptButton:hover {
  background-color: #1d4ed8;
}

.declineButton {
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.declineButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
