/**
 * Main Content Component for EditorPageV3
 * Handles the main content area with prompt input, plan review, and preview
 */

import React from 'react';
import { SPAShell } from '../SPAShell';

interface MainContentProps {
  // Content state
  isCreatingNewPage: boolean;
  projectPages: any[];
  isLoadingProjectPages: boolean;
  projectId: number | undefined;
  state: any; // EditorV3 state

  // Plan review state
  showPlanReview: boolean;
  generatedPlan: any;
  generatedPageName: string;

  // New page creation
  newPagePrompt: string;
  isGeneratingPlan: boolean;

  // SPA mode (hidden functionality)
  spaEditMode: boolean;
  useSPAMode: boolean;
  generationProgress: any;

  // View mode
  viewMode?: 'preview' | 'code';

  // Event handlers
  onNewPagePromptChange: (prompt: string) => void;
  onNewPagePromptSubmit: () => void;
  onCancelNewPage: () => void;
  onRejectPlan: () => void;
  onGeneratePageFromPlan: () => void;
  onElementClick: (element: any) => void;
  onSPAEditModeToggle: () => void;

  // Utility functions
  formatPlanForDisplay: (plan: any) => string;
}

export const MainContent: React.FC<MainContentProps> = ({
  isCreatingNewPage,
  projectPages,
  isLoadingProjectPages,
  projectId,
  state,
  showPlanReview,
  generatedPlan,
  generatedPageName,
  newPagePrompt,
  isGeneratingPlan,
  spaEditMode,
  useSPAMode,
  generationProgress,
  viewMode = 'preview',
  onNewPagePromptChange,
  onNewPagePromptSubmit,
  onCancelNewPage,
  onRejectPlan,
  onGeneratePageFromPlan,
  onElementClick,
  onSPAEditModeToggle,
  formatPlanForDisplay,
}) => {
  const shouldShowPrompt = isCreatingNewPage || (projectId && projectPages.length === 0 && !isLoadingProjectPages);
  const shouldShowCodePreview = state.isGenerating;
  const hasContent = state.htmlContent || state.stableIframeContent;
  // During generation, always show the preview/editor area, not the prompt screen
  const shouldShowPromptScreen = shouldShowPrompt && !shouldShowCodePreview && !hasContent && !state.isGenerating;

  if (shouldShowPromptScreen) {
    if (showPlanReview) {
      return (
        <div className="h-full flex flex-col justify-center px-4 bg-gray-50">
          <div className="w-full max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Review Your Page Plan</h1>
              <p className="text-gray-600">
                Page: <span className="font-semibold text-blue-600">{generatedPageName || 'New Page'}</span>
              </p>
            </div>

            {/* Plan Display */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 mb-6">
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                  {generatedPlan ? formatPlanForDisplay(generatedPlan) : 'Loading plan...'}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-center space-x-4">
              <button
                onClick={onRejectPlan}
                className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
              >
                ← Edit Prompt
              </button>
              <button
                onClick={onGeneratePageFromPlan}
                disabled={state.isGenerating}
                className="flex items-center space-x-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {state.isGenerating ? (
                  <>
                    <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Generating Page...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span>Generate Page</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="h-full flex flex-col justify-center px-4 bg-gray-50">
        <style>{`
          .no-scrollbar::-webkit-scrollbar {
            display: none;
          }
          .no-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
        `}</style>

        <div className="w-full max-w-3xl mx-auto">
          {/* Header */}
          <div className="text-center mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              {projectPages.length === 0 && !isCreatingNewPage ? (
                <>
                  Create your first page
                  <span className="block text-blue-600">for this project</span>
                </>
              ) : (
                <>
                  What would you like to
                  <span className="block text-blue-600">create today?</span>
                </>
              )}
            </h1>
          </div>

          {/* Prompt Input - Main Focus Area */}
          <form onSubmit={(e) => { e.preventDefault(); onNewPagePromptSubmit(); }} className="mb-3">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
              <textarea
                value={newPagePrompt}
                onChange={(e) => onNewPagePromptChange(e.target.value)}
                placeholder="Describe your new page in detail...

For example: 'A login page with email and password fields, forgot password link, and social login options. Use a clean, modern design with blue accents.'"
                className="w-full h-40 px-6 py-4 text-gray-900 placeholder-gray-500 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 no-scrollbar text-base leading-relaxed"
                disabled={state.isGenerating}
                autoFocus
              />

              {/* Action buttons */}
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  {projectPages.length > 0 && (
                    <button
                      type="button"
                      onClick={onCancelNewPage}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                  )}

                  <button
                    type="submit"
                    disabled={!newPagePrompt.trim() || isGeneratingPlan}
                    className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {isGeneratingPlan ? (
                      <>
                        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Generating Plan...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>Generate Plan</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>

          {/* Examples - Compact */}
          <div className="text-center">
            <p className="text-xs text-gray-500 mb-1">Quick examples:</p>
            <div className="flex flex-wrap justify-center gap-1 max-w-4xl mx-auto">
              {[
                'Login page with email and password fields',
                'Pricing page with three subscription tiers',
                'Contact form with company information',
                'About page with team member profiles'
              ].map((example, index) => (
                <button
                  key={index}
                  onClick={() => onNewPagePromptChange(example)}
                  className="px-2 py-1 text-xs text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors disabled:opacity-50"
                  disabled={state.isGenerating}
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Preview/Editor content
  return (
    <div className="h-full relative">
      {/* Generation Progress Indicator */}
      {generationProgress?.isActive && (
        <div className="absolute top-4 right-4 z-10">
          <div className="bg-blue-100 border border-blue-300 rounded-lg px-3 py-2 text-xs font-medium text-blue-800 max-w-xs">
            <div className="flex items-center">
              <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
              <div>
                <p className="font-medium">{generationProgress.message}</p>
                <p className="text-blue-600">
                  {Math.floor((Date.now() - generationProgress.startTime) / 1000)}s elapsed
                </p>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Content Display */}
      {viewMode === 'code' ? (
        /* Code View */
        <div className="h-full bg-gray-900 text-gray-100 p-4 overflow-auto">
          <pre className="text-sm">
            <code>
              {(() => {
                const rawContent = state.htmlContent || state.stableIframeContent || '';
                let cleanContent = rawContent;

                if (rawContent && (rawContent.includes('```html') || rawContent.includes('Here\'s') || rawContent.includes('```'))) {
                  const htmlMatch = rawContent.match(/```html\s*([\s\S]*?)\s*```/);
                  if (htmlMatch) {
                    cleanContent = htmlMatch[1].trim();
                  } else if (rawContent.includes('<') && rawContent.includes('>')) {
                    const firstTagIndex = rawContent.indexOf('<');
                    cleanContent = rawContent.substring(firstTagIndex).trim();
                  }
                }

                return cleanContent || 'No content available';
              })()}
            </code>
          </pre>
        </div>
      ) : (
        /* SPA Mode - SPAShell with conditional SPA functionality */
        <div className="h-full relative">
          {/* Generation Progress Indicator */}
          {generationProgress?.isActive && (
            <div className="absolute top-4 right-4 z-10">
              <div className="bg-blue-100 border border-blue-300 rounded-lg px-3 py-2 text-xs font-medium text-blue-800 max-w-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <div>
                    <p className="font-medium">{generationProgress.message}</p>
                    <p className="text-blue-600">
                      {Math.floor((Date.now() - generationProgress.startTime) / 1000)}s elapsed
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}



          {/* SPAShell - SPA mode enabled but UI controls hidden */}
          <SPAShell
            className="h-full"
            enableEditMode={spaEditMode && useSPAMode} // Enable edit mode when SPA mode is active
            dashboardHtml={(() => {
              const rawContent = state.htmlContent || state.stableIframeContent || '';
              let cleanContent = rawContent;

              // Extract clean HTML from LLM response if needed
              if (rawContent && (rawContent.includes('```html') || rawContent.includes('Here\'s') || rawContent.includes('```'))) {
                const htmlMatch = rawContent.match(/```html\s*([\s\S]*?)\s*```/);
                if (htmlMatch) {
                  cleanContent = htmlMatch[1].trim();
                  console.log('🔧 Extracted HTML from markdown code block');
                } else if (rawContent.includes('<') && rawContent.includes('>')) {
                  const firstTagIndex = rawContent.indexOf('<');
                  cleanContent = rawContent.substring(firstTagIndex).trim();
                  console.log('🔧 Extracted HTML from first tag');
                }
              }

              console.log('🔍 SPAShell rendering:', {
                useSPAMode,
                spaEditMode,
                hasHtmlContent: !!state.htmlContent,
                htmlContentLength: state.htmlContent?.length || 0,
                hasStableContent: !!state.stableIframeContent,
                stableContentLength: state.stableIframeContent?.length || 0,
                rawContent: rawContent.substring(0, 100) || 'Empty',
                cleanContent: cleanContent.substring(0, 100) || 'Empty',
                wasProcessed: cleanContent !== rawContent
              });
              return cleanContent;
            })()}
            onElementClick={useSPAMode ? onElementClick : undefined} // Enable element clicking in SPA mode
            viewMode={viewMode}
            // Use actual SPA mode value (UI controls are hidden but functionality works)
            useSPAMode={useSPAMode}
          />
        </div>
      )}
    </div>
  );
};

export default MainContent;
