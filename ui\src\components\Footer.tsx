import { Link } from 'react-router-dom';
import { useState } from 'react';
import styles from './Footer.module.css';
import { ContactModal } from './ContactModal';

interface FooterLink {
  label: string;
  url: string;
}

interface FooterProps {
  logo?: string;
  links?: FooterLink[];
  className?: string;
}

export function Footer({
  logo = 'JustPrototype',
  links = [
    { label: 'Contact', url: '#contact' },
    { label: 'Discord', url: 'https://discord.com/channels/1371127601736974356/1371127729000419391' },
    { label: 'Privacy Policy', url: '/privacy' }
  ],
  className = ''
}: FooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`${styles.footer} ${className}`}>
      <div className={styles.container}>
        <div className={styles.logo}>{logo}</div>

        <div className={styles.links}>
          {links.map((link) => (
            <FooterLink key={link.label} {...link} />
          ))}
        </div>

        <div className={styles.copyright}>
          © {currentYear} {logo}. All rights reserved.
        </div>
      </div>
    </footer>
  );
}

function FooterLink({ label, url }: FooterLink) {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  // Handle Contact link
  if (url === '#contact') {
    return (
      <>
        <button
          className={styles.link}
          onClick={() => setIsContactModalOpen(true)}
          style={{ background: 'none', border: 'none', cursor: 'pointer', padding: 0 }}
        >
          {label}
        </button>
        <ContactModal
          isOpen={isContactModalOpen}
          onClose={() => setIsContactModalOpen(false)}
        />
      </>
    );
  }

  // Check if the URL is external
  const isExternal = url.startsWith('http');

  if (isExternal) {
    return (
      <a
        href={url}
        className={styles.link}
        target="_blank"
        rel="noopener noreferrer"
      >
        {label}
      </a>
    );
  }

  // For internal links, use React Router's Link
  return (
    <Link to={url} className={styles.link}>
      {label}
    </Link>
  );
}
