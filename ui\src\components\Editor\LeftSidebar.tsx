/**
 * Left Sidebar Component for EditorPageV3
 * Handles project pages list and navigation
 */

import React from 'react';

interface LeftSidebarProps {
  // Panel state
  pagesPanelCollapsed: boolean;
  pagesPanelWidth: number;

  // Project pages data
  projectPages: any[];
  isLoadingProjectPages: boolean;
  currentSessionId: string | null;

  // Page management state
  renamingPageId: string | null;
  newPageName: string;
  openDropdownId: string | null;
  isLoadingPage: boolean;

  // Event handlers
  onCreateNewPage: () => void;
  onProjectPageSelect: (page: any) => void;
  onPagesPanelCollapse: (collapsed: boolean) => void;
  onMouseDown: (type: 'pages') => (e: React.MouseEvent) => void;
  onLinkPages?: () => void;

  // Page operations
  setNewPageName: (name: string) => void;
  setOpenDropdownId: (id: string | null) => void;
  onStartRename: (page: any) => void;
  onCancelRename: () => void;
  onConfirmRename: (pageId: string) => void;
  onStartDelete: (pageId: string) => void;
}

export const LeftSidebar: React.FC<LeftSidebarProps> = ({
  pagesPanelCollapsed,
  pagesPanelWidth,
  projectPages,
  isLoadingProjectPages,
  currentSessionId,
  renamingPageId,
  newPageName,
  openDropdownId,
  isLoadingPage,
  onCreateNewPage,
  onProjectPageSelect,
  onPagesPanelCollapse,
  onMouseDown,
  setNewPageName,
  setOpenDropdownId,
  onStartRename,
  onCancelRename,
  onConfirmRename,
  onStartDelete,
  onLinkPages,
}) => {
  if (pagesPanelCollapsed) {
    return (
      <div className="w-12 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col items-center py-4">
        <button
          onClick={() => onPagesPanelCollapse(false)}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          title="Expand Pages Panel"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
        <div className="mt-4 text-xs text-gray-400 transform -rotate-90 whitespace-nowrap">
          Pages
        </div>
        {projectPages.length > 0 && (
          <div className="mt-2 w-5 h-5 bg-blue-100 text-blue-600 text-xs font-medium rounded-full flex items-center justify-center">
            {projectPages.length}
          </div>
        )}
        {currentSessionId && (
          <div className="mt-4 w-2 h-2 bg-blue-600 rounded-full" title="Current page"></div>
        )}
      </div>
    );
  }

  return (
    <>
      <div
        className="flex-shrink-0 bg-white border-r border-gray-200 relative"
        style={{ width: pagesPanelWidth }}
      >
        {/* Project Pages List */}
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="px-4 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-gray-900">Project Pages</h2>
            </div>

            {/* Create New Page Button */}
            <button
              onClick={onCreateNewPage}
              className="w-full mt-3 flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create New Page
            </button>

            {/* Link Pages Button - Show only when there are 2+ pages */}
            {projectPages.length >= 2 && onLinkPages && (
              <button
                onClick={onLinkPages}
                className="w-full mt-2 flex items-center justify-center px-3 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
              >
                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Link Pages
              </button>
            )}
          </div>

          {/* Pages List */}
          <div className="flex-1 overflow-y-auto p-4">
            {isLoadingProjectPages ? (
              <div className="flex items-center justify-center py-8">
                <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                <span className="text-sm text-gray-500">Loading pages...</span>
              </div>
            ) : projectPages.length > 0 ? (
              <div className="space-y-2">
                {projectPages.map((page) => (
                  <div key={page.id} className="relative">
                    {renamingPageId === page.id ? (
                      /* Rename Input */
                      <div className="p-3 bg-white border border-blue-300 rounded-lg">
                        <input
                          type="text"
                          value={newPageName}
                          onChange={(e) => setNewPageName(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              onConfirmRename(page.id);
                            } else if (e.key === 'Escape') {
                              onCancelRename();
                            }
                          }}
                          className="w-full text-sm font-medium border-0 focus:outline-none focus:ring-0 p-0"
                          autoFocus
                        />
                        <div className="flex items-center justify-end space-x-2 mt-2">
                          <button
                            onClick={onCancelRename}
                            className="px-2 py-1 text-xs text-gray-500 hover:text-gray-700"
                          >
                            Cancel
                          </button>
                          <button
                            onClick={() => onConfirmRename(page.id)}
                            className="px-2 py-1 text-xs text-blue-600 hover:text-blue-800 font-medium"
                          >
                            Save
                          </button>
                        </div>
                      </div>
                    ) : (
                      /* Regular Page Card */
                      <div className={`relative group rounded-lg border transition-colors ${
                        currentSessionId === page.id
                          ? 'bg-blue-50 border-blue-200 shadow-sm'
                          : 'bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                      }`}>
                        <button
                          onClick={() => onProjectPageSelect(page)}
                          disabled={isLoadingPage}
                          className={`w-full text-left p-3 ${isLoadingPage ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h3 className={`text-sm font-medium truncate ${
                                currentSessionId === page.id ? 'text-blue-900' : 'text-gray-900'
                              }`}>
                                {page.title || `Page ${page.id}`}
                              </h3>
                              {page.url && (
                                <p className="text-xs text-gray-500 truncate mt-1">
                                  {page.url}
                                </p>
                              )}
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-xs text-gray-400">
                                  {new Date(page.updated_at).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                            {currentSessionId === page.id && (
                              <div className="ml-2 w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                          </div>
                        </button>

                        {/* Dropdown Menu Button */}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setOpenDropdownId(openDropdownId === page.id ? null : page.id);
                            }}
                            className="dropdown-button p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                            </svg>
                          </button>

                          {/* Dropdown Menu */}
                          {openDropdownId === page.id && (
                            <div
                              className="dropdown-menu absolute right-0 top-8 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  setTimeout(() => {
                                    onStartRename(page);
                                  }, 0);
                                }}
                                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
                              >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Rename
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  setTimeout(() => {
                                    onStartDelete(page.id);
                                  }, 0);
                                }}
                                className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
                              >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-8 h-8 text-gray-300 mx-auto mb-3">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">No pages in this project yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  Create your first page to get started
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Collapse button */}
        <button
          onClick={() => onPagesPanelCollapse(true)}
          className="absolute top-4 right-4 p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors z-10"
          title="Collapse Pages Panel"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      </div>

      {/* Pages Resize Handle */}
      <div
        className="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors flex-shrink-0"
        onMouseDown={onMouseDown('pages')}
        title="Drag to resize pages panel"
      />
    </>
  );
};

export default LeftSidebar;
