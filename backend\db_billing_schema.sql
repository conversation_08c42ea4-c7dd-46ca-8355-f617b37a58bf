-- Add quota and usage tracking to users table
ALTER TABLE users
ADD COLUMN IF NOT EXISTS token_usage INTEGER DEFAULT 0 CHECK (token_usage >= 0),
ADD COLUMN IF NOT EXISTS prototype_count INTEGER DEFAULT 0 CHECK (prototype_count >= 0),
ADD COLUMN IF NOT EXISTS plan VARCHAR(32) DEFAULT 'free',
ADD COLUMN IF NOT EXISTS quota_tokens INTEGER DEFAULT 10000 CHECK (quota_tokens >= 0),
ADD COLUMN IF NOT EXISTS quota_prototypes INTEGER DEFAULT 3 CHECK (quota_prototypes >= 0);

-- Billing table for Stripe or other providers
CREATE TABLE IF NOT EXISTS billing (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    stripe_customer_id VARCHAR(128) UNIQUE,
    plan VARCHAR(32) DEFAULT 'free',
    status VARCHAR(32) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trigger to auto-update updated_at on billing row update
CREATE OR REPLACE FUNCTION update_billing_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_billing_updated_at ON billing;
CREATE TRIGGER set_billing_updated_at
BEFORE UPDATE ON billing
FOR EACH ROW
EXECUTE FUNCTION update_billing_updated_at();

-- Optional: Detailed usage log for analytics/auditing
CREATE TABLE IF NOT EXISTS usage_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    event VARCHAR(64), -- e.g. 'prompt', 'prototype_created'
    tokens_used INTEGER CHECK (tokens_used >= 0),
    context TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for analytics
CREATE INDEX IF NOT EXISTS idx_usage_log_user_id ON usage_log(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_log_event ON usage_log(event);

-- Function: decrement tokens and log usage atomically
CREATE OR REPLACE FUNCTION use_tokens_and_log(
    p_user_id INTEGER,
    p_tokens_used INTEGER,
    p_event VARCHAR,
    p_context TEXT DEFAULT NULL,
    p_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    current_tokens INTEGER;
BEGIN
    SELECT quota_tokens - token_usage INTO current_tokens FROM users WHERE id = p_user_id FOR UPDATE;
    IF current_tokens < p_tokens_used THEN
        RETURN FALSE; -- Not enough tokens
    END IF;

    UPDATE users
    SET token_usage = token_usage + p_tokens_used
    WHERE id = p_user_id;

    INSERT INTO usage_log (user_id, event, tokens_used, context, details)
    VALUES (p_user_id, p_event, p_tokens_used, p_context, p_details);

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function: increment prototype count with free plan enforcement
CREATE OR REPLACE FUNCTION increment_prototype_count(p_user_id INTEGER) RETURNS BOOLEAN AS $$
DECLARE
    user_plan VARCHAR;
    current_count INTEGER;
    quota INTEGER;
BEGIN
    SELECT plan, prototype_count, quota_prototypes INTO user_plan, current_count, quota
    FROM users WHERE id = p_user_id FOR UPDATE;

    IF user_plan = 'free' AND current_count >= 3 THEN
        RETURN FALSE; -- Free plan limit reached
    END IF;

    IF current_count >= quota THEN
        RETURN FALSE; -- Any plan quota reached
    END IF;

    UPDATE users
    SET prototype_count = prototype_count + 1
    WHERE id = p_user_id;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
