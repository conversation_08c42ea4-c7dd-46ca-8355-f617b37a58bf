// Example showing how to use the V2 LLM implementation with SSE streaming

// Frontend Example:
const exampleFrontend = `
// Using EventSource to handle SSE streaming
const generatePlan = async (prompt) => {
  const eventSource = new EventSource('/api/llm/v2/plan');
  let content = '';

  eventSource.addEventListener('start', (event) => {
    const { message } = JSON.parse(event.data);
    console.log('Start:', message);
    // e.g., "I'll help you create a structured plan for your UI components..."
  });

  eventSource.addEventListener('data', (event) => {
    const { token } = JSON.parse(event.data);
    content += token;
    // Update UI with streamed content
  });

  eventSource.addEventListener('end', (event) => {
    const { message, summary } = JSON.parse(event.data);
    console.log('End:', message);
    console.log('Final Content:', summary);
    eventSource.close();
  });

  eventSource.addEventListener('error', (event) => {
    const { error } = JSON.parse(event.data);
    console.error('Error:', error);
    eventSource.close();
  });
}`;

// Example API Calls:

// 1. Generate Plan
const planExample = `
// Endpoint: POST http://localhost:5000/api/llm/v2/plan
fetch('/api/llm/v2/plan', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: "Create a responsive dashboard with charts and data tables",
    provider: "openai" // optional, defaults to openai
  })
});

// SSE Events:
// start: "I'll help you create a structured plan for your UI components..."
// data: "Layout:\n- Responsive grid container\n..."
// end: "I've completed the feature plan. Feel free to proceed with code generation."
`;

// 2. Generate Code
const codeExample = `
// Endpoint: POST http://localhost:5000/api/llm/v2/generate
fetch('/api/llm/v2/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    plan: "Layout:\n- Responsive grid\nComponents:\n- Data table\n...",
    provider: "anthropic" // optional
  })
});

// SSE Events:
// start: "I'll help you implement the planned features and components..."
// data: "<div class=\"dashboard-container\">..."
// end: "I've implemented all the requested features and components."
`;

// 3. Modify Element
const modifyElementExample = `
// Endpoint: POST http://localhost:5000/api/llm/v2/modify-element
fetch('/api/llm/v2/modify-element', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    htmlContent: "<div>...</div>",
    elementSelector: "#dataTable",
    prompt: "Add sorting functionality",
    provider: "openai" // optional
  })
});

// SSE Events:
// start: "I'll help you modify the selected element according to your requirements..."
// data: "<div id=\"dataTable\" class=\"sortable\">..."
// end: "I've modified the element according to your specifications."
`;

// 4. Modify Content
const modifyContentExample = `
// Endpoint: POST http://localhost:5000/api/llm/v2/modify-content
fetch('/api/llm/v2/modify-content', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    htmlContent: "<div>...</div>",
    prompt: "Add dark mode support",
    provider: "anthropic" // optional
  })
});

// SSE Events:
// start: "I'll help you update the content with your requested changes..."
// data: "<div class=\"dark-mode\">..."
// end: "I've updated the content with your requested changes."
`;

// Using curl for testing
const curlExamples = `
# Test plan generation
curl -X POST \\
  http://localhost:5000/api/llm/v2/plan \\
  -H "Content-Type: application/json" \\
  -d '{"prompt": "Create a responsive dashboard", "provider": "openai"}'

# Test code generation
curl -X POST \\
  http://localhost:5000/api/llm/v2/generate \\
  -H "Content-Type: application/json" \\
  -d '{"plan": "Layout:\\n- Responsive grid", "provider": "openai"}'
`;

// Error Handling Example
const errorExample = `
// If an error occurs, you'll receive an error event:
eventSource.addEventListener('error', (event) => {
  const { error } = JSON.parse(event.data);
  console.error('Generation failed:', error);
  // e.g., "API key not set for provider openai (OPENAI_API_KEY)"
  eventSource.close();
});`;

// Token Capture Example
const tokenExample = `
// The StreamCaptureMiddleware automatically captures tokens
// and logs usage after the stream ends:
{
  user_id: 'user123',
  event: 'llm_plan_v2',
  tokens_used: 1234,
  context: 'Create dashboard...',
  timestamp: '2025-05-22T09:48:54.000Z'
}`;
