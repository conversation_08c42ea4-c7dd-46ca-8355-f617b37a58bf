event:data
data:<!DOCTYPE html>
data:<html lang="en">
data:<head>
data:    <meta charset="UTF-8">
data:    <meta name="viewport" content="width=device-width, initial-scale=1.0">
data:

event:data
data:    <title>Todo App</title>
data:    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
data:    <link rel="preconnect" href="https://fonts.googleapis.com">
data:

event:data
data:    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
data:    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
data:

event:data
data:    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
data:    <style>
data:        :where([class^="ri-"])::before { content: "\f3c2"; }
data:

event:data
data:        .task-enter {
data:            opacity: 0;
data:            transform: translateY(-20px);
data:        }
data:        .task-enter-active {
data:            opacity: 1;
data:

event:data
data:            transform: translateY(0);
data:            transition: opacity 300ms, transform 300ms;
data:        }
data:        .task-exit {
data:            opacity: 1;
data:

event:data
data:        }
data:        .task-exit-active {
data:            opacity: 0;
data:            transform: translateY(-20px);
data:            transition: opacity 300ms, transform 300ms;
data:

event:data
data:        }
data:        input[type="checkbox"] {
data:            appearance: none;
data:            -webkit-appearance: none;
data:            width: 1.5rem;
data:

event:data
data:            height: 1.5rem;
data:            border: 2px solid #e2e8f0;
data:            border-radius: 4px;
data:            background-color: white;
data:

event:data
data:            cursor: pointer;
data:            position: relative;
data:        }
data:        input[type="checkbox"]:checked {
data:            background-color: #3b82f6;
data:

event:data
data:            border-color: #3b82f6;
data:        }
data:        input[type="checkbox"]:checked::after {
data:            content: "";
data:            position: absolute;
data:

event:data
data:            top: 50%;
data:            left: 50%;
data:            transform: translate(-50%, -50%) rotate(45deg);
data:            width: 0.25rem;
data:

event:data
data:            height: 0.5rem;
data:            border-bottom: 2px solid white;
data:            border-right: 2px solid white;
data:        }
data:    </style>
data:

event:data
data:    <script>tailwind.config={theme:{extend:{colors:{primary:'#3b82f6',secondary:'#64748b'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
data:

event:data
data:</head>
data:<body class="bg-gray-50 min-h-screen">
data:    <div class="max-w-3xl mx-auto px-4 py-12">
data:        <header class="mb-10 text-center">
data:

event:data
data:            <h1 class="text-4xl font-bold text-gray-800 mb-2 font-['Pacifico']">Todo App</h1>
data:            <p class="text-gray-600">Organize your tasks efficiently</p>
data:

event:data
data:        </header>
data:
data:        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
data:            <div class="flex gap-3">
data:                <input 
data:

event:data
data:                    type="text" 
data:                    id="taskInput" 
data:                    class="flex-1 px-4 py-3 text-gray-700 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" 
data:

event:data
data:                    placeholder="Enter your task"
data:                >
data:                <button 
data:                    id="addTaskBtn" 
data:

event:data
data:                    class="bg-primary text-white px-6 py-3 !rounded-button whitespace-nowrap hover:bg-primary/90 transition-colors"
data:

event:data
data:                >
data:                    <span class="flex items-center gap-2">
data:                        <span class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:                            <i class="ri-add-line"></i>
data:                        </span>
data:                        Add Task
data:                    </span>
data:

event:data
data:                </button>
data:            </div>
data:        </div>
data:
data:        <div class="bg-white rounded-lg shadow-md p-6">
data:            <h2 class="text-xl font-semibold text-gray-800 mb-4">My Tasks</h2>
data:

event:data
data:            
data:            <div id="taskList" class="space-y-3">
data:                <!-- Tasks will be added here -->
data:            </div>
data:

event:data
data:
data:            <div id="emptyState" class="text-center py-10">
data:                <div class="w-16 h-16 mx-auto mb-4 text-gray-400 flex items-center justify-center">
data:

event:data
data:                    <i class="ri-clipboard-line ri-3x"></i>
data:                </div>
data:                <h3 class="text-lg font-medium text-gray-700 mb-1">No tasks yet</h3>
data:

event:data
data:                <p class="text-gray-500">Add your first task to get started</p>
data:            </div>
data:        </div>
data:    </div>
data:
data:    <script id="todo-functionality">
data:

event:data
data:        document.addEventListener('DOMContentLoaded', function() {
data:            const taskInput = document.getElementById('taskInput');
data:

event:data
data:            const addTaskBtn = document.getElementById('addTaskBtn');
data:            const taskList = document.getElementById('taskList');
data:

event:data
data:            const emptyState = document.getElementById('emptyState');
data:            
data:            // Load tasks from localStorage
data:            let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
data:

event:data
data:            
data:            // Render initial tasks
data:            renderTasks();
data:            
data:            // Add task event
data:            addTaskBtn.addEventListener('click', addTask);
data:

event:data
data:            taskInput.addEventListener('keypress', function(e) {
data:                if (e.key === 'Enter') {
data:                    addTask();
data:

event:data
data:                }
data:            });
data:            
data:            function addTask() {
data:                const taskText = taskInput.value.trim();
data:

event:data
data:                if (taskText) {
data:                    const newTask = {
data:                        id: Date.now().toString(),
data:                        text: taskText,
data:

event:data
data:                        completed: false,
data:                        createdAt: new Date().toISOString()
data:                    };
data:                    
data:

event:data
data:                    tasks.unshift(newTask);
data:                    saveTasks();
data:                    renderTasks();
data:                    taskInput.value = '';
data:

event:data
data:                    taskInput.focus();
data:                }
data:            }
data:            
data:            function deleteTask(taskId) {
data:                tasks = tasks.filter(task => task.id !== taskId);
data:

event:data
data:                saveTasks();
data:                renderTasks();
data:            }
data:            
data:            function toggleTaskStatus(taskId) {
data:

event:data
data:                tasks = tasks.map(task => {
data:                    if (task.id === taskId) {
data:                        return { ...task, completed: !task.completed };
data:

event:data
data:                    }
data:                    return task;
data:                });
data:                saveTasks();
data:                renderTasks();
data:

event:data
data:            }
data:            
data:            function saveTasks() {
data:                localStorage.setItem('tasks', JSON.stringify(tasks));
data:

event:data
data:            }
data:            
data:            function renderTasks() {
data:                taskList.innerHTML = '';
data:                
data:                if (tasks.length === 0) {
data:

event:data
data:                    emptyState.classList.remove('hidden');
data:                } else {
data:                    emptyState.classList.add('hidden');
data:

event:data
data:                    
data:                    tasks.forEach(task => {
data:                        const taskElement = document.createElement('div');
data:

event:data
data:                        taskElement.className = 'flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group';
data:

event:data
data:                        taskElement.dataset.id = task.id;
data:                        
data:                        taskElement.innerHTML = `
data:

event:data
data:                            <input type="checkbox" class="mr-3" ${task.completed ? 'checked' : ''}>
data:                            <span class="flex-1 text-gray-800 ${task.completed ? 'line-through text-gray-500' : ''}">
data:

event:data
data:                                ${task.text}
data:                            </span>
data:                            <button class="delete-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity">
data:

event:data
data:                                <i class="ri-delete-bin-line"></i>
data:                            </button>
data:                        `;
data:

event:data
data:                        
data:                        taskList.appendChild(taskElement);
data:                        
data:                        // Add event listeners
data:

event:data
data:                        const checkbox = taskElement.querySelector('input[type="checkbox"]');
data:                        checkbox.addEventListener('change', () => {
data:

event:data
data:                            toggleTaskStatus(task.id);
data:                        });
data:                        
data:                        const deleteBtn = taskElement.querySelector('.delete-btn');
data:

event:data
data:                        deleteBtn.addEventListener('click', () => {
data:                            deleteTask(task.id);
data:                        });
data:

event:data
data:                    });
data:                }
data:            }
data:        });
data:    </script>
data:</body>
data:</html>

event:response
data:{"record_id":3078279,"request_id":"1877f871-f7b3-4227-bf89-e298b51f66de","error":null}

