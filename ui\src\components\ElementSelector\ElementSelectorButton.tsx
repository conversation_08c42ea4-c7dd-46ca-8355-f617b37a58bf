import React, { useState } from 'react';
import { Fi<PERSON><PERSON>hair } from 'react-icons/fi';
import styles from './ElementSelectorButton.module.css';

export interface SelectedElement {
  id: string;
  tagName: string;
  className?: string;
  elementId?: string;
  ref: string;
  element?: HTMLElement;
  styles?: Record<string, string>;
}

interface ElementSelectorButtonProps {
  iframeRef: React.RefObject<HTMLIFrameElement>;
  onElementSelect?: (element: SelectedElement) => void;
  disabled?: boolean;
}

export const ElementSelectorButton: React.FC<ElementSelectorButtonProps> = ({
  iframeRef,
  onElementSelect,
  disabled = false
}) => {
  const [isSelecting, setIsSelecting] = useState(false);

  // Toggle selection mode
  const toggleSelectionMode = () => {
    const newSelectionState = !isSelecting;
    setIsSelecting(newSelectionState);

    // Send message to iframe to toggle selection mode
    if (iframeRef.current && iframeRef.current.contentWindow) {
      console.log('Sending toggleSelectionMode message to iframe:', newSelectionState);
      iframeRef.current.contentWindow.postMessage({
        type: 'toggleSelectionMode',
        active: newSelectionState
      }, '*');
    } else {
      console.error('Cannot access iframe content window');
    }
  };

  return (
    <div className={styles.elementSelectorContainer}>
      <button
        className={`${styles.selectorButton} ${isSelecting ? styles.active : ''}`}
        onClick={toggleSelectionMode}
        disabled={disabled}
        title={isSelecting ? "Exit selection mode" : "Select elements"}
      >
        <FiCrosshair />
      </button>
    </div>
  );
};
