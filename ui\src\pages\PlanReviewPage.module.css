.planReviewPage {
  max-width: 600px;
  margin: 0 auto;
  padding: 16px 0 32px 0;
  font-size: 0.98rem;
}

.heading {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-align: center;
}

.featureList {
  margin-bottom: 18px;
  padding: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 0;
  margin: 0;
  font-size: 0.97em;
  min-height: 28px;
}

.sectionHeading {
  background: none;
  font-weight: 600;
  font-size: 1em;
  margin-top: 6px;
  margin-bottom: 2px;
  padding: 0 0 0 2px;
  color: #2a2a2a;
  letter-spacing: 0.01em;
  border: none;
}

.checkbox {
  margin-right: 4px;
  width: 16px;
  height: 16px;
}

.featureText {
  flex: 1;
  font-size: 0.97em;
  padding: 0 2px;
  margin: 0;
  line-height: 1.25;
}

.featureInput {
  flex: 1;
  font-size: 0.97em;
  padding: 2px 4px;
  margin: 0;
  border-radius: 4px;
  border: 1px solid #bbb;
  background: #f8f8f8;
}

.editBtn {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 2px 4px;
  font-size: 1em;
  margin-left: 2px;
  transition: color 0.15s;
}
.editBtn:hover {
  color: #333;
}

.addBtn {
  background: none;
  border: none;
  color: #0074d9;
  cursor: pointer;
  font-size: 0.97em;
  margin-top: 4px;
  margin-left: 2px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 0;
}
.addBtn:hover {
  color: #005fa3;
}

.adding {
  background: #f3f7fa;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.regenBtn {
  font-size: 0.97em;
  padding: 4px 10px;
}

.proceedBtn {
  font-size: 1em;
  padding: 6px 16px;
}

.error {
  color: #c00;
  background: #fff0f0;
  border: 1px solid #fbb;
  border-radius: 4px;
  padding: 4px 8px;
  margin-bottom: 8px;
  font-size: 0.97em;
}

.streamingNotice {
  margin-top: 10px;
  text-align: center;
  color: #888;
  font-size: 0.95em;
}
