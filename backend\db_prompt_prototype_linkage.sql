-- SQL file to ensure proper linkage between prompts and prototypes
-- Run this file to update the schema and fix any existing data

-- First, check if the prompt_id column exists in the prototypes table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prototypes' AND column_name = 'prompt_id'
    ) THEN
        -- Add the prompt_id column if it doesn't exist
        ALTER TABLE prototypes 
        ADD COLUMN prompt_id INTEGER REFERENCES prompts(id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added prompt_id column to prototypes table';
    ELSE
        RAISE NOTICE 'prompt_id column already exists in prototypes table';
    END IF;
END $$;

-- Make sure the foreign key constraint exists and is correct
DO $$
BEGIN
    -- Check if the constraint exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu 
            ON tc.constraint_name = ccu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND tc.table_name = 'prototypes' 
            AND ccu.column_name = 'id'
            AND ccu.table_name = 'prompts'
    ) THEN
        -- Drop any existing constraint that might be incorrect
        BEGIN
            ALTER TABLE prototypes DROP CONSTRAINT IF EXISTS prototypes_prompt_id_fkey;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not drop constraint: %', SQLERRM;
        END;
        
        -- Add the correct foreign key constraint
        ALTER TABLE prototypes
        ADD CONSTRAINT prototypes_prompt_id_fkey
        FOREIGN KEY (prompt_id)
        REFERENCES prompts(id)
        ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key constraint for prompt_id';
    ELSE
        RAISE NOTICE 'Foreign key constraint for prompt_id already exists';
    END IF;
END $$;

-- Create an index on prompt_id for better query performance
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'prototypes' AND indexname = 'idx_prototypes_prompt_id'
    ) THEN
        CREATE INDEX idx_prototypes_prompt_id ON prototypes(prompt_id);
        RAISE NOTICE 'Created index on prototypes.prompt_id';
    ELSE
        RAISE NOTICE 'Index on prototypes.prompt_id already exists';
    END IF;
END $$;

-- Make sure the prompts table has the necessary columns
DO $$
BEGIN
    -- Check if user_id column exists in prompts table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prompts' AND column_name = 'user_id'
    ) THEN
        -- Add the user_id column if it doesn't exist
        ALTER TABLE prompts 
        ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added user_id column to prompts table';
    ELSE
        RAISE NOTICE 'user_id column already exists in prompts table';
    END IF;
    
    -- Check if prompt_text column exists in prompts table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prompts' AND column_name = 'prompt_text'
    ) THEN
        -- Add the prompt_text column if it doesn't exist
        ALTER TABLE prompts 
        ADD COLUMN prompt_text TEXT NOT NULL DEFAULT '';
        
        RAISE NOTICE 'Added prompt_text column to prompts table';
    ELSE
        RAISE NOTICE 'prompt_text column already exists in prompts table';
    END IF;
END $$;

-- Make sure the prompt_iterations table has the necessary columns
DO $$
BEGIN
    -- Check if prompt_id column exists in prompt_iterations table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prompt_iterations' AND column_name = 'prompt_id'
    ) THEN
        -- Add the prompt_id column if it doesn't exist
        ALTER TABLE prompt_iterations 
        ADD COLUMN prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added prompt_id column to prompt_iterations table';
    ELSE
        RAISE NOTICE 'prompt_id column already exists in prompt_iterations table';
    END IF;
    
    -- Check if iteration_number column exists in prompt_iterations table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prompt_iterations' AND column_name = 'iteration_number'
    ) THEN
        -- Add the iteration_number column if it doesn't exist
        ALTER TABLE prompt_iterations 
        ADD COLUMN iteration_number INTEGER NOT NULL DEFAULT 1;
        
        RAISE NOTICE 'Added iteration_number column to prompt_iterations table';
    ELSE
        RAISE NOTICE 'iteration_number column already exists in prompt_iterations table';
    END IF;
    
    -- Check if input_text column exists in prompt_iterations table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prompt_iterations' AND column_name = 'input_text'
    ) THEN
        -- Add the input_text column if it doesn't exist
        ALTER TABLE prompt_iterations 
        ADD COLUMN input_text TEXT NOT NULL DEFAULT '';
        
        RAISE NOTICE 'Added input_text column to prompt_iterations table';
    ELSE
        RAISE NOTICE 'input_text column already exists in prompt_iterations table';
    END IF;
    
    -- Check if output_text column exists in prompt_iterations table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'prompt_iterations' AND column_name = 'output_text'
    ) THEN
        -- Add the output_text column if it doesn't exist
        ALTER TABLE prompt_iterations 
        ADD COLUMN output_text TEXT;
        
        RAISE NOTICE 'Added output_text column to prompt_iterations table';
    ELSE
        RAISE NOTICE 'output_text column already exists in prompt_iterations table';
    END IF;
END $$;

-- Create indexes for better query performance
DO $$
BEGIN
    -- Index on prompts.user_id
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'prompts' AND indexname = 'idx_prompts_user_id'
    ) THEN
        CREATE INDEX idx_prompts_user_id ON prompts(user_id);
        RAISE NOTICE 'Created index on prompts.user_id';
    ELSE
        RAISE NOTICE 'Index on prompts.user_id already exists';
    END IF;
    
    -- Index on prompt_iterations.prompt_id
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'prompt_iterations' AND indexname = 'idx_prompt_iterations_prompt_id'
    ) THEN
        CREATE INDEX idx_prompt_iterations_prompt_id ON prompt_iterations(prompt_id);
        RAISE NOTICE 'Created index on prompt_iterations.prompt_id';
    ELSE
        RAISE NOTICE 'Index on prompt_iterations.prompt_id already exists';
    END IF;
END $$;

-- Add a function to get all iterations for a prototype
CREATE OR REPLACE FUNCTION get_prototype_prompt_iterations(p_prototype_id INTEGER)
RETURNS TABLE (
    prompt_id INTEGER,
    iteration_number INTEGER,
    input_text TEXT,
    output_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pi.prompt_id,
        pi.iteration_number,
        pi.input_text,
        pi.output_text,
        pi.created_at
    FROM 
        prototypes p
    JOIN 
        prompt_iterations pi ON p.prompt_id = pi.prompt_id
    WHERE 
        p.id = p_prototype_id
    ORDER BY 
        pi.iteration_number ASC;
END;
$$ LANGUAGE plpgsql;

-- Print completion message
DO $$
BEGIN
    RAISE NOTICE 'Schema update for prompt-prototype linkage completed successfully.';
END $$;
