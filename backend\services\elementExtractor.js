// Element Extraction Service
// Responsible for extracting elements from HTML, generating selectors, and validating/sanitizing elements.

const { parseDOM } = require('../utils/domParser');

/**
 * Extracts elements from HTML string.
 * @param {string} html
 * @returns {Array} Array of element objects
 */
function extractElements(html) {
  const dom = parseDOM(html);
  // Traverse DOM and extract interactive elements (buttons, inputs, links, etc.)
  const elements = [];
  const traverse = (node, parentSelector = '') => {
    if (!node || !node.tagName) return;
    const tag = node.tagName.toLowerCase();
    // Only extract interactive elements
    if (['button', 'a', 'input', 'select', 'textarea'].includes(tag)) {
      elements.push({
        tagName: tag,
        id: node.id || '',
        classNames: node.classList ? Array.from(node.classList) : [],
        text: node.text || '',
        attributes: node.rawAttrs || '',
        selector: generateSelectorFromNode(node, parentSelector),
      });
    }
    if (node.childNodes && node.childNodes.length) {
      node.childNodes.forEach(child => traverse(child, parentSelector));
    }
  };
  traverse(dom, '');
  return elements;
}

// Helper to generate a selector from a node
function generateSelectorFromNode(node, parentSelector = '') {
  let selector = node.tagName ? node.tagName.toLowerCase() : '';
  if (node.id) selector += `#${node.id}`;
  if (node.classList && node.classList.length)
    selector += '.' + Array.from(node.classList).join('.');
  return parentSelector ? `${parentSelector} ${selector}` : selector;
}

/**
 * Generates a unique selector for a given element.
 * @param {Object} element
 * @returns {string} Selector string
 */
function generateSelector(element) {
  // Generate a CSS selector from element info
  let selector = element.tagName;
  if (element.id) selector += `#${element.id}`;
  if (element.classNames && element.classNames.length)
    selector += '.' + element.classNames.join('.');
  return selector;
}

/**
 * Validates and sanitizes an element.
 * @param {Object} element
 * @returns {Object} Sanitized element or null if invalid
 */
function validateAndSanitize(element) {
  // Basic validation: must have tagName and selector
  if (!element.tagName || !element.selector) return null;
  // Remove dangerous attributes
  const sanitized = { ...element };
  delete sanitized.onClick;
  delete sanitized.onclick;
  return sanitized;
}

module.exports = {
  extractElements,
  generateSelector,
  validateAndSanitize,
};
