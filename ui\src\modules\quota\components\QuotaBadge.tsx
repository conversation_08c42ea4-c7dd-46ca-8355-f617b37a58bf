import React from 'react';
import styles from './QuotaBadge.module.css';
import { QuotaInfo } from '../QuotaModule';

interface QuotaBadgeProps {
  /**
   * Quota information
   */
  quota: QuotaInfo;

  /**
   * Item name (e.g., "prototype", "credit")
   */
  itemName?: string;

  /**
   * Optional click handler
   */
  onClick?: () => void;

  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * A badge component that displays quota information
 */
export const QuotaBadge: React.FC<QuotaBadgeProps> = ({
  quota,
  itemName = 'prototype',
  onClick,
  className = ''
}) => {
  const { plan, remainingCount } = quota;
  const isPro = plan.toLowerCase() !== 'free';

  // Determine badge style based on quota status
  const getBadgeStyle = () => {
    if (remainingCount <= 0) {
      return styles.exceeded;
    }

    if (remainingCount <= 1) {
      return styles.warning;
    }

    return isPro ? styles.pro : styles.free;
  };

  // Determine badge text based on quota status
  const getBadgeText = () => {
    if (remainingCount <= 0) {
      return `${isPro ? 'Pro' : 'Free'} limit reached`;
    }

    const itemText = remainingCount === 1 ? itemName : `${itemName}s`;

    if (isPro) {
      return `Pro: ${remainingCount} ${itemText} left`;
    }

    return `Free: ${remainingCount} ${itemText} left`;
  };

  return (
    <div
      className={`${styles.badge} ${getBadgeStyle()} ${className} ${onClick ? styles.clickable : ''}`}
      onClick={onClick}
    >
      {getBadgeText()}

      {remainingCount <= 0 && !isPro && (
        <span
          className={styles.upgradeText}
          onClick={(e) => {
            e.stopPropagation(); // Prevent parent click handler
            window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');
          }}
        >
          Upgrade
        </span>
      )}
    </div>
  );
};
