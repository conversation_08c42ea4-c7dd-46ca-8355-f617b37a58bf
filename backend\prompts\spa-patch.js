/**
 * SPA Patch Prompts
 * Specialized prompts for SPA view editing and improvements
 */

const spaPatchPrompts = {
  /**
   * Base SPA editing prompt
   */
  editView: (viewName, currentContent, userRequest) => `
You are a SPA (Single Page Application) view editor. Your task is to improve the provided HTML content based on the user's request.

CONTEXT:
- View Name: ${viewName}
- This is part of a larger SPA with navigation and dynamic components
- User Request: ${userRequest}

REQUIREMENTS:
1. Only modify the provided HTML content - do not add wrapper divs or extra structure
2. Maintain ALL existing data-component attributes (they're used for dynamic components like charts, grids)
3. Preserve the same overall structure and semantic layout
4. Use Tailwind CSS classes for styling improvements
5. Ensure responsive design with proper mobile/tablet breakpoints
6. Keep accessibility attributes and semantic HTML
7. Maintain existing IDs and important classes for JavaScript functionality

CURRENT CONTENT TO IMPROVE:
${currentContent}

Please provide the improved HTML content that directly replaces the current content while following all requirements above.`,

  /**
   * Component enhancement prompt
   */
  enhanceComponent: (componentType, currentContent, enhancement) => `
You are enhancing a ${componentType} component within a SPA. 

ENHANCEMENT REQUEST: ${enhancement}

CURRENT COMPONENT HTML:
${currentContent}

REQUIREMENTS:
- Keep all data-component attributes intact
- Improve styling with Tailwind CSS
- Maintain component functionality
- Ensure responsive design

Provide the enhanced component HTML:`,

  /**
   * Layout improvement prompt
   */
  improveLayout: (currentContent) => `
Improve the layout and visual hierarchy of this SPA view content.

FOCUS ON:
- Better spacing and typography
- Improved visual hierarchy
- Responsive grid layouts
- Modern card-based designs
- Consistent color scheme

MAINTAIN:
- All data-* attributes
- Existing functionality
- Semantic structure

CURRENT CONTENT:
${currentContent}

Provide improved HTML:`,

  /**
   * Accessibility enhancement prompt
   */
  enhanceAccessibility: (currentContent) => `
Enhance the accessibility of this SPA view while maintaining all functionality.

IMPROVEMENTS TO ADD:
- ARIA labels and roles
- Screen reader friendly text
- Keyboard navigation support
- Color contrast improvements
- Focus management

CURRENT CONTENT:
${currentContent}

Provide accessibility-enhanced HTML:`,

  /**
   * Mobile responsiveness prompt
   */
  makeMobileResponsive: (currentContent) => `
Optimize this SPA view for mobile devices while keeping desktop experience intact.

MOBILE OPTIMIZATIONS:
- Stack layouts vertically on small screens
- Improve touch targets
- Optimize spacing for mobile
- Ensure text readability
- Hide/show elements appropriately

CURRENT CONTENT:
${currentContent}

Provide mobile-optimized HTML:`
};

module.exports = spaPatchPrompts;
