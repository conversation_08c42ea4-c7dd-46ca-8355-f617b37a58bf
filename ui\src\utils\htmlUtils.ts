/**
 * Utility functions for working with HTML content as strings
 */

import { ElementProperty } from '../components/ElementProperties/ElementProperties';

/**
 * Finds an element in HTML content using a CSS selector and applies style changes
 * @param htmlContent The original HTML content
 * @param selector The CSS selector to find the element
 * @param property The property to apply
 * @returns The modified HTML content
 */
export function updateElementStyleInHtml(
  htmlContent: string,
  selector: string,
  property: ElementProperty
): string {
  try {
    console.log('Updating element style in HTML, selector:', selector);

    // Create a DOM parser to work with the HTML content
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // Find the element using the selector
    const element = doc.querySelector(selector) as HTMLElement;
    if (!element) {
      console.error(`Element not found with selector: ${selector}`);
      return htmlContent;
    }

    console.log('Element found:', element.tagName);

    // Apply the property based on its name
    switch (property.name) {
      case 'MarginX':
        element.style.marginLeft = `${property.value}${property.unit || 'px'}`;
        element.style.marginRight = `${property.value}${property.unit || 'px'}`;
        break;
      case 'MarginY':
        element.style.marginTop = `${property.value}${property.unit || 'px'}`;
        element.style.marginBottom = `${property.value}${property.unit || 'px'}`;
        break;
      case 'PaddingX':
        element.style.paddingLeft = `${property.value}${property.unit || 'px'}`;
        element.style.paddingRight = `${property.value}${property.unit || 'px'}`;
        break;
      case 'PaddingY':
        element.style.paddingTop = `${property.value}${property.unit || 'px'}`;
        element.style.paddingBottom = `${property.value}${property.unit || 'px'}`;
        break;
      case 'Background':
        element.style.backgroundColor = property.value;
        break;
      case 'Border radius':
        applyBorderRadiusToElement(element, property.value);
        break;
      case 'Text Content':
        // Update the text content of the element
        element.textContent = property.value;
        break;
      case 'advancedCSS':
        applyStylesToElement(element, property.value);
        break;
      default:
        console.warn(`Unknown property: ${property.name}`);
    }

    // Get the updated HTML content
    const updatedHtml = doc.body.innerHTML;
    console.log('Updated HTML content length:', updatedHtml.length);

    // Return the updated HTML content
    return updatedHtml;
  } catch (error) {
    console.error('Error updating element style in HTML:', error);
    return htmlContent;
  }
}

/**
 * Applies border radius to an element based on predefined sizes
 * @param element The element to apply the border radius to
 * @param value The border radius value or size name
 */
function applyBorderRadiusToElement(element: HTMLElement, value: string): void {
  switch (value) {
    case 'None':
      element.style.borderRadius = '0';
      break;
    case 'XS':
      element.style.borderRadius = '4px';
      break;
    case 'SM':
      element.style.borderRadius = '8px';
      break;
    case 'MD':
      element.style.borderRadius = '12px';
      break;
    case 'LG':
      element.style.borderRadius = '16px';
      break;
    case 'XL':
      element.style.borderRadius = '24px';
      break;
    case 'Full':
      element.style.borderRadius = '9999px';
      break;
    default:
      element.style.borderRadius = value;
  }
}

/**
 * Applies CSS properties to an element
 * @param element The element to apply styles to
 * @param cssText The CSS text to parse and apply
 */
function applyStylesToElement(element: HTMLElement, cssText: string): void {
  // Quick exit for empty CSS
  if (!cssText.trim()) return;

  // Parse CSS properties
  const properties = cssText.split(';')
    .filter(Boolean)
    .map(prop => {
      const [key, value] = prop.split(':').map(p => p.trim());
      return { key, value };
    })
    .filter(({ key, value }) => key && value);

  // Apply properties in batch
  properties.forEach(({ key, value }) => {
    const camelKey = kebabToCamelCase(key);
    try {
      (element.style as any)[camelKey] = value;
    } catch (error) {
      console.warn(`Failed to apply style "${key}: ${value}"`, error);
    }
  });
}

/**
 * Converts a kebab-case string to camelCase
 * @param str The kebab-case string to convert
 * @returns The camelCase version of the string
 */
function kebabToCamelCase(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}
