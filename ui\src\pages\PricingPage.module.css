.pricingRoot {
  max-width: 700px;
  margin: 0 auto;
  padding: 2.5rem 1rem 3rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #23272f;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.subtext {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.cardSection {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 2.5rem;
}

.pricingCard {
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 4px 24px 0 rgba(37,99,235,0.10);
  padding: 2.5rem 2rem 2rem 2rem;
  max-width: 370px;
  width: 100%;
  text-align: center;
  transition: box-shadow 0.2s, transform 0.2s;
  margin: 0 auto;
}

.pricingCard:hover {
  box-shadow: 0 8px 32px 0 rgba(37,99,235,0.18);
  transform: translateY(-2px) scale(1.02);
}

.planName {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
  letter-spacing: 0.01em;
}

.price {
  font-size: 2.5rem;
  font-weight: 800;
  color: #23272f;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 0.3em;
}

.priceValue {
  font-size: 2.5rem;
  font-weight: 800;
  color: #23272f;
}

.pricePeriod {
  font-size: 1.1rem;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 0.3em;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0 2rem 0;
  color: #23272f;
  font-size: 1.08rem;
  text-align: left;
}

.featuresList li {
  margin-bottom: 0.7em;
  padding-left: 1.5em;
  position: relative;
}

.featuresList li:before {
  content: "✔";
  color: #22c55e;
  position: absolute;
  left: 0;
  font-size: 1.1em;
}

.ctaButton {
  display: inline-block;
  background: #2563eb;
  color: #fff;
  font-weight: 700;
  padding: 0.85em 2.2em;
  border-radius: 999px;
  text-decoration: none;
  font-size: 1.15rem;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
  transition: background 0.2s, color 0.2s, transform 0.1s;
  margin-top: 0.5em;
}

.ctaButton:hover {
  background: #1e40af;
  color: #fff;
  transform: translateY(-2px) scale(1.03);
}

.faqSection {
  width: 100%;
  max-width: 600px;
  margin: 2.5rem auto 0 auto;
}

.faqTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #23272f;
  margin-bottom: 1.2rem;
  text-align: center;
}

.faqList {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.faqItem {
  background: #f3f4f6;
  border-radius: 1rem;
  padding: 1.1rem 1.3rem;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.03);
}

.faqQ {
  font-weight: 700;
  color: #23272f;
  margin-bottom: 0.3em;
}

.faqA {
  color: #374151;
  font-size: 1.05rem;
}

@media (max-width: 700px) {
  .pricingRoot {
    padding: 1.5rem 0.5rem 2rem 0.5rem;
  }
  .pricingCard {
    padding: 1.5rem 0.7rem 1.2rem 0.7rem;
    max-width: 100%;
  }
  .faqSection {
    padding: 0 0.2rem;
  }
}
