.shareButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.shareIcon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Variants */
.primary {
  background-color: #4f46e5;
  color: white;
  border: none;
}

.primary:hover {
  background-color: #4338ca;
}

.secondary {
  background-color: white;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.secondary:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.text {
  background-color: transparent;
  color: #4f46e5;
  border: none;
}

.text:hover {
  background-color: rgba(79, 70, 229, 0.05);
}

/* Sizes */
.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.small .shareIcon {
  width: 1rem;
  height: 1rem;
}

.medium {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.large {
  padding: 0.625rem 1.25rem;
  font-size: 1rem;
}

.large .shareIcon {
  width: 1.5rem;
  height: 1.5rem;
}
