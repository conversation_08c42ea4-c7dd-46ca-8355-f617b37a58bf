{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://justprototype.dev/schemas/uinode.schema.json", "title": "UI Node Schema", "description": "Schema for validating UI component tree nodes", "type": "object", "required": ["id", "type", "props"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["Page", "Section", "<PERSON><PERSON>", "Input", "Text", "Image", "Link", "Form"]}, "props": {"type": "object", "additionalProperties": true}, "children": {"type": "array", "items": {"$ref": "#"}}}}