.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalHidden {
  display: none;
}

.modalContent {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modalHeader {
  padding: 20px 20px 10px;
  text-align: center;
}

.modalTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1F2937;
  margin: 0;
}

.modalBody {
  padding: 0 20px 20px;
  text-align: center;
}

.modalText {
  color: #4B5563;
  margin-bottom: 16px;
}

.progressContainer {
  margin-bottom: 24px;
  padding: 0 16px;
}

.progressLabels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.progressLabel {
  font-size: 0.875rem;
  color: #4B5563;
}

.progressBar {
  height: 8px;
  width: 100%;
  background-color: rgba(209, 213, 219, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background-color: #8B5CF6;
  border-radius: 4px;
}

.progressFillExceeded {
  background-color: #EF4444;
}

.benefitsBox {
  padding: 24px;
  background-color: rgba(139, 92, 246, 0.05);
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.benefitsTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #8B5CF6;
  margin-bottom: 8px;
}

.benefitItem {
  color: #4B5563;
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.modalFooter {
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.secondaryButton {
  background-color: transparent;
  border: 1px solid #E5E7EB;
  color: #4B5563;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
}

.secondaryButton:hover {
  background-color: rgba(75, 85, 99, 0.04);
  border-color: #D1D5DB;
}

.primaryButton {
  background-color: #8B5CF6;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
}

.primaryButton:hover {
  background-color: #7C3AED;
}
