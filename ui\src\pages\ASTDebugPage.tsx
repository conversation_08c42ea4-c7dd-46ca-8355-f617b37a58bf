import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON>, 
  Button, 
  Container, 
  TextField, 
  Typography, 
  Paper, 
  Stack, 
  IconButton, 
  Divider 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import { codeAstService } from '../services/codeAstService';
import type { FormattedCodeEvent, ASTMetadata } from '../services/codeAstService';

interface LogBoxProps {
  title: string;
  children: React.ReactNode;
}

const LogBox: React.FC<LogBoxProps> = ({ title, children }) => (
  <Paper
    elevation={2}
    sx={{
      p: 2,
      my: 2,
      maxHeight: '300px',
      overflow: 'auto',
      bgcolor: 'background.paper'
    }}
  >
    <Typography variant="subtitle2" color="primary" gutterBottom>
      {title}
    </Typography>
    <pre style={{ margin: 0, fontFamily: 'monospace', fontSize: '0.875rem' }}>
      {children}
    </pre>
  </Paper>
);

const ASTDebugPage: React.FC = () => {
  const [prompt, setPrompt] = useState('Create a login page');
  const [planOutput, setPlanOutput] = useState('');
  const [codeOutput, setCodeOutput] = useState('');
  const [rawEvents, setRawEvents] = useState<string[]>([]);
  const [sseLog, setSseLog] = useState<string[]>([]);
  const [currentProvider] = useState('openai');
  const eventSourceRef = useRef<EventSource | null>(null);

  const logSSE = (msg: string) => {
    setSseLog(prev => [...prev, msg]);
  };

  const logRawEvent = (event: string, data: string) => {
    setRawEvents(prev => [...prev, `event:${event}\ndata:${data}\n`]);
  };

  const clearLogs = () => {
    setPlanOutput('');
    setCodeOutput('');
    setSseLog([]);
    setRawEvents([]);
  };

  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const generatePlan = async () => {
    clearLogs();
    logSSE('Starting plan generation...');

    let currentMessage = '';

    eventSourceRef.current = codeAstService.generatePlan(
      prompt,
      currentProvider,
      { streamTokens: true },
      (token: string) => {
        currentMessage += token;
        setPlanOutput(currentMessage);
        logRawEvent('data', token);
      },
      (error: string) => {
        logSSE(`[Error] ${error}`);
        logRawEvent('error', error);
      },
      () => {
        logSSE('[Complete] Plan generation finished');
        logRawEvent('end', '');
      }
    );
  };

  const generateCode = async () => {
    clearLogs();
    logSSE('Starting code generation...');

    let currentMessage = '';

    eventSourceRef.current = codeAstService.generateCodeWithAST(
      planOutput || prompt,
      currentProvider,
      {
        streamTokens: true,
        language: 'html',
        transformations: [
          codeAstService.createTransformations.sanitizeHTML(),
          codeAstService.createTransformations.improveAccessibility()
        ]
      },
      (token: string) => {
        currentMessage += token;
        setCodeOutput(currentMessage);
        logRawEvent('data', token);
      },
      (metadata: ASTMetadata) => {
        logSSE(`[AST Metadata] ${JSON.stringify(metadata, null, 2)}`);
        logRawEvent('ast_metadata', JSON.stringify(metadata));
      },
      (event: FormattedCodeEvent) => {
        logSSE(`[Formatted] ${event.code.substring(0, 100)}...`);
        logRawEvent('formatted_code', event.code);
      },
      (error: string) => {
        logSSE(`[Error] ${error}`);
        logRawEvent('error', error);
      },
      () => {
        logSSE('[Complete] Code generation finished');
        logRawEvent('end', '');
      }
    );
  };

  const cancelGeneration = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      logSSE('[Cancelled] Generation stopped');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>AST Debug Page</Typography>
      
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>Input Prompt</Typography>
        <TextField
          multiline
          minRows={3}
          fullWidth
          value={prompt}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setPrompt(e.target.value)}
          placeholder="Enter your prompt here..."
          variant="outlined"
          sx={{ mb: 2 }}
        />
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PlayArrowIcon />}
            onClick={generatePlan}
          >
            Generate Plan
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PlayArrowIcon />}
            onClick={generateCode}
          >
            Generate Code
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<StopIcon />}
            onClick={cancelGeneration}
          >
            Cancel
          </Button>
        </Stack>
      </Box>

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
        <Box>
          <Typography variant="h6" gutterBottom>Plan Output</Typography>
          <LogBox title="Plan">
            {planOutput || 'No plan generated yet'}
          </LogBox>
        </Box>

        <Box>
          <Typography variant="h6" gutterBottom>Code Output</Typography>
          <LogBox title="Code">
            {codeOutput || 'No code generated yet'}
          </LogBox>
        </Box>
      </Box>

      <Box sx={{ mt: 3, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
        <Box>
          <Typography variant="h6" gutterBottom>SSE Event Log</Typography>
          <LogBox title="Processed Events">
            {sseLog.map((log, i) => (
              <div key={i}>{log}</div>
            ))}
          </LogBox>
        </Box>

        <Box>
          <Typography variant="h6" gutterBottom>Raw SSE Events</Typography>
          <LogBox title="Raw Events">
            {rawEvents.map((event, i) => (
              <div key={i} style={{ whiteSpace: 'pre-wrap' }}>{event}</div>
            ))}
          </LogBox>
        </Box>
      </Box>
    </Container>
  );
};

export default ASTDebugPage;
