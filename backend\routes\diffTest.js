/**
 * Test routes for diff functionality
 * Used to verify diff-match-patch integration works correctly
 */

const express = require('express');
const router = express.Router();
const DiffService = require('../services/diffService');

// Initialize diff service
const diffService = new DiffService();

/**
 * Test diff generation endpoint
 * POST /api/diff-test/generate
 */
router.post('/generate', (req, res) => {
  try {
    const { originalHtml, modifiedHtml } = req.body;

    if (!originalHtml || !modifiedHtml) {
      return res.status(400).json({
        error: 'Both originalHtml and modifiedHtml are required'
      });
    }

    // Generate diff
    const diffResult = diffService.generateDiff(originalHtml, modifiedHtml);

    res.json({
      success: true,
      diffResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in diff test:', error);
    res.status(500).json({
      error: error.message,
      success: false
    });
  }
});

/**
 * Test diff application endpoint
 * POST /api/diff-test/apply
 */
router.post('/apply', (req, res) => {
  try {
    const { originalHtml, patchText } = req.body;

    if (!originalHtml || !patchText) {
      return res.status(400).json({
        error: 'Both originalHtml and patchText are required'
      });
    }

    // Apply diff
    const applyResult = diffService.applyDiff(originalHtml, patchText);

    res.json({
      success: true,
      applyResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in diff apply test:', error);
    res.status(500).json({
      error: error.message,
      success: false
    });
  }
});

/**
 * Test complete diff workflow
 * POST /api/diff-test/workflow
 */
router.post('/workflow', (req, res) => {
  try {
    const { originalHtml, modifiedHtml } = req.body;

    if (!originalHtml || !modifiedHtml) {
      return res.status(400).json({
        error: 'Both originalHtml and modifiedHtml are required'
      });
    }

    // Step 1: Generate diff
    const diffResult = diffService.generateDiff(originalHtml, modifiedHtml);

    if (!diffResult.success) {
      return res.json({
        success: false,
        error: 'Failed to generate diff',
        diffResult
      });
    }

    // Step 2: Apply diff to verify it works
    const applyResult = diffService.applyDiff(originalHtml, diffResult.patches);

    // Step 3: Compare results
    const isIdentical = applyResult.html === modifiedHtml;

    res.json({
      success: true,
      workflow: {
        diffGenerated: diffResult.success,
        diffApplied: applyResult.success,
        isIdentical,
        compressionRatio: diffResult.metadata?.compressionRatio,
        changePercentage: diffResult.stats?.changePercentage,
        patchSize: diffResult.metadata?.patchSize,
        originalSize: diffResult.metadata?.originalSize,
        modifiedSize: diffResult.metadata?.modifiedSize
      },
      diffResult,
      applyResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in diff workflow test:', error);
    res.status(500).json({
      error: error.message,
      success: false
    });
  }
});

/**
 * Get diff service stats
 * GET /api/diff-test/stats
 */
router.get('/stats', (req, res) => {
  try {
    const stats = diffService.getStats();
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting diff stats:', error);
    res.status(500).json({
      error: error.message,
      success: false
    });
  }
});

/**
 * Test with sample HTML data
 * GET /api/diff-test/sample
 */
router.get('/sample', (req, res) => {
  try {
    const originalHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="app" class="p-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Welcome to My App</h1>
        <p class="text-gray-700 mb-4">This is a sample paragraph.</p>
        <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Click Me
        </button>
    </div>
</body>
</html>`;

    const modifiedHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page - Updated</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="app" class="p-8">
        <h1 class="text-4xl font-bold text-blue-900 mb-6">Welcome to My Updated App</h1>
        <p class="text-gray-700 mb-4">This is a sample paragraph with more content.</p>
        <p class="text-green-600 mb-4">This is a new paragraph added to the page.</p>
        <button class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
            Click Me - Updated
        </button>
        <button class="bg-red-500 text-white px-4 py-2 rounded ml-4 hover:bg-red-600">
            New Button
        </button>
    </div>
</body>
</html>`;

    // Generate diff
    const diffResult = diffService.generateDiff(originalHtml, modifiedHtml);

    // Apply diff
    const applyResult = diffService.applyDiff(originalHtml, diffResult.patches);

    res.json({
      success: true,
      sample: {
        originalHtml,
        modifiedHtml,
        diffResult,
        applyResult,
        isIdentical: applyResult.html === modifiedHtml
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in sample diff test:', error);
    res.status(500).json({
      error: error.message,
      success: false
    });
  }
});

module.exports = router;
