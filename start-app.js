const { spawn } = require('child_process');
const path = require('path');

// Start frontend
const frontend = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'frontend'),
  shell: true,
  stdio: 'inherit'
});

// Start backend
const backend = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'backend'),
  shell: true,
  stdio: 'inherit'
});

// Handle process termination
process.on('SIGINT', () => {
  frontend.kill();
  backend.kill();
  process.exit();
});

console.log('JustProtoType app is running!');
console.log('- Frontend: http://localhost:5173');
console.log('- Backend: http://localhost:5000');
console.log('Press Ctrl+C to stop both servers.');
