import { useState, useEffect } from 'react';
import { getPrototypeQuota } from '../services/planService';
import QuotaExceededModal from './QuotaExceededModal';

/**
 * Higher-order component that checks if a user has exceeded their quota
 * before allowing them to generate a prototype
 *
 * @param {Object} props - Component props
 * @param {Function} props.onQuotaExceeded - Function to call when quota is exceeded
 * @param {Function} props.children - Function that returns the component to render
 * @returns {React.ReactNode} - The wrapped component
 */
function QuotaCheck({ onQuotaExceeded, children }) {
  const [quota, setQuota] = useState({
    plan: 'free',
    totalCount: 3,
    usedCount: 0,
    remainingCount: 3
  });
  const [isLoading, setIsLoading] = useState(true);
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [error, setError] = useState(null);

  // Fetch quota information on mount
  useEffect(() => {
    const fetchQuota = async () => {
      try {
        setIsLoading(true);
        const quotaData = await getPrototypeQuota();
        setQuota(quotaData);

        // If user has no remaining prototypes, show the upgrade modal
        if (quotaData.remainingCount <= 0) {
          setUpgradeModalOpen(true);
          if (onQuotaExceeded) {
            onQuotaExceeded();
          }
        }
      } catch (err) {
        console.error('Error fetching quota:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuota();
  }, [onQuotaExceeded]);

  // Handle upgrade modal
  const handleCloseUpgradeModal = () => {
    setUpgradeModalOpen(false);
  };

  const handleUpgrade = () => {
    // Open payment page in a new tab
    window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');
    setUpgradeModalOpen(false);
  };

  // Check if quota is exceeded
  const isQuotaExceeded = quota.remainingCount <= 0 || quota.usedCount >= quota.totalCount;

  return (
    <>
      {children({ isQuotaExceeded, isLoading, quota, error })}

      <QuotaExceededModal
        open={upgradeModalOpen}
        onClose={handleCloseUpgradeModal}
        onUpgrade={handleUpgrade}
        quota={quota}
      />
    </>
  );
}

export default QuotaCheck;
