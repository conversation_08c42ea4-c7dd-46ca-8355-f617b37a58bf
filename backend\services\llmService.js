const axios = require('axios');
const {
  getPlanPromptMessages,
  getCodeGenerationPromptMessages,
  getElementModificationPromptMessages,
  getContentModificationPromptMessages,
  withRetry,
  isRetryableError,
  getProviderTimeoutConfig,
  continuationPrompt
} = require('../utils/promptUtils');

// Import token utilities
let tokenUtils;
try {
  tokenUtils = require('../utils/tokenUtils');
  console.log('[llmService] Successfully imported tokenUtils');
} catch (error) {
  console.error('[llmService] Error importing tokenUtils:', error);
  // Fallback implementations if the module fails to load
  tokenUtils = {
    countTokens: (text, model) => Math.ceil((text || '').length / 4),
    countMessageTokens: (messages, model) => {
      return messages.reduce((sum, msg) => sum + Math.ceil((msg.content || '').length / 4), 0);
    },
    getModelTokenLimit: (model) => {
      const limits = {
        'gpt-4': 8192,
        'gpt-4-turbo': 128000,
        'gpt-3.5-turbo': 16385,
        'claude-3-opus-20240229': 200000,
        'claude-3-sonnet-20240229': 200000,
        'claude-3-haiku-20240307': 200000,
        'claude-3-7-sonnet-20250219': 200000,
        'deepseek-chat': 8192,
        'default': 4096
      };
      return limits[model] || limits.default;
    },
    trimHtmlToTokenLimit: (html, limit, model) => {
      if (!html) return '';
      const charsPerToken = 4;
      const maxChars = limit * charsPerToken;
      return html.length > maxChars ? html.slice(0, maxChars) : html;
    }
  };
  console.log('[llmService] Using fallback token utilities');
}

const {
  countTokens,
  countMessageTokens,
  getModelTokenLimit,
  trimHtmlToTokenLimit
} = tokenUtils;

/**
 * Get the configured LLM provider from environment variables or use default
 * @returns {string} The provider to use ('openai', 'anthropic', or 'deepseek')
 */
function getConfiguredProvider() {
  // Read from environment variable, default to 'openai' if not set
  return process.env.LLM_PROVIDER || 'openai';
}

/**
 * Generate a feature plan from a prompt using the configured provider.
 * Supported providers: 'openai', 'anthropic', 'deepseek'
 * Returns an array of plan lines (strings) or streams text to res.
 *
 * Note: The provider parameter is kept for backward compatibility but is ignored
 * if LLM_PROVIDER environment variable is set.
 */
async function generateFeaturePlan(prompt, provider = null, stream = false, res = null) {
  // Use the configured provider from environment variable
  const configuredProvider = getConfiguredProvider();
  console.log(`[llmService] Using provider: ${configuredProvider} (from environment)`);

  if (configuredProvider === 'deepseek') {
    return generatePlanDeepSeek(prompt, stream, res);
  }
  if (configuredProvider === 'anthropic') {
    return generatePlanAnthropic(prompt, stream, res);
  }
  // Default: OpenAI
  return generatePlanOpenAI(prompt, stream, res);
}

async function generatePlanDeepSeek(prompt, stream = false, res = null) {
  const DEEPSEEK_API_URL = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/v1/chat/completions';
  const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
  if (!DEEPSEEK_API_KEY) throw new Error('DeepSeek API key not set in environment variables.');

  const messages = getPlanPromptMessages(prompt);

  const payload = {
    model: 'deepseek-chat',
    messages,
    temperature: 0.2,
    max_tokens: 1024,
    stream,
  };

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
  };

  // Get timeout configuration for DeepSeek
  const timeoutConfig = getProviderTimeoutConfig('deepseek', stream);
  console.log(`[DeepSeek] Using timeout: ${timeoutConfig.timeout}ms, maxRetries: ${timeoutConfig.maxRetries}`);

  if (stream && res) {
    // Streaming: parse and forward text chunks with retry
    try {
      return await withRetry(
        async (timeout) => {
          const response = await axios.post(DEEPSEEK_API_URL, payload, {
            headers,
            responseType: 'stream',
            timeout: timeout // Apply timeout from retry utility
          });

          response.data.on('data', chunk => {
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.replace('data: ', '').trim();
                if (data === '[DONE]') continue;
                try {
                  const parsed = JSON.parse(data);
                  const delta = parsed.choices?.[0]?.delta?.content;
                  if (delta) res.write(delta);
                } catch (e) {
                  // ignore malformed lines
                }
              }
            }
          });

          return new Promise(resolve => {
            response.data.on('end', () => {
              res.end();
              resolve();
            });
          });
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay, error) => {
            console.log(`[DeepSeek] Retry ${attempt}/${maxRetries} for streaming request after ${delay}ms`);
            if (res && typeof res.write === 'function') {
              try {
                res.write(`\n[Connection lost. Retrying... (${attempt}/${maxRetries})]\n`);
              } catch (e) {
                console.error('[DeepSeek] Failed to write retry message to response:', e);
              }
            }
          }
        }
      );
    } catch (error) {
      console.error('[DeepSeek] All retry attempts failed for streaming request:', error);
      if (res && typeof res.write === 'function') {
        try {
          res.write('\n[Connection failed. Please try again.]\n');
          res.end();
        } catch (e) {
          console.error('[DeepSeek] Failed to write error message to response:', e);
        }
      }
      throw error;
    }
  } else {
    // Non-streaming: return array of plan lines with retry
    try {
      return await withRetry(
        async (timeout) => {
          const response = await axios.post(DEEPSEEK_API_URL, payload, {
            headers,
            timeout: timeout // Apply timeout from retry utility
          });

          let content = response.data.choices?.[0]?.message?.content;
          if (!content) throw new Error('No content in DeepSeek response');

          const lines = content.split('\n').map(l => l.trim()).filter(Boolean);
          return lines;
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay, error) => {
            console.log(`[DeepSeek] Retry ${attempt}/${maxRetries} for non-streaming request after ${delay}ms`);
          }
        }
      );
    } catch (error) {
      console.error('[DeepSeek] All retry attempts failed for non-streaming request:', error);
      throw error;
    }
  }
}

async function generatePlanOpenAI(prompt, stream = false, res = null) {
  const OPENAI_API_URL = process.env.OPENAI_API_URL || 'https://api.openai.com/v1/chat/completions';
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
  if (!OPENAI_API_KEY) throw new Error('OpenAI API key not set in environment variables.');

  const messages = getPlanPromptMessages(prompt);

  const payload = {
    model: process.env.OPENAI_MODEL || 'gpt-4.1', // Use GPT-4.1 model
    messages,
    temperature: 0.2,
    max_tokens: 1024,
    stream,
  };

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${OPENAI_API_KEY}`,
  };

  // Get timeout configuration for OpenAI
  const timeoutConfig = getProviderTimeoutConfig('openai', stream);
  console.log(`[OpenAI] Using timeout: ${timeoutConfig.timeout}ms, maxRetries: ${timeoutConfig.maxRetries}`);

  if (stream && res) {
    // Streaming: parse and forward text chunks with retry
    try {
      return await withRetry(
        async (timeout) => {
          const response = await axios.post(OPENAI_API_URL, payload, {
            headers,
            responseType: 'stream',
            timeout: timeout // Apply timeout from retry utility
          });

          response.data.on('data', chunk => {
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.replace('data: ', '').trim();
                if (data === '[DONE]') continue;
                try {
                  const parsed = JSON.parse(data);
                  const delta = parsed.choices?.[0]?.delta?.content;
                  if (delta) res.write(delta);
                } catch (e) {
                  // ignore malformed lines
                }
              }
            }
          });

          return new Promise(resolve => {
            response.data.on('end', () => {
              res.end();
              resolve();
            });
          });
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[OpenAI] Retry ${attempt}/${maxRetries} for streaming request after ${delay}ms`);
            if (res && typeof res.write === 'function') {
              try {
                res.write(`\n[Connection lost. Retrying... (${attempt}/${maxRetries})]\n`);
              } catch (e) {
                console.error('[OpenAI] Failed to write retry message to response:', e);
              }
            }
          }
        }
      );
    } catch (error) {
      console.error('[OpenAI] All retry attempts failed for streaming request:', error);
      if (res && typeof res.write === 'function') {
        try {
          res.write('\n[Connection failed. Please try again.]\n');
          res.end();
        } catch (e) {
          console.error('[OpenAI] Failed to write error message to response:', e);
        }
      }
      throw error;
    }
  } else {
    // Non-streaming: return array of plan lines with retry
    try {
      return await withRetry(
        async (timeout) => {
          const response = await axios.post(OPENAI_API_URL, payload, {
            headers,
            timeout: timeout // Apply timeout from retry utility
          });

          let content = response.data.choices?.[0]?.message?.content;
          if (!content) throw new Error('No content in OpenAI response');

          const lines = content.split('\n').map(l => l.trim()).filter(Boolean);
          return lines;
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[OpenAI] Retry ${attempt}/${maxRetries} for non-streaming request after ${delay}ms`);
          }
        }
      );
    } catch (error) {
      console.error('[OpenAI] All retry attempts failed for non-streaming request:', error);
      throw error;
    }
  }
}

// --- Anthropic Claude 3.7 implementation ---
async function generatePlanAnthropic(prompt, stream = false, res = null) {
  const ANTHROPIC_API_URL = process.env.ANTHROPIC_API_URL || 'https://api.anthropic.com/v1/messages';
  const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY;
  if (!ANTHROPIC_API_KEY) throw new Error('Anthropic API key not set in environment variables.');

  // Use Claude 3.7 Sonnet as per Anthropic docs
  const model = 'claude-3-7-sonnet-20250219'; // Claude 3.7 Sonnet

  const systemPrompt = "You are a senior frontend engineer. Given a UI description, generate a concise, structured feature plan (grouped by section: Layout, Components, Interactions, etc). Output only the plan, no explanation.";

  const payload = {
    model,
    max_tokens: 1024,
    temperature: 0.2,
    stream,
    system: systemPrompt,
    messages: [
      { role: "user", content: prompt }
    ]
  };

  const headers = {
    'Content-Type': 'application/json',
    'x-api-key': ANTHROPIC_API_KEY,
    'anthropic-version': '2023-06-01'
  };

  // Get timeout configuration for Anthropic
  const timeoutConfig = getProviderTimeoutConfig('anthropic', stream);
  console.log(`[Anthropic] Using timeout: ${timeoutConfig.timeout}ms, maxRetries: ${timeoutConfig.maxRetries}`);

  try {
    if (stream && res) {
      console.log('[Anthropic] Streaming plan request:', { prompt });

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(ANTHROPIC_API_URL, payload, {
            headers,
            responseType: 'stream',
            timeout: timeout // Apply timeout from retry utility
          });

          response.data.on('data', chunk => {
            // Anthropic streams JSON objects, one per line
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (!line.trim()) continue;
              try {
                const parsed = JSON.parse(line);
                const delta = parsed.delta?.text || parsed.completion || parsed.content?.[0]?.text;
                if (delta) {
                  res.write(delta);
                  console.log('[Anthropic] Stream chunk:', delta);
                }
              } catch (e) {
                // ignore malformed lines
              }
            }
          });

          return new Promise(resolve => {
            response.data.on('end', () => {
              console.log('[Anthropic] Stream ended');
              res.end();
              resolve();
            });
          });
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[Anthropic] Retry ${attempt}/${maxRetries} for streaming request after ${delay}ms`);
            if (res && typeof res.write === 'function') {
              try {
                res.write(`\n[Connection lost. Retrying... (${attempt}/${maxRetries})]\n`);
              } catch (e) {
                console.error('[Anthropic] Failed to write retry message to response:', e);
              }
            }
          }
        }
      );
    } else {
      console.log('[Anthropic] Non-streaming plan request:', { prompt });

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(ANTHROPIC_API_URL, payload, {
            headers,
            timeout: timeout // Apply timeout from retry utility
          });

          let content = response.data.content?.[0]?.text || response.data.completion;
          console.log('[Anthropic] Non-streaming plan response:', content);
          if (!content) throw new Error('No content in Anthropic response');

          const lines = content.split('\n').map(l => l.trim()).filter(Boolean);
          return lines;
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[Anthropic] Retry ${attempt}/${maxRetries} for non-streaming request after ${delay}ms`);
          }
        }
      );
    }
  } catch (err) {
    console.error('[Anthropic] All retry attempts failed:', err);
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Connection failed. Please try again.]\n');
        res.end();
      } catch (e) {
        console.error('[Anthropic] Failed to write error message to response:', e);
      }
    }
    throw err;
  }
}

/**
 * Generate code (HTML/CSS/JS) from a plan using the configured provider.
 * Returns the code as a string or streams code to res.
 * If output is incomplete, auto-continue until </html> or max 5 chunks.
 *
 * Note: The provider parameter is kept for backward compatibility but is ignored
 * if LLM_PROVIDER environment variable is set.
 */
async function generateCodeFromPlan(plan, provider = null, stream = false, res = null) {
  // Use the configured provider from environment variable
  const configuredProvider = getConfiguredProvider();
  console.log(`[generateCodeFromPlan] Starting with provider: ${configuredProvider} (from environment), stream: ${stream}`);

  // Use the prompt utility to get the code generation prompt
  const codePrompt = getCodeGenerationPromptMessages(plan);

  let result;
  if (configuredProvider === 'deepseek') {
    result = await generateCodeAutoContinue(generateCodeDeepSeek, codePrompt, stream, res);
  } else if (configuredProvider === 'anthropic') {
    result = await generateCodeAutoContinue(generateCodeAnthropic, codePrompt, stream, res);
  } else {
    // Default: OpenAI
    result = await generateCodeAutoContinue(generateCodeOpenAI, codePrompt, stream, res);
  }

  if (!stream) {
    console.log('[generateCodeFromPlan] Non-streaming response completed');
    return result;
  }

  console.log('[generateCodeFromPlan] Streaming response completed');
  // For streaming, the response is handled by the controller
}


/**
 * Auto-continue code generation if output is incomplete.
 * Calls the LLM up to 5 times with "Continue the code from where you left off."
 */
async function generateCodeAutoContinue(llmFn, messages, stream, res) {
  console.log(`[generateCodeAutoContinue] Starting with stream: ${stream}`);

  let codeBuffer = '';
  let chunkCount = 0;
  let maxChunks = 5;
  let lastMessages = messages;

  // Helper to extract the full <div id="app">...</div> block from a string (from first open to last close)
  function extractFullAppDiv(html) {
    const openTag = html.search(/<div\s+id=["']app["'][^>]*>/i);
    const closeTag = html.lastIndexOf('</div>');
    if (openTag !== -1 && closeTag !== -1 && closeTag > openTag) {
      return html.slice(openTag, closeTag + 6);
    }
    return html;
  }

  async function streamOrConcat(newMessages, append = false) {
    console.log(`[streamOrConcat] Called with stream: ${stream}, append: ${append}`);

    if (stream && res) {
      let lastChunk = '';
      try {
        await llmFn(newMessages, true, {
          write: (delta) => {
            if (!delta) return;

            codeBuffer += delta;
            if (typeof res.write === 'function') {
              try {
                res.write(delta);
              //  console.log(`[streamOrConcat] Wrote chunk: ${delta.length} bytes`);
              } catch (writeErr) {
                console.error('[streamOrConcat] Error writing to response:', writeErr);
              }
            } else {
              console.warn('[streamOrConcat] res.write is not a function');
            }
            lastChunk = delta;
          },
          end: () => {
            console.log('[streamOrConcat] End called (but not ending response)');
          },
        });
        console.log('[streamOrConcat] Stream completed successfully');
      } catch (err) {
        console.error('[streamOrConcat] Error during streaming:', err);
      }
      return lastChunk;
    } else {
      console.log('[streamOrConcat] Using non-streaming approach');
      try {
        const code = await llmFn(newMessages, false, null);
        if (append) codeBuffer += code;
        else codeBuffer = code;
        return code;
      } catch (err) {
        console.error('[streamOrConcat] Error during non-streaming call:', err);
        throw err;
      }
    }
  }

  try {
    // Initial generation
    console.log('[generateCodeAutoContinue] Starting initial generation');
    await streamOrConcat(lastMessages);

    // Auto-continue if incomplete
    while (
      chunkCount < maxChunks &&
      !codeBuffer.trim().endsWith('</div>')
    ) {
      chunkCount++;
      console.log(`[generateCodeAutoContinue] Continuing generation (chunk ${chunkCount}/${maxChunks})`);

      lastMessages = [
        ...messages,
        { role: 'assistant', content: codeBuffer.slice(-2000) },
        { role: 'user', content: 'Continue the code from where you left off. Output only code.' },
      ];
      await streamOrConcat(lastMessages, true);
    }

    // After all chunks, extract the full <div id="app">...</div>
    console.log('[generateCodeAutoContinue] Extracting app div from buffer');
    codeBuffer = extractFullAppDiv(codeBuffer);

    console.log(`[generateCodeAutoContinue] Completed with ${chunkCount} chunks, buffer length: ${codeBuffer.length}`);
    return codeBuffer;
  } catch (err) {
    console.error('[generateCodeAutoContinue] Error:', err);
    throw err;
  }
}

// --- Anthropic Claude 3.7 code generation ---
async function generateCodeAnthropic(messages, stream = false, res = null) {
  const ANTHROPIC_API_URL = process.env.ANTHROPIC_API_URL || 'https://api.anthropic.com/v1/messages';
  const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY;
  if (!ANTHROPIC_API_KEY) throw new Error('Anthropic API key not set in environment variables.');

  // Anthropic Claude expects a single system prompt and a messages array (see docs)
  const systemPrompt = messages.find(m => m.role === 'system')?.content || '';
  const userPrompt = messages.find(m => m.role === 'user')?.content || '';

  const payload = {
    model: 'claude-3-7-sonnet-20250219', // Claude 3.7 Sonnet
    max_tokens: 4096,
    temperature: 0.2,
    stream,
    system: systemPrompt,
    messages: [
      { role: "user", content: userPrompt }
    ]
  };

  const headers = {
    'Content-Type': 'application/json',
    'x-api-key': ANTHROPIC_API_KEY,
    'anthropic-version': '2023-06-01'
  };

  // Get timeout configuration for Anthropic
  const timeoutConfig = getProviderTimeoutConfig('anthropic', stream);
  console.log(`[Anthropic] Using timeout: ${timeoutConfig.timeout}ms, maxRetries: ${timeoutConfig.maxRetries}`);

  let codeBuffer = '';

  try {
    if (stream && res) {
      console.log('[Anthropic] Streaming code request:', { userPrompt });

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(ANTHROPIC_API_URL, payload, {
            headers,
            responseType: 'stream',
            timeout: timeout // Apply timeout from retry utility
          });

          response.data.on('data', chunk => {
            // Anthropic streams JSON objects, one per line
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (!line.trim()) continue;
              try {
                const parsed = JSON.parse(line);
                const delta = parsed.delta?.text || parsed.completion || parsed.content?.[0]?.text;
                if (delta) {
                  if (typeof res.write === 'function') res.write(delta);
                  codeBuffer += delta;
                  console.log('[Anthropic] Stream chunk:', delta);
                }
              } catch (e) {
                // ignore malformed lines
              }
            }
          });

          return new Promise(resolve => {
            response.data.on('end', () => {
              console.log('[Anthropic] Stream ended');
              resolve(codeBuffer);
            });
          });
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[Anthropic] Retry ${attempt}/${maxRetries} for streaming code request after ${delay}ms`);
            if (res && typeof res.write === 'function') {
              try {
                res.write(`\n[Connection lost. Retrying... (${attempt}/${maxRetries})]\n`);
              } catch (e) {
                console.error('[Anthropic] Failed to write retry message to response:', e);
              }
            }
          }
        }
      );
    } else {
      console.log('[Anthropic] Non-streaming code request:', { userPrompt });

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(ANTHROPIC_API_URL, payload, {
            headers,
            timeout: timeout // Apply timeout from retry utility
          });

          let content = response.data.content?.[0]?.text || response.data.completion;
          console.log('[Anthropic] Non-streaming code response:', content);
          if (!content) throw new Error('No content in Anthropic response');

          return content;
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[Anthropic] Retry ${attempt}/${maxRetries} for non-streaming code request after ${delay}ms`);
          }
        }
      );
    }
  } catch (err) {
    console.error('[Anthropic] All retry attempts failed for code generation:', err);
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Connection failed. Please try again.]\n');
      } catch (e) {
        console.error('[Anthropic] Failed to write error message to response:', e);
      }
    }
    throw err;
  }
}

async function generateCodeDeepSeek(messages, stream = false, res = null) {
  const DEEPSEEK_API_URL = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/v1/chat/completions';
  const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
  if (!DEEPSEEK_API_KEY) throw new Error('DeepSeek API key not set in environment variables.');

  const payload = {
    model: 'deepseek-chat',
    messages,
    temperature: 0.2,
    max_tokens: 4096,
    stream,
  };

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
  };

  // Get timeout configuration for DeepSeek
  const timeoutConfig = getProviderTimeoutConfig('deepseek', stream);
  console.log(`[DeepSeek] Using timeout: ${timeoutConfig.timeout}ms, maxRetries: ${timeoutConfig.maxRetries}`);

  let codeBuffer = '';

  try {
    if (stream && res) {
      console.log('[DeepSeek] Streaming code request');

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(DEEPSEEK_API_URL, payload, {
            headers,
            responseType: 'stream',
            timeout: timeout // Apply timeout from retry utility
          });

          response.data.on('data', chunk => {
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.replace('data: ', '').trim();
                if (data === '[DONE]') {
                  continue;
                }
                try {
                  const parsed = JSON.parse(data);
                  const delta = parsed.choices?.[0]?.delta?.content;
                  if (delta) {
                    if (typeof res.write === 'function') res.write(delta);
                    codeBuffer += delta;
                  }
                } catch (e) {
                  // ignore malformed lines
                }
              }
            }
          });

          return new Promise(resolve => {
            response.data.on('end', () => {
              console.log('[DeepSeek] Stream ended');
              // Don't end the response here, let the controller handle it
              resolve(codeBuffer);
            });
          });
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[DeepSeek] Retry ${attempt}/${maxRetries} for streaming code request after ${delay}ms`);
            if (res && typeof res.write === 'function') {
              try {
                res.write(`\n[Connection lost. Retrying... (${attempt}/${maxRetries})]\n`);
              } catch (e) {
                console.error('[DeepSeek] Failed to write retry message to response:', e);
              }
            }
          }
        }
      );
    } else {
      console.log('[DeepSeek] Non-streaming code request');

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(DEEPSEEK_API_URL, payload, {
            headers,
            timeout: timeout // Apply timeout from retry utility
          });

          let content = response.data.choices?.[0]?.message?.content;
          if (!content) throw new Error('No content in DeepSeek response');

          return content;
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[DeepSeek] Retry ${attempt}/${maxRetries} for non-streaming code request after ${delay}ms`);
          }
        }
      );
    }
  } catch (err) {
    console.error('[DeepSeek] All retry attempts failed for code generation:', err);
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Connection failed. Please try again.]\n');
      } catch (e) {
        console.error('[DeepSeek] Failed to write error message to response:', e);
      }
    }
    throw err;
  }
}

async function generateCodeOpenAI(messages, stream = false, res = null) {
  const OPENAI_API_URL = process.env.OPENAI_API_URL || 'https://api.openai.com/v1/chat/completions';
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
  if (!OPENAI_API_KEY) throw new Error('OpenAI API key not set in environment variables.');

  const model = process.env.OPENAI_MODEL || 'gpt-4.1';
  console.log(`[OpenAI] Using model for code generation: ${model}`);

  const payload = {
    model,
    messages,
    temperature: 0.2,
    max_tokens: 4096,
    stream,
  };

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${OPENAI_API_KEY}`,
  };

  // Get timeout configuration for OpenAI
  const timeoutConfig = getProviderTimeoutConfig('openai', stream);
  console.log(`[OpenAI] Using timeout: ${timeoutConfig.timeout}ms, maxRetries: ${timeoutConfig.maxRetries}`);

  let codeBuffer = '';

  try {
    if (stream && res) {
      console.log('[OpenAI] Streaming code request');

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(OPENAI_API_URL, payload, {
            headers,
            responseType: 'stream',
            timeout: timeout // Apply timeout from retry utility
          });

          response.data.on('data', chunk => {
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.replace('data: ', '').trim();
                if (data === '[DONE]') {
                  continue;
                }
                try {
                  const parsed = JSON.parse(data);
                  const delta = parsed.choices?.[0]?.delta?.content;
                  if (delta) {
                    if (typeof res.write === 'function') res.write(delta);
                    codeBuffer += delta;
                  }
                } catch (e) {
                  // ignore malformed lines
                }
              }
            }
          });

          return new Promise(resolve => {
            response.data.on('end', () => {
              console.log('[OpenAI] Stream ended');
              // Don't end the response here, let the controller handle it
              resolve(codeBuffer);
            });
          });
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[OpenAI] Retry ${attempt}/${maxRetries} for streaming code request after ${delay}ms`);
            if (res && typeof res.write === 'function') {
              try {
                res.write(`\n[Connection lost. Retrying... (${attempt}/${maxRetries})]\n`);
              } catch (e) {
                console.error('[OpenAI] Failed to write retry message to response:', e);
              }
            }
          }
        }
      );
    } else {
      console.log('[OpenAI] Non-streaming code request');

      return await withRetry(
        async (timeout) => {
          const response = await axios.post(OPENAI_API_URL, payload, {
            headers,
            timeout: timeout // Apply timeout from retry utility
          });

          let content = response.data.choices?.[0]?.message?.content;
          if (!content) throw new Error('No content in OpenAI response');

          return content;
        },
        {
          maxRetries: timeoutConfig.maxRetries,
          initialDelay: timeoutConfig.initialDelay,
          maxDelay: timeoutConfig.maxDelay,
          timeout: timeoutConfig.timeout,
          retryCondition: isRetryableError,
          onRetry: (attempt, maxRetries, delay) => {
            console.log(`[OpenAI] Retry ${attempt}/${maxRetries} for non-streaming code request after ${delay}ms`);
          }
        }
      );
    }
  } catch (err) {
    console.error('[OpenAI] All retry attempts failed for code generation:', err);
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Connection failed. Please try again.]\n');
      } catch (e) {
        console.error('[OpenAI] Failed to write error message to response:', e);
      }
    }
    throw err;
  }
}

// This line was moved to the top of the file

/**
 * Modifies a specific element in the HTML content based on the prompt.
 * @param {string} htmlContent - The full HTML content
 * @param {string} elementSelector - CSS selector for the element to modify
 * @param {string} prompt - Natural language prompt describing the desired changes
 * @param {string} provider - LLM provider to use (ignored if LLM_PROVIDER is set)
 * @param {boolean} stream - Whether to stream the response (default: false)
 * @param {object|function} res - Response object or callback function for streaming
 * @returns {Promise<string>} - The modified HTML content (if not streaming)
 */
async function modifyElementWithLLM(htmlContent, elementSelector, prompt, provider = null, stream = false, res = null) {
  // Create messages for the LLM using the prompt utility
  const messages = getElementModificationPromptMessages(htmlContent, elementSelector, prompt);

  // Use the configured provider from environment variable
  const configuredProvider = getConfiguredProvider();
  console.log(`[modifyElementWithLLM] Starting with provider: ${configuredProvider} (from environment), stream: ${stream}`);

  try {
    // Call the appropriate LLM provider
    let modifiedHtml;
    if (configuredProvider === 'deepseek') {
      modifiedHtml = await generateCodeDeepSeek(messages, stream, res);
    } else if (configuredProvider === 'anthropic') {
      modifiedHtml = await generateCodeAnthropic(messages, stream, res);
    } else {
      // Default: OpenAI
      modifiedHtml = await generateCodeOpenAI(messages, stream, res);
    }

    // If not streaming, clean up the response
    if (!stream) {
      console.log('[modifyElementWithLLM] Non-streaming response received');
      modifiedHtml = cleanupLLMResponse(modifiedHtml);
      return modifiedHtml;
    }

    console.log('[modifyElementWithLLM] Streaming response completed');
    // For streaming, the response is handled by the LLM provider functions
    // and the controller will end the response
  } catch (error) {
    console.error('[modifyElementWithLLM] Error:', error);
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Error modifying element. Please try again.]\n');
        res.end();
      } catch (e) {
        console.error('[modifyElementWithLLM] Failed to write error message to response:', e);
      }
    }
    throw error;
  }
}

/**
 * Post-processes HTML content to fix common issues with LLM-generated content.
 * @param {string} html - The HTML content to process
 * @returns {string} - The processed HTML content
 */
function postProcessHtml(html) {
  if (!html) return '';

  try {
    console.log('[postProcessHtml] Processing HTML content, length:', html.length);

    // Fix 0: Handle CLEAN_HTML_FOLLOWS marker
    // This ensures we don't have duplicate content from previous clean HTML attempts
    const cleanHtmlMarkerIndex = html.indexOf('<!-- CLEAN_HTML_FOLLOWS -->');
    if (cleanHtmlMarkerIndex > -1) {
      console.log('[postProcessHtml] Found CLEAN_HTML_FOLLOWS marker');

      // Check if there's content after the marker
      const contentAfterMarker = html.substring(cleanHtmlMarkerIndex + '<!-- CLEAN_HTML_FOLLOWS -->'.length).trim();

      if (contentAfterMarker) {
        console.log('[postProcessHtml] Using content after CLEAN_HTML_FOLLOWS marker');
        // Use the content after the marker as it's the cleaned version
        html = contentAfterMarker;
      } else {
        console.log('[postProcessHtml] No content after CLEAN_HTML_FOLLOWS marker, removing marker');
        // If no content after marker, just remove the marker
        html = html.substring(0, cleanHtmlMarkerIndex);
      }
    }

    // Fix 1: Find and fix JavaScript code outside of script tags
    // First, check if there's a script tag followed by JavaScript code outside of any script tag
    const scriptEndRegex = /<\/script>\s*(\s*try\s*\{[\s\S]*?function|echarts\.init|document\.querySelectorAll)/;
    const scriptEndMatch = scriptEndRegex.exec(html);
    if (scriptEndMatch && scriptEndMatch.index > 0) {
      console.log('[postProcessHtml] Found JavaScript code outside of script tags, moving it inside');

      // Find the last script tag
      const lastScriptMatch = html.match(/<script[^>]*data-exec=["']inline["'][^>]*>([\s\S]*?)<\/script>/);
      if (lastScriptMatch) {
        // Extract the JavaScript code outside the script tag
        const scriptEndIndex = scriptEndMatch.index + '</script>'.length;
        const jsCode = html.substring(scriptEndIndex);

        // Remove the JavaScript code from outside the script tag
        html = html.substring(0, scriptEndIndex);

        // Add the JavaScript code to the script tag
        html = html.replace(lastScriptMatch[0], `<script data-exec="inline">${lastScriptMatch[1]}\n\n${jsCode}</script>`);

        console.log('[postProcessHtml] Moved JavaScript code inside script tag');
      }
    }

    // Fix 2: Combine duplicate script content
    // This pattern looks for script blocks
    const scriptPattern = /<script[^>]*data-exec=["']inline["'][^>]*>([\s\S]*?)<\/script>/g;
    const scripts = [];
    let match;

    while ((match = scriptPattern.exec(html)) !== null) {
      scripts.push({
        fullMatch: match[0],
        content: match[1].trim(),
        index: match.index
      });
    }

    // If we have multiple script tags, combine them
    if (scripts.length > 1) {
      console.log(`[postProcessHtml] Found ${scripts.length} script tags, combining them`);

      // Sort scripts by their position in the document
      scripts.sort((a, b) => a.index - b.index);

      // Create a combined script with all content
      let combinedContent = '';
      const processedFunctions = new Set();

      for (const script of scripts) {
        // Extract function declarations to avoid duplicates
        const functionMatches = script.content.match(/function\s+(\w+)\s*\([^)]*\)\s*\{[\s\S]*?\}/g) || [];

        for (const funcMatch of functionMatches) {
          const funcNameMatch = funcMatch.match(/function\s+(\w+)/);
          if (funcNameMatch && funcNameMatch[1]) {
            const funcName = funcNameMatch[1];
            if (!processedFunctions.has(funcName)) {
              combinedContent += funcMatch + '\n\n';
              processedFunctions.add(funcName);
            }
          } else {
            // If we can't extract the name, just add it
            combinedContent += funcMatch + '\n\n';
          }
        }

        // Add non-function code
        const nonFunctionCode = script.content.replace(/function\s+(\w+)\s*\([^)]*\)\s*\{[\s\S]*?\}/g, '').trim();
        if (nonFunctionCode) {
          combinedContent += nonFunctionCode + '\n\n';
        }
      }

      // Remove all script tags except the first one
      for (let i = 1; i < scripts.length; i++) {
        html = html.replace(scripts[i].fullMatch, '');
      }

      // Replace the content of the first script tag with the combined content
      html = html.replace(scripts[0].fullMatch, `<script data-exec="inline">\n${combinedContent.trim()}\n</script>`);
      console.log('[postProcessHtml] Combined all script tags into one');
    }

    // Fix 2: Remove duplicate HTML content after </div> closing tag
    const appDivCloseIndex = html.indexOf('</div>');
    if (appDivCloseIndex > -1) {
      const afterClosingDiv = html.substring(appDivCloseIndex + 6).trim();
      if (afterClosingDiv) {
        // Check if content after closing div is a duplicate of content inside the div
        const mainContent = html.substring(0, appDivCloseIndex);

        // Look for chunks of the afterClosingDiv content in the main content
        const chunkSize = Math.min(50, afterClosingDiv.length);
        let isDuplicate = false;

        for (let i = 0; i < afterClosingDiv.length - chunkSize; i += chunkSize) {
          const chunk = afterClosingDiv.substring(i, i + chunkSize);
          if (mainContent.includes(chunk)) {
            isDuplicate = true;
            break;
          }
        }

        if (isDuplicate) {
          // Remove the duplicate content
          html = html.substring(0, appDivCloseIndex + 6);
          console.log('[postProcessHtml] Removed duplicate content after closing div');
        }
      }
    }

    // Fix 3: Fix malformed tags
    // This is a simple fix for common issues, a full parser would be better for complex cases
    html = html.replace(/<([a-zA-Z]+)[^>]*>[^<]*<\1/g, (match) => {
      // This might be a duplicated opening tag
      const tagName = match.match(/<([a-zA-Z]+)/)[1];
      return `<${tagName}`;
    });

    // Fix 4: Remove any content outside the main app div and handle multiple app divs
    const appDivMatches = html.match(/<div\s+id=["']app["'][^>]*>[\s\S]*?<\/div>/g);
    if (appDivMatches && appDivMatches.length > 0) {
      if (appDivMatches.length > 1) {
        console.log(`[postProcessHtml] Found ${appDivMatches.length} app divs, using only the first one`);
        html = appDivMatches[0];
      } else {
        html = appDivMatches[0];
      }
    }

    console.log('[postProcessHtml] Processed HTML content, new length:', html.length);
    return html;
  } catch (error) {
    console.error('[postProcessHtml] Error processing HTML:', error);
    return html; // Return original on error
  }
}

/**
 * Cleans up the LLM response by removing markdown code blocks and other formatting.
 * @param {string} response - The raw LLM response
 * @returns {string} - The cleaned HTML content
 */
function cleanupLLMResponse(response) {
  // Remove markdown code blocks if present
  let cleanedResponse = response;

  // Remove ```html and ``` markers
  const htmlBlockRegex = /```html\s*([\s\S]*?)\s*```/;
  const markdownMatch = htmlBlockRegex.exec(cleanedResponse);
  if (markdownMatch && markdownMatch[1]) {
    cleanedResponse = markdownMatch[1];
  } else {
    // Try without the language specifier
    const simpleBlockRegex = /```\s*([\s\S]*?)\s*```/;
    const simpleMatch = simpleBlockRegex.exec(cleanedResponse);
    if (simpleMatch && simpleMatch[1]) {
      cleanedResponse = simpleMatch[1];
    }
  }

  return cleanedResponse.trim();
}

// Function was moved to promptUtils.js

/**
 * Auto-continue content modification if output is incomplete.
 * Calls the LLM up to 5 times with a continuation prompt if the response is truncated.
 * @param {Function} llmFn - The LLM provider function to call
 * @param {Array} messages - The initial messages array
 * @param {string} model - The model name
 * @param {boolean} stream - Whether to stream the response
 * @param {object|function} res - Response object or callback function for streaming
 * @returns {Promise<string>} - The complete modified HTML content
 */
async function modifyContentWithLLMAutoContinue(llmFn, messages, model, stream, res) {
  console.log(`[modifyContentWithLLMAutoContinue] Starting with model: ${model}, stream: ${stream}`);

  let htmlBuffer = '';
  let chunkCount = 0;
  let maxChunks = 5;
  let isComplete = false;

  // Helper to check if HTML is complete (ends with </div> for app div)
  function isHtmlComplete(html) {
    if (!html) return false;

    const trimmedHtml = html.trim();

    // Check if it ends with </div>
    if (trimmedHtml.endsWith('</div>')) {
      // Check if it has a complete app div structure
      const appDivRegex = /<div\s+id=["']app["'][^>]*>[\s\S]*?<\/div>$/;
      const hasAppDiv = appDivRegex.test(trimmedHtml);

      // Check if it has a script tag
      const hasScript = trimmedHtml.includes('<script') && trimmedHtml.includes('</script>');

      // Log the completeness check results
      console.log(`[isHtmlComplete] HTML ends with </div>: true, has app div: ${hasAppDiv}, has script: ${hasScript}`);

      return hasAppDiv;
    }

    console.log(`[isHtmlComplete] HTML does not end with </div>`);
    return false;
  }

  // Helper to find the proper continuation point in HTML
  function findContinuationPoint(html) {
    // Find the last complete HTML tag
    const lastTagMatch = html.match(/<\/[a-zA-Z][a-zA-Z0-9]*>\s*$/);
    if (lastTagMatch) {
      return html.length;
    }

    // If no complete tag at the end, find the last opening tag
    const openingTagMatch = html.match(/<[a-zA-Z][a-zA-Z0-9]*[^>]*>\s*$/);
    if (openingTagMatch) {
      // Return the position before this opening tag
      return html.lastIndexOf('<', html.length - 2);
    }

    // If we can't find a good point, just return the end
    return html.length;
  }

  // Removed unused cleanupForContinuation function

  // Helper to check for common JavaScript syntax errors and HTML structure issues
  function validateContent(html) {
    try {
      let hasErrors = false;
      let errorMessages = [];

      // Check for JavaScript syntax errors
      const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/g;
      let match;

      while ((match = scriptRegex.exec(html)) !== null) {
        const scriptContent = match[1];
        if (!scriptContent.trim()) continue;

        // Check for common syntax errors
        const missingEqualsRegex = /\bconst\s+(\w+)\s+(\w+)/g;
        let syntaxMatch;

        while ((syntaxMatch = missingEqualsRegex.exec(scriptContent)) !== null) {
          hasErrors = true;
          const varName = syntaxMatch[1];
          const nextWord = syntaxMatch[2];
          errorMessages.push(`Missing equals sign in variable declaration: 'const ${varName} ${nextWord}'`);
        }

        // Check for other common errors
        if (scriptContent.includes('trigger event')) {
          hasErrors = true;
          errorMessages.push("Missing equals sign: 'trigger event' should be 'trigger = event'");
        }
      }

      // Check for duplicated content patterns (more general approach)
      const patterns = [
        // SVG path duplication
        { regex: /<path\s+[^>]*d="[^"]*<path/g, message: "Duplicated SVG path elements detected" },
        // General tag attribute duplication
        { regex: /(\s+\w+="[^"]*)\1/g, message: "Duplicated HTML attributes detected" },
        // Tag duplication
        { regex: /(<\w+[^>]*>)\s*\1/g, message: "Duplicated HTML tags detected" },
        // Broken tag nesting
        { regex: /<(\w+)[^>]*>[^<]*<\1/g, exclude: ['div', 'span', 'p', 'a', 'button', 'li', 'ul', 'ol', 'table', 'tr', 'td', 'th'], message: "Improper tag nesting detected" }
      ];

      for (const pattern of patterns) {
        const regex = pattern.regex;
        let dupeMatch;
        const matches = new Set();

        while ((dupeMatch = regex.exec(html)) !== null) {
          if (pattern.exclude) {
            const tagName = dupeMatch[1];
            if (pattern.exclude.includes(tagName)) continue;
          }

          // Extract a snippet of the problematic content for better error messages
          const matchStart = Math.max(0, dupeMatch.index - 10);
          const matchEnd = Math.min(html.length, dupeMatch.index + dupeMatch[0].length + 10);
          const snippet = html.substring(matchStart, matchEnd).replace(/\s+/g, ' ').trim();

          matches.add(snippet);
        }

        if (matches.size > 0) {
          hasErrors = true;
          errorMessages.push(`${pattern.message}: ${Array.from(matches).join(', ')}`);
        }
      }

      // Removed the findRepeatedChunks validation as it was incorrectly flagging valid HTML patterns
      // For example, common class names like "text-amber-700" appearing multiple times in the HTML
      // This was causing false positives and rejecting valid HTML

      if (hasErrors) {
        console.warn('[validateContent] Found potential content errors:', errorMessages);
        return { valid: false, errors: errorMessages };
      }

      return { valid: true };
    } catch (error) {
      console.error('[validateContent] Error validating content:', error);
      return { valid: true }; // Default to valid if validation fails
    }
  }

  // Using the continuationPrompt imported from promptUtils.js

  // Flag to track if we've sent the clean HTML marker
  let cleanHtmlMarkerSent = false;

  async function streamOrConcat(newMessages, append = false) {
    console.log(`[streamOrConcat] Called with stream: ${stream}, append: ${append}`);

    if (stream && res) {
      let lastChunk = '';
      let currentChunk = '';
      try {
        await llmFn(newMessages, true, {
          write: (delta) => {
            if (!delta) return;

            // Don't write to the response if we've already sent the clean HTML marker
            // Just accumulate the content for the final clean HTML
            if (!cleanHtmlMarkerSent) {
              currentChunk += delta;
              if (typeof res.write === 'function') {
                try {
                  res.write(delta);
                } catch (writeErr) {
                  console.error('[streamOrConcat] Error writing to response:', writeErr);
                }
              } else {
                console.warn('[streamOrConcat] res.write is not a function');
              }
            } else {
              // Just accumulate the content without writing to the response
              currentChunk += delta;
            }

            lastChunk = delta;
          },
          end: () => {
            console.log('[streamOrConcat] End called (but not ending response)');
          },
        });
        console.log('[streamOrConcat] Stream completed successfully');

        // If appending, find the proper continuation point to avoid malformed HTML
        if (append && htmlBuffer) {
          // Find the proper continuation point
          const continuationPoint = findContinuationPoint(htmlBuffer);
          console.log(`[streamOrConcat] Found continuation point at position ${continuationPoint} of ${htmlBuffer.length}`);

          // Trim the HTML buffer to the continuation point
          if (continuationPoint < htmlBuffer.length) {
            const originalLength = htmlBuffer.length;
            htmlBuffer = htmlBuffer.substring(0, continuationPoint);
            console.log(`[streamOrConcat] Trimmed HTML buffer from ${originalLength} to ${htmlBuffer.length} characters`);
          }
        }

        // Add the current chunk to the buffer
        htmlBuffer += currentChunk;
      } catch (err) {
        console.error('[streamOrConcat] Error during streaming:', err);
      }
      return lastChunk;
    } else {
      console.log('[streamOrConcat] Using non-streaming approach');
      try {
        const code = await llmFn(newMessages, false, null);
        console.log(`[streamOrConcat] Non-streaming response received, length: ${code ? code.length : 0}`);

        // If appending, find the proper continuation point to avoid malformed HTML
        if (append && htmlBuffer) {
          // Find the proper continuation point
          const continuationPoint = findContinuationPoint(htmlBuffer);
          console.log(`[streamOrConcat] Found continuation point at position ${continuationPoint} of ${htmlBuffer.length}`);

          // Trim the HTML buffer to the continuation point
          if (continuationPoint < htmlBuffer.length) {
            const originalLength = htmlBuffer.length;
            htmlBuffer = htmlBuffer.substring(0, continuationPoint);
            console.log(`[streamOrConcat] Trimmed HTML buffer from ${originalLength} to ${htmlBuffer.length} characters`);
          }

          // Check for CLEAN_HTML_FOLLOWS marker in the new code
          const cleanHtmlMarkerIndex = code.indexOf('<!-- CLEAN_HTML_FOLLOWS -->');
          if (cleanHtmlMarkerIndex > -1) {
            console.log('[streamOrConcat] Found CLEAN_HTML_FOLLOWS marker in non-streaming response');
            const contentAfterMarker = code.substring(cleanHtmlMarkerIndex + '<!-- CLEAN_HTML_FOLLOWS -->'.length).trim();

            if (contentAfterMarker) {
              console.log('[streamOrConcat] Using content after CLEAN_HTML_FOLLOWS marker');
              // Use the content after the marker for appending
              htmlBuffer += contentAfterMarker;
            } else {
              console.log('[streamOrConcat] No content after CLEAN_HTML_FOLLOWS marker, using content before marker');
              // If no content after marker, use content before marker
              htmlBuffer += code.substring(0, cleanHtmlMarkerIndex);
            }
          } else {
            // No marker, append normally
            htmlBuffer += code;
          }
        } else {
          // Not appending, just set the buffer to the code
          // Check for CLEAN_HTML_FOLLOWS marker
          const cleanHtmlMarkerIndex = code.indexOf('<!-- CLEAN_HTML_FOLLOWS -->');
          if (cleanHtmlMarkerIndex > -1) {
            console.log('[streamOrConcat] Found CLEAN_HTML_FOLLOWS marker in non-streaming response');
            const contentAfterMarker = code.substring(cleanHtmlMarkerIndex + '<!-- CLEAN_HTML_FOLLOWS -->'.length).trim();

            if (contentAfterMarker) {
              console.log('[streamOrConcat] Using content after CLEAN_HTML_FOLLOWS marker');
              // Use the content after the marker
              htmlBuffer = contentAfterMarker;
            } else {
              console.log('[streamOrConcat] No content after CLEAN_HTML_FOLLOWS marker, using content before marker');
              // If no content after marker, use content before marker
              htmlBuffer = code.substring(0, cleanHtmlMarkerIndex);
            }
          } else {
            // No marker, use the full code
            htmlBuffer = code;
          }
        }

        return code;
      } catch (err) {
        console.error('[streamOrConcat] Error during non-streaming call:', err);
        throw err;
      }
    }
  }

  try {
    // Initial generation
    console.log('[modifyContentWithLLMAutoContinue] Starting initial generation');
    await streamOrConcat(messages);
    isComplete = isHtmlComplete(htmlBuffer);

    // Validate content in the initial response
    const contentValidation = validateContent(htmlBuffer);
    if (!contentValidation.valid) {
      console.warn('[modifyContentWithLLMAutoContinue] Content validation failed, adding fix instructions');
      // Add fix instructions to the continuation prompt
      const fixPrompt = continuationPrompt + `\n\nIMPORTANT: Fix these errors in your code:\n${contentValidation.errors.map(err => `- ${err}`).join('\n')}`;

      // Create continuation messages with fix instructions
      // Use the same context-aware approach as for regular continuations
      const lastCompleteTag = htmlBuffer.match(/<\/[a-zA-Z][a-zA-Z0-9]*>\s*$/);
      let contextStart = 0;

      if (lastCompleteTag) {
        const tagName = lastCompleteTag[0].substring(2, lastCompleteTag[0].length - 1);
        const openingTagRegex = new RegExp(`<${tagName}[^>]*>`, 'g');
        let match;
        let positions = [];

        // Find all opening tags of this type
        while ((match = openingTagRegex.exec(htmlBuffer)) !== null) {
          positions.push(match.index);
        }

        // If we have opening tags, use the last one as context start
        if (positions.length > 0) {
          // Get the last opening tag position
          contextStart = Math.max(0, positions[positions.length - 1] - 200); // Include some context before the tag
        }
      }

      // If we couldn't find a good context point, use the last 4000 chars
      if (contextStart === 0) {
        contextStart = Math.max(0, htmlBuffer.length - 4000);
      }

      const fixContext = htmlBuffer.slice(contextStart);
      console.log(`[modifyContentWithLLMAutoContinue] Providing ${fixContext.length} chars of context for initial fix`);

      const fixMessages = [
        messages[0], // Keep the system message
        { role: 'assistant', content: fixContext }, // Context for fix
        { role: 'user', content: fixPrompt }
      ];

      // Get a fixed version
      await streamOrConcat(fixMessages, true);
      isComplete = isHtmlComplete(htmlBuffer);
    }

    // Check if the HTML is already complete before attempting continuation

    if (isComplete) {
      console.log('[modifyContentWithLLMAutoContinue] HTML is already complete, no continuation needed');
    } else {
      console.log('[modifyContentWithLLMAutoContinue] HTML is incomplete, starting continuation process');

      // Auto-continue if incomplete
      while (chunkCount < maxChunks && !isComplete) {
        chunkCount++;
        console.log(`[modifyContentWithLLMAutoContinue] Continuing generation (chunk ${chunkCount}/${maxChunks})`);

        // Create continuation messages with context
        // Find the last complete tag to provide better context
        const lastCompleteTag = htmlBuffer.match(/<\/[a-zA-Z][a-zA-Z0-9]*>\s*$/);
        let contextStart = 0;

        if (lastCompleteTag) {
          const tagName = lastCompleteTag[0].substring(2, lastCompleteTag[0].length - 1);
          const openingTagRegex = new RegExp(`<${tagName}[^>]*>`, 'g');
          let match;
          let positions = [];

          // Find all opening tags of this type
          while ((match = openingTagRegex.exec(htmlBuffer)) !== null) {
            positions.push(match.index);
          }

          // If we have opening tags, use the last one as context start
          if (positions.length > 0) {
            // Get the last opening tag position
            contextStart = Math.max(0, positions[positions.length - 1] - 200); // Include some context before the tag
          }
        }

        // If we couldn't find a good context point, use the last 4000 chars
        if (contextStart === 0) {
          contextStart = Math.max(0, htmlBuffer.length - 4000);
        }

        const continuationContext = htmlBuffer.slice(contextStart);
        console.log(`[modifyContentWithLLMAutoContinue] Providing ${continuationContext.length} chars of context for continuation`);

        // Check if we have a complete app div but missing the closing script or div tags
        const appDivOpeningTag = /<div\s+id=["']app["'][^>]*>/g.exec(htmlBuffer);
        const scriptTag = /<script\s+data-exec=["']inline["'][^>]*>/g.exec(htmlBuffer);

        let customContinuationPrompt = continuationPrompt;

        if (appDivOpeningTag && !htmlBuffer.endsWith('</div>')) {
          if (scriptTag && !htmlBuffer.includes('</script>')) {
            // We have an open script tag that needs to be closed
            customContinuationPrompt = `
Continue the code or HTML from the last line. Do not repeat any instructions or meta text. You were in the middle of a script tag.
Complete the script tag and then close the app div with </script></div>.
Do NOT add any explanations or comments outside the script tag.
`;
          } else if (!scriptTag) {
            // We need to add a script tag and close the app div
            customContinuationPrompt = `
Continue the code or HTML from the last line. Do not repeat any instructions or meta text.
Add a script tag with the SPA router functionality and then close the app div.
The script should look like:
<script data-exec="inline">
  const pages = document.querySelectorAll('.page');

  function showPage(pageId) {
    const trigger = event?.target || null;
    const target = document.getElementById('page-' + pageId);
    if (target) {
      pages.forEach(p => p.style.display = 'none');
      target.style.display = 'block';
    }
  }

  document.addEventListener('DOMContentLoaded', () => {
    showPage('feed');
  });

  document.addEventListener('click', (e) => {
    const target = e.target.closest('[data-nav]');
    if (target) {
      e.preventDefault();
      const pageId = target.getAttribute('data-nav');
      showPage(pageId);
    }
  });
</script>
</div>
`;
          }
        }

        const continuationMessages = [
          messages[0], // Keep the system message
          { role: 'assistant', content: continuationContext }, // Context for continuation
          { role: 'user', content: customContinuationPrompt }
        ];

        await streamOrConcat(continuationMessages, true);
        isComplete = isHtmlComplete(htmlBuffer);
      }
    }

    // Final validation after all continuations
    const finalValidation = validateContent(htmlBuffer);
    if (!finalValidation.valid) {
      console.warn(`[modifyContentWithLLMAutoContinue] Content validation failed after all continuations, adding fix instructions`);
      // Add fix instructions to the continuation prompt
      const fixPrompt = continuationPrompt + `\n\nIMPORTANT: Fix these errors in your code:\n${finalValidation.errors.map(err => `- ${err}`).join('\n')}`;

      // Create continuation messages with fix instructions
      // Use the same context-aware approach as for regular continuations
      const lastCompleteTag = htmlBuffer.match(/<\/[a-zA-Z][a-zA-Z0-9]*>\s*$/);
      let contextStart = 0;

      if (lastCompleteTag) {
        const tagName = lastCompleteTag[0].substring(2, lastCompleteTag[0].length - 1);
        const openingTagRegex = new RegExp(`<${tagName}[^>]*>`, 'g');
        let match;
        let positions = [];

        // Find all opening tags of this type
        while ((match = openingTagRegex.exec(htmlBuffer)) !== null) {
          positions.push(match.index);
        }

        // If we have opening tags, use the last one as context start
        if (positions.length > 0) {
          // Get the last opening tag position
          contextStart = Math.max(0, positions[positions.length - 1] - 200); // Include some context before the tag
        }
      }

      // If we couldn't find a good context point, use the last 4000 chars
      if (contextStart === 0) {
        contextStart = Math.max(0, htmlBuffer.length - 4000);
      }

      const fixContext = htmlBuffer.slice(contextStart);
      console.log(`[modifyContentWithLLMAutoContinue] Providing ${fixContext.length} chars of context for fix`);

      const fixMessages = [
        messages[0], // Keep the system message
        { role: 'assistant', content: fixContext }, // Context for fix
        { role: 'user', content: fixPrompt }
      ];

      // Get a fixed version
      await streamOrConcat(fixMessages, true);
      isComplete = isHtmlComplete(htmlBuffer);
    }

    console.log(`[modifyContentWithLLMAutoContinue] Completed with ${chunkCount} chunks, buffer length: ${htmlBuffer.length}`);
    return htmlBuffer;
  } catch (err) {
    console.error('[modifyContentWithLLMAutoContinue] Error:', err);
    throw err;
  }
}

/**
 * Modifies the entire HTML content based on the prompt.
 * @param {string} htmlContent - The full HTML content
 * @param {string} prompt - Natural language prompt describing the desired changes
 * @param {boolean} stream - Whether to stream the response (default: false)
 * @param {object|function} res - Response object or callback function for streaming
 * @returns {Promise<string>} - The modified HTML content (if not streaming)
 */
async function modifyContentWithLLM(htmlContent, prompt, stream = false, res = null) {
  // Get the configured provider
  const configuredProvider = getConfiguredProvider();
  console.log(`[modifyContentWithLLM] Starting with provider: ${configuredProvider} (from environment), stream: ${stream}`);

  try {
    // Determine which LLM provider function to use
    let llmFn;
    if (configuredProvider === 'deepseek') {
      llmFn = generateCodeDeepSeek;
      console.log('[modifyContentWithLLM] Using DeepSeek provider');
    } else if (configuredProvider === 'anthropic') {
      llmFn = generateCodeAnthropic;
      console.log('[modifyContentWithLLM] Using Anthropic provider');
    } else {
      // Default: OpenAI
      llmFn = generateCodeOpenAI;
      console.log('[modifyContentWithLLM] Using OpenAI provider (default)');
    }

    // Get the model name based on provider
    const modelName = configuredProvider === 'deepseek' ? 'deepseek-chat' :
                      configuredProvider === 'anthropic' ? 'claude-3-7-sonnet-20250219' :
                      'gpt-4-1106-preview';

    // Calculate token limits
    const modelTokenLimit = getModelTokenLimit(modelName);
    const maxOutputTokens = Math.min(4096, Math.floor(modelTokenLimit * 0.7)); // 70% of model limit for output

    // Create messages for the LLM using the prompt utility
    const messages = getContentModificationPromptMessages(htmlContent, prompt);

    // Count tokens in the messages
    const messageTokens = countMessageTokens(messages, modelName);
    console.log(`[modifyContentWithLLM] Message tokens: ${messageTokens}, Model limit: ${modelTokenLimit}`);

    // Trim HTML content if needed to fit within token limits
    const availableTokens = modelTokenLimit - messageTokens - maxOutputTokens;
    if (availableTokens < 0) {
      console.log(`[modifyContentWithLLM] Token limit exceeded, trimming HTML content`);
      // Find the HTML content in the messages and trim it
      for (let i = 0; i < messages.length; i++) {
        if (messages[i].role === 'user' && messages[i].content.includes('CURRENT HTML DOCUMENT:')) {
          const originalContent = messages[i].content;
          // Extract HTML between ```html and ``` markers
          const htmlMatch = /```html\s*([\s\S]*?)\s*```/.exec(originalContent);
          if (htmlMatch && htmlMatch[1]) {
            const originalHtml = htmlMatch[1];
            const trimmedHtml = trimHtmlToTokenLimit(originalHtml, Math.max(1000, availableTokens), modelName);
            // Replace the original HTML with the trimmed version
            messages[i].content = originalContent.replace(
              /```html\s*([\s\S]*?)\s*```/,
              '```html\n' + trimmedHtml + '\n```'
            );
            console.log(`[modifyContentWithLLM] Trimmed HTML from ${countTokens(originalHtml, modelName)} to ${countTokens(trimmedHtml, modelName)} tokens`);
          }
          break;
        }
      }
    }

    console.log('[modifyContentWithLLM] Using auto-continue function with model:', modelName);

    // Use the auto-continue function to handle truncated responses
    const modifiedHtml = await modifyContentWithLLMAutoContinue(
      llmFn,
      messages,
      modelName,
      stream,
      res
    );

    // If not streaming, clean up the response
    if (!stream) {
      console.log('[modifyContentWithLLM] Non-streaming response received, length:', modifiedHtml ? modifiedHtml.length : 0);
      if (!modifiedHtml) {
        throw new Error('No content returned from LLM');
      }

      // Check if the HTML is complete (has closing div tag)
      const isComplete = modifiedHtml.trim().endsWith('</div>');
      console.log(`[modifyContentWithLLM] Non-streaming response is ${isComplete ? 'complete' : 'incomplete'}`);

      // Apply post-processing to fix any remaining issues
      const cleanedHtml = postProcessHtml(modifiedHtml);

      // Log the final HTML length after processing
      console.log(`[modifyContentWithLLM] Final HTML length after processing: ${cleanedHtml.length}`);

      // Return the cleaned HTML
      return cleanupLLMResponse(cleanedHtml);
    }

    console.log('[modifyContentWithLLM] Streaming response completed');

    // For streaming, we need to post-process the HTML and send a final clean version
    if (stream && res && typeof res.write === 'function') {
      try {
        // Apply post-processing to fix any remaining issues
        const cleanedHtml = postProcessHtml(modifiedHtml);

        // Always send the cleaned HTML for consistency
        console.log('[modifyContentWithLLM] Sending post-processed HTML to client');

        // Set the flag to indicate we've sent the clean HTML marker
        // This will prevent any further streaming of raw content
        cleanHtmlMarkerSent = true;

        // Instead of appending to the response, we'll replace it entirely
        // First, clear the response by sending a special marker
        res.write('\n\n<!-- CLEAN_HTML_FOLLOWS -->\n\n');

        // Send the cleaned HTML as the complete response
        res.write(cleanedHtml);

        // Add a comment to indicate the end of the cleaned HTML
        res.write('\n\n<!-- END_CLEAN_HTML -->\n\n');

        // End the response to prevent any further writing
        res.end();
      } catch (e) {
        console.error('[modifyContentWithLLM] Error during post-processing:', e);

        // Try to end the response even if there was an error
        try {
          if (!res.writableEnded) {
            res.end();
          }
        } catch (endError) {
          console.error('[modifyContentWithLLM] Error ending response:', endError);
        }
      }
    }
  } catch (error) {
    console.error('[modifyContentWithLLM] Error:', error);
    console.error('[modifyContentWithLLM] Error stack:', error.stack);

    if (error.response) {
      console.error('[modifyContentWithLLM] API response error:', {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers
      });
    }

    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Error modifying content. Please try again.]\n');
        res.end();
      } catch (e) {
        console.error('[modifyContentWithLLM] Failed to write error message to response:', e);
      }
    }
    throw error;
  }
}

/**
 * Robust implementation for modifying HTML content with auto-continuation.
 * This function handles both streaming and non-streaming modes consistently,
 * ensuring complete HTML is returned in all cases.
 *
 * @param {string} htmlContent - The full HTML content
 * @param {string} prompt - Natural language prompt describing the desired changes
 * @param {boolean} stream - Whether to stream the response (default: false)
 * @param {object|function} res - Response object or callback function for streaming
 * @returns {Promise<string>} - The modified HTML content
 */
async function robustModifyContentWithLLM(htmlContent, prompt, stream = false, res = null) {
  const configuredProvider = getConfiguredProvider();
  console.log(`[robustModifyContentWithLLM] Starting with provider: ${configuredProvider}, stream: ${stream}`);

  // Set a longer timeout for the entire operation (5 minutes)
  const OPERATION_TIMEOUT = 300000; // 5 minutes
  let operationTimeoutId = null;

  // Track when the operation started
  const startTime = Date.now();

  // Create an AbortController to handle timeouts
  const abortController = new AbortController();
  const signal = abortController.signal;

  // Set up the timeout
  operationTimeoutId = setTimeout(() => {
    console.warn('[robustModifyContentWithLLM] Operation timed out after 5 minutes');
    abortController.abort('timeout');

    // If we're streaming, send a timeout message to the client
    if (stream && res && typeof res.write === 'function' && !res.writableEnded) {
      try {
        res.write('\n\n[Operation timed out after 5 minutes. Returning partial results.]\n\n');
        res.end();
      } catch (e) {
        console.error('[robustModifyContentWithLLM] Error sending timeout message:', e);
      }
    }
  }, OPERATION_TIMEOUT);

  try {
    // Add signal to the abort controller
    signal.addEventListener('abort', () => {
      console.warn('[robustModifyContentWithLLM] Operation aborted:', signal.reason);
    });
    // Determine which LLM provider function to use
    let llmFn;
    if (configuredProvider === 'deepseek') {
      llmFn = generateCodeDeepSeek;
    } else if (configuredProvider === 'anthropic') {
      llmFn = generateCodeAnthropic;
    } else {
      // Default: OpenAI
      llmFn = generateCodeOpenAI;
    }

    // Get the model name based on provider (for logging purposes)
    const providerInfo = configuredProvider === 'deepseek' ? 'deepseek-chat' :
                        configuredProvider === 'anthropic' ? 'claude-3-7-sonnet-20250219' :
                        'gpt-4-1106-preview';
    console.log(`[robustModifyContentWithLLM] Using provider: ${providerInfo}`);

    // Create messages for the LLM using the prompt utility
    const messages = getContentModificationPromptMessages(htmlContent, prompt);

    // Prepare for content collection
    let completeHtml = '';
    let contentBuffer = '';
    let isComplete = false;
    let continuationCount = 0;
    const MAX_CONTINUATIONS = 5;

    // Function to check if HTML is complete using a proper HTML parser
    const checkHtmlCompleteness = (html) => {
      if (!html) return false;

      const trimmedHtml = html.trim();

      try {
        // Use node-html-parser which is already a dependency in the project
        const parser = require('node-html-parser');
        const root = parser.parse(trimmedHtml);

        // Check if parsing was successful
        if (!root) {
          console.log('[checkHtmlCompleteness] Failed to parse HTML');
          return false;
        }

        // Basic structural checks
        const appDiv = root.querySelector('div#app');
        const hasAppDiv = !!appDiv;

        // Check for script tag
        const scriptTags = root.querySelectorAll('script');
        const hasScript = scriptTags.length > 0;

        // Check if the document ends with a div (common pattern in SPAs)
        const endsWithDiv = trimmedHtml.endsWith('</div>');

        // Find all page divs (any div with id starting with "page-")
        const pageElements = root.querySelectorAll('div[id^="page-"]');
        const pageCount = pageElements.length;
        const pageContentStatus = {};

        // Track if we have at least one page div
        const hasPageDivs = pageCount > 0;

        // Check each page div for content
        pageElements.forEach(page => {
          const pageId = page.id;
          const hasContent = page.innerHTML.trim() !== '';
          const hasPlaceholderComment = page.innerHTML.includes('<!-- ') && page.innerHTML.includes(' -->');

          pageContentStatus[pageId] = {
            hasContent,
            hasPlaceholderComment
          };
        });

        // Log all checks for debugging
        console.log(`[checkHtmlCompleteness] HTML validation results:
          - Parsed successfully: ${!!root}
          - Has app div: ${hasAppDiv}
          - Has page divs: ${hasPageDivs} (${pageCount} found)
          - Has scripts: ${hasScript} (${scriptTags.length} found)
          - Ends with div: ${endsWithDiv}
          - Page content status: ${JSON.stringify(pageContentStatus)}
        `);

        // Check if any page has only placeholder comments
        const hasOnlyPlaceholderPages = Object.values(pageContentStatus).some(
          status => status.hasPlaceholderComment && !status.hasContent
        );

        if (hasOnlyPlaceholderPages) {
          console.log('[checkHtmlCompleteness] Warning: Some pages have only placeholder comments');
        }

        // For JustPrototype, we consider HTML complete if:
        // 1. It has the app div
        // 2. It has at least one page div
        // 3. It ends with a closing div tag
        // 4. It has at least one script tag
        // 5. No pages have only placeholder comments
        const isComplete = hasAppDiv && hasPageDivs && endsWithDiv && hasScript && !hasOnlyPlaceholderPages;

        return isComplete;
      } catch (error) {
        // If parsing fails, the HTML is likely incomplete or malformed
        console.error('[checkHtmlCompleteness] Error parsing HTML:', error.message);
        return false;
      }
    };

    // We don't need a continuation point function anymore since we're using the full content

    // Function to handle content from LLM
    const processLLMContent = async (initialMessages, isInitial = true) => {
      console.log(`[robustModifyContentWithLLM] Processing ${isInitial ? 'initial' : 'continuation'} content`);

      // Check if the operation has been aborted
      if (signal.aborted) {
        console.log(`[robustModifyContentWithLLM] Operation was aborted: ${signal.reason}`);
        return '';
      }

      let content = '';

      if (stream && res) {
        // For streaming mode, we'll stream chunks as they come in
        let streamBuffer = '';

        // Check if the response is still writable before proceeding
        const isResponseWritable = res && typeof res.write === 'function' && !res.writableEnded;

        if (!isResponseWritable) {
          console.warn('[robustModifyContentWithLLM] Response is not writable, falling back to non-streaming mode');
          // Fall back to non-streaming mode
          content = await llmFn(initialMessages, false, null);
          contentBuffer += content;
          return content;
        }

        // Set up error handling for client disconnection
        const handleClientDisconnect = () => {
          console.warn('[robustModifyContentWithLLM] Client disconnected during streaming');
          // We'll continue processing but won't try to write to the response
        };

        // Add event listener for client disconnection if res is an Express response
        if (res.socket) {
          res.socket.on('close', handleClientDisconnect);
        }

        try {
          // Use streaming and write chunks to the response as they come in
          await llmFn(initialMessages, true, {
            write: (chunk) => {
              if (!chunk) return;

              // Add to our buffer
              streamBuffer += chunk;
              contentBuffer += chunk;

              // Write chunk to response immediately for streaming
              if (isResponseWritable && !res.writableEnded) {
                try {
                  res.write(chunk);
                  console.log(`[robustModifyContentWithLLM] Streamed chunk of size ${chunk.length}`);
                } catch (writeErr) {
                  console.error('[robustModifyContentWithLLM] Error writing chunk to response:', writeErr);
                }
              }
            },
            end: () => {
              console.log('[robustModifyContentWithLLM] Stream chunk completed');
            }
          });
        } catch (streamErr) {
          console.error('[robustModifyContentWithLLM] Error during streaming:', streamErr);
          // Continue with what we have so far
        } finally {
          // Remove the event listener if it was added
          if (res.socket) {
            res.socket.removeListener('close', handleClientDisconnect);
          }
        }

        content = streamBuffer;
      } else {
        // Non-streaming mode
        content = await llmFn(initialMessages, false, null);
        contentBuffer += content;
      }

      // Process the content
      console.log(`[robustModifyContentWithLLM] Received content chunk, length: ${content.length}`);

      // Return the raw content without any marker handling
      return content;
    };

    // Initial content generation
    completeHtml = await processLLMContent(messages);
    isComplete = checkHtmlCompleteness(completeHtml);

    // Continue if not complete and not aborted
    while (!isComplete && continuationCount < MAX_CONTINUATIONS && !signal.aborted) {
      continuationCount++;
      console.log(`[robustModifyContentWithLLM] Content incomplete, starting continuation #${continuationCount}`);

      // Check if we've been running for too long
      if (Date.now() - startTime > OPERATION_TIMEOUT - 10000) { // 10 seconds before timeout
        console.warn('[robustModifyContentWithLLM] Approaching timeout, stopping continuations');
        break;
      }

      // Extract the last few lines of HTML to provide context without sending the entire document
      // This is a simpler approach that avoids complex regex patterns
      const lastLines = extractLastLines(completeHtml, 10); // Get last 10 lines for context
      console.log(`[robustModifyContentWithLLM] Using last ${lastLines.split('\n').length} lines for continuation context`);

      // Create a continuation prompt that explicitly asks the LLM to continue without repeating
      const continuationPrompt = `
      I'll show you the last few lines of an HTML document that is incomplete.
      Please continue the HTML from the last line. Do not repeat any instructions or meta text.
      DO NOT repeat any of the HTML I'm showing you - just continue from where it ends.
      Here are the last lines:

      ${lastLines}

      Continue from here:`;

      // Create continuation messages
      const continuationMessages = [
        messages[0], // Keep the system message
        { role: 'user', content: continuationPrompt }
      ];

      // Get continuation content
      const continuationContent = await processLLMContent(continuationMessages, false);
      console.log(`[robustModifyContentWithLLM] Received continuation content, length: ${continuationContent.length}`);

      // For debugging, log a snippet of the continuation
      const continuationSnippet = continuationContent.substring(0, Math.min(100, continuationContent.length));
      console.log(`[robustModifyContentWithLLM] Continuation starts with: "${continuationSnippet}"`);

      // Simply append the continuation to the existing HTML
      // The LLM has been instructed not to repeat content
      completeHtml += continuationContent;

      // Check if complete now
      isComplete = checkHtmlCompleteness(completeHtml);

      console.log(`[robustModifyContentWithLLM] After continuation #${continuationCount}, isComplete=${isComplete}`);
    }

    // Helper function to extract the last N lines from HTML
    function extractLastLines(html, lineCount) {
      if (!html) return '';

      const lines = html.split('\n');
      const startLine = Math.max(0, lines.length - lineCount);
      return lines.slice(startLine).join('\n');
    }

    // No cleanup - return the raw HTML as is
    console.log(`[robustModifyContentWithLLM] Complete HTML ready, length: ${completeHtml.length}`);

    // For streaming, we need to ensure the response is properly ended
    if (stream && res && typeof res.write === 'function') {
      try {
        // Check if the HTML is incomplete and we need to use a fallback
        if (!isComplete) {
          console.log('[robustModifyContentWithLLM] HTML is incomplete, attempting to use non-streaming approach');

          // If HTML is incomplete, try to get a complete version using non-streaming approach
          // This is a fallback mechanism to ensure we always return complete HTML
          try {
            // Create a new set of messages for a fresh request
            const fallbackMessages = getContentModificationPromptMessages(htmlContent, prompt);

            // Use non-streaming approach to get complete HTML
            const fallbackHtml = await llmFn(fallbackMessages, false, null);

            // Check if the fallback HTML is complete
            if (checkHtmlCompleteness(fallbackHtml)) {
              console.log('[robustModifyContentWithLLM] Fallback non-streaming approach returned complete HTML');

              // Just update the complete HTML for the return value
              // We won't send any additional content to avoid potential stitching issues
              console.log('[robustModifyContentWithLLM] Using fallback HTML for return value only');
              completeHtml = fallbackHtml;
            } else {
              console.log('[robustModifyContentWithLLM] Fallback approach also returned incomplete HTML');
            }
          } catch (fallbackError) {
            console.error('[robustModifyContentWithLLM] Error in fallback approach:', fallbackError);
          }
        }

        // End the response if it's not already ended
        if (!res.writableEnded) {
          console.log('[robustModifyContentWithLLM] Ending response stream');
          res.end();
        }

        console.log('[robustModifyContentWithLLM] Response stream completed, total HTML length:', completeHtml.length);
      } catch (err) {
        console.error('[robustModifyContentWithLLM] Error handling response completion:', err);

        // Try to end the response
        try {
          if (!res.writableEnded) {
            res.end();
          }
        } catch (endErr) {
          console.error('[robustModifyContentWithLLM] Error ending response:', endErr);
        }
      }
    }

    // Return the complete HTML without any cleanup
    return completeHtml;
  } catch (error) {
    console.error('[robustModifyContentWithLLM] Error:', error);

    // Handle streaming errors
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n\n[Error modifying content. Please try again.]\n\n');
        res.end();
      } catch (err) {
        console.error('[robustModifyContentWithLLM] Error writing error message:', err);
      }
    }

    throw error;
  } finally {
    // Clear the operation timeout
    if (operationTimeoutId) {
      clearTimeout(operationTimeoutId);
    }
  }
}

/**
 * Generates functionality for an unimplemented interactive element
 * @param {string} htmlContent - The full HTML content
 * @param {string} elementSelector - CSS selector for the element to implement
 * @param {string} elementType - Type of element (button, link, form, etc.)
 * @param {string} elementContext - Context or purpose of the element
 * @param {string} prompt - Additional instructions for the functionality
 * @param {boolean} stream - Whether to stream the response
 * @param {object} res - Express response object (only needed if stream=true)
 * @returns {Promise<string>} - Modified HTML content with implemented functionality
 */
async function generateFunctionalityWithLLM(
  htmlContent,
  elementSelector,
  elementType = '',
  elementContext = '',
  prompt = '',
  stream = false,
  res = null
) {
  // Create messages for the LLM
  const messages = getFunctionalityGenerationPromptMessages(
    htmlContent,
    elementSelector,
    elementType,
    elementContext,
    prompt
  );

  // Use the configured provider from environment variable
  const configuredProvider = getConfiguredProvider();
  console.log(`[generateFunctionalityWithLLM] Using provider: ${configuredProvider} (from environment)`);

  try {
    // Call the appropriate LLM provider
    if (configuredProvider === 'deepseek') {
      return await generateCodeDeepSeek(messages, stream, res);
    } else if (configuredProvider === 'anthropic') {
      return await generateCodeAnthropic(messages, stream, res);
    } else {
      // Default: OpenAI
      return await generateCodeOpenAI(messages, stream, res);
    }
  } catch (error) {
    console.error('[generateFunctionalityWithLLM] Error:', error);
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n[Error generating functionality. Please try again.]\n');
        res.end();
      } catch (e) {
        console.error('[generateFunctionalityWithLLM] Failed to write error message to response:', e);
      }
    }
    throw error;
  }
}

/**
 * Creates messages for generating functionality for an interactive element
 * @param {string} htmlContent - The full HTML content
 * @param {string} elementSelector - CSS selector for the element to implement
 * @param {string} elementType - Type of element (button, link, form, etc.)
 * @param {string} elementContext - Context or purpose of the element
 * @param {string} prompt - Additional instructions for the functionality
 * @returns {Array} - Array of messages for the LLM
 */
function getFunctionalityGenerationPromptMessages(
  htmlContent,
  elementSelector,
  elementType = '',
  elementContext = '',
  prompt = ''
) {
  // Extract the target element from the HTML content
  let targetElementHtml = '';
  try {
    const parser = new (require('node-html-parser')).default;
    const root = parser.parse(htmlContent);
    const targetElement = root.querySelector(elementSelector);
    if (targetElement) {
      targetElementHtml = targetElement.outerHTML;
      console.log(`[getFunctionalityGenerationPromptMessages] Found target element: ${elementSelector}`);
      console.log(`[getFunctionalityGenerationPromptMessages] Target element HTML: ${targetElementHtml.substring(0, 100)}...`);
    } else {
      console.warn(`[getFunctionalityGenerationPromptMessages] Could not find element with selector: ${elementSelector}`);
    }
  } catch (error) {
    console.error(`[getFunctionalityGenerationPromptMessages] Error extracting target element:`, error);
  }

  return [
    {
      role: 'system',
      content: `
You are an expert web developer specializing in implementing functionality for interactive elements in HTML/CSS/JavaScript.
Your task is to add functionality to an unimplemented interactive element while preserving the rest of the HTML document.

CRITICAL INSTRUCTIONS:
1. ONLY modify the element specified by the CSS selector and add necessary JavaScript
2. Preserve the EXACT structure, attributes, and content of all other elements
3. Make the ABSOLUTE MINIMAL changes necessary to implement the requested functionality
4. DO NOT change the document structure, DOCTYPE, or any meta information
5. Return the COMPLETE HTML document with your modifications
6. DO NOT add any comments, explanations, or markdown formatting
7. DO NOT wrap your response in markdown code blocks (no \`\`\`html or \`\`\` tags)
8. Return ONLY the raw HTML without any markdown formatting

FUNCTIONALITY IMPLEMENTATION INSTRUCTIONS:
9. Use jQuery for implementing functionality (it's already included in the document)
10. For buttons, implement appropriate click handlers
11. For forms, implement form submission and validation
12. For links, implement navigation or action handlers
13. For navigation elements, implement page switching functionality
14. Include all necessary JavaScript within <script> tags
15. Make sure all event handlers are properly attached
16. Implement client-side validation where appropriate
17. Add visual feedback for user interactions (loading states, success/error messages)
18. Ensure the functionality works within the single-page application context
19. Use hash-based navigation (#page-id) for page transitions
20. Make sure all JavaScript is properly enclosed in <script> tags

The user will provide:
1. The full HTML content
2. A CSS selector identifying the element to implement
3. The type of element (button, link, form, etc.)
4. Context about the element's purpose
5. Additional instructions for the functionality

REMEMBER: Your goal is to implement functionality for the specified element while preserving the rest of the document.
IMPORTANT: Return ONLY the raw HTML without any markdown formatting or code blocks.
`.trim()
    },
    {
      role: 'user',
      content: `
HTML Content:
\`\`\`html
${htmlContent}
\`\`\`

Element to implement (CSS selector): ${elementSelector}

${targetElementHtml ? `Current element HTML:\n\`\`\`html\n${targetElementHtml}\n\`\`\`` : ''}

Element type: ${elementType || 'Not specified'}
Element context: ${elementContext || 'Not specified'}
Additional instructions: ${prompt || 'Implement appropriate functionality for this element'}

IMPORTANT INSTRUCTIONS:
1. Implement functionality for ONLY the element matching the CSS selector: "${elementSelector}"
2. Make ONLY the minimal changes needed to implement the functionality
3. Preserve ALL other HTML exactly as is - do not change any other elements
4. Use jQuery for implementing the functionality
5. Return the COMPLETE HTML document with your modifications
6. DO NOT add any comments, explanations, or markdown formatting
7. Return ONLY the raw HTML without any code blocks or markdown formatting
8. Your response should start directly with the HTML content

Your response should be the complete HTML document with the implemented functionality for the specified element.
`.trim()
    }
  ];
}

/**
 * Robust implementation for generating code from a plan with auto-continuation.
 * This function handles both streaming and non-streaming modes consistently,
 * ensuring complete HTML is returned in all cases.
 *
 * @param {string} plan - The feature plan to generate code from
 * @param {boolean} stream - Whether to stream the response (default: false)
 * @param {object|function} res - Response object or callback function for streaming
 * @returns {Promise<string>} - The generated HTML content
 */
async function robustGenerateCodeFromPlan(plan, stream = false, res = null) {
  const configuredProvider = getConfiguredProvider();
  console.log(`[robustGenerateCodeFromPlan] Starting with provider: ${configuredProvider}, stream: ${stream}`);

  try {
    // Determine which LLM provider function to use
    let llmFn;
    if (configuredProvider === 'deepseek') {
      llmFn = generateCodeDeepSeek;
    } else if (configuredProvider === 'anthropic') {
      llmFn = generateCodeAnthropic;
    } else {
      // Default: OpenAI
      llmFn = generateCodeOpenAI;
    }

    // Provider is already determined above, no need for model name in this function

    // Create messages for the LLM using the prompt utility
    const messages = getCodeGenerationPromptMessages(plan);

    // Prepare for content collection
    let completeHtml = '';
    let contentBuffer = '';
    let isComplete = false;
    let continuationCount = 0;
    const MAX_CONTINUATIONS = 5;

    // Function to check if HTML is complete using a proper HTML parser
    const checkHtmlCompleteness = (html) => {
      if (!html) return false;

      const trimmedHtml = html.trim();

      try {
        // Use node-html-parser which is already a dependency in the project
        const parser = require('node-html-parser');
        const root = parser.parse(trimmedHtml);

        // Check if parsing was successful
        if (!root) {
          console.log('[checkHtmlCompleteness] Failed to parse HTML');
          return false;
        }

        // Basic structural checks
        const appDiv = root.querySelector('div#app');
        const hasAppDiv = !!appDiv;
        const scriptTags = root.querySelectorAll('script');
        const hasScript = scriptTags.length > 0;

        // Check for balanced structure
        // If the HTML parser successfully parsed the document without errors,
        // it means the HTML structure is valid (all tags are balanced)
        const isValidStructure = true;

        // Check if the document ends with a div (common pattern in SPAs)
        const endsWithDiv = trimmedHtml.endsWith('</div>');

        // Log all checks for debugging
        console.log(`[checkHtmlCompleteness] HTML validation results:
          - Parsed successfully: ${!!root}
          - Has app div: ${hasAppDiv}
          - Has scripts: ${hasScript} (${scriptTags.length} found)
          - Ends with div: ${endsWithDiv}
          - Valid structure: ${isValidStructure}
        `);

        // For JustPrototype, we consider HTML complete if:
        // 1. It has the app div
        // 2. It ends with a closing div tag
        // 3. It has valid HTML structure (all tags balanced)
        const isComplete = hasAppDiv && endsWithDiv && isValidStructure;

        return isComplete;
      } catch (error) {
        // If parsing fails, the HTML is likely incomplete or malformed
        console.error('[checkHtmlCompleteness] Error parsing HTML:', error.message);
        return false;
      }
    };

    // Function to handle content from LLM
    const processLLMContent = async (initialMessages, isInitial = true) => {
      console.log(`[robustGenerateCodeFromPlan] Processing ${isInitial ? 'initial' : 'continuation'} content`);

      let content = '';

      if (stream && res) {
        // Streaming mode - but don't stream to client yet
        // We'll collect all content and stream the complete HTML at the end
        let streamBuffer = '';

        await llmFn(initialMessages, true, {
          write: (chunk) => {
            if (!chunk) return;

            // Add to our buffer
            streamBuffer += chunk;
            contentBuffer += chunk;

            // Write to response for immediate feedback
            if (typeof res.write === 'function') {
              try {
                res.write(chunk);
              } catch (writeErr) {
                console.error('[robustGenerateCodeFromPlan] Error writing to response:', writeErr);
              }
            }
          },
          end: () => {
            console.log('[robustGenerateCodeFromPlan] Stream chunk completed');
          }
        });

        content = streamBuffer;
      } else {
        // Non-streaming mode
        content = await llmFn(initialMessages, false, null);
        contentBuffer += content;
      }

      // Process the content
      //console.log(`[robustGenerateCodeFromPlan] Received content chunk, length: ${content.length}`);

      // Return the raw content without any marker handling
      return content;
    };

    // Initial content generation
    completeHtml = await processLLMContent(messages);
    isComplete = checkHtmlCompleteness(completeHtml);

    // Continue if not complete
    while (!isComplete && continuationCount < MAX_CONTINUATIONS) {
      continuationCount++;
      console.log(`[robustGenerateCodeFromPlan] Content incomplete, starting continuation #${continuationCount}`);

      // Extract the last few lines of HTML to provide context without sending the entire document
      // This is a simpler approach that avoids complex regex patterns
      const lastLines = extractLastLines(completeHtml, 10); // Get last 10 lines for context
      console.log(`[robustGenerateCodeFromPlan] Using last ${lastLines.split('\n').length} lines for continuation context`);

      // Create a continuation prompt that explicitly asks the LLM to continue without repeating
      const continuationPrompt = `
      I'll show you the last few lines of an HTML document that is incomplete.
      Please continue the HTML from exactly where it left off.
      DO NOT repeat any of the HTML I'm showing you - just continue from where it ends.
      Here are the last lines:

      ${lastLines}

      Continue from here:`;

      // Create continuation messages
      const continuationMessages = [
        messages[0], // Keep the system message
        { role: 'user', content: continuationPrompt }
      ];

      // Get continuation content
      const continuationContent = await processLLMContent(continuationMessages, false);
      console.log(`[robustGenerateCodeFromPlan] Received continuation content, length: ${continuationContent.length}`);

      // For debugging, log a snippet of the continuation
      const continuationSnippet = continuationContent.substring(0, Math.min(100, continuationContent.length));
      console.log(`[robustGenerateCodeFromPlan] Continuation starts with: "${continuationSnippet}"`);

      // Simply append the continuation to the existing HTML
      // The LLM has been instructed not to repeat content
      completeHtml += continuationContent;

      // Check if complete now
      isComplete = checkHtmlCompleteness(completeHtml);

      console.log(`[robustGenerateCodeFromPlan] After continuation #${continuationCount}, isComplete=${isComplete}`);
    }

    // Helper function to extract the last N lines from HTML
    function extractLastLines(html, lineCount) {
      if (!html) return '';

      const lines = html.split('\n');
      const startLine = Math.max(0, lines.length - lineCount);
      return lines.slice(startLine).join('\n');
    }

    // Extract the full <div id="app">...</div> block from the complete HTML
    function extractFullAppDiv(html) {
      const openTag = html.search(/<div\s+id=["']app["'][^>]*>/i);
      const closeTag = html.lastIndexOf('</div>');
      if (openTag !== -1 && closeTag !== -1 && closeTag > openTag) {
        return html.slice(openTag, closeTag + 6);
      }
      return html;
    }

    // Extract the app div for consistency with the original implementation
    completeHtml = extractFullAppDiv(completeHtml);

    // No cleanup - return the raw HTML as is
    console.log(`[robustGenerateCodeFromPlan] Complete HTML ready, length: ${completeHtml.length}`);

    // For streaming, we've already been streaming the content
    if (stream && res && typeof res.write === 'function') {
      try {
        // End the response
        if (!res.writableEnded) {
          res.end();
        }

        console.log('[robustGenerateCodeFromPlan] Streaming response completed');
      } catch (err) {
        console.error('[robustGenerateCodeFromPlan] Error ending response:', err);

        // Try to end the response
        try {
          if (!res.writableEnded) {
            res.end();
          }
        } catch (endErr) {
          console.error('[robustGenerateCodeFromPlan] Error ending response:', endErr);
        }
      }
    }

    // Return the complete HTML without any cleanup
    return completeHtml;
  } catch (error) {
    console.error('[robustGenerateCodeFromPlan] Error:', error);

    // Handle streaming errors
    if (stream && res && typeof res.write === 'function') {
      try {
        res.write('\n\n[Error generating code. Please try again.]\n\n');
        res.end();
      } catch (err) {
        console.error('[robustGenerateCodeFromPlan] Error writing error message:', err);
      }
    }

    throw error;
  }
}

/**
 * Export the public API of the LLM service
 *
 * Note: All functions use the configured LLM provider from environment variables.
 * The provider parameter is kept in some functions for backward compatibility
 * but is ignored if LLM_PROVIDER environment variable is set.
 */
module.exports = {
  // Plan generation
  generateFeaturePlan,

  // Code generation
  generateCodeFromPlan,
  robustGenerateCodeFromPlan,

  // Content modification
  modifyElementWithLLM,
  modifyContentWithLLM,
  robustModifyContentWithLLM,

  // Functionality generation
  generateFunctionalityWithLLM,
};
