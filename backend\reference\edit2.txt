curl ^"https://readdy.ai/api/page_gen/edit^" ^
  -H ^"sec-ch-ua-platform: ^\^"Windows^\^"^" ^
  -H ^"Authorization: Bearer eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDIyMkFBQSIsImtpZCI6Imluc18ycWtRbmFmNnRNcW9DQVZRMjRsWEwzRzRDcnQiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************.GMxI1OHuHvrA2add1AQglH-dzsndrijcy3TjGU_Jq5iDjH8ctglDU-r3PcSegJBN3e8qh0yCaBvEL5PZYe7y2LhUbPgwqrecj0uYR1nZkerx8HvN1rdr_iNwj3Nd1DHEbpjhGygmV9ZD3vekqm78pT5p3I6PuStzE9Js1OfV636vN6ktUt7U6nfx6QJaUJ28K8BBHPwjFOXFDS_9S2Rjoz4mfmYTqfJF0PgYkmgiLvoiNGZsZBx7SOuICCG4WLYs_kYK8SEWAiZQGtu0wA8NAmk4x4tS5DPQ9S_3XgQcrCINvmm84DRspREOzrnF5GjkPL-vGMuC4ScVpbMz4wfT5w^" ^
  -H ^"Referer: https://readdy.ai/home/<USER>/a78f8145-4ad7-4523-a210-b1e729cbfc85^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"136^\^", ^\^"Google Chrome^\^";v=^\^"136^\^", ^\^"Not.A/Brand^\^";v=^\^"99^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?0^" ^
  -H ^"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36^" ^
  -H ^"accept: text/event-stream^" ^
  -H ^"Content-Type: application/json^" ^
  --data-raw ^"^{^\^"sessionKey^\^":^\^"a78f8145-4ad7-4523-a210-b1e729cbfc85^\^",^\^"desc^\^":^\^"**1. Page Header**- Top navigation bar with company logo and main menu items^\^\n^\^\n- ^\^\^\^"Reports^\^\^\^" link in the navigation menu is highlighted to indicate current page^\^\n^\^\n- User profile and notification icons in the top right^\^\n^\^\n- Back button/link to return to original page^\^\n^\^\n**2. Reports Control Panel**^\^\n^\^\n- Filter section with:  ^\^\n^\^\n- Date range picker (From/To dates)^\^\n^\^\n- Client dropdown selector  ^\^\n^\^\n- Report type selector (Performance, Client, Investment, Compliance)^\^\n^\^\n- Action buttons:  ^\^\n^\^\n- Generate New Report^\^\n^\^\n- Export Reports  ^\^\n^\^\n- Bulk Actions**3. Report Categories**^\^\n^\^\n- Four main category cards displayed in a grid:  ^\^\n^\^\n- Performance Reports  ^\^\n^\^\n- Client Reports    ^\^\n^\^\n- Investment Reports  ^\^\n^\^\n- Compliance Reports^\^\n^\^\n- Each card shows:  ^\^\n^\^\n- Category icon  ^\^\n^\^\n- Report count  ^\^\n^\^\n- Last generated date^\^\n^\^\n- Quick action buttons**4. Recent Reports Table**^\^\n^\^\n- Sortable table columns:^\^\n^\^\n- Report Name^\^\n^\^\n- Type  ^\^\n^\^\n- Generated Date  ^\^\n^\^\n- Status  ^\^\n^\^\n- Size  ^\^\n^\^\n- Actions^\^\n^\^\n- Preview thumbnail for each report^\^\n^\^\n- Download/Share/Delete actions^\^\n^\^\n- Pagination controls**5. Generate New Report Section**^\^\n^\^\n- Report template selector^\^\n^\^\n- Configurable parameters:  ^\^\n^\^\n- Report type^\^\n^\^\n- Date range^\^\n^\^\n- Client selection  ^\^\n^\^\n- Custom fields^\^\n^\^\n- Format options:^\^\n^\^\n- PDF^\^\n^\^\n- Excel  ^\^\n^\^\n- CSV  ^\^\n^\^\n- Interactive Dashboard^\^\n^\^\n- Generate button**6. Visualization Options**^\^\n^\^\n- Toggle between table and grid views^\^\n^\^\n- Chart/graph preview options^\^\n^\^\n- Data visualization controls^\^\n^\^\n- Export format selector**7. Footer**^\^\n^\^\n- Export progress indicator^\^\n^\^\n- Recently exported reports^\^\n^\^\n- Quick access to report templates^\^\n^\^\n- Help/Documentation links^</design-analyze^>^\^",^\^"recordId^\^":1627126,^\^"query^\^":^\^"can you add regulatory reports as welll ?^\^",^\^"style^\^":^\^"light^\^",^\^"color^\^":^\^"^\^",^\^"borderRadius^\^":^\^"medium^\^",^\^"language^\^":^\^"English^\^",^\^"framework^\^":^\^"react^\^",^\^"lib^\^":^\^"^\^",^\^"messages^\^":^[^{^\^"role^\^":^\^"user^\^",^\^"content^\^":^\^"I already have a original page: The user clicked on the ^\^\^\^"Reports^\^\^\^" navigation link in the header menu. They likely want to access a reports page where they can view, generate, or manage various financial and client reports related to their investment banking activities.Now your task is: Create a Reports page that displays various report types (Performance Reports, Client Reports, Investment Reports, Compliance Reports) with filtering options by date range and client. Include a table of recent reports with preview/download options, and a section to generate new custom reports with configurable parameters. Add visualization options and export functionality for different formats.Important: When generating the new page, ensure that users can click on a page element to access the original page. The Original page link is: https://readdy.ai/home/<USER>/366da360-7d43-403a-9e4a-f7a792963011. Add the original page link to a suitable UI element (such as a navigation bar item, tab bar option, or back button). And the link must be added on a ^<a^>^</a^>, such as ^<a href=^\^\^\^"https://readdy.ai/home/<USER>/366da360-7d43-403a-9e4a-f7a792963011^\^\^\^" data-readdy=^\^\^\^"true^\^\^\^"^>^</a^>^\^"^},^{^\^"role^\^":^\^"assistant^\^",^\^"content^\^":^\^"**1. Page Header**- Top navigation bar with company logo and main menu items^\^\n^\^\n- ^\^\^\^"Reports^\^\^\^" link in the navigation menu is highlighted to indicate current page^\^\n^\^\n- User profile and notification icons in the top right^\^\n^\^\n- Back button/link to return to original page^\^\n^\^\n**2. Reports Control Panel**^\^\n^\^\n- Filter section with:  ^\^\n^\^\n- Date range picker (From/To dates)^\^\n^\^\n- Client dropdown selector  ^\^\n^\^\n- Report type selector (Performance, Client, Investment, Compliance)^\^\n^\^\n- Action buttons:  ^\^\n^\^\n- Generate New Report^\^\n^\^\n- Export Reports  ^\^\n^\^\n- Bulk Actions**3. Report Categories**^\^\n^\^\n- Four main category cards displayed in a grid:  ^\^\n^\^\n- Performance Reports  ^\^\n^\^\n- Client Reports    ^\^\n^\^\n- Investment Reports  ^\^\n^\^\n- Compliance Reports^\^\n^\^\n- Each card shows:  ^\^\n^\^\n- Category icon  ^\^\n^\^\n- Report count  ^\^\n^\^\n- Last generated date^\^\n^\^\n- Quick action buttons**4. Recent Reports Table**^\^\n^\^\n- Sortable table columns:^\^\n^\^\n- Report Name^\^\n^\^\n- Type  ^\^\n^\^\n- Generated Date  ^\^\n^\^\n- Status  ^\^\n^\^\n- Size  ^\^\n^\^\n- Actions^\^\n^\^\n- Preview thumbnail for each report^\^\n^\^\n- Download/Share/Delete actions^\^\n^\^\n- Pagination controls**5. Generate New Report Section**^\^\n^\^\n- Report template selector^\^\n^\^\n- Configurable parameters:  ^\^\n^\^\n- Report type^\^\n^\^\n- Date range^\^\n^\^\n- Client selection  ^\^\n^\^\n- Custom fields^\^\n^\^\n- Format options:^\^\n^\^\n- PDF^\^\n^\^\n- Excel  ^\^\n^\^\n- CSV  ^\^\n^\^\n- Interactive Dashboard^\^\n^\^\n- Generate button**6. Visualization Options**^\^\n^\^\n- Toggle between table and grid views^\^\n^\^\n- Chart/graph preview options^\^\n^\^\n- Data visualization controls^\^\n^\^\n- Export format selector**7. Footer**^\^\n^\^\n- Export progress indicator^\^\n^\^\n- Recently exported reports^\^\n^\^\n- Quick access to report templates^\^\n^\^\n- Help/Documentation links^</design-analyze^>^\^"^}^],^\^"force^\^":false,^\^"seq^\^":1^}^"







  event:startMsg
data:I

event:startMsg
data:'

event:startMsg
data:l

event:startMsg
data:l

event:startMsg
data: 

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data:l

event:startMsg
data:p

event:startMsg
data: 

event:startMsg
data:y

event:startMsg
data:o

event:startMsg
data:u

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:d

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data: 

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data:g

event:startMsg
data:u

event:startMsg
data:l

event:startMsg
data:a

event:startMsg
data:t

event:startMsg
data:o

event:startMsg
data:r

event:startMsg
data:y

event:startMsg
data: 

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data:p

event:startMsg
data:o

event:startMsg
data:r

event:startMsg
data:t

event:startMsg
data:s

event:startMsg
data: 

event:startMsg
data:s

event:startMsg
data:e

event:startMsg
data:c

event:startMsg
data:t

event:startMsg
data:i

event:startMsg
data:o

event:startMsg
data:n

event:startMsg
data:.

event:startMsg
data: 

event:startMsg
data:I

event:startMsg
data:'

event:startMsg
data:l

event:startMsg
data:l

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:d

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:i

event:startMsg
data:t

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:s

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:n

event:startMsg
data:o

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data:r

event:startMsg
data: 

event:startMsg
data:c

event:startMsg
data:a

event:startMsg
data:t

event:startMsg
data:e

event:startMsg
data:g

event:startMsg
data:o

event:startMsg
data:r

event:startMsg
data:y

event:startMsg
data: 

event:startMsg
data:c

event:startMsg
data:a

event:startMsg
data:r

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:n

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:u

event:startMsg
data:p

event:startMsg
data:d

event:startMsg
data:a

event:startMsg
data:t

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data:l

event:startMsg
data:a

event:startMsg
data:t

event:startMsg
data:e

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:c

event:startMsg
data:o

event:startMsg
data:m

event:startMsg
data:p

event:startMsg
data:o

event:startMsg
data:n

event:startMsg
data:e

event:startMsg
data:n

event:startMsg
data:t

event:startMsg
data:s

event:startMsg
data:.

event:startMsg
data:
data:

event:startMsg
data:
data:

event:startMsgFinish
data:

event:ping
data:

event:ping
data:

event:ping
data:

event:ping
data:

event:ping
data:

event:ping
data:

event:data
data:import React, { useState, useEffect } from 'react';
data:

event:data
data:import * as echarts from 'echarts';
data:

event:data
data:const App: React.FC = () => {
data:

event:data
data:const [isDropdownOpen, setIsDropdownOpen] = useState(false);
data:

event:data
data:const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
data:

event:data
data:const [activeReportType, setActiveReportType] = useState('all');
data:

event:data
data:const [startDate, setStartDate] = useState('');
data:

event:data
data:const [endDate, setEndDate] = useState('');
data:

event:data
data:const [selectedClient, setSelectedClient] = useState('');
data:

event:data
data:const [viewMode, setViewMode] = useState('table');
data:

event:data
data:const [currentPage, setCurrentPage] = useState(1);
data:

event:data
data:const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
data:

event:data
data:const [selectedFormat, setSelectedFormat] = useState('pdf');
data:

event:data
data:useEffect(() => {
data:

event:data
data:// Performance trend chart
data:

event:data
data:const performanceChart = echarts.init(document.getElementById('performance-trend-chart'));
data:

event:data
data:const performanceOption = {
data:

event:data
data:animation: false,
data:

event:data
data:tooltip: {
data:

event:data
data:trigger: 'axis'
data:

event:data
data:},
data:

event:data
data:legend: {
data:

event:data
data:data: ['Monthly Reports', 'Quarterly Reports', 'Annual Reports']
data:

event:data
data:},
data:

event:data
data:grid: {
data:

event:data
data:left: '3%',
data:

event:data
data:right: '4%',
data:

event:data
data:bottom: '3%',
data:

event:data
data:containLabel: true
data:

event:data
data:},
data:

event:data
data:xAxis: {
data:

event:data
data:type: 'category',
data:

event:data
data:boundaryGap: false,
data:

event:data
data:data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
data:

event:data
data:},
data:

event:data
data:yAxis: {
data:

event:data
data:type: 'value'
data:

event:data
data:},
data:

event:data
data:series: [
data:

event:data
data:{
data:

event:data
data:name: 'Monthly Reports',
data:

event:data
data:type: 'line',
data:

event:data
data:data: [12, 15, 18, 14, 22, 25, 20, 18, 24, 28, 30, 32],
data:

event:data
data:itemStyle: {
data:

event:data
data:color: '#3B82F6'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:{
data:

event:data
data:name: 'Quarterly Reports',
data:

event:data
data:type: 'line',
data:

event:data
data:data: [5, 4, 6, 5, 7, 8, 9, 8, 10, 12, 14, 15],
data:

event:data
data:itemStyle: {
data:

event:data
data:color: '#10B981'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:{
data:

event:data
data:name: 'Annual Reports',
data:

event:data
data:type: 'line',
data:

event:data
data:data: [2, 1, 3, 2, 4, 3, 5, 4, 6, 5, 7, 8],
data:

event:data
data:itemStyle: {
data:

event:data
data:color: '#F59E0B'
data:

event:data
data:}
data:

event:data
data:}
data:

event:data
data:]
data:

event:data
data:};
data:

event:data
data:performanceChart.setOption(performanceOption);
data:

event:data
data:// Report distribution chart
data:

event:data
data:const distributionChart = echarts.init(document.getElementById('report-distribution-chart'));
data:

event:data
data:const distributionOption = {
data:

event:data
data:animation: false,
data:

event:data
data:tooltip: {
data:

event:data
data:trigger: 'item',
data:

event:data
data:formatter: '{a} <br/>{b}: {c} ({d}%)'
data:

event:data
data:},
data:

event:data
data:legend: {
data:

event:data
data:orient: 'vertical',
data:

event:data
data:right: 10,
data:

event:data
data:top: 'center',
data:

event:data
data:data: ['Performance', 'Client', 'Investment', 'Compliance']
data:

event:data
data:},
data:

event:data
data:series: [
data:

event:data
data:{
data:

event:data
data:name: 'Report Types',
data:

event:data
data:type: 'pie',
data:

event:data
data:radius: ['50%', '70%'],
data:

event:data
data:avoidLabelOverlap: false,
data:

event:data
data:itemStyle: {
data:

event:data
data:borderRadius: 10,
data:

event:data
data:borderColor: '#fff',
data:

event:data
data:borderWidth: 2
data:

event:data
data:},
data:

event:data
data:label: {
data:

event:data
data:show: false,
data:

event:data
data:position: 'center'
data:

event:data
data:},
data:

event:data
data:emphasis: {
data:

event:data
data:label: {
data:

event:data
data:show: true,
data:

event:data
data:fontSize: 14,
data:

event:data
data:fontWeight: 'bold'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:labelLine: {
data:

event:data
data:show: false
data:

event:data
data:},
data:

event:data
data:data: [
data:

event:data
data:{ value: 35, name: 'Performance' },
data:

event:data
data:{ value: 30, name: 'Client' },
data:

event:data
data:{ value: 20, name: 'Investment' },
data:

event:data
data:{ value: 15, name: 'Compliance' }
data:

event:data
data:]
data:

event:data
data:}
data:

event:data
data:]
data:

event:data
data:};
data:

event:data
data:distributionChart.setOption(distributionOption);
data:

event:data
data:// Handle resize
data:

event:data
data:const handleResize = () => {
data:

event:data
data:performanceChart.resize();
data:

event:data
data:distributionChart.resize();
data:

event:data
data:};
data:

event:data
data:window.addEventListener('resize', handleResize);
data:

event:data
data:return () => {
data:

event:data
data:window.removeEventListener('resize', handleResize);
data:

event:data
data:performanceChart.dispose();
data:

event:data
data:distributionChart.dispose();
data:

event:data
data:};
data:

event:data
data:}, []);
data:

event:data
data:return (
data:

event:data
data:<div className="min-h-screen bg-gray-50">
data:

event:data
data:{/* Header */}
data:

event:data
data:<header className="bg-white shadow-sm">
data:

event:data
data:<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
data:

event:data
data:<div className="flex justify-between h-16">
data:

event:data
data:<div className="flex">
data:

event:data
data:<div className="flex-shrink-0 flex items-center">
data:

event:data
data:<img className="h-8 w-auto" src="https://readdy.ai/api/search-image?query=Modern%2520minimalist%2520investment%2520banking%2520logo%2520with%2520blue%2520and%2520gray%2520colors%252C%2520professional%2520and%2520clean%2520design%252C%2520suitable%2520for%2

event:data
data:520a%2520high-end%2520financial%2520institution%252C%2520simple%2520geometric%2520shapes%2520with%2520subtle%2520gradient&width=160&height=40&seq=1&orientation=landscape" alt="Dashing CRM" />
data:

event:data
data:</div>
data:

event:data
data:<nav className="ml-6 flex space-x-8">
data:

event:data
data:<div className="relative">
data:

event:data
data:<button
data:

event:data
data:onClick={() => setIsDropdownOpen(!isDropdownOpen)}
data:

event:data
data:className="border-transparent text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 text-sm font-medium cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:>
data:

event:data
data:Dashboard
data:

event:data
data:<i className="fas fa-chevron-down ml-1 text-xs"></i>
data:

event:data
data:</button>
data:

event:data
data:{isDropdownOpen && (
data:

event:data
data:<div className="absolute z-10 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
data:

event:data
data:<div className="py-1" role="menu" aria-orientation="vertical">
data:

event:data
data:<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Overview</a>
data:

event:data
data:<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Performance</a>
data:

event:data
data:<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Analytics</a>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:)}
data:

event:data
data:</div>
data:

event:data
data:<a href="#" className="border-transparent text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 text-sm font-medium whitespace-nowrap">Clients</a>
data:

event:data
data:<a href="#" className="border-transparent text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 text-sm font-medium whitespace-nowrap">Investments</a>
data:

event:data
data:<a href="https://readdy.ai/home/<USER>/366da360-7d43-403a-9e4a-f7a792963011" data-readdy="true" className="border-indigo-500 text-indigo-600 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium whitespace-nowrap">

event:data
data:Reports</a>
data:

event:data
data:</nav>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0">
data:

event:data
data:<div className="relative rounded-md shadow-sm">
data:

event:data
data:<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
data:

event:data
data:<i className="fas fa-search text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:<input
data:

event:data
data:type="text"
data:

event:data
data:className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 pr-3 py-2 border-gray-300 rounded-md text-sm"
data:

event:data
data:placeholder="Search..."
data:

event:data
data:/>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<button className="ml-4 relative p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<span className="sr-only">View notifications</span>
data:

event:data
data:<i className="fas fa-bell"></i>
data:

event:data
data:<span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500"></span>
data:

event:data
data:</button>
data:

event:data
data:<div className="ml-4 relative flex-shrink-0">
data:

event:data
data:<div>
data:

event:data
data:<button
data:

event:data
data:onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
data:

event:data
data:className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:>
data:

event:data
data:<span className="sr-only">Open user menu</span>
data:

event:data
data:<img
data:

event:data
data:className="h-8 w-8 rounded-full"
data:

event:data
data:src="https://readdy.ai/api/search-image?query=Professional%2520headshot%2520of%2520a%2520male%2520investment%2520banker%2520in%2520a%2520suit%2520with%2520a%2520neutral%2520background%252C%2520business%2520attire%252C%2520confident%2520expression%252C%2520

event:data
data:high%2520quality%2520portrait%2520photo&width=32&height=32&seq=2&orientation=squarish"
data:

event:data
data:alt=""
data:

event:data
data:/>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:{isProfileDropdownOpen && (
data:

event:data
data:<div className="absolute right-0 z-10 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5">
data:

event:data
data:<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
data:

event:data
data:<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
data:

event:data
data:<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
data:

event:data
data:</div>
data:

event:data
data:)}
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</header>
data:

event:data
data:<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
data:

event:data
data:{/* Page Header */}
data:

event:data
data:<div className="md:flex md:items-center md:justify-between mb-8">
data:

event:data
data:<div className="flex-1 min-w-0">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<a href="https://readdy.ai/home/<USER>/366da360-7d43-403a-9e4a-f7a792963011" data-readdy="true" className="mr-2 text-gray-500 hover:text-gray-700 cursor-pointer">
data:

event:data
data:<i className="fas fa-arrow-left"></i>
data:

event:data
data:</a>
data:

event:data
data:<h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
data:

event:data
data:Reports Dashboard
data:

event:data
data:</h1>
data:

event:data
data:</div>
data:

event:data
data:<p className="mt-1 text-sm text-gray-500">
data:

event:data
data:Thursday, May 1, 2025
data:

event:data
data:</p>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex md:mt-0 md:ml-4">
data:

event:data
data:<button
data:

event:data
data:type="button"
data:

event:data
data:className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button"
data:

event:data
data:onClick={() => {}}
data:

event:data
data:>
data:

event:data
data:<i className="fas fa-download mr-2"></i>
data:

event:data
data:Export Reports
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:type="button"
data:

event:data
data:className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whit

event:data
data:espace-nowrap !rounded-button"
data:

event:data
data:onClick={() => setIsGenerateModalOpen(true)}
data:

event:data
data:>
data:

event:data
data:<i className="fas fa-plus mr-2"></i>
data:

event:data
data:Generate New Report
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Reports Control Panel */}
data:

event:data
data:<div className="bg-white shadow rounded-lg mb-8">
data:

event:data
data:<div className="p-6">
data:

event:data
data:<h2 className="text-lg font-medium text-gray-900 mb-4">Filter Reports</h2>
data:

event:data
data:<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
data:

event:data
data:<div>
data:

event:data
data:<label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
data:

event:data
data:<input
data:

event:data
data:type="date"
data:

event:data
data:id="start-date"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
data:

event:data
data:value={startDate}
data:

event:data
data:onChange={(e) => setStartDate(e.target.value)}
data:

event:data
data:/>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
data:

event:data
data:<input
data:

event:data
data:type="date"
data:

event:data
data:id="end-date"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
data:

event:data
data:value={endDate}
data:

event:data
data:onChange={(e) => setEndDate(e.target.value)}
data:

event:data
data:/>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label htmlFor="client" className="block text-sm font-medium text-gray-700 mb-1">Client</label>
data:

event:data
data:<div className="relative">
data:

event:data
data:<select
data:

event:data
data:id="client"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10"
data:

event:data
data:value={selectedClient}
data:

event:data
data:onChange={(e) => setSelectedClient(e.target.value)}
data:

event:data
data:>
data:

event:data
data:<option value="">All Clients</option>
data:

event:data
data:<option value="morgan-stanley">Morgan Stanley</option>
data:

event:data
data:<option value="goldman-sachs">Goldman Sachs</option>
data:

event:data
data:<option value="jp-morgan">JP Morgan</option>
data:

event:data
data:<option value="blackrock">Blackrock</option>
data:

event:data
data:<option value="bank-of-america">Bank of America</option>
data:

event:data
data:</select>
data:

event:data
data:<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
data:

event:data
data:<i className="fas fa-chevron-down text-xs"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label htmlFor="report-type" className="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
data:

event:data
data:<div className="relative">
data:

event:data
data:<select
data:

event:data
data:id="report-type"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10"
data:

event:data
data:value={activeReportType}
data:

event:data
data:onChange={(e) => setActiveReportType(e.target.value)}
data:

event:data
data:>
data:

event:data
data:<option value="all">All Reports</option>
data:

event:data
data:<option value="performance">Performance Reports</option>
data:

event:data
data:<option value="client">Client Reports</option>
data:

event:data
data:<option value="investment">Investment Reports</option>
data:

event:data
data:<option value="compliance">Compliance Reports</option>
data:

event:data
data:<option value="regulatory">Regulatory Reports</option>
data:

event:data
data:</select>
data:

event:data
data:<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
data:

event:data
data:<i className="fas fa-chevron-down text-xs"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex justify-end">
data:

event:data
data:<button
data:

event:data
data:type="button"
data:

event:data
data:className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button"
data:

event:data
data:>
data:

event:data
data:<i className="fas fa-sync-alt mr-2"></i>
data:

event:data
data:Reset Filters
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:type="button"
data:

event:data
data:className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whit

event:data
data:espace-nowrap !rounded-button"
data:

event:data
data:>
data:

event:data
data:<i className="fas fa-search mr-2"></i>
data:

event:data
data:Apply Filters
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Report Categories */}
data:

event:data
data:<div className="mb-8">
data:

event:data
data:<h2 className="text-lg font-medium text-gray-900 mb-4">Report Categories</h2>
data:

event:data
data:<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
data:

event:data
data:{/* Performance Reports */}
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-6">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
data:

event:data
data:<i className="fas fa-chart-line text-blue-600 text-xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<h3 className="text-lg font-medium text-gray-900">Performance Reports</h3>
data:

event:data
data:<div className="mt-1 flex items-baseline">
data:

event:data
data:<p className="text-2xl font-semibold text-gray-900">35</p>
data:

event:data
data:<p className="ml-2 text-sm text-gray-500">reports</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 text-sm text-gray-500">
data:

event:data
data:Last generated: Today at 10:30 AM
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex space-x-2">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer whitespace-nowr

event:data
data:ap !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> View
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowr

event:data
data:ap !rounded-button">
data:

event:data
data:<i className="fas fa-plus mr-1"></i> New
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Client Reports */}
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-6">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 bg-green-100 rounded-md p-3">
data:

event:data
data:<i className="fas fa-user-tie text-green-600 text-xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<h3 className="text-lg font-medium text-gray-900">Client Reports</h3>
data:

event:data
data:<div className="mt-1 flex items-baseline">
data:

event:data
data:<p className="text-2xl font-semibold text-gray-900">30</p>
data:

event:data
data:<p className="ml-2 text-sm text-gray-500">reports</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 text-sm text-gray-500">
data:

event:data
data:Last generated: Yesterday at 4:15 PM
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex space-x-2">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 cursor-pointer whitespace-

event:data
data:nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> View
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowr

event:data
data:ap !rounded-button">
data:

event:data
data:<i className="fas fa-plus mr-1"></i> New
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Investment Reports */}
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-6">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 bg-purple-100 rounded-md p-3">
data:

event:data
data:<i className="fas fa-briefcase text-purple-600 text-xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<h3 className="text-lg font-medium text-gray-900">Investment Reports</h3>
data:

event:data
data:<div className="mt-1 flex items-baseline">
data:

event:data
data:<p className="text-2xl font-semibold text-gray-900">20</p>
data:

event:data
data:<p className="ml-2 text-sm text-gray-500">reports</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 text-sm text-gray-500">
data:

event:data
data:Last generated: Apr 29, 2025 at 2:00 PM
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex space-x-2">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 cursor-pointer whitesp

event:data
data:ace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> View
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowr

event:data
data:ap !rounded-button">
data:

event:data
data:<i className="fas fa-plus mr-1"></i> New
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Compliance Reports */}
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-6">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 bg-yellow-100 rounded-md p-3">
data:

event:data
data:<i className="fas fa-shield-alt text-yellow-600 text-xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<h3 className="text-lg font-medium text-gray-900">Compliance Reports</h3>
data:

event:data
data:<div className="mt-1 flex items-baseline">
data:

event:data
data:<p className="text-2xl font-semibold text-gray-900">15</p>
data:

event:data
data:<p className="ml-2 text-sm text-gray-500">reports</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 text-sm text-gray-500">
data:

event:data
data:Last generated: Apr 28, 2025 at 11:30 AM
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex space-x-2">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 cursor-pointer whitesp

event:data
data:ace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> View
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowr

event:data
data:ap !rounded-button">
data:

event:data
data:<i className="fas fa-plus mr-1"></i> New
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-6">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 bg-red-100 rounded-md p-3">
data:

event:data
data:<i className="fas fa-balance-scale text-red-600 text-xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<h3 className="text-lg font-medium text-gray-900">Regulatory Reports</h3>
data:

event:data
data:<div className="mt-1 flex items-baseline">
data:

event:data
data:<p className="text-2xl font-semibold text-gray-900">12</p>
data:

event:data
data:<p className="ml-2 text-sm text-gray-500">reports</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 text-sm text-gray-500">
data:

event:data
data:Last generated: Apr 27, 2025 at 9:15 AM
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 flex space-x-2">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 cursor-pointer whitespace-nowrap !

event:data
data:rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> View
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowr

event:data
data:ap !rounded-button">
data:

event:data
data:<i className="fas fa-plus mr-1"></i> New
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Analytics Overview */}
data:

event:data
data:<div className="mb-8">
data:

event:data
data:<h2 className="text-lg font-medium text-gray-900 mb-4">Reports Analytics</h2>
data:

event:data
data:<div className="bg-white shadow rounded-lg">
data:

event:data
data:<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-6">
data:

event:data
data:<div className="h-64">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-500 mb-2">Report Generation Trends</h3>
data:

event:data
data:<div id="performance-trend-chart" className="w-full h-56"></div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-64">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-500 mb-2">Report Type Distribution</h3>
data:

event:data
data:<div id="report-distribution-chart" className="w-full h-56"></div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{/* Recent Reports */}
data:

event:data
data:<div className="mb-8">
data:

event:data
data:<div className="flex justify-between items-center mb-4">
data:

event:data
data:<h2 className="text-lg font-medium text-gray-900">Recent Reports</h2>
data:

event:data
data:<div className="flex items-center space-x-2">
data:

event:data
data:<button
data:

event:data
data:onClick={() => setViewMode('table')}
data:

event:data
data:className={`p-2 rounded-md ${viewMode === 'table' ? 'bg-gray-200 text-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'} cursor-pointer whitespace-nowrap !rounded-button`}
data:

event:data
data:>
data:

event:data
data:<i className="fas fa-list"></i>
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:onClick={() => setViewMode('grid')}
data:

event:data
data:className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-gray-200 text-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'} cursor-pointer whitespace-nowrap !rounded-button`}
data:

event:data
data:>
data:

event:data
data:<i className="fas fa-th-large"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:{viewMode === 'table' ? (
data:

event:data
data:<div className="bg-white shadow overflow-hidden sm:rounded-lg">
data:

event:data
data:<div className="overflow-x-auto">
data:

event:data
data:<table className="min-w-full divide-y divide-gray-200">
data:

event:data
data:<thead className="bg-gray-50">
data:

event:data
data:<tr>
data:

event:data
data:<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
data:

event:data
data:Report Name
data:

event:data
data:</th>
data:

event:data
data:<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
data:

event:data
data:Type
data:

event:data
data:</th>
data:

event:data
data:<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
data:

event:data
data:Generated Date
data:

event:data
data:</th>
data:

event:data
data:<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
data:

event:data
data:Status
data:

event:data
data:</th>
data:

event:data
data:<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
data:

event:data
data:Size
data:

event:data
data:</th>
data:

event:data
data:<th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
data:

event:data
data:Actions
data:

event:data
data:</th>
data:

event:data
data:</tr>
data:

event:data
data:</thead>
data:

event:data
data:<tbody className="bg-white divide-y divide-gray-200">
data:

event:data
data:<tr>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-pdf text-blue-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<div className="text-sm font-medium text-gray-900">Q1 Performance Summary</div>
data:

event:data
data:<div className="text-sm text-gray-500">Morgan Stanley</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
data:

event:data
data:Performance
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:May 1, 2025
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Completed
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:4.2 MB
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
data:

event:data
data:<div className="flex justify-end space-x-2">
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-share-alt"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-ellipsis-v"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-green-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-excel text-green-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<div className="text-sm font-medium text-gray-900">Client Portfolio Analysis</div>
data:

event:data
data:<div className="text-sm text-gray-500">Goldman Sachs</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Client
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:Apr 30, 2025
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Completed
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:3.8 MB
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
data:

event:data
data:<div className="flex justify-end space-x-2">
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-share-alt"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-ellipsis-v"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-alt text-purple-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<div className="text-sm font-medium text-gray-900">Investment Strategy Report</div>
data:

event:data
data:<div className="text-sm text-gray-500">JP Morgan</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
data:

event:data
data:Investment
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:Apr 29, 2025
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Completed
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:5.1 MB
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
data:

event:data
data:<div className="flex justify-end space-x-2">
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-share-alt"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-ellipsis-v"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-yellow-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-pdf text-yellow-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<div className="text-sm font-medium text-gray-900">Regulatory Compliance Review</div>
data:

event:data
data:<div className="text-sm text-gray-500">Blackrock</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
data:

event:data
data:Compliance
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:Apr 28, 2025
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Completed
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:6.3 MB
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
data:

event:data
data:<div className="flex justify-end space-x-2">
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-share-alt"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-ellipsis-v"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-csv text-blue-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-4">
data:

event:data
data:<div className="text-sm font-medium text-gray-900">Monthly Performance Metrics</div>
data:

event:data
data:<div className="text-sm text-gray-500">Bank of America</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
data:

event:data
data:Performance
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:Apr 27, 2025
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap">
data:

event:data
data:<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Completed
data:

event:data
data:</span>
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
data:

event:data
data:2.7 MB
data:

event:data
data:</td>
data:

event:data
data:<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
data:

event:data
data:<div className="flex justify-end space-x-2">
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-eye"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-share-alt"></i>
data:

event:data
data:</button>
data:

event:data
data:<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-ellipsis-v"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:</tr>
data:

event:data
data:</tbody>
data:

event:data
data:</table>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
data:

event:data
data:<div className="flex-1 flex justify-between sm:hidden">
data:

event:data
data:<button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:Previous
data:

event:data
data:</button>
data:

event:data
data:<button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:Next
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:<div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
data:

event:data
data:<div>
data:

event:data
data:<p className="text-sm text-gray-700">
data:

event:data
data:Showing <span className="font-medium">1</span> to <span className="font-medium">5</span> of <span className="font-medium">25</span> results
data:

event:data
data:</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
data:

event:data
data:<button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<span className="sr-only">Previous</span>
data:

event:data
data:<i className="fas fa-chevron-left text-xs"></i>
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:className="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600 cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:onClick={() => setCurrentPage(1)}
data:

event:data
data:>
data:

event:data
data:1
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:onClick={() => setCurrentPage(2)}
data:

event:data
data:>
data:

event:data
data:2
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:onClick={() => setCurrentPage(3)}
data:

event:data
data:>
data:

event:data
data:3
data:

event:data
data:</button>
data:

event:data
data:<button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
data:

event:data
data:<span className="sr-only">Next</span>
data:

event:data
data:<i className="fas fa-chevron-right text-xs"></i>
data:

event:data
data:</button>
data:

event:data
data:</nav>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:) : (
data:

event:data
data:<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
data:

event:data
data:{/* Grid view items */}
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-4 border-b border-gray-200">
data:

event:data
data:<div className="flex justify-between items-start">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-pdf text-blue-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-3">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-900">Q1 Performance Summary</h3>
data:

event:data
data:<p className="text-xs text-gray-500">Morgan Stanley</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
data:

event:data
data:Performance
data:

event:data
data:</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="p-4">
data:

event:data
data:<div className="flex justify-between text-sm text-gray-500 mb-4">
data:

event:data
data:<div>Generated: May 1, 2025</div>
data:

event:data
data:<div>4.2 MB</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
data:

event:data
data:<i className="fas fa-file-pdf text-gray-400 text-4xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex justify-between">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> Preview
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespac

event:data
data:e-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download mr-1"></i> Download
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-4 border-b border-gray-200">
data:

event:data
data:<div className="flex justify-between items-start">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-green-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-excel text-green-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-3">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-900">Client Portfolio Analysis</h3>
data:

event:data
data:<p className="text-xs text-gray-500">Goldman Sachs</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Client
data:

event:data
data:</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="p-4">
data:

event:data
data:<div className="flex justify-between text-sm text-gray-500 mb-4">
data:

event:data
data:<div>Generated: Apr 30, 2025</div>
data:

event:data
data:<div>3.8 MB</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
data:

event:data
data:<i className="fas fa-file-excel text-gray-400 text-4xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex justify-between">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> Preview
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespac

event:data
data:e-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download mr-1"></i> Download
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-4 border-b border-gray-200">
data:

event:data
data:<div className="flex justify-between items-start">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-alt text-purple-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-3">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-900">Investment Strategy Report</h3>
data:

event:data
data:<p className="text-xs text-gray-500">JP Morgan</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
data:

event:data
data:Investment
data:

event:data
data:</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="p-4">
data:

event:data
data:<div className="flex justify-between text-sm text-gray-500 mb-4">
data:

event:data
data:<div>Generated: Apr 29, 2025</div>
data:

event:data
data:<div>5.1 MB</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
data:

event:data
data:<i className="fas fa-file-alt text-gray-400 text-4xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex justify-between">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> Preview
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespac

event:data
data:e-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download mr-1"></i> Download
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-4 border-b border-gray-200">
data:

event:data
data:<div className="flex justify-between items-start">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-yellow-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-pdf text-yellow-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-3">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-900">Regulatory Compliance Review</h3>
data:

event:data
data:<p className="text-xs text-gray-500">Blackrock</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
data:

event:data
data:Compliance
data:

event:data
data:</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="p-4">
data:

event:data
data:<div className="flex justify-between text-sm text-gray-500 mb-4">
data:

event:data
data:<div>Generated: Apr 28, 2025</div>
data:

event:data
data:<div>6.3 MB</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
data:

event:data
data:<i className="fas fa-file-pdf text-gray-400 text-4xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex justify-between">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> Preview
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespac

event:data
data:e-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download mr-1"></i> Download
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-4 border-b border-gray-200">
data:

event:data
data:<div className="flex justify-between items-start">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-csv text-blue-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-3">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-900">Monthly Performance Metrics</h3>
data:

event:data
data:<p className="text-xs text-gray-500">Bank of America</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
data:

event:data
data:Performance
data:

event:data
data:</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="p-4">
data:

event:data
data:<div className="flex justify-between text-sm text-gray-500 mb-4">
data:

event:data
data:<div>Generated: Apr 27, 2025</div>
data:

event:data
data:<div>2.7 MB</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
data:

event:data
data:<i className="fas fa-file-csv text-gray-400 text-4xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex justify-between">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> Preview
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespac

event:data
data:e-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download mr-1"></i> Download
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
data:

event:data
data:<div className="p-4 border-b border-gray-200">
data:

event:data
data:<div className="flex justify-between items-start">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<div className="flex-shrink-0 h-10 w-10 bg-green-100 rounded-md flex items-center justify-center">
data:

event:data
data:<i className="fas fa-file-excel text-green-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="ml-3">
data:

event:data
data:<h3 className="text-sm font-medium text-gray-900">Client Acquisition Report</h3>
data:

event:data
data:<p className="text-xs text-gray-500">Citigroup</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
data:

event:data
data:Client
data:

event:data
data:</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="p-4">
data:

event:data
data:<div className="flex justify-between text-sm text-gray-500 mb-4">
data:

event:data
data:<div>Generated: Apr 26, 2025</div>
data:

event:data
data:<div>3.1 MB</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
data:

event:data
data:<i className="fas fa-file-excel text-gray-400 text-4xl"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex justify-between">
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap

event:data
data: !rounded-button">
data:

event:data
data:<i className="fas fa-eye mr-1"></i> Preview
data:

event:data
data:</button>
data:

event:data
data:<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespac

event:data
data:e-nowrap !rounded-button">
data:

event:data
data:<i className="fas fa-download mr-1"></i> Download
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:)}
data:

event:data
data:</div>
data:

event:data
data:</main>
data:

event:data
data:{/* Footer */}
data:

event:data
data:<footer className="bg-white border-t border-gray-200">
data:

event:data
data:<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
data:

event:data
data:<div className="md:flex md:items-center md:justify-between">
data:

event:data
data:<div className="flex space-x-6 md:order-2">
data:

event:data
data:<a href="#" className="text-gray-400 hover:text-gray-500">
data:

event:data
data:<span className="sr-only">Help Center</span>
data:

event:data
data:<i className="fas fa-question-circle"></i>
data:

event:data
data:</a>
data:

event:data
data:<a href="#" className="text-gray-400 hover:text-gray-500">
data:

event:data
data:<span className="sr-only">Settings</span>
data:

event:data
data:<i className="fas fa-cog"></i>
data:

event:data
data:</a>
data:

event:data
data:<a href="#" className="text-gray-400 hover:text-gray-500">
data:

event:data
data:<span className="sr-only">Support</span>
data:

event:data
data:<i className="fas fa-headset"></i>
data:

event:data
data:</a>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-8 md:mt-0 md:order-1 flex flex-col sm:flex-row sm:items-center">
data:

event:data
data:<div className="flex items-center text-sm text-gray-500">
data:

event:data
data:<i className="fas fa-circle text-green-500 mr-1 text-xs"></i>
data:

event:data
data:System Status: Online
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-2 sm:mt-0 sm:ml-4 text-sm text-gray-500">
data:

event:data
data:Last synced: Today at 12:45 PM
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-4 border-t border-gray-200 pt-4">
data:

event:data
data:<p className="text-sm text-gray-500">&copy; 2025 Dashing CRM. All rights reserved.</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</footer>
data:

event:data
data:{/* Generate New Report Modal */}
data:

event:data
data:{isGenerateModalOpen && (
data:

event:data
data:<div className="fixed z-10 inset-0 overflow-y-auto">
data:

event:data
data:<div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
data:

event:data
data:<div className="fixed inset-0 transition-opacity" aria-hidden="true">
data:

event:data
data:<div className="absolute inset-0 bg-gray-500 opacity-75"></div>
data:

event:data
data:</div>
data:

event:data
data:<span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
data:

event:data
data:<div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
data:

event:data
data:<div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
data:

event:data
data:<div className="sm:flex sm:items-start">
data:

event:data
data:<div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
data:

event:data
data:<i className="fas fa-file-plus text-indigo-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
data:

event:data
data:<h3 className="text-lg leading-6 font-medium text-gray-900">
data:

event:data
data:Generate New Report
data:

event:data
data:</h3>
data:

event:data
data:<div className="mt-4">
data:

event:data
data:<div className="mb-4">
data:

event:data
data:<label htmlFor="report-template" className="block text-sm font-medium text-gray-700 mb-1">Report Template</label>
data:

event:data
data:<div className="relative">
data:

event:data
data:<select
data:

event:data
data:id="report-template"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10"
data:

event:data
data:>
data:

event:data
data:<option value="">Select a template</option>
data:

event:data
data:<option value="performance-summary">Performance Summary</option>
data:

event:data
data:<option value="client-portfolio">Client Portfolio Analysis</option>
data:

event:data
data:<option value="investment-strategy">Investment Strategy</option>
data:

event:data
data:<option value="compliance-review">Compliance Review</option>
data:

event:data
data:<option value="regulatory-filing">Regulatory Filing</option>
data:

event:data
data:<option value="regulatory-disclosure">Regulatory Disclosure</option>
data:

event:data
data:<option value="custom">Custom Report</option>
data:

event:data
data:</select>
data:

event:data
data:<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
data:

event:data
data:<i className="fas fa-chevron-down text-xs"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mb-4">
data:

event:data
data:<label htmlFor="report-name" className="block text-sm font-medium text-gray-700 mb-1">Report Name</label>
data:

event:data
data:<input
data:

event:data
data:type="text"
data:

event:data
data:id="report-name"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
data:

event:data
data:placeholder="Enter report name"
data:

event:data
data:/>
data:

event:data
data:</div>
data:

event:data
data:<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
data:

event:data
data:<div>
data:

event:data
data:<label htmlFor="modal-start-date" className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
data:

event:data
data:<input
data:

event:data
data:type="date"
data:

event:data
data:id="modal-start-date"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
data:

event:data
data:/>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label htmlFor="modal-end-date" className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
data:

event:data
data:<input
data:

event:data
data:type="date"
data:

event:data
data:id="modal-end-date"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
data:

event:data
data:/>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mb-4">
data:

event:data
data:<label htmlFor="modal-client" className="block text-sm font-medium text-gray-700 mb-1">Client</label>
data:

event:data
data:<div className="relative">
data:

event:data
data:<select
data:

event:data
data:id="modal-client"
data:

event:data
data:className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10"
data:

event:data
data:>
data:

event:data
data:<option value="">Select a client</option>
data:

event:data
data:<option value="morgan-stanley">Morgan Stanley</option>
data:

event:data
data:<option value="goldman-sachs">Goldman Sachs</option>
data:

event:data
data:<option value="jp-morgan">JP Morgan</option>
data:

event:data
data:<option value="blackrock">Blackrock</option>
data:

event:data
data:<option value="bank-of-america">Bank of America</option>
data:

event:data
data:</select>
data:

event:data
data:<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
data:

event:data
data:<i className="fas fa-chevron-down text-xs"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="mb-4">
data:

event:data
data:<label className="block text-sm font-medium text-gray-700 mb-1">Format</label>
data:

event:data
data:<div className="flex space-x-4">
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<input
data:

event:data
data:id="format-pdf"
data:

event:data
data:name="format"
data:

event:data
data:type="radio"
data:

event:data
data:className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
data:

event:data
data:checked={selectedFormat === 'pdf'}
data:

event:data
data:onChange={() => setSelectedFormat('pdf')}
data:

event:data
data:/>
data:

event:data
data:<label htmlFor="format-pdf" className="ml-2 block text-sm text-gray-700">
data:

event:data
data:PDF
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<input
data:

event:data
data:id="format-excel"
data:

event:data
data:name="format"
data:

event:data
data:type="radio"
data:

event:data
data:className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
data:

event:data
data:checked={selectedFormat === 'excel'}
data:

event:data
data:onChange={() => setSelectedFormat('excel')}
data:

event:data
data:/>
data:

event:data
data:<label htmlFor="format-excel" className="ml-2 block text-sm text-gray-700">
data:

event:data
data:Excel
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<input
data:

event:data
data:id="format-csv"
data:

event:data
data:name="format"
data:

event:data
data:type="radio"
data:

event:data
data:className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
data:

event:data
data:checked={selectedFormat === 'csv'}
data:

event:data
data:onChange={() => setSelectedFormat('csv')}
data:

event:data
data:/>
data:

event:data
data:<label htmlFor="format-csv" className="ml-2 block text-sm text-gray-700">
data:

event:data
data:CSV
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:<div className="flex items-center">
data:

event:data
data:<input
data:

event:data
data:id="format-dashboard"
data:

event:data
data:name="format"
data:

event:data
data:type="radio"
data:

event:data
data:className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
data:

event:data
data:checked={selectedFormat === 'dashboard'}
data:

event:data
data:onChange={() => setSelectedFormat('dashboard')}
data:

event:data
data:/>
data:

event:data
data:<label htmlFor="format-dashboard" className="ml-2 block text-sm text-gray-700">
data:

event:data
data:Dashboard
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
data:

event:data
data:<button
data:

event:data
data:type="button"
data:

event:data
data:className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-

event:data
data:auto sm:text-sm cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:>
data:

event:data
data:Generate Report
data:

event:data
data:</button>
data:

event:data
data:<button
data:

event:data
data:type="button"
data:

event:data
data:className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 

event:data
data:sm:w-auto sm:text-sm cursor-pointer whitespace-nowrap !rounded-button"
data:

event:data
data:onClick={() => setIsGenerateModalOpen(false)}
data:

event:data
data:>
data:

event:data
data:Cancel
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:)}
data:

event:data
data:</div>
data:

event:data
data:);
data:

event:data
data:};
data:

event:data
data:export default App

event:endMsg
data:
data:

event:endMsg
data:
data:

event:endMsg
data:I've added a new regulatory reports category card with a distinct red color scheme and balance scale icon to represent regulatory compliance. I've also updated the report type filters and templates in the modal to include regulatory report options. Would y

event:endMsg
data:ou like me to add any specific regulatory report types or make any adjustments to the design?

event:response
data:{"record_id":2405835,"request_id":"a0564d63-3277-453c-9355-e567738cd9f3","error":null}

