curl 'https://readdy.ai/api/page_gen/generate' \
  -H 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8' \
  -H 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDIyMkFBQSIsImtpZCI6Imluc18ycWtRbmFmNnRNcW9DQVZRMjRsWEwzRzRDcnQiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************.guIvjp_NArpsDCha4onfoLupC_j1vFAKSVi7kzY_zZ6UvyjQq7haqw9jUUG4wU-KI2DwNs1bx8YjS43vOlvWfz7V6JI3uUslR2Jw_Tu5wiNJl5lhEqx5bw4CVd8MSi1JaHs_5Qs7C5vj71Dclzi8quEmdphGOBmN59sNbErCuv4IQkkKv0Vb0iCOMlp2DJcIKKIVpS26LgeVfGf3x5Wix9oWpPGtmt0pzKklRnO0mBuhAdvJBPwYOqicHd_cobWMrW4EtoEu4W5FWlHB3jRzQZMEYUxdmqiAOBXzudwcA2Go5YzPTVgynkG8INcLMqCzwEwzBMzBXATgKNL4RbpDRg' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b 'featurebase-anon-id=01968c1b-3e71-7ecd-978f-f44b51da35e5; _ga=GA1.1.923355252.1746887781; _ga_MHDZ46BL5J=GS2.1.s1750259423$o7$g0$t1750259427$j56$l0$h0; g_state={"i_p":1750266633837,"i_l":1}; __client_uat=1750259449; __client_uat_ABo4F7rL=1750259449; clerk_active_context=sess_2ygc2T7NLl6KCZDKgdzAaOzUmPH:; featurebase-access-67554c8c97bd6ab5bb7e9290=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODUyZDZmZGM1MGI2NTI0MTRmZWZiMTAiLCJ1dCI6ImN1c3RvbWVyIiwiaWF0IjoxNzUwMjU5NDU1LCJleHAiOjE3NTI4NTE0NTUsIm5iZiI6MTc1MDI1OTMzNSwiaXNzIjoicHJvZHVjdGlvbjpiYWNrZW5kIiwiYXVkIjoic2VzcyIsInR0IjoiYWNjZXNzIiwidHYiOjAsIm9pZCI6IjY3NTU0YzhjOTdiZDZhYjViYjdlOTI5MCIsInNyYyI6InNzbyJ9.l2328WzMYJ0zo9S0aGWvAmXLLaWdxz0mWRz9Dm_ZdlA; __session=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __session_ABo4F7rL=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; ph_phc_V7JMHB0fVJGRu8UHyrsj6pSL1BS76P5zD8qCi7lrTTV_posthog=%7B%22distinct_id%22%3A%22user_2ygc2SD4vXKK6LzdPjiWB1SfTnM%22%2C%22%24sesid%22%3A%5B1750259517533%2C%2201978397-3bed-7c2a-80b2-ad14b6c3339a%22%2C1750259416045%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Freaddy.ai%2Fsignin%22%2C%22u%22%3A%22https%3A%2F%2Freaddy.ai%2Fhome%22%7D%7D' \
  -H 'Origin: https://readdy.ai' \
  -H 'Referer: https://readdy.ai/home/<USER>/643f9592-e02e-42ec-9073-7f7604619055' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0' \
  -H 'accept: text/event-stream' \
  -H 'sec-ch-ua: "Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  --data-raw '{"language":"English","deviceType":"web","style":"light","desc":"**1. Page Layout Overview**\n\nThe page follows a clean, organized layout designed to showcase insurance products effectively on web browsers. The structure prioritizes readability and easy navigation.\n\n**2. Header Section**- A prominent header with the company logo aligned to the left\n\n- Main navigation menu with key sections\n\n- Search functionality in the top right corner\n\n- Clear call-to-action button for \"Get a Quote\" in a contrasting color\n\n**3. Hero Section**\n\n- Large banner section with a relevant insurance-themed background image\n\n- Bold headline highlighting main value proposition\n\n- Brief subheading explaining key benefits\n\n- Primary CTA button for immediate action\n\n**4. Product Grid**\n\n- Grid layout displaying different insurance products (3 columns)\n\n- Each product card contains:\n\n- Product icon or illustration\n\n- Product name (e.g., Home, Auto, Life Insurance)\n\n- Brief description (2-3 lines)\n\n- \"Learn More\" button- Consistent spacing and alignment between cards\n\n**5. Key Features**\n\n- Section highlighting 3-4 key benefits or features\n\n- Each feature includes:\n\n- Icon representation  \n\n- Feature title\n\n- Short description\n\n- Organized in a horizontal row with equal spacing\n\n**6. Call-to-Action Section**\n\n- Prominent section with contrasting background color\n\n- Compelling headline encouraging action- Secondary CTA button for consultation or quote\n\n- Contact information or support details\n\n**7. Footer**\n\n- Company information and links\n\n- Quick access to important pages\n\n- Contact details\n\n- Social media links\n\n- Legal information and privacy policy links**8. Responsive Design Elements**\n\n- Fluid grid system that adapts to different screen sizes\n\n- Mobile-friendly navigation- Scalable images and text\n \n- Appropriate spacing for touch interfaces","sessionKey":"643f9592-e02e-42ec-9073-7f7604619055","color":"","borderRadius":"medium","framework":"html","lib":"","messages":[{"type":"text","content":"create a simple page insurance products"}],"refImageConfig":{"layout":false,"color":false,"copywriting":false},"refPages":[],"seq":1}'