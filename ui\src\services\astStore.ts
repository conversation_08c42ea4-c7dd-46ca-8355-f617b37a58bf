import { UINode, ASTHistory, ASTVersion } from '../types/uiNode';

class ASTStore {
  private history: ASTHistory;

  constructor() {
    this.history = {
      currentVersion: '',
      versions: {}
    };
  }

  /**
   * Get the current AST
   */
  getCurrentAST(): UINode {
    return this.getCurrentVersion().node;
  }

  /**
   * Set the current AST
   */
  setCurrentAST(node: UINode) {
    const currentId = this.generateVersionId();
    const newVersion: ASTVersion = {
      id: currentId,
      timestamp: Date.now(),
      node,
      changes: []
    };

    this.history.versions[currentId] = newVersion;
    this.history.currentVersion = currentId;
  }

  /**
   * Initialize the AST store with a root node
   */
  initialize(rootNode: UINode) {
    const initialVersion: ASTVersion = {
      id: 'initial',
      timestamp: Date.now(),
      node: rootNode,
      changes: []
    };

    this.history = {
      currentVersion: 'initial',
      versions: {
        initial: initialVersion
      }
    };
  }

  /**
   * Find node by ID in the current AST
   */
  findNodeById(id: string): UINode | null {
    const traverse = (node: UINode): UINode | null => {
      if (node.id === id) return node;
      if (!node.children) return null;
      
      for (const child of node.children) {
        const found = traverse(child);
        if (found) return found;
      }
      
      return null;
    };

    return traverse(this.getCurrentAST());
  }

  /**
   * Add a child node to a parent node
   */
  addChild(parentId: string, child: UINode) {
    const currentId = this.generateVersionId();
    const prevVersion = this.getCurrentVersion();
    const newNode = { ...prevVersion.node };
    
    const addToParent = (node: UINode): boolean => {
      if (node.id === parentId) {
        node.children = node.children || [];
        node.children.push(child);
        return true;
      }
      if (!node.children) return false;
      
      return node.children.some(n => addToParent(n));
    };

    addToParent(newNode);

    const newVersion: ASTVersion = {
      id: currentId,
      timestamp: Date.now(),
      node: newNode,
      changes: [
        ...prevVersion.changes,
        {
          type: 'add',
          path: [parentId],
          node: child
        }
      ]
    };

    this.history.versions[currentId] = newVersion;
    this.history.currentVersion = currentId;
  }

  /**
   * Update node at specified path
   */
  updateNodeProps(nodeId: string, updates: Partial<UINode['props']>) {
    this.updateNode([nodeId], updates);
  }

  /**
   * Update node at specified path
   */
  updateNode(path: string[], updates: Partial<UINode['props']>) {
    const currentId = this.generateVersionId();
    const prevVersion = this.getCurrentVersion();
    
    const newVersion: ASTVersion = {
      id: currentId,
      timestamp: Date.now(),
      node: prevVersion.node,
      changes: [
        ...prevVersion.changes,
        {
          type: 'update',
          path,
          props: updates
        }
      ]
    };

    this.history.versions[currentId] = newVersion;
    this.history.currentVersion = currentId;
  }

  /**
   * Remove a node from the AST
   */
  removeNode(nodeId: string) {
    const currentId = this.generateVersionId();
    const prevVersion = this.getCurrentVersion();
    const newNode = { ...prevVersion.node };
    
    const removeFromParent = (node: UINode): boolean => {
      if (!node.children) return false;
      
      const index = node.children.findIndex(n => n.id === nodeId);
      if (index !== -1) {
        node.children.splice(index, 1);
        return true;
      }
      
      return node.children.some(n => removeFromParent(n));
    };

    removeFromParent(newNode);

    const newVersion: ASTVersion = {
      id: currentId,
      timestamp: Date.now(),
      node: newNode,
      changes: [
        ...prevVersion.changes,
        {
          type: 'delete',
          path: [nodeId]
        }
      ]
    };

    this.history.versions[currentId] = newVersion;
    this.history.currentVersion = currentId;
  }

  /**
   * Save current state as a snapshot
   */
  saveSnapshot(name: string) {
    const currentId = `snapshot-${name}-${Date.now()}`;
    const prevVersion = this.getCurrentVersion();
    
    const newVersion: ASTVersion = {
      id: currentId,
      timestamp: Date.now(),
      node: prevVersion.node,
      changes: []
    };

    this.history.versions[currentId] = newVersion;
    this.history.currentVersion = currentId;
  }

  /**
   * Get a specific snapshot
   */
  getSnapshot(id: string): ASTVersion | null {
    return this.history.versions[id] || null;
  }

  /**
   * List all available snapshots
   */
  listSnapshots(): string[] {
    return Object.keys(this.history.versions).filter(id => id.startsWith('snapshot-'));
  }

  /**
   * Restore a specific snapshot
   */
  restoreSnapshot(id: string): boolean {
    if (this.history.versions[id]) {
      this.history.currentVersion = id;
      return true;
    }
    return false;
  }

  /**
   * Get the current version from history
   */
  getCurrentVersion(): ASTVersion {
    return this.history.versions[this.history.currentVersion];
  }

  /**
   * Get AST history
   */
  getHistory(): ASTHistory {
    return this.history;
  }

  /**
   * Generate a unique version ID
   */
  private generateVersionId(): string {
    return `v${Date.now()}`;
  }
}

export const astStore = new ASTStore();
export default astStore;
