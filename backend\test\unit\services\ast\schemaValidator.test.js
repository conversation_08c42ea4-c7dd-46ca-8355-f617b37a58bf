// Import the schema validator
const schemaValidator = require('../../services/ast/schemaValidator');

// Mock the AJV instance
jest.mock('ajv');
const Ajv = require('ajv');

// Mock the addFormats function
jest.mock('ajv-formats', () => ({
  default: jest.fn()
}));

// Setup default mocks
beforeEach(() => {
  jest.clearAllMocks();
  
  // Mock AJV instance methods
  Ajv.mockImplementation(() => ({
    addSchema: jest.fn(),
    addKeyword: jest.fn(),
    compile: jest.fn(schema => (data) => ({
      valid: true,
      errors: []
    })),
    errorsText: jest.fn(() => 'Validation error')
  }));
});

describe('SchemaValidator', () => {
  describe('validateTree', () => {
    it('should validate a valid AST node', () => {
      const validNode = {
        id: 'test-node',
        type: 'div',
        props: {
          className: 'test-class',
          style: { color: 'red' }
        },
        children: []
      };
      
      const result = schemaValidator.validateTree(validNode);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    it('should invalidate a node with missing required fields', () => {
      const invalidNode = {
        // Missing id and type
        props: {},
        children: []
      };
      
      const result = schemaValidator.validateTree(invalidNode);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
      
      // Check that the error messages are helpful
      const errorMessages = result.errors.map(e => e.message).join(',');
      expect(errorMessages).toContain('must have required property \'id\'');
      expect(errorMessages).toContain('must have required property \'type\'');
    });
    
    it('should validate a complex tree structure', () => {
      const complexTree = {
        id: 'root',
        type: 'div',
        props: { className: 'container' },
        children: [
          {
            id: 'header',
            type: 'header',
            children: [
              {
                id: 'title',
                type: 'h1',
                content: 'Hello World'
              }
            ]
          },
          {
            id: 'content',
            type: 'div',
            props: { className: 'content' },
            children: [
              {
                id: 'paragraph',
                type: 'p',
                content: 'This is a test.'
              }
            ]
          }
        ]
      };
      
      const result = schemaValidator.validateTree(complexTree);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });
  
  describe('validatePatch', () => {
    it('should validate a valid patch operation', () => {
      const validPatch = [
        {
          op: 'add',
          path: '/children/0',
          value: { 
            id: 'new-node',
            type: 'div',
            children: []
          }
        }
      ];
      
      const result = schemaValidator.validatePatch(validPatch);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    it('should invalidate a patch with invalid operation', () => {
      const invalidPatch = [
        {
          op: 'invalid-op', // Invalid operation
          path: '/children/0',
          value: { type: 'div' }
        }
      ];
      
      const result = schemaValidator.validatePatch(invalidPatch);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
      
      const errorMessages = result.errors.map(e => e.message).join(',');
      expect(errorMessages).toContain('must be equal to one of the allowed values');
    });
    
    it('should validate a complex patch operation', () => {
      const complexPatch = [
        {
          op: 'add',
          path: '/children/0',
          value: {
            id: 'header',
            type: 'header',
            children: [
              {
                id: 'title',
                type: 'h1',
                content: 'New Title'
              }
            ]
          }
        },
        {
          op: 'replace',
          path: '/props/className',
          value: 'updated-class'
        },
        {
          op: 'remove',
          path: '/children/1'
        }
      ];
      
      const result = schemaValidator.validatePatch(complexPatch);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });
  
  describe('validateNode', () => {
    it('should validate a valid node', () => {
      const validNode = {
        id: 'test-node',
        type: 'button',
        props: {
          className: 'btn',
          onClick: 'handleClick',
          disabled: false
        },
        children: [
          { id: 'btn-text', type: 'text', content: 'Click me' }
        ]
      };
      
      const result = schemaValidator.validateNode(validNode);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    it('should invalidate a node with invalid properties', () => {
      const invalidNode = {
        id: 'test-node',
        type: 'button',
        props: {
          invalidProp: 'this is not allowed',
          onClick: 123 // Should be a string or function
        },
        children: 'not an array' // Should be an array
      };
      
      const result = schemaValidator.validateNode(invalidNode);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
      
      const errorMessages = JSON.stringify(result.errors, null, 2);
      
      // Check for specific validation errors
      expect(errorMessages).toContain('must be array');
      expect(errorMessages).toContain('must be string');
    });
  });
  
  describe('validatePatchOperation', () => {
    it('should validate a valid patch operation', () => {
      const validOp = {
        op: 'add',
        path: '/children/0',
        value: { id: 'new-node', type: 'div' }
      };
      
      const result = schemaValidator.validatePatchOperation(validOp);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    it('should invalidate a patch operation with missing required fields', () => {
      const invalidOp = {
        // Missing path and value
        op: 'add'
      };
      
      const result = schemaValidator.validatePatchOperation(invalidOp);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
      
      const errorMessages = result.errors.map(e => e.message).join(',');
      expect(errorMessages).toContain('must have required property \'path\'');
    });
    
    it('should validate operation-specific requirements', () => {
      // Test add operation requires 'value'
      const addOp = {
        op: 'add',
        path: '/children/0',
        // Missing value
      };
      
      let result = schemaValidator.validatePatchOperation(addOp);
      expect(result.valid).toBe(false);
      expect(JSON.stringify(result.errors)).toContain('must have required property \'value\'');
      
      // Test move operation requires 'from'
      const moveOp = {
        op: 'move',
        path: '/new/location',
        // Missing from
      };
      
      result = schemaValidator.validatePatchOperation(moveOp);
      expect(result.valid).toBe(false);
      expect(JSON.stringify(result.errors)).toContain('must have required property \'from\'');
    });
  });
});
