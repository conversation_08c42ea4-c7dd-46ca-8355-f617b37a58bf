/**
 * Utility functions for Google Analytics
 */

// Define the gtag function type
declare global {
  interface Window {
    gtag: (
      command: string,
      action: string,
      params?: {
        [key: string]: any;
      }
    ) => void;
    dataLayer: any[];
  }
}

/**
 * Track a page view
 * @param {string} path - The page path
 * @param {string} title - The page title
 */
export const trackPageView = (path: string, title: string): void => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-YM22J2X5HP', {
      page_path: path,
      page_title: title
    });
    console.log(`[Analytics] Tracked page view: ${title} (${path})`);
  }
};

/**
 * Track an event
 * @param {string} action - The event action
 * @param {object} params - The event parameters
 */
export const trackEvent = (
  action: string,
  params: { [key: string]: any } = {}
): void => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, params);
    console.log(`[Analytics] Tracked event: ${action}`, params);
  }
};

/**
 * Track a user login
 * @param {string} method - The login method (e.g., 'Google', 'Email')
 */
export const trackLogin = (method: string): void => {
  trackEvent('login', { method });
};

/**
 * Track a prototype creation
 * @param {string} id - The prototype ID
 * @param {string} title - The prototype title
 */
export const trackPrototypeCreation = (id: string, title: string): void => {
  trackEvent('create_prototype', { prototype_id: id, prototype_title: title });
};

/**
 * Track a prototype edit
 * @param {string} id - The prototype ID
 * @param {string} title - The prototype title
 */
export const trackPrototypeEdit = (id: string, title: string): void => {
  trackEvent('edit_prototype', { prototype_id: id, prototype_title: title });
};

/**
 * Track a prototype share
 * @param {string} id - The prototype ID
 * @param {string} method - The share method (e.g., 'link', 'email')
 */
export const trackPrototypeShare = (id: string, method: string): void => {
  trackEvent('share_prototype', { prototype_id: id, share_method: method });
};

/**
 * Track an upgrade click
 * @param {string} location - Where the upgrade was clicked (e.g., 'nav', 'pricing')
 * @param {string} plan - The plan being upgraded to
 */
export const trackUpgradeClick = (location: string, plan: string): void => {
  trackEvent('upgrade_click', { location, plan });
};

/**
 * Track a feature usage
 * @param {string} feature - The feature being used
 * @param {object} params - Additional parameters
 */
export const trackFeatureUsage = (
  feature: string,
  params: { [key: string]: any } = {}
): void => {
  trackEvent('use_feature', { feature, ...params });
};
