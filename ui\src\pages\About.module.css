.container {
  min-height: 100vh;
  background-color: #f3f4f6;
  transition: background-color 0.2s;
}

.header {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 80rem;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.main {
  max-width: 80rem;
  margin: 0 auto;
  padding: 1.5rem 1rem;
}

.content {
  padding: 1rem 0;
}

.card {
  background-color: white;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
}

.cardContent {
  padding: 1.25rem;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 1rem;
}

.cardText {
  font-size: 0.875rem;
  color: #6b7280;
}

.paragraph {
  margin-bottom: 1rem;
}

.button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  color: white;
  background-color: #2563eb;
  margin-top: 1.5rem;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #1d4ed8;
}

.button:focus {
  outline: none;
}

/* Dark mode styles */
.darkContainer {
  background-color: #111827;
}

.darkHeader {
  background-color: #1f2937;
}

.darkTitle {
  color: white;
}

.darkCard {
  background-color: #1f2937;
}

.darkCardTitle {
  color: white;
}

.darkCardText {
  color: #9ca3af;
}
