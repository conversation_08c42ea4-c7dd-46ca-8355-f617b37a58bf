.comparisonContainer {
  margin: 4rem 0;
}

.comparisonTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  text-align: center;
  margin-bottom: 2rem;
}

.tableWrapper {
  overflow-x: auto;
  margin: 0 -1rem;
  padding: 0 1rem;
}

.comparisonTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.featureHeader {
  text-align: left;
  padding: 1rem;
  font-weight: 600;
  color: #111827;
  border-bottom: 2px solid #e5e7eb;
  min-width: 200px;
}

.planHeader {
  text-align: center;
  padding: 1rem;
  font-weight: 600;
  color: #111827;
  border-bottom: 2px solid #e5e7eb;
  min-width: 120px;
}

.categoryRow {
  background-color: #f9fafb;
}

.categoryName {
  font-weight: 600;
  color: #111827;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.featureRow:nth-child(even) {
  background-color: #f9fafb;
}

.featureName {
  padding: 0.75rem 1rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
}

.featureDescription {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.featureValue {
  text-align: center;
  padding: 0.75rem 1rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
}

.checkmark {
  color: #10b981;
  font-weight: bold;
}

.crossmark {
  color: #ef4444;
}

@media (max-width: 768px) {
  .comparisonTable {
    font-size: 0.75rem;
  }
  
  .featureHeader,
  .planHeader,
  .featureName,
  .featureValue {
    padding: 0.5rem;
  }
}
