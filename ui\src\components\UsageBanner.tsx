import React from "react";
import { useNavigate } from "react-router-dom";

export interface UsageBannerProps {
  plan: "Free" | "Pay-as-you-go" | "Pro";
  prototypeCount: number;
  prototypeLimit: number;
}

export const UsageBanner: React.FC<UsageBannerProps> = ({
  plan,
  prototypeCount,
  prototypeLimit,
}) => {
  // Calculate prototypes left
  const prototypesLeft = Math.max(0, prototypeLimit - prototypeCount);

  // Don't show banner for Pro users with plenty of prototypes left
  if (plan !== "Free" && prototypesLeft > 5) return null;

  // Don't show banner for Free users with more than 1 prototype left
  if (plan === "Free" && prototypesLeft > 1) return null;

  const navigate = useNavigate();

  const handleUpgradeClick = () => {
    navigate('/pricing');
  };

  return (
    <div className="w-full flex justify-center mb-4">
      <div className="bg-blue-50 border border-blue-200 text-blue-800 px-4 py-2 rounded-lg shadow-sm flex items-center gap-2 text-sm font-medium cursor-pointer" onClick={handleUpgradeClick}>
        {plan === "Free" && prototypesLeft === 1 && (
          <>
            <span>Free plan: Last prototype remaining!</span>
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs ml-2">
              Last one!
            </span>
          </>
        )}
        {plan === "Free" && prototypesLeft === 0 && (
          <>
            <span>Free plan: No prototypes remaining</span>
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs ml-2 hover:bg-red-200">
              Upgrade to continue
            </span>
          </>
        )}
        {plan !== "Free" && (
          <>
            <span>Pro plan: {prototypesLeft} prototype{prototypesLeft !== 1 ? 's' : ''} remaining of {prototypeLimit}</span>
            {prototypesLeft <= 5 && (
              <span className={`${prototypesLeft <= 2 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'} px-2 py-1 rounded text-xs ml-2`}>
                {prototypesLeft <= 2 ? 'Almost out!' : 'Running low'}
              </span>
            )}
          </>
        )}
      </div>
    </div>
  );
};
