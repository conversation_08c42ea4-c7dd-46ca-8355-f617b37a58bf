const service = require('../services/llmServiceV3');

// Test prompt analysis
const testPrompts = [
  'change the header background color to red',
  'add a new menu item called Services', 
  'add a bar chart showing monthly sales',
  'change the title to Admin Panel',
  'redesign the entire layout'
];

const sampleHTML = `
<div id="app">
  <header class="bg-blue-600">
    <nav class="nav">
      <h1>Dashboard</h1>
      <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
      </ul>
    </nav>
  </header>
  <main>
    <div id="chart-container">Chart here</div>
  </main>
</div>
`;

async function runTests() {
  console.log('🧪 Testing Prompt Analysis:\n');
  
  for (let i = 0; i < testPrompts.length; i++) {
    const prompt = testPrompts[i];
    try {
      console.log(`${i+1}. "${prompt}"`);
      const result = await service.analyzePromptIntent(prompt, sampleHTML);
      console.log('   Strategy:', result.isTargeted ? 'FRAGMENT' : 'FULL');
      console.log('   Selector:', result.elementSelector || 'none');
      console.log('   Type:', result.changeType);
      console.log('   Confidence:', result.confidence);
      
      // Test fragment extraction if targeted
      if (result.isTargeted && result.elementSelector) {
        const fragment = service.extractFragment(sampleHTML, result.elementSelector);
        console.log('   Fragment:', fragment ? 'EXTRACTED' : 'FAILED');
        if (fragment) {
          console.log('   Fragment length:', fragment.length);
        }
      }
      console.log('');
    } catch (e) {
      console.error(`   Error:`, e.message);
      console.log('');
    }
  }
  
  console.log('🔍 Testing Fragment Extraction:\n');
  
  const selectors = ['header', '.nav', '#chart-container', 'h1'];
  selectors.forEach(selector => {
    const fragment = service.extractFragment(sampleHTML, selector);
    console.log(`Selector: ${selector}`);
    console.log(`Result: ${fragment ? 'SUCCESS' : 'FAILED'}`);
    if (fragment) {
      console.log(`Length: ${fragment.length}`);
      console.log(`Preview: ${fragment.substring(0, 50)}...`);
    }
    console.log('');
  });
}

runTests().catch(console.error);
