# Improved LLM Prompts for Production-Grade HTML

## Changes Made

### 1. Enhanced System Prompts
- **Before**: Basic requirements with simple instructions
- **After**: Comprehensive design system with specific standards

### 2. Code Quality Standards
- Semantic HTML5 with proper document structure
- Modern CSS with custom properties (CSS variables)
- Mobile-first responsive design
- Accessibility best practices (ARIA labels, semantic elements)
- Consistent naming conventions (BEM methodology)
- Proper meta tags and viewport configuration

### 3. Design System Implementation
- **Typography**: System fonts with proper hierarchy (2.5rem, 2rem, 1.5rem, 1.25rem, 1rem)
- **Colors**: Professional palette with primary (#2563eb), secondary (#10b981), neutral grays
- **Spacing**: Consistent 8px grid system (0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem)
- **Components**: Clean buttons, cards, forms with subtle shadows and proper states
- **Layout**: CSS Grid for page structure, Flexbox for components

### 4. Code Structure Requirements
- Properly formatted and indented HTML
- Organized CSS with logical grouping (reset, variables, layout, components)
- Minimal, efficient JavaScript for interactions
- Comments for complex sections
- No external dependencies unless absolutely necessary

### 5. Visual Excellence Standards
- Modern, clean aesthetic with generous whitespace
- Subtle animations and hover effects
- Professional color schemes and typography
- Consistent component styling
- Mobile-responsive design

## Expected Output Quality

The new prompts should generate HTML that matches the quality of:
- **Readdy.ai** - Clean, professional, well-structured
- **Figma/Framer** - Modern design patterns and components
- **Production websites** - Semantic, accessible, maintainable code

## Files Updated

1. `backend/services/llmServiceV3.js` - Main HTML generation prompts
2. `ui/src/pages/EditorPageV3.tsx` - Improved HTML formatting and display

## Testing

To test the improvements:
1. Create a new prototype with a simple prompt like "modern coffee shop landing page"
2. Check the generated HTML for:
   - Clean, properly indented code structure
   - Semantic HTML5 elements
   - Modern CSS with custom properties
   - Professional visual design
   - Responsive layout
   - Accessibility features

The output should now be significantly cleaner and more professional than before.
