const service = require('../services/llmServiceV3');

async function validate() {
  console.log('🎯 Final Validation of Edit Strategy\n');
  
  const tests = [
    { prompt: 'change the chart color', expect: 'FRAGMENT' },
    { prompt: 'change button color', expect: 'FRAGMENT' },
    { prompt: 'change the header background color to red', expect: 'FRAGMENT' },
    { prompt: 'add a new menu item', expect: 'FRAGMENT' },
    { prompt: 'add one more chart box', expect: 'FRAGMENT' },
    { prompt: 'redesign everything', expect: 'FULL' }
  ];
  
  let passed = 0;
  
  for (const test of tests) {
    try {
      const result = await service.analyzePromptIntent(test.prompt, '<div>test</div>');
      const strategy = result.isTargeted && result.elementSelector ? 'FRAGMENT' : 'FULL';
      const success = strategy === test.expect;
      
      console.log(`"${test.prompt}"`);
      console.log(`  Expected: ${test.expect}, Got: ${strategy} ${success ? '✅' : '❌'}`);
      
      if (success) passed++;
    } catch (e) {
      console.log(`  Error: ${e.message} ❌`);
    }
  }
  
  console.log(`\n📊 Results: ${passed}/${tests.length} passed`);
  console.log(passed === tests.length ? '🎉 All tests passed!' : '⚠️ Some tests failed');
}

validate();
