/**
 * Simple tests for the version service
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { versionService } from '../services/versionService';

// Mock fetch
const mockFetch = vi.fn();
(globalThis as any).fetch = mockFetch;

describe('Version Service - Simple Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should format relative time correctly', () => {
    // Test with a known time difference
    const now = new Date('2024-01-01T12:05:00Z');
    vi.setSystemTime(now);

    const fiveMinutesAgo = '2024-01-01T12:00:00Z';
    const result = versionService.formatRelativeTime(fiveMinutesAgo);
    
    expect(result).toBe('5 minutes ago');
    
    vi.useRealTimers();
  });

  test('should return correct operation icons', () => {
    expect(versionService.getOperationIcon('generate')).toBe('🤖');
    expect(versionService.getOperationIcon('edit')).toBe('✏️');
    expect(versionService.getOperationIcon('implement')).toBe('🔧');
    expect(versionService.getOperationIcon('unknown')).toBe('📝');
  });

  test('should return correct operation names', () => {
    expect(versionService.getOperationName('generate')).toBe('Generated');
    expect(versionService.getOperationName('edit')).toBe('Manual Edit');
    expect(versionService.getOperationName('implement')).toBe('Implementation');
    expect(versionService.getOperationName('unknown')).toBe('Modified');
  });

  test('should fetch version history successfully', async () => {
    const mockVersions = [
      {
        id: 1,
        version_number: 1,
        change_description: 'Initial version',
        operation_type: 'generate',
        created_at: '2024-01-01T10:00:00Z'
      }
    ];

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ versions: mockVersions })
    });

    const result = await versionService.getVersionHistory(123, 10, 0);

    expect(mockFetch).toHaveBeenCalledWith(
      '/api/llm/v3/versions/123?limit=10&offset=0',
      expect.objectContaining({
        method: 'GET',
        credentials: 'include'
      })
    );
    expect(result).toEqual(mockVersions);
  });

  test('should create version successfully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ versionId: 4 })
    });

    const result = await versionService.createVersion(
      123,
      '<div>Test content</div>',
      undefined,
      'Test change',
      'manual_edit'
    );

    expect(mockFetch).toHaveBeenCalledWith(
      '/api/llm/v3/versions/123',
      expect.objectContaining({
        method: 'POST',
        credentials: 'include'
      })
    );
    expect(result).toBe(4);
  });
});
