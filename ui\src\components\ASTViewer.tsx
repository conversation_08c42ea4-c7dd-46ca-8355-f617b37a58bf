import React from 'react';
import { useAst } from '../hooks/useAst';
import { UINode } from '../types/uiNode';

interface Props {
  prompt?: string;
}

export function ASTViewer({ prompt = 'Create a simple login form' }: Props) {
  const astApi = useAst();
  
  // Destructure the values we need
  const { 
    ast, 
    error, 
    loading, 
    generateFromPlan,
    addChild,
    updateNodeProps,
    removeNode 
  } = astApi;

  const handleGenerate = async () => {
    try {
      await generateFromPlan(prompt);
    } catch (err) {
      console.error('Generation failed:', err);
    }
  };

  const renderNode = (node: UINode) => {
    return (
      <div 
        key={node.id} 
        style={{ 
          border: '1px solid #ccc',
          margin: '4px',
          padding: '8px'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>
            <strong>{node.type}</strong> (ID: {node.id})
          </span>
          <button onClick={() => removeNode(node.id)}>Remove</button>
        </div>
        
        <div style={{ marginTop: '8px' }}>
          <strong>Props:</strong>
          <pre>{JSON.stringify(node.props, null, 2)}</pre>
        </div>

        {node.children && node.children.length > 0 && (
          <div style={{ marginLeft: '20px' }}>
            {node.children.map(child => renderNode(child))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return <div>Loading AST...</div>;
  }

  if (error) {
    return (
      <div style={{ color: 'red' }}>
        Error: {error}
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: '20px' }}>
        <button onClick={handleGenerate}>
          Generate AST from Prompt
        </button>
      </div>

      {ast ? (
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          {renderNode(ast)}
        </div>
      ) : (
        <div>No AST available. Click generate to create one.</div>
      )}
    </div>
  );
}
