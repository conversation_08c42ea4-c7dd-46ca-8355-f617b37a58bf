.toggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 9999px;
  background-color: #e5e7eb;
  transition: background-color 0.2s;
}

.toggleButton:hover {
  background-color: #d1d5db;
}

.darkToggleButton {
  background-color: #4b5563;
}

.darkToggleButton:hover {
  background-color: #374151;
}

.moonIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: #1f2937;
}

.sunIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: #fcd34d;
}
