import { Page, Locator } from '@playwright/test';
import { BasePage } from './base.page';

/**
 * Authentication Page Object Model
 * 
 * Handles all authentication-related interactions including:
 * - Google OAuth flow
 * - Session management
 * - Authentication state verification
 * - Test authentication bypass
 */
export class AuthPage extends BasePage {
  
  // Selectors
  private readonly signInButton = 'button:has-text("Sign In")';
  private readonly startFreeTrialButton = 'button:has-text("Start Free Trial")';
  private readonly userDropdown = '[data-testid="user-dropdown"]';
  private readonly logoutButton = 'button:has-text("Logout")';
  private readonly userEmail = '[data-testid="user-email"]';
  
  // Google OAuth selectors
  private readonly googleEmailInput = 'input[type="email"]';
  private readonly googlePasswordInput = 'input[type="password"]';
  private readonly googleNextButton = '#identifierNext';
  private readonly googlePasswordNext = '#passwordNext';
  
  constructor(page: Page) {
    super(page);
  }
  
  /**
   * Click the Sign In button to start authentication
   */
  async clickSignIn() {
    console.log('🔐 Clicking Sign In button...');
    await this.waitForElement(this.signInButton);
    await this.page.click(this.signInButton);
  }
  
  /**
   * Click the Start Free Trial button
   */
  async clickStartFreeTrial() {
    console.log('🆓 Clicking Start Free Trial button...');
    await this.waitForElement(this.startFreeTrialButton);
    await this.page.click(this.startFreeTrialButton);
  }
  
  /**
   * Handle Google OAuth authentication flow
   */
  async handleGoogleOAuth(email: string, password: string) {
    console.log('🔍 Handling Google OAuth flow...');
    
    try {
      // Wait for Google OAuth page to load
      await this.page.waitForURL('**/accounts.google.com/**', { timeout: 10000 });
      
      // Enter email
      await this.waitForElement(this.googleEmailInput);
      await this.page.fill(this.googleEmailInput, email);
      await this.page.click(this.googleNextButton);
      
      // Enter password
      await this.waitForElement(this.googlePasswordInput);
      await this.page.fill(this.googlePasswordInput, password);
      await this.page.click(this.googlePasswordNext);
      
      // Wait for redirect back to application
      await this.page.waitForURL('**/prototypes', { timeout: 30000 });
      
      console.log('✅ Google OAuth completed successfully');
      
    } catch (error) {
      console.error('❌ Google OAuth failed:', error);
      await this.takeScreenshot('google-oauth-failure');
      throw error;
    }
  }
  
  /**
   * Bypass authentication for testing purposes
   * This method sets up a test session directly
   */
  async bypassAuthForTesting(): Promise<boolean> {
    console.log('🔧 Attempting to bypass authentication for testing...');
    
    try {
      // Set test authentication cookies/localStorage
      await this.setLocalStorage('isAuthenticated', 'true');
      await this.setLocalStorage('testUser', JSON.stringify({
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User'
      }));
      
      // Set authentication cookie
      await this.page.context().addCookies([{
        name: 'isLoggedIn',
        value: 'true',
        domain: 'localhost',
        path: '/'
      }]);
      
      // Navigate to prototypes page to verify
      await this.page.goto('/prototypes');
      
      // Check if we're successfully authenticated
      const isAuth = await this.isAuthenticated();
      
      if (isAuth) {
        console.log('✅ Authentication bypass successful');
        return true;
      } else {
        console.log('❌ Authentication bypass failed');
        return false;
      }
      
    } catch (error) {
      console.error('❌ Authentication bypass error:', error);
      return false;
    }
  }
  
  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      // Method 1: Check for user dropdown/profile element
      const hasUserDropdown = await this.isElementVisible(this.userDropdown);
      if (hasUserDropdown) return true;
      
      // Method 2: Check current URL (authenticated users go to /prototypes)
      const currentUrl = await this.getCurrentUrl();
      if (currentUrl.includes('/prototypes')) return true;
      
      // Method 3: Check localStorage
      const authState = await this.getLocalStorage('isAuthenticated');
      if (authState === 'true') return true;
      
      // Method 4: Check for authentication cookie
      const cookies = await this.page.context().cookies();
      const authCookie = cookies.find(cookie => cookie.name === 'isLoggedIn');
      if (authCookie && authCookie.value === 'true') return true;
      
      return false;
      
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return false;
    }
  }
  
  /**
   * Get current user information
   */
  async getUserInfo(): Promise<{ email?: string; name?: string } | null> {
    try {
      // Try to get user info from localStorage first
      const testUser = await this.getLocalStorage('testUser');
      if (testUser) {
        return JSON.parse(testUser);
      }
      
      // Try to get from UI elements
      if (await this.isElementVisible(this.userEmail)) {
        const email = await this.getElementText(this.userEmail);
        return { email };
      }
      
      // Try to get from API call
      const userInfo = await this.page.evaluate(async () => {
        try {
          const response = await fetch('/api/auth/me', {
            credentials: 'include'
          });
          if (response.ok) {
            const data = await response.json();
            return data.user;
          }
        } catch (error) {
          console.error('Failed to fetch user info:', error);
        }
        return null;
      });
      
      return userInfo;
      
    } catch (error) {
      console.error('Error getting user info:', error);
      return null;
    }
  }
  
  /**
   * Wait for authentication to complete (for manual auth)
   */
  async waitForAuthentication(timeout: number = 60000) {
    console.log('⏳ Waiting for authentication to complete...');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await this.isAuthenticated()) {
        console.log('✅ Authentication detected');
        return;
      }
      
      await this.page.waitForTimeout(1000);
    }
    
    throw new Error('Authentication timeout - user did not complete authentication');
  }
  
  /**
   * Logout the current user
   */
  async logout() {
    console.log('🚪 Logging out...');
    
    try {
      // Method 1: Try UI logout
      if (await this.isElementVisible(this.userDropdown)) {
        await this.page.click(this.userDropdown);
        await this.waitForElement(this.logoutButton);
        await this.page.click(this.logoutButton);
      } else {
        // Method 2: Direct API logout
        await this.page.goto('/api/auth/logout');
      }
      
      // Wait for logout to complete
      await this.page.waitForURL('/', { timeout: 10000 });
      
      // Clear any remaining auth state
      await this.clearStorage();
      
      console.log('✅ Logout completed');
      
    } catch (error) {
      console.error('❌ Logout failed:', error);
      throw error;
    }
  }
  
  /**
   * Verify authentication state matches expected
   */
  async verifyAuthenticationState(expectedState: boolean) {
    const actualState = await this.isAuthenticated();
    
    if (actualState !== expectedState) {
      throw new Error(
        `Authentication state mismatch. Expected: ${expectedState}, Actual: ${actualState}`
      );
    }
    
    console.log(`✅ Authentication state verified: ${expectedState}`);
  }
}
