const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'JustPrototype API',
      version: '2.0.0',
      description: 'API documentation for JustPrototype backend, including V2 LLM endpoints',
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://daring-presence-production.up.railway.app'
          : 'http://localhost:5000',
        description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server'
      }
    ],
  },
  apis: [
    './routes/*.js',      // Route documentation
    './controllers/*.js', // Controller documentation
  ]
};

// Generate swagger specification
const swaggerSpec = swaggerJSDoc(options);

module.exports = swaggerSpec;
