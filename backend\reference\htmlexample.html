<iframe data-v-67e34b3c="" id="preview-iframe" class="interaction__web-iframe" srcdoc="<!DOCTYPE html><html lang=&quot;en&quot;><head><script>
    (function(){
      let proxyEcharts = null;
    
      let originInit = null;
    
      const DefaultFontOptions = [
          { key: 'xAxis', stylePath: 'xAxis.axisLabel' },
          { key: 'yAxis', stylePath: 'yAxis.axisLabel' },
          { key: 'legend', stylePath: 'legend.textStyle' },
          { key: 'tooltip', stylePath: 'tooltip.textStyle' },
          { key: 'title', stylePath: 'title.textStyle' },
          { key: 'series', stylePath: 'series.n.label', isArray: true },
      ];
    
      function setVal(source, keys, val, setFn) {
          if (!Array.isArray(keys)) {
              keys = keys.split('.');
          }
          keys = [...keys];
          const valKey = keys.pop();
          while (keys.length) {
              const parentKey = keys.shift();
              if (typeof source[parentKey] !== 'object' &amp;&amp; typeof source[parentKey] !== 'function') {
                  const childKey = keys[0] === undefined ? valKey : keys[0];
                  let newVal = {};
                  if (!isNaN(childKey * 1)) {
                      newVal = [];
                  }
                  if (setFn) {
                      setFn(source, parentKey, newVal);
                  } else {
                      source[parentKey] = newVal;
                  }
              }
              source = source[parentKey];
          }
          if (setFn) {
              setFn(source, valKey, val);
          } else {
              source[valKey] = val;
          }
      }
    
      function setFontFamily(options) {
          return options;
      }
    
      function setSeriesFamily(options) {
          if (options.series) {
              options.series = options.series.map(item => {
                  item.label = { ...item.label };
                  return item;
              });
          }
          return options;
      }
    
    
      function addResizeListener(echartsInstance) {
          const mountDom = echartsInstance.getDom();
          const resizeObserver = new ResizeObserver(() => {
              echartsInstance.resize();
          });
          resizeObserver.observe(mountDom);
          return resizeObserver;
      }
    
      function proxyInit(...arg) {
          if (!originInit) return null;
          let instance = null;
          try {
            instance = originInit.apply(this, arg);
          } catch (e) {
            console.error(e);
            const div = document.createElement('div');
            div.style.display = 'none';
            document.body.appendChild(div);
            instance = originInit.call(this, div);
            document.body.removeChild(div);
          }
          const originSetOption = instance.setOption;
          addResizeListener(instance);
          instance.setOption = async function setOptionProxy(options) {
              options = options || {};
              options.animation = false;
              try {
                return originSetOption.call(this, options);
              } catch (e) {
                console.error(e);
                return originSetOption.call(this, {});
              }
          };
          return instance;
      }
    
      Object.defineProperty(globalThis, 'echarts', {
          get() {
              return proxyEcharts;
          },
          set(value) {
              originInit = value.init;
              defineInit(value);
              proxyEcharts = value;
          },
          configurable: true,
      });
    
      function defineInit(value) {
          Object.defineProperty(value, 'init', {
              get() {
                  return proxyInit;
              },
              set(v) {
                  originInit = v;
              },
              enumerable: true,
          });
      }
    })()
    </script><script>console.warn = () => {};console.log = () => {};</script>
        <meta charset=&quot;UTF-8&quot;>
        <meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;>
        <title>Analytics</title>
        <script src=&quot;https://cdn.tailwindcss.com/3.4.16&quot;></script>
        <script>tailwind.config={theme:{extend:{colors:{primary:'#4f46e5',secondary:'#a855f7'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
        <link rel=&quot;preconnect&quot; href=&quot;https://fonts.googleapis.com&quot;>
        <link rel=&quot;preconnect&quot; href=&quot;https://fonts.gstatic.com&quot; crossorigin=&quot;&quot;>
        <link href=&quot;https://fonts.googleapis.com/css2?family=Pacifico&amp;amp;display=swap&quot; rel=&quot;stylesheet&quot;>
        <link rel=&quot;stylesheet&quot; href=&quot;https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css&quot;>
        <script src=&quot;https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js&quot;></script>
        <style>
            :where([class^=&quot;ri-&quot;])::before { content: &quot;\f3c2&quot;; }
            
            body {
                font-family: 'Inter', sans-serif;
                overflow-x: hidden;
            }
            
            .sidebar-collapsed {
                width: 5rem;
            }
            
            .main-expanded {
                margin-left: 5rem;
            }
            
            .sidebar-expanded {
                width: 16rem;
            }
            
            .main-collapsed {
                margin-left: 16rem;
            }
            
            .chart-container {
                height: 280px;
            }
            
            input[type=&quot;number&quot;]::-webkit-inner-spin-button,
            input[type=&quot;number&quot;]::-webkit-outer-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }
            
            .custom-checkbox {
                position: relative;
                display: inline-block;
                width: 20px;
                height: 20px;
                border-radius: 4px;
                border: 2px solid #d1d5db;
                background-color: white;
                cursor: pointer;
            }
            
            .custom-checkbox.checked {
                background-color: #4f46e5;
                border-color: #4f46e5;
            }
            
            .custom-checkbox.checked::after {
                content: '';
                position: absolute;
                left: 6px;
                top: 2px;
                width: 6px;
                height: 10px;
                border: solid white;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }
            
            .custom-switch {
                position: relative;
                display: inline-block;
                width: 44px;
                height: 24px;
            }
            
            .custom-switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            
            .switch-slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #e5e7eb;
                transition: .4s;
                border-radius: 34px;
            }
            
            .switch-slider:before {
                position: absolute;
                content: &quot;&quot;;
                height: 18px;
                width: 18px;
                left: 3px;
                bottom: 3px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
            }
            
            input:checked + .switch-slider {
                background-color: #4f46e5;
            }
            
            input:checked + .switch-slider:before {
                transform: translateX(20px);
            }
            
            .date-range-picker {
                position: relative;
            }
            
            .date-range-dropdown {
                position: absolute;
                top: 100%;
                right: 0;
                width: 280px;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                z-index: 10;
            }
            
            .funnel-section {
                position: relative;
                height: 50px;
                margin-bottom: 2px;
            }
            
            .funnel-section:after {
                content: &quot;&quot;;
                position: absolute;
                left: 0;
                bottom: -10px;
                width: 100%;
                height: 10px;
                background: linear-gradient(45deg, transparent 33.33%, white 33.33%, white 66.66%, transparent 66.66%);
                background-size: 20px 10px;
                background-repeat: repeat-x;
            }
            
            .funnel-section:last-child:after {
                display: none;
            }
            
            .tab-active {
                color: #4f46e5;
                border-bottom: 2px solid #4f46e5;
            }
        </style>
    <script>
        // window.setInterval = () => {};
        // window.clearInterval = () => {};
        // window.setTimeout = () => {};
        // window.clearTimeout = () => {};
      </script><style>
        /* 自定义滚动条样式 */
        /* WebKit 浏览器 (Chrome, Safari, Edge) */
        ::-webkit-scrollbar {
            width: 0; /* 滚动条宽度 */
            height: 0; /* 水平滚动条高度 */
        }
    
        ::-webkit-scrollbar-track {
            background: transparent; /* 轨道透明，使内容可见 */
        }
    
        ::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.5); /* 滚动条滑块颜色 */
            border-radius: 4px; /* 滑块圆角 */
        }
    
        ::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.7); /* 悬停时颜色 */
        }
      </style><script>
      window.addEventListener('load', () => {
        if (typeof AOS !== 'undefined' &amp;&amp; AOS) {
          AOS.refresh?.();
        }
      });
    </script><script>(function () {
      // 内存存储
      const memoryStorage = {
        local: new Map(),
        session: new Map()
      };
    
      // 保存原始方法
      // const originalLocalStorage = {
      //   setItem: localStorage.setItem.bind(localStorage),
      //   getItem: localStorage.getItem.bind(localStorage),
      //   removeItem: localStorage.removeItem.bind(localStorage),
      //   clear: localStorage.clear.bind(localStorage),
      //   key: localStorage.key.bind(localStorage)
      // };
    
      // const originalSessionStorage = {
      //   setItem: sessionStorage.setItem.bind(sessionStorage),
      //   getItem: sessionStorage.getItem.bind(sessionStorage),
      //   removeItem: sessionStorage.removeItem.bind(sessionStorage),
      //   clear: sessionStorage.clear.bind(sessionStorage),
      //   key: sessionStorage.key.bind(sessionStorage)
      // };
    
      // 只重写方法，不动属性
      localStorage.setItem = function (key, value) {
        memoryStorage.local.set(key, value);
      };
    
      localStorage.getItem = function (key) {
        return memoryStorage.local.get(key) ?? null;
      };
    
      localStorage.removeItem = function (key) {
        memoryStorage.local.delete(key);
      };
    
      localStorage.clear = function () {
        memoryStorage.local.clear();
      };
    
      localStorage.key = function (n) {
        return Array.from(memoryStorage.local.keys())[n] ?? null;
      };
    
      // sessionStorage 同样的处理
      sessionStorage.setItem = function (key, value) {
        memoryStorage.session.set(key, value);
      };
    
      sessionStorage.getItem = function (key) {
        return memoryStorage.session.get(key) ?? null;
      };
    
      sessionStorage.removeItem = function (key) {
        memoryStorage.session.delete(key);
      };
    
      sessionStorage.clear = function () {
        memoryStorage.session.clear();
      };
    
      sessionStorage.key = function (n) {
        return Array.from(memoryStorage.session.keys())[n] ?? null;
      };
    })();
    </script></head>
    <body class=&quot;bg-gray-50&quot;>
        <div class=&quot;flex h-screen&quot;>
            <!-- Sidebar -->
            <aside id=&quot;sidebar&quot; class=&quot;sidebar-expanded bg-white shadow-lg transition-all duration-300 ease-in-out fixed h-full z-10&quot;>
                <div class=&quot;flex flex-col h-full&quot;>
                    <!-- Logo -->
                    <div class=&quot;p-4 flex justify-between items-center&quot;>
                        <div class=&quot;flex items-center&quot;>
                            <span class=&quot;text-2xl font-['Pacifico'] text-primary&quot;>logo</span>
                            <span class=&quot;sidebar-text ml-2 font-semibold text-gray-800&quot;>Dashboard</span>
                        </div>
                        <button id=&quot;toggle-sidebar&quot; class=&quot;w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary&quot;>
                            <i class=&quot;ri-menu-fold-line ri-lg&quot;></i>
                        </button>
                    </div>
                    
                    <!-- Navigation -->
                    <nav class=&quot;mt-6 flex-1 overflow-y-auto&quot;>
                        <ul class=&quot;space-y-1 px-3&quot;>
                            <li>
                                <a href=&quot;https://readdy.ai/home/<USER>/f937e618-3743-4efa-b55c-6ab4f79e419c&quot; data-readdy=&quot;true&quot; class=&quot;flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-lg&quot;>
                                    <div class=&quot;w-6 h-6 flex items-center justify-center&quot;>
                                        <i class=&quot;ri-dashboard-line&quot;></i>
                                    </div>
                                    <span class=&quot;sidebar-text ml-3&quot;>Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a href=&quot;#&quot; class=&quot;flex items-center p-3 text-primary bg-indigo-50 rounded-lg&quot;>
                                    <div class=&quot;w-6 h-6 flex items-center justify-center&quot;>
                                        <i class=&quot;ri-bar-chart-line&quot;></i>
                                    </div>
                                    <span class=&quot;sidebar-text ml-3 font-medium&quot;>Analytics</span>
                                </a>
                            </li>
                            <li>
                                <a href=&quot;#&quot; class=&quot;flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-lg&quot;>
                                    <div class=&quot;w-6 h-6 flex items-center justify-center&quot;>
                                        <i class=&quot;ri-shopping-bag-line&quot;></i>
                                    </div>
                                    <span class=&quot;sidebar-text ml-3&quot;>Products</span>
                                </a>
                            </li>
                            <li>
                                <a href=&quot;#&quot; class=&quot;flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-lg&quot;>
                                    <div class=&quot;w-6 h-6 flex items-center justify-center&quot;>
                                        <i class=&quot;ri-user-line&quot;></i>
                                    </div>
                                    <span class=&quot;sidebar-text ml-3&quot;>Customers</span>
                                </a>
                            </li>
                            <li>
                                <a href=&quot;#&quot; class=&quot;flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-lg&quot;>
                                    <div class=&quot;w-6 h-6 flex items-center justify-center&quot;>
                                        <i class=&quot;ri-file-list-line&quot;></i>
                                    </div>
                                    <span class=&quot;sidebar-text ml-3&quot;>Orders</span>
                                </a>
                            </li>
                            <li>
                                <a href=&quot;#&quot; class=&quot;flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-lg&quot;>
                                    <div class=&quot;w-6 h-6 flex items-center justify-center&quot;>
                                        <i class=&quot;ri-settings-line&quot;></i>
                                    </div>
                                    <span class=&quot;sidebar-text ml-3&quot;>Settings</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    
                    <!-- Bottom section -->
                    <div class=&quot;p-4 border-t&quot;>
                        <div class=&quot;flex items-center&quot;>
                            <div class=&quot;w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden&quot;>
                                <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520headshot%2520of%2520a%2520young%2520business%2520man%2520with%2520short%2520hair%252C%2520wearing%2520a%2520suit%252C%2520high%2520quality%252C%2520realistic%252C%2520detailed%2520facial%2520features%252C%2520neutral%2520expression%252C%2520studio%2520lighting&amp;amp;width=100&amp;amp;height=100&amp;amp;seq=avatar1&amp;amp;orientation=squarish&quot; alt=&quot;User avatar&quot; class=&quot;w-full h-full object-cover&quot;>
                            </div>
                            <div class=&quot;sidebar-text ml-3&quot;>
                                <p class=&quot;text-sm font-medium text-gray-800&quot;>Michael Anderson</p>
                                <p class=&quot;text-xs text-gray-500&quot;>Product Manager</p>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>
    
            <!-- Main Content -->
            <main id=&quot;main-content&quot; class=&quot;main-collapsed flex-1 transition-all duration-300 ease-in-out overflow-x-hidden&quot;>
                <!-- Header -->
                <header class=&quot;bg-white shadow-sm py-4 px-6 flex items-center justify-between&quot;>
                    <div>
                        <div class=&quot;flex items-center text-sm text-gray-500&quot;>
                            <a href=&quot;https://readdy.ai/home/<USER>/f937e618-3743-4efa-b55c-6ab4f79e419c&quot; data-readdy=&quot;true&quot; class=&quot;hover:text-primary&quot;>Dashboard</a>
                            <div class=&quot;w-4 h-4 flex items-center justify-center mx-1&quot;>
                                <i class=&quot;ri-arrow-right-s-line&quot;></i>
                            </div>
                            <span class=&quot;text-gray-800&quot;>Analytics</span>
                        </div>
                        <h1 class=&quot;text-xl font-semibold text-gray-800 mt-1&quot;>Analytics &amp;amp; Insights</h1>
                    </div>
                    
                    <!-- Search -->
                    <div class=&quot;relative w-72&quot;>
                        <div class=&quot;absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none&quot;>
                            <div class=&quot;w-5 h-5 flex items-center justify-center text-gray-400&quot;>
                                <i class=&quot;ri-search-line&quot;></i>
                            </div>
                        </div>
                        <input type=&quot;text&quot; class=&quot;bg-gray-100 w-full pl-10 pr-4 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white border-none&quot; placeholder=&quot;Search analytics...&quot;>
                    </div>
                    
                    <!-- User actions -->
                    <div class=&quot;flex items-center space-x-4&quot;>
                        <button class=&quot;relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary&quot;>
                            <i class=&quot;ri-notification-3-line ri-lg&quot;></i>
                            <span class=&quot;absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full&quot;></span>
                        </button>
                        
                        <div class=&quot;relative&quot;>
                            <button id=&quot;user-menu-button&quot; class=&quot;flex items-center space-x-2&quot;>
                                <div class=&quot;w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden&quot;>
                                    <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520headshot%2520of%2520a%2520young%2520business%2520man%2520with%2520short%2520hair%252C%2520wearing%2520a%2520suit%252C%2520high%2520quality%252C%2520realistic%252C%2520detailed%2520facial%2520features%252C%2520neutral%2520expression%252C%2520studio%2520lighting&amp;amp;width=100&amp;amp;height=100&amp;amp;seq=avatar1&amp;amp;orientation=squarish&quot; alt=&quot;User avatar&quot; class=&quot;w-full h-full object-cover&quot;>
                                </div>
                                <div class=&quot;hidden md:block&quot;>
                                    <p class=&quot;text-sm font-medium text-gray-800&quot;>Michael Anderson</p>
                                    <div class=&quot;flex items-center text-xs text-gray-500&quot;>
                                        <span>Product Manager</span>
                                        <div class=&quot;w-4 h-4 flex items-center justify-center ml-1&quot;>
                                            <i class=&quot;ri-arrow-down-s-line&quot;></i>
                                        </div>
                                    </div>
                                </div>
                            </button>
                            
                            <div id=&quot;user-dropdown&quot; class=&quot;hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10&quot;>
                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Your Profile</a>
                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Settings</a>
                                <div class=&quot;border-t border-gray-100&quot;></div>
                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Sign out</a>
                            </div>
                        </div>
                    </div>
                </header>
                
                <!-- Analytics Content -->
                <div class=&quot;p-6&quot;>
                    <!-- Date Range &amp; Controls -->
                    <div class=&quot;bg-white rounded-lg shadow-sm p-4 mb-6&quot;>
                        <div class=&quot;flex flex-wrap items-center justify-between&quot;>
                            <div class=&quot;flex items-center space-x-4 mb-2 md:mb-0&quot;>
                                <div class=&quot;relative date-range-picker&quot;>
                                    <button id=&quot;date-range-button&quot; class=&quot;flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap&quot;>
                                        <div class=&quot;w-4 h-4 flex items-center justify-center mr-2&quot;>
                                            <i class=&quot;ri-calendar-line&quot;></i>
                                        </div>
                                        <span id=&quot;date-range-text&quot;>Last 30 Days</span>
                                        <div class=&quot;w-4 h-4 flex items-center justify-center ml-2&quot;>
                                            <i class=&quot;ri-arrow-down-s-line&quot;></i>
                                        </div>
                                    </button>
                                    
                                    <div id=&quot;date-range-dropdown&quot; class=&quot;hidden date-range-dropdown p-4&quot;>
                                        <div class=&quot;mb-3&quot;>
                                            <h4 class=&quot;text-sm font-medium text-gray-700 mb-2&quot;>Quick Select</h4>
                                            <div class=&quot;grid grid-cols-2 gap-2&quot;>
                                                <button class=&quot;text-xs px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 !rounded-button whitespace-nowrap&quot;>Today</button>
                                                <button class=&quot;text-xs px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 !rounded-button whitespace-nowrap&quot;>Yesterday</button>
                                                <button class=&quot;text-xs px-3 py-1.5 bg-indigo-100 text-primary rounded !rounded-button whitespace-nowrap&quot;>Last 7 Days</button>
                                                <button class=&quot;text-xs px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 !rounded-button whitespace-nowrap&quot;>Last 30 Days</button>
                                                <button class=&quot;text-xs px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 !rounded-button whitespace-nowrap&quot;>This Month</button>
                                                <button class=&quot;text-xs px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 !rounded-button whitespace-nowrap&quot;>Last Month</button>
                                            </div>
                                        </div>
                                        
                                        <div class=&quot;mb-3&quot;>
                                            <h4 class=&quot;text-sm font-medium text-gray-700 mb-2&quot;>Custom Range</h4>
                                            <div class=&quot;grid grid-cols-2 gap-2&quot;>
                                                <div>
                                                    <label class=&quot;text-xs text-gray-500&quot;>Start Date</label>
                                                    <input type=&quot;date&quot; class=&quot;w-full mt-1 px-2 py-1.5 border border-gray-300 rounded text-sm border-none&quot;>
                                                </div>
                                                <div>
                                                    <label class=&quot;text-xs text-gray-500&quot;>End Date</label>
                                                    <input type=&quot;date&quot; class=&quot;w-full mt-1 px-2 py-1.5 border border-gray-300 rounded text-sm border-none&quot;>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class=&quot;flex justify-end&quot;>
                                            <button class=&quot;px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-sm text-gray-700 mr-2 !rounded-button whitespace-nowrap&quot;>Cancel</button>
                                            <button class=&quot;px-3 py-1.5 bg-primary text-white rounded text-sm !rounded-button whitespace-nowrap&quot;>Apply</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class=&quot;flex items-center space-x-2&quot;>
                                    <button class=&quot;flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 !rounded-button whitespace-nowrap&quot;>
                                        <div class=&quot;w-4 h-4 flex items-center justify-center mr-1&quot;>
                                            <i class=&quot;ri-refresh-line&quot;></i>
                                        </div>
                                        <span>Refresh</span>
                                    </button>
                                    
                                    <div class=&quot;relative&quot;>
                                        <button id=&quot;comparison-button&quot; class=&quot;flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 !rounded-button whitespace-nowrap&quot;>
                                            <div class=&quot;w-4 h-4 flex items-center justify-center mr-1&quot;>
                                                <i class=&quot;ri-contrast-2-line&quot;></i>
                                            </div>
                                            <span>Compare</span>
                                            <div class=&quot;w-4 h-4 flex items-center justify-center ml-1&quot;>
                                                <i class=&quot;ri-arrow-down-s-line&quot;></i>
                                            </div>
                                        </button>
                                        
                                        <div id=&quot;comparison-dropdown&quot; class=&quot;hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10&quot;>
                                            <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Previous Period</a>
                                            <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Same Period Last Year</a>
                                            <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Custom Period</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class=&quot;flex items-center space-x-2&quot;>
                                <button class=&quot;flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap&quot;>
                                    <div class=&quot;w-4 h-4 flex items-center justify-center mr-1&quot;>
                                        <i class=&quot;ri-save-line&quot;></i>
                                    </div>
                                    <span>Save View</span>
                                </button>
                                
                                <div class=&quot;relative&quot;>
                                    <button id=&quot;export-button&quot; class=&quot;flex items-center px-3 py-2 bg-primary text-white rounded-lg text-sm font-medium !rounded-button whitespace-nowrap&quot;>
                                        <div class=&quot;w-4 h-4 flex items-center justify-center mr-1&quot;>
                                            <i class=&quot;ri-download-line&quot;></i>
                                        </div>
                                        <span>Export</span>
                                        <div class=&quot;w-4 h-4 flex items-center justify-center ml-1&quot;>
                                            <i class=&quot;ri-arrow-down-s-line&quot;></i>
                                        </div>
                                    </button>
                                    
                                    <div id=&quot;export-dropdown&quot; class=&quot;hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10&quot;>
                                        <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>Excel (.xlsx)</a>
                                        <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>CSV (.csv)</a>
                                        <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>PDF (.pdf)</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sales Analytics Section -->
                    <div class=&quot;mb-6&quot;>
                        <h2 class=&quot;text-lg font-semibold text-gray-800 mb-4&quot;>Sales Analytics</h2>
                        
                        <!-- Sales Metrics Cards -->
                        <div class=&quot;grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6&quot;>
                            <!-- Total Revenue -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-5&quot;>
                                <div class=&quot;flex justify-between items-start&quot;>
                                    <div>
                                        <p class=&quot;text-sm font-medium text-gray-500&quot;>Total Revenue</p>
                                        <h3 class=&quot;text-2xl font-bold text-gray-800 mt-1&quot;>$127,842.35</h3>
                                        <div class=&quot;flex items-center mt-2&quot;>
                                            <div class=&quot;w-4 h-4 flex items-center justify-center text-green-500&quot;>
                                                <i class=&quot;ri-arrow-up-line&quot;></i>
                                            </div>
                                            <span class=&quot;text-sm font-medium text-green-500 ml-1&quot;>12.8%</span>
                                            <span class=&quot;text-xs text-gray-500 ml-2&quot;>vs previous period</span>
                                        </div>
                                    </div>
                                    <div class=&quot;w-12 h-12 rounded-lg bg-indigo-50 flex items-center justify-center text-primary&quot;>
                                        <i class=&quot;ri-money-dollar-circle-line ri-xl&quot;></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Average Order Value -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-5&quot;>
                                <div class=&quot;flex justify-between items-start&quot;>
                                    <div>
                                        <p class=&quot;text-sm font-medium text-gray-500&quot;>Avg. Order Value</p>
                                        <h3 class=&quot;text-2xl font-bold text-gray-800 mt-1&quot;>$86.42</h3>
                                        <div class=&quot;flex items-center mt-2&quot;>
                                            <div class=&quot;w-4 h-4 flex items-center justify-center text-green-500&quot;>
                                                <i class=&quot;ri-arrow-up-line&quot;></i>
                                            </div>
                                            <span class=&quot;text-sm font-medium text-green-500 ml-1&quot;>5.3%</span>
                                            <span class=&quot;text-xs text-gray-500 ml-2&quot;>vs previous period</span>
                                        </div>
                                    </div>
                                    <div class=&quot;w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center text-blue-500&quot;>
                                        <i class=&quot;ri-shopping-basket-line ri-xl&quot;></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Revenue Growth Rate -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-5&quot;>
                                <div class=&quot;flex justify-between items-start&quot;>
                                    <div>
                                        <p class=&quot;text-sm font-medium text-gray-500&quot;>Growth Rate</p>
                                        <h3 class=&quot;text-2xl font-bold text-gray-800 mt-1&quot;>+18.7%</h3>
                                        <div class=&quot;flex items-center mt-2&quot;>
                                            <div class=&quot;w-4 h-4 flex items-center justify-center text-green-500&quot;>
                                                <i class=&quot;ri-arrow-up-line&quot;></i>
                                            </div>
                                            <span class=&quot;text-sm font-medium text-green-500 ml-1&quot;>3.2%</span>
                                            <span class=&quot;text-xs text-gray-500 ml-2&quot;>vs previous period</span>
                                        </div>
                                    </div>
                                    <div class=&quot;w-12 h-12 rounded-lg bg-green-50 flex items-center justify-center text-green-500&quot;>
                                        <i class=&quot;ri-line-chart-line ri-xl&quot;></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Sales Target Progress -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-5&quot;>
                                <div class=&quot;flex justify-between items-start&quot;>
                                    <div>
                                        <p class=&quot;text-sm font-medium text-gray-500&quot;>Target Progress</p>
                                        <h3 class=&quot;text-2xl font-bold text-gray-800 mt-1&quot;>78.4%</h3>
                                        <div class=&quot;flex items-center mt-2&quot;>
                                            <div class=&quot;w-4 h-4 flex items-center justify-center text-yellow-500&quot;>
                                                <i class=&quot;ri-arrow-right-line&quot;></i>
                                            </div>
                                            <span class=&quot;text-sm font-medium text-yellow-500 ml-1&quot;>On track</span>
                                            <span class=&quot;text-xs text-gray-500 ml-2&quot;>$150K target</span>
                                        </div>
                                    </div>
                                    <div class=&quot;w-12 h-12 rounded-lg bg-purple-50 flex items-center justify-center text-purple-500&quot;>
                                        <i class=&quot;ri-target-line ri-xl&quot;></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Revenue Trend Chart -->
                        <div class=&quot;bg-white rounded-lg shadow-sm p-6&quot;>
                            <div class=&quot;flex justify-between items-center mb-6&quot;>
                                <h3 class=&quot;text-lg font-semibold text-gray-800&quot;>Revenue Trend</h3>
                                <div class=&quot;flex space-x-2&quot;>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-primary bg-indigo-50 rounded-full !rounded-button whitespace-nowrap&quot;>Monthly</button>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-full !rounded-button whitespace-nowrap&quot;>Weekly</button>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-full !rounded-button whitespace-nowrap&quot;>Daily</button>
                                </div>
                            </div>
                            <div id=&quot;revenue-chart&quot; class=&quot;chart-container&quot;></div>
                            
                            <!-- Category Breakdown -->
                            <div class=&quot;mt-6 grid grid-cols-2 md:grid-cols-4 gap-4&quot;>
                                <div class=&quot;p-3 bg-gray-50 rounded-lg&quot;>
                                    <div class=&quot;flex items-center justify-between mb-2&quot;>
                                        <span class=&quot;text-sm font-medium text-gray-700&quot;>Electronics</span>
                                        <span class=&quot;text-xs font-medium text-green-500&quot;>+14.5%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-lg font-semibold text-gray-800&quot;>$42,580</span>
                                        <span class=&quot;text-xs text-gray-500&quot;>33.3%</span>
                                    </div>
                                </div>
                                
                                <div class=&quot;p-3 bg-gray-50 rounded-lg&quot;>
                                    <div class=&quot;flex items-center justify-between mb-2&quot;>
                                        <span class=&quot;text-sm font-medium text-gray-700&quot;>Clothing</span>
                                        <span class=&quot;text-xs font-medium text-green-500&quot;>+8.2%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-lg font-semibold text-gray-800&quot;>$35,127</span>
                                        <span class=&quot;text-xs text-gray-500&quot;>27.5%</span>
                                    </div>
                                </div>
                                
                                <div class=&quot;p-3 bg-gray-50 rounded-lg&quot;>
                                    <div class=&quot;flex items-center justify-between mb-2&quot;>
                                        <span class=&quot;text-sm font-medium text-gray-700&quot;>Home &amp;amp; Kitchen</span>
                                        <span class=&quot;text-xs font-medium text-green-500&quot;>+22.7%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-lg font-semibold text-gray-800&quot;>$28,954</span>
                                        <span class=&quot;text-xs text-gray-500&quot;>22.6%</span>
                                    </div>
                                </div>
                                
                                <div class=&quot;p-3 bg-gray-50 rounded-lg&quot;>
                                    <div class=&quot;flex items-center justify-between mb-2&quot;>
                                        <span class=&quot;text-sm font-medium text-gray-700&quot;>Others</span>
                                        <span class=&quot;text-xs font-medium text-red-500&quot;>-3.8%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-lg font-semibold text-gray-800&quot;>$21,181</span>
                                        <span class=&quot;text-xs text-gray-500&quot;>16.6%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Customer Behavior Section -->
                    <div class=&quot;mb-6&quot;>
                        <h2 class=&quot;text-lg font-semibold text-gray-800 mb-4&quot;>Customer Behavior</h2>
                        
                        <div class=&quot;grid grid-cols-1 lg:grid-cols-3 gap-6&quot;>
                            <!-- Customer Metrics -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-6&quot;>
                                <h3 class=&quot;text-base font-semibold text-gray-800 mb-4&quot;>Customer Metrics</h3>
                                
                                <div class=&quot;space-y-5&quot;>
                                    <div>
                                        <div class=&quot;flex justify-between mb-1&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>Active Users</span>
                                            <span class=&quot;text-sm font-medium text-gray-900&quot;>8,742</span>
                                        </div>
                                        <div class=&quot;w-full bg-gray-200 rounded-full h-2&quot;>
                                            <div class=&quot;bg-primary h-2 rounded-full&quot; style=&quot;width: 78%&quot;></div>
                                        </div>
                                        <div class=&quot;flex justify-between mt-1&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs last period: 7,853</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>+11.3%</span>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class=&quot;flex justify-between mb-1&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>New Users</span>
                                            <span class=&quot;text-sm font-medium text-gray-900&quot;>2,156</span>
                                        </div>
                                        <div class=&quot;w-full bg-gray-200 rounded-full h-2&quot;>
                                            <div class=&quot;bg-blue-500 h-2 rounded-full&quot; style=&quot;width: 45%&quot;></div>
                                        </div>
                                        <div class=&quot;flex justify-between mt-1&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs last period: 1,982</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>+8.8%</span>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class=&quot;flex justify-between mb-1&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>Avg. Session Duration</span>
                                            <span class=&quot;text-sm font-medium text-gray-900&quot;>4m 32s</span>
                                        </div>
                                        <div class=&quot;w-full bg-gray-200 rounded-full h-2&quot;>
                                            <div class=&quot;bg-green-500 h-2 rounded-full&quot; style=&quot;width: 62%&quot;></div>
                                        </div>
                                        <div class=&quot;flex justify-between mt-1&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs last period: 4m 12s</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>+7.9%</span>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class=&quot;flex justify-between mb-1&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>Bounce Rate</span>
                                            <span class=&quot;text-sm font-medium text-gray-900&quot;>32.8%</span>
                                        </div>
                                        <div class=&quot;w-full bg-gray-200 rounded-full h-2&quot;>
                                            <div class=&quot;bg-yellow-500 h-2 rounded-full&quot; style=&quot;width: 33%&quot;></div>
                                        </div>
                                        <div class=&quot;flex justify-between mt-1&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs last period: 35.4%</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>-7.3%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- User Type Distribution -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-6&quot;>
                                <h3 class=&quot;text-base font-semibold text-gray-800 mb-4&quot;>User Type Distribution</h3>
                                <div id=&quot;user-type-chart&quot; class=&quot;chart-container&quot;></div>
                            </div>
                            
                            <!-- Geographic Distribution -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-6&quot;>
                                <h3 class=&quot;text-base font-semibold text-gray-800 mb-4&quot;>Geographic Distribution</h3>
                                <div class=&quot;h-[280px] w-full bg-cover bg-center rounded-lg overflow-hidden&quot; style=&quot;background-image: url('https://public.readdy.ai/gen_page/map_placeholder_1280x720.png')&quot;></div>
                                
                                <div class=&quot;mt-4 space-y-2&quot;>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-sm text-gray-700&quot;>United States</span>
                                        <span class=&quot;text-sm font-medium text-gray-900&quot;>42.5%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-sm text-gray-700&quot;>United Kingdom</span>
                                        <span class=&quot;text-sm font-medium text-gray-900&quot;>18.3%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-sm text-gray-700&quot;>Germany</span>
                                        <span class=&quot;text-sm font-medium text-gray-900&quot;>12.7%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-sm text-gray-700&quot;>Canada</span>
                                        <span class=&quot;text-sm font-medium text-gray-900&quot;>8.4%</span>
                                    </div>
                                    <div class=&quot;flex items-center justify-between&quot;>
                                        <span class=&quot;text-sm text-gray-700&quot;>Other Countries</span>
                                        <span class=&quot;text-sm font-medium text-gray-900&quot;>18.1%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cohort Analysis -->
                        <div class=&quot;mt-6 bg-white rounded-lg shadow-sm p-6&quot;>
                            <div class=&quot;flex justify-between items-center mb-6&quot;>
                                <h3 class=&quot;text-base font-semibold text-gray-800&quot;>Cohort Analysis</h3>
                                <div class=&quot;flex space-x-2&quot;>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-primary bg-indigo-50 rounded-full !rounded-button whitespace-nowrap&quot;>Retention</button>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-full !rounded-button whitespace-nowrap&quot;>Revenue</button>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-full !rounded-button whitespace-nowrap&quot;>Frequency</button>
                                </div>
                            </div>
                            
                            <div class=&quot;overflow-x-auto&quot;>
                                <table class=&quot;w-full&quot;>
                                    <thead>
                                        <tr>
                                            <th class=&quot;px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Cohort</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Users</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Month 0</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Month 1</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Month 2</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Month 3</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Month 4</th>
                                            <th class=&quot;px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>Month 5</th>
                                        </tr>
                                    </thead>
                                    <tbody class=&quot;divide-y divide-gray-100&quot;>
                                        <tr>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800&quot;>Dec 2024</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500&quot;>1,245</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-primary text-white text-xs font-medium rounded text-center&quot;>100%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>68%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>54%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>47%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>42%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>38%</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800&quot;>Jan 2025</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500&quot;>1,387</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-primary text-white text-xs font-medium rounded text-center&quot;>100%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>72%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>58%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>51%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>45%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800&quot;>Feb 2025</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500&quot;>1,512</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-primary text-white text-xs font-medium rounded text-center&quot;>100%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>75%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>62%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800&quot;>Mar 2025</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500&quot;>1,678</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-primary text-white text-xs font-medium rounded text-center&quot;>100%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded text-center&quot;>78%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800&quot;>Apr 2025</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500&quot;>1,824</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-primary text-white text-xs font-medium rounded text-center&quot;>100%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800&quot;>May 2025</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center text-gray-500&quot;>1,956</td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-primary text-white text-xs font-medium rounded text-center&quot;>100%</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                            <td class=&quot;px-4 py-3 whitespace-nowrap text-sm text-center&quot;>
                                                <span class=&quot;inline-block w-12 py-1 bg-gray-100 text-gray-400 text-xs font-medium rounded text-center&quot;>-</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Product Performance Section -->
                    <div class=&quot;mb-6&quot;>
                        <h2 class=&quot;text-lg font-semibold text-gray-800 mb-4&quot;>Product Performance</h2>
                        
                        <div class=&quot;grid grid-cols-1 lg:grid-cols-3 gap-6&quot;>
                            <!-- Top Products Table -->
                            <div class=&quot;lg:col-span-2 bg-white rounded-lg shadow-sm&quot;>
                                <div class=&quot;p-6 border-b border-gray-100&quot;>
                                    <div class=&quot;flex justify-between items-center&quot;>
                                        <h3 class=&quot;text-base font-semibold text-gray-800&quot;>Top Performing Products</h3>
                                        <div class=&quot;relative&quot;>
                                            <button id=&quot;product-filter-button&quot; class=&quot;flex items-center text-sm text-gray-500 hover:text-gray-700&quot;>
                                                <span>Filter</span>
                                                <div class=&quot;w-4 h-4 flex items-center justify-center ml-1&quot;>
                                                    <i class=&quot;ri-arrow-down-s-line&quot;></i>
                                                </div>
                                            </button>
                                            
                                            <div id=&quot;product-filter-dropdown&quot; class=&quot;hidden absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg py-1 z-10&quot;>
                                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>By Revenue</a>
                                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>By Units Sold</a>
                                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>By Profit Margin</a>
                                                <a href=&quot;#&quot; class=&quot;block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100&quot;>By Growth Rate</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class=&quot;overflow-x-auto&quot;>
                                    <table class=&quot;w-full&quot;>
                                        <thead>
                                            <tr class=&quot;text-left text-xs font-medium text-gray-500 uppercase tracking-wider&quot;>
                                                <th class=&quot;px-6 py-3&quot;>Product</th>
                                                <th class=&quot;px-6 py-3&quot;>Category</th>
                                                <th class=&quot;px-6 py-3&quot;>Units Sold</th>
                                                <th class=&quot;px-6 py-3&quot;>Revenue</th>
                                                <th class=&quot;px-6 py-3&quot;>Profit Margin</th>
                                                <th class=&quot;px-6 py-3&quot;>Trend</th>
                                            </tr>
                                        </thead>
                                        <tbody class=&quot;divide-y divide-gray-100&quot;>
                                            <tr>
                                                <td class=&quot;px-6 py-4&quot;>
                                                    <div class=&quot;flex items-center&quot;>
                                                        <div class=&quot;w-10 h-10 rounded bg-gray-100 flex items-center justify-center overflow-hidden&quot;>
                                                            <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520product%2520photo%2520of%2520a%2520modern%2520wireless%2520headphones%2520on%2520clean%2520white%2520background%252C%2520high%2520quality%252C%2520detailed%252C%2520commercial%2520product%2520photography%252C%2520minimalist%2520style&amp;amp;width=80&amp;amp;height=80&amp;amp;seq=product1&amp;amp;orientation=squarish&quot; alt=&quot;Product&quot; class=&quot;w-full h-full object-cover&quot;>
                                                        </div>
                                                        <div class=&quot;ml-3&quot;>
                                                            <p class=&quot;text-sm font-medium text-gray-800&quot;>Premium Wireless Headphones</p>
                                                            <p class=&quot;text-xs text-gray-500&quot;>SKU: PRD-7245</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm text-gray-500&quot;>Electronics</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>1,842</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>$184,200</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600&quot;>42.5%</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap&quot;>
                                                    <div class=&quot;w-16 h-8&quot;>
                                                        <div id=&quot;trend-chart-1&quot; class=&quot;w-full h-full&quot;></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class=&quot;px-6 py-4&quot;>
                                                    <div class=&quot;flex items-center&quot;>
                                                        <div class=&quot;w-10 h-10 rounded bg-gray-100 flex items-center justify-center overflow-hidden&quot;>
                                                            <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520product%2520photo%2520of%2520a%2520stylish%2520leather%2520wallet%2520on%2520clean%2520white%2520background%252C%2520high%2520quality%252C%2520detailed%252C%2520commercial%2520product%2520photography%252C%2520minimalist%2520style&amp;amp;width=80&amp;amp;height=80&amp;amp;seq=product2&amp;amp;orientation=squarish&quot; alt=&quot;Product&quot; class=&quot;w-full h-full object-cover&quot;>
                                                        </div>
                                                        <div class=&quot;ml-3&quot;>
                                                            <p class=&quot;text-sm font-medium text-gray-800&quot;>Leather Wallet</p>
                                                            <p class=&quot;text-xs text-gray-500&quot;>SKU: PRD-6587</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm text-gray-500&quot;>Accessories</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>2,156</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>$129,360</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600&quot;>58.2%</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap&quot;>
                                                    <div class=&quot;w-16 h-8&quot;>
                                                        <div id=&quot;trend-chart-2&quot; class=&quot;w-full h-full&quot;></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class=&quot;px-6 py-4&quot;>
                                                    <div class=&quot;flex items-center&quot;>
                                                        <div class=&quot;w-10 h-10 rounded bg-gray-100 flex items-center justify-center overflow-hidden&quot;>
                                                            <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520product%2520photo%2520of%2520a%2520modern%2520smart%2520watch%2520on%2520clean%2520white%2520background%252C%2520high%2520quality%252C%2520detailed%252C%2520commercial%2520product%2520photography%252C%2520minimalist%2520style&amp;amp;width=80&amp;amp;height=80&amp;amp;seq=product3&amp;amp;orientation=squarish&quot; alt=&quot;Product&quot; class=&quot;w-full h-full object-cover&quot;>
                                                        </div>
                                                        <div class=&quot;ml-3&quot;>
                                                            <p class=&quot;text-sm font-medium text-gray-800&quot;>Smart Watch Pro</p>
                                                            <p class=&quot;text-xs text-gray-500&quot;>SKU: PRD-5432</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm text-gray-500&quot;>Electronics</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>1,258</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>$125,800</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600&quot;>38.7%</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap&quot;>
                                                    <div class=&quot;w-16 h-8&quot;>
                                                        <div id=&quot;trend-chart-3&quot; class=&quot;w-full h-full&quot;></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class=&quot;px-6 py-4&quot;>
                                                    <div class=&quot;flex items-center&quot;>
                                                        <div class=&quot;w-10 h-10 rounded bg-gray-100 flex items-center justify-center overflow-hidden&quot;>
                                                            <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520product%2520photo%2520of%2520a%2520stylish%2520backpack%2520on%2520clean%2520white%2520background%252C%2520high%2520quality%252C%2520detailed%252C%2520commercial%2520product%2520photography%252C%2520minimalist%2520style&amp;amp;width=80&amp;amp;height=80&amp;amp;seq=product4&amp;amp;orientation=squarish&quot; alt=&quot;Product&quot; class=&quot;w-full h-full object-cover&quot;>
                                                        </div>
                                                        <div class=&quot;ml-3&quot;>
                                                            <p class=&quot;text-sm font-medium text-gray-800&quot;>Urban Backpack</p>
                                                            <p class=&quot;text-xs text-gray-500&quot;>SKU: PRD-4321</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm text-gray-500&quot;>Bags</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>1,654</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>$115,780</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600&quot;>45.2%</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap&quot;>
                                                    <div class=&quot;w-16 h-8&quot;>
                                                        <div id=&quot;trend-chart-4&quot; class=&quot;w-full h-full&quot;></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class=&quot;px-6 py-4&quot;>
                                                    <div class=&quot;flex items-center&quot;>
                                                        <div class=&quot;w-10 h-10 rounded bg-gray-100 flex items-center justify-center overflow-hidden&quot;>
                                                            <img src=&quot;https://readdy.ai/api/search-image?query=professional%2520product%2520photo%2520of%2520a%2520portable%2520bluetooth%2520speaker%2520on%2520clean%2520white%2520background%252C%2520high%2520quality%252C%2520detailed%252C%2520commercial%2520product%2520photography%252C%2520minimalist%2520style&amp;amp;width=80&amp;amp;height=80&amp;amp;seq=product5&amp;amp;orientation=squarish&quot; alt=&quot;Product&quot; class=&quot;w-full h-full object-cover&quot;>
                                                        </div>
                                                        <div class=&quot;ml-3&quot;>
                                                            <p class=&quot;text-sm font-medium text-gray-800&quot;>Portable Bluetooth Speaker</p>
                                                            <p class=&quot;text-xs text-gray-500&quot;>SKU: PRD-3214</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm text-gray-500&quot;>Electronics</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>1,427</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800&quot;>$99,890</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600&quot;>36.8%</td>
                                                <td class=&quot;px-6 py-4 whitespace-nowrap&quot;>
                                                    <div class=&quot;w-16 h-8&quot;>
                                                        <div id=&quot;trend-chart-5&quot; class=&quot;w-full h-full&quot;></div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Category Performance -->
                            <div class=&quot;bg-white rounded-lg shadow-sm p-6&quot;>
                                <h3 class=&quot;text-base font-semibold text-gray-800 mb-4&quot;>Category Performance</h3>
                                <div id=&quot;category-chart&quot; class=&quot;chart-container&quot;></div>
                                
                                <div class=&quot;mt-4&quot;>
                                    <h4 class=&quot;text-sm font-medium text-gray-700 mb-2&quot;>Inventory Status</h4>
                                    <div class=&quot;space-y-3&quot;>
                                        <div>
                                            <div class=&quot;flex justify-between mb-1&quot;>
                                                <span class=&quot;text-xs text-gray-500&quot;>Electronics</span>
                                                <span class=&quot;text-xs font-medium text-green-600&quot;>Healthy</span>
                                            </div>
                                            <div class=&quot;w-full bg-gray-200 rounded-full h-1.5&quot;>
                                                <div class=&quot;bg-green-500 h-1.5 rounded-full&quot; style=&quot;width: 82%&quot;></div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div class=&quot;flex justify-between mb-1&quot;>
                                                <span class=&quot;text-xs text-gray-500&quot;>Clothing</span>
                                                <span class=&quot;text-xs font-medium text-yellow-600&quot;>Medium</span>
                                            </div>
                                            <div class=&quot;w-full bg-gray-200 rounded-full h-1.5&quot;>
                                                <div class=&quot;bg-yellow-500 h-1.5 rounded-full&quot; style=&quot;width: 45%&quot;></div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div class=&quot;flex justify-between mb-1&quot;>
                                                <span class=&quot;text-xs text-gray-500&quot;>Home &amp;amp; Kitchen</span>
                                                <span class=&quot;text-xs font-medium text-green-600&quot;>Healthy</span>
                                            </div>
                                            <div class=&quot;w-full bg-gray-200 rounded-full h-1.5&quot;>
                                                <div class=&quot;bg-green-500 h-1.5 rounded-full&quot; style=&quot;width: 78%&quot;></div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div class=&quot;flex justify-between mb-1&quot;>
                                                <span class=&quot;text-xs text-gray-500&quot;>Accessories</span>
                                                <span class=&quot;text-xs font-medium text-red-600&quot;>Low</span>
                                            </div>
                                            <div class=&quot;w-full bg-gray-200 rounded-full h-1.5&quot;>
                                                <div class=&quot;bg-red-500 h-1.5 rounded-full&quot; style=&quot;width: 18%&quot;></div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div class=&quot;flex justify-between mb-1&quot;>
                                                <span class=&quot;text-xs text-gray-500&quot;>Bags</span>
                                                <span class=&quot;text-xs font-medium text-green-600&quot;>Healthy</span>
                                            </div>
                                            <div class=&quot;w-full bg-gray-200 rounded-full h-1.5&quot;>
                                                <div class=&quot;bg-green-500 h-1.5 rounded-full&quot; style=&quot;width: 65%&quot;></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Conversion Funnels Section -->
                    <div class=&quot;mb-6&quot;>
                        <h2 class=&quot;text-lg font-semibold text-gray-800 mb-4&quot;>Conversion Funnels</h2>
                        
                        <div class=&quot;bg-white rounded-lg shadow-sm p-6&quot;>
                            <div class=&quot;flex justify-between items-center mb-6&quot;>
                                <h3 class=&quot;text-base font-semibold text-gray-800&quot;>Sales Funnel</h3>
                                <div class=&quot;flex space-x-2&quot;>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-primary bg-indigo-50 rounded-full !rounded-button whitespace-nowrap&quot;>All Traffic</button>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-full !rounded-button whitespace-nowrap&quot;>Desktop</button>
                                    <button class=&quot;px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-full !rounded-button whitespace-nowrap&quot;>Mobile</button>
                                </div>
                            </div>
                            
                            <div class=&quot;max-w-3xl mx-auto&quot;>
                                <!-- Funnel Visualization -->
                                <div class=&quot;space-y-2&quot;>
                                    <div class=&quot;funnel-section bg-primary rounded-t-lg px-4 py-3 text-white flex justify-between items-center&quot;>
                                        <div>
                                            <p class=&quot;font-medium&quot;>Visits</p>
                                            <p class=&quot;text-sm opacity-80&quot;>Total website visits</p>
                                        </div>
                                        <div class=&quot;text-right&quot;>
                                            <p class=&quot;text-xl font-semibold&quot;>24,582</p>
                                            <p class=&quot;text-sm opacity-80&quot;>100%</p>
                                        </div>
                                    </div>
                                    
                                    <div class=&quot;funnel-section bg-blue-500 px-4 py-3 text-white flex justify-between items-center&quot; style=&quot;width: 85%&quot;>
                                        <div>
                                            <p class=&quot;font-medium&quot;>Product Views</p>
                                            <p class=&quot;text-sm opacity-80&quot;>Viewed product pages</p>
                                        </div>
                                        <div class=&quot;text-right&quot;>
                                            <p class=&quot;text-xl font-semibold&quot;>20,895</p>
                                            <p class=&quot;text-sm opacity-80&quot;>85.0%</p>
                                        </div>
                                    </div>
                                    
                                    <div class=&quot;funnel-section bg-indigo-400 px-4 py-3 text-white flex justify-between items-center&quot; style=&quot;width: 42%&quot;>
                                        <div>
                                            <p class=&quot;font-medium&quot;>Add to Cart</p>
                                            <p class=&quot;text-sm opacity-80&quot;>Added items to cart</p>
                                        </div>
                                        <div class=&quot;text-right&quot;>
                                            <p class=&quot;text-xl font-semibold&quot;>10,324</p>
                                            <p class=&quot;text-sm opacity-80&quot;>42.0%</p>
                                        </div>
                                    </div>
                                    
                                    <div class=&quot;funnel-section bg-purple-500 px-4 py-3 text-white flex justify-between items-center&quot; style=&quot;width: 28%&quot;>
                                        <div>
                                            <p class=&quot;font-medium&quot;>Checkout</p>
                                            <p class=&quot;text-sm opacity-80&quot;>Started checkout</p>
                                        </div>
                                        <div class=&quot;text-right&quot;>
                                            <p class=&quot;text-xl font-semibold&quot;>6,883</p>
                                            <p class=&quot;text-sm opacity-80&quot;>28.0%</p>
                                        </div>
                                    </div>
                                    
                                    <div class=&quot;funnel-section bg-green-500 rounded-b-lg px-4 py-3 text-white flex justify-between items-center&quot; style=&quot;width: 18%&quot;>
                                        <div>
                                            <p class=&quot;font-medium&quot;>Purchases</p>
                                            <p class=&quot;text-sm opacity-80&quot;>Completed purchases</p>
                                        </div>
                                        <div class=&quot;text-right&quot;>
                                            <p class=&quot;text-xl font-semibold&quot;>4,425</p>
                                            <p class=&quot;text-sm opacity-80&quot;>18.0%</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Conversion Metrics -->
                                <div class=&quot;mt-8 grid grid-cols-1 md:grid-cols-3 gap-4&quot;>
                                    <div class=&quot;p-4 bg-gray-50 rounded-lg&quot;>
                                        <div class=&quot;flex items-center justify-between mb-2&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>Cart Abandonment</span>
                                            <span class=&quot;text-sm font-medium text-red-500&quot;>33.4%</span>
                                        </div>
                                        <div class=&quot;flex items-center justify-between&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs. Industry Avg: 69.8%</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>Better by 36.4%</span>
                                        </div>
                                    </div>
                                    
                                    <div class=&quot;p-4 bg-gray-50 rounded-lg&quot;>
                                        <div class=&quot;flex items-center justify-between mb-2&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>Checkout Conversion</span>
                                            <span class=&quot;text-sm font-medium text-green-500&quot;>64.3%</span>
                                        </div>
                                        <div class=&quot;flex items-center justify-between&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs. Industry Avg: 45.2%</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>Better by 19.1%</span>
                                        </div>
                                    </div>
                                    
                                    <div class=&quot;p-4 bg-gray-50 rounded-lg&quot;>
                                        <div class=&quot;flex items-center justify-between mb-2&quot;>
                                            <span class=&quot;text-sm font-medium text-gray-700&quot;>Overall Conversion</span>
                                            <span class=&quot;text-sm font-medium text-green-500&quot;>18.0%</span>
                                        </div>
                                        <div class=&quot;flex items-center justify-between&quot;>
                                            <span class=&quot;text-xs text-gray-500&quot;>vs. Industry Avg: 3.2%</span>
                                            <span class=&quot;text-xs font-medium text-green-500&quot;>Better by 14.8%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    
        <script id=&quot;sidebar-toggle-script&quot;>
            document.addEventListener('DOMContentLoaded', function() {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('main-content');
                const toggleButton = document.getElementById('toggle-sidebar');
                const sidebarTexts = document.querySelectorAll('.sidebar-text');
                
                toggleButton.addEventListener('click', function() {
                    if (sidebar.classList.contains('sidebar-expanded')) {
                        // Collapse sidebar
                        sidebar.classList.remove('sidebar-expanded');
                        sidebar.classList.add('sidebar-collapsed');
                        mainContent.classList.remove('main-collapsed');
                        mainContent.classList.add('main-expanded');
                        toggleButton.innerHTML = '<i class=&quot;ri-menu-unfold-line ri-lg&quot;></i>';
                        
                        // Hide text elements
                        sidebarTexts.forEach(text => {
                            text.style.display = 'none';
                        });
                    } else {
                        // Expand sidebar
                        sidebar.classList.remove('sidebar-collapsed');
                        sidebar.classList.add('sidebar-expanded');
                        mainContent.classList.remove('main-expanded');
                        mainContent.classList.add('main-collapsed');
                        toggleButton.innerHTML = '<i class=&quot;ri-menu-fold-line ri-lg&quot;></i>';
                        
                        // Show text elements
                        sidebarTexts.forEach(text => {
                            text.style.display = 'block';
                        });
                    }
                });
            });
        </script>
    
        <script id=&quot;dropdown-script&quot;>
            document.addEventListener('DOMContentLoaded', function() {
                // User dropdown
                const userMenuButton = document.getElementById('user-menu-button');
                const userDropdown = document.getElementById('user-dropdown');
                
                userMenuButton.addEventListener('click', function() {
                    userDropdown.classList.toggle('hidden');
                });
                
                // Date range dropdown
                const dateRangeButton = document.getElementById('date-range-button');
                const dateRangeDropdown = document.getElementById('date-range-dropdown');
                
                dateRangeButton.addEventListener('click', function() {
                    dateRangeDropdown.classList.toggle('hidden');
                });
                
                // Comparison dropdown
                const comparisonButton = document.getElementById('comparison-button');
                const comparisonDropdown = document.getElementById('comparison-dropdown');
                
                comparisonButton.addEventListener('click', function() {
                    comparisonDropdown.classList.toggle('hidden');
                });
                
                // Export dropdown
                const exportButton = document.getElementById('export-button');
                const exportDropdown = document.getElementById('export-dropdown');
                
                exportButton.addEventListener('click', function() {
                    exportDropdown.classList.toggle('hidden');
                });
                
                // Product filter dropdown
                const productFilterButton = document.getElementById('product-filter-button');
                const productFilterDropdown = document.getElementById('product-filter-dropdown');
                
                if (productFilterButton &amp;&amp; productFilterDropdown) {
                    productFilterButton.addEventListener('click', function() {
                        productFilterDropdown.classList.toggle('hidden');
                    });
                }
                
                // Close dropdowns when clicking outside
                document.addEventListener('click', function(event) {
                    if (userMenuButton &amp;&amp; userDropdown) {
                        if (!userMenuButton.contains(event.target) &amp;&amp; !userDropdown.contains(event.target)) {
                            userDropdown.classList.add('hidden');
                        }
                    }
                    
                    if (dateRangeButton &amp;&amp; dateRangeDropdown) {
                        if (!dateRangeButton.contains(event.target) &amp;&amp; !dateRangeDropdown.contains(event.target)) {
                            dateRangeDropdown.classList.add('hidden');
                        }
                    }
                    
                    if (comparisonButton &amp;&amp; comparisonDropdown) {
                        if (!comparisonButton.contains(event.target) &amp;&amp; !comparisonDropdown.contains(event.target)) {
                            comparisonDropdown.classList.add('hidden');
                        }
                    }
                    
                    if (exportButton &amp;&amp; exportDropdown) {
                        if (!exportButton.contains(event.target) &amp;&amp; !exportDropdown.contains(event.target)) {
                            exportDropdown.classList.add('hidden');
                        }
                    }
                    
                    if (productFilterButton &amp;&amp; productFilterDropdown) {
                        if (!productFilterButton.contains(event.target) &amp;&amp; !productFilterDropdown.contains(event.target)) {
                            productFilterDropdown.classList.add('hidden');
                        }
                    }
                });
            });
        </script>
    
        <script id=&quot;charts-script&quot;>
            document.addEventListener('DOMContentLoaded', function() {
                // Revenue Chart
                const revenueChart = echarts.init(document.getElementById('revenue-chart'));
                const revenueOption = {
                    animation: false,
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        borderColor: '#e5e7eb',
                        textStyle: {
                            color: '#1f2937'
                        }
                    },
                    legend: {
                        data: ['Current Period', 'Previous Period'],
                        bottom: 0,
                        textStyle: {
                            color: '#6b7280'
                        }
                    },
                    grid: {
                        top: 10,
                        right: 10,
                        bottom: 30,
                        left: 40
                    },
                    xAxis: {
                        type: 'category',
                        data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                        axisLine: {
                            lineStyle: {
                                color: '#e5e7eb'
                            }
                        },
                        axisLabel: {
                            color: '#6b7280'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            color: '#6b7280'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#f3f4f6'
                            }
                        }
                    },
                    series: [
                        {
                            name: 'Current Period',
                            type: 'line',
                            smooth: true,
                            data: [8400, 9200, 10800, 12400, 14200, 15800, 17200, 18800, 20200, 21800, 23400, 25200],
                            lineStyle: {
                                color: 'rgba(87, 181, 231, 1)'
                            },
                            symbol: 'none',
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: 'rgba(87, 181, 231, 0.2)'
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(87, 181, 231, 0.05)'
                                    }
                                ])
                            }
                        },
                        {
                            name: 'Previous Period',
                            type: 'line',
                            smooth: true,
                            data: [7200, 8000, 9400, 10800, 12200, 13600, 15000, 16400, 17800, 19200, 20600, 22000],
                            lineStyle: {
                                color: 'rgba(141, 211, 199, 1)'
                            },
                            symbol: 'none',
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: 'rgba(141, 211, 199, 0.2)'
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(141, 211, 199, 0.05)'
                                    }
                                ])
                            }
                        }
                    ]
                };
                revenueChart.setOption(revenueOption);
                
                // User Type Chart
                const userTypeChart = echarts.init(document.getElementById('user-type-chart'));
                const userTypeOption = {
                    animation: false,
                    tooltip: {
                        trigger: 'item',
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        borderColor: '#e5e7eb',
                        textStyle: {
                            color: '#1f2937'
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        right: 10,
                        top: 'center',
                        textStyle: {
                            color: '#6b7280'
                        }
                    },
                    series: [
                        {
                            name: 'User Type',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            center: ['40%', '50%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 8,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: false
                            },
                            emphasis: {
                                label: {
                                    show: false
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: [
                                { value: 6586, name: 'Returning Users', itemStyle: { color: 'rgba(87, 181, 231, 1)' } },
                                { value: 2156, name: 'New Users', itemStyle: { color: 'rgba(141, 211, 199, 1)' } }
                            ]
                        }
                    ]
                };
                userTypeChart.setOption(userTypeOption);
                
                // Category Chart
                const categoryChart = echarts.init(document.getElementById('category-chart'));
                const categoryOption = {
                    animation: false,
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        borderColor: '#e5e7eb',
                        textStyle: {
                            color: '#1f2937'
                        }
                    },
                    grid: {
                        top: 10,
                        right: 10,
                        bottom: 20,
                        left: 60
                    },
                    xAxis: {
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: '#e5e7eb'
                            }
                        },
                        axisLabel: {
                            color: '#6b7280'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#f3f4f6'
                            }
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: ['Bags', 'Accessories', 'Home &amp; Kitchen', 'Clothing', 'Electronics'],
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            color: '#6b7280'
                        }
                    },
                    series: [
                        {
                            name: 'Revenue',
                            type: 'bar',
                            data: [21181, 28954, 35127, 42580, 56842],
                            itemStyle: {
                                color: 'rgba(87, 181, 231, 1)',
                                borderRadius: [0, 4, 4, 0]
                            }
                        }
                    ]
                };
                categoryChart.setOption(categoryOption);
                
                // Mini Trend Charts
                const createTrendChart = (id, data, color) => {
                    const chart = echarts.init(document.getElementById(id));
                    const option = {
                        animation: false,
                        grid: {
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0
                        },
                        xAxis: {
                            type: 'category',
                            show: false,
                            data: [1, 2, 3, 4, 5, 6, 7]
                        },
                        yAxis: {
                            type: 'value',
                            show: false
                        },
                        series: [
                            {
                                type: 'line',
                                data: data,
                                showSymbol: false,
                                lineStyle: {
                                    color: color
                                }
                            }
                        ]
                    };
                    chart.setOption(option);
                };
                
                createTrendChart('trend-chart-1', [120, 132, 101, 134, 90, 230, 210], 'rgba(87, 181, 231, 1)');
                createTrendChart('trend-chart-2', [220, 182, 191, 234, 290, 330, 310], 'rgba(87, 181, 231, 1)');
                createTrendChart('trend-chart-3', [150, 232, 201, 154, 190, 330, 410], 'rgba(87, 181, 231, 1)');
                createTrendChart('trend-chart-4', [320, 332, 301, 334, 390, 330, 320], 'rgba(87, 181, 231, 1)');
                createTrendChart('trend-chart-5', [820, 932, 901, 934, 1290, 1330, 1320], 'rgba(87, 181, 231, 1)');
                
                // Resize charts when window size changes
                window.addEventListener('resize', function() {
                    revenueChart.resize();
                    userTypeChart.resize();
                    categoryChart.resize();
                    
                    // Resize trend charts
                    document.querySelectorAll('[id^=&quot;trend-chart-&quot;]').forEach(el => {
                        echarts.getInstanceByDom(el)?.resize();
                    });
                });
            });
        </script>
    
    <script>
          (function () {
      'use strict';
    
      const originalWindowOpen = window.originalWindowOpen ?? window.open;
    
      function processAnchor(url) {
        // 查找锚点，平滑滚动
        const anchor = document.querySelector(url);
        if (anchor) {
          anchor.scrollIntoView({ behavior: 'smooth' });
        }
        // 发送 hashchange 事件
        const event = new Event('hashchange');
        event.newURL = url;
        event.oldURL = window.location.href;
        // 因为预览 iframe 没有 url，所以需要手动设置
        window.location.hash = url;
        window.dispatchEvent(event);
      }
    
      // 重写 window.open
      window.open = function (url, target, features) {
        if (url.startsWith('#')) {
          // 查找锚点，平滑滚动
          processAnchor(url);
          return;
        }
        originalWindowOpen(url, '_blank', features);
        return null;
      };
    })();
    
          
      window.addEventListener('click', (event) => {
        const target = event.target;
        const closest = target.closest('a');
        if (!closest) return;
        if (closest.tagName === 'A') {
          event.preventDefault();
          const href = closest.getAttribute('href');
          if (!href) return;
          // check is void link
          if (['#', 'javascript:void(0)', ''].includes(href)) {
            return;
          }
          // 锚点链接也不处理
          if (href.startsWith('#')) {
            return;
          }
          window.open(closest.href, '_blank');
        }
      });
    
          
      document.addEventListener('submit', (event) => {
        event.preventDefault();
      }, true);
    
        </script></body></html>" style="display: block; transform: scale(0.340972); height: 2140.94px;"></iframe>