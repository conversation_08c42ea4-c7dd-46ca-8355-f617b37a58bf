import React from 'react';
import styles from './PricingComparison.module.css';
import { ComparisonFeature, PricingPlan } from '../types';

interface PricingComparisonProps {
  /**
   * Features to compare
   */
  features: Array<ComparisonFeature | { name: string; description?: string }>;
  
  /**
   * Feature availability by plan
   */
  planFeatures: Record<string, Array<boolean | string>>;
  
  /**
   * Plans data
   */
  plans: PricingPlan[];
  
  /**
   * Optional title override
   */
  title?: string;
  
  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * Comparison table component for the pricing module
 */
export const PricingComparison: React.FC<PricingComparisonProps> = ({
  features,
  planFeatures,
  plans,
  title = 'Compare Plans',
  className = ''
}) => {
  // Group features by category if available
  const featuresByCategory: Record<string, typeof features> = {};
  
  features.forEach(feature => {
    const category = (feature as ComparisonFeature).category || 'Features';
    if (!featuresByCategory[category]) {
      featuresByCategory[category] = [];
    }
    featuresByCategory[category].push(feature);
  });
  
  const categories = Object.keys(featuresByCategory);
  
  return (
    <div className={`${styles.comparisonContainer} ${className}`}>
      <h2 className={styles.comparisonTitle}>{title}</h2>
      
      <div className={styles.tableWrapper}>
        <table className={styles.comparisonTable}>
          <thead>
            <tr>
              <th className={styles.featureHeader}>Feature</th>
              {plans.map(plan => (
                <th key={plan.id} className={styles.planHeader}>
                  {plan.name}
                </th>
              ))}
            </tr>
          </thead>
          
          <tbody>
            {categories.map((category, categoryIndex) => (
              <React.Fragment key={categoryIndex}>
                {categories.length > 1 && (
                  <tr className={styles.categoryRow}>
                    <td colSpan={plans.length + 1} className={styles.categoryName}>
                      {category}
                    </td>
                  </tr>
                )}
                
                {featuresByCategory[category].map((feature, featureIndex) => (
                  <tr key={`${categoryIndex}-${featureIndex}`} className={styles.featureRow}>
                    <td className={styles.featureName}>
                      {feature.name}
                      {feature.description && (
                        <span className={styles.featureDescription}>
                          {feature.description}
                        </span>
                      )}
                    </td>
                    
                    {plans.map(plan => {
                      const featureValue = planFeatures[plan.id]?.[featureIndex];
                      
                      return (
                        <td key={plan.id} className={styles.featureValue}>
                          {typeof featureValue === 'boolean' ? (
                            featureValue ? (
                              <span className={styles.checkmark}>✓</span>
                            ) : (
                              <span className={styles.crossmark}>✕</span>
                            )
                          ) : (
                            <span>{featureValue}</span>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
