import React, { useState, useEffect } from 'react';
import { FiSave, FiX, FiChevronDown, FiChevronRight, FiMessageSquare, FiTrash2, FiZap } from 'react-icons/fi';
import { ElementProperty } from '../ElementProperties/ElementProperties';
import { SelectedElement } from '../ElementSelector/SimpleElementSelector';
import { DirectionIcon } from '../DirectionIcons/DirectionIcons';
import { ColorPicker } from '../ColorPicker/ColorPicker';
import styles from './ChatElementProperties.module.css';

interface ChatElementPropertiesProps {
  selectedElements: SelectedElement[];
  onPropertyChange: (elementId: string, property: ElementProperty) => void;
  onAskAI: (elementId: string) => void;
  onGenerateFunctionality?: (element: SelectedElement) => void;
  onCancel: () => void;
}

export const ChatElementProperties: React.FC<ChatElementPropertiesProps> = ({
  selectedElements,
  onPropertyChange,
  onAskAI,
  onGenerateFunctionality,
  onCancel
}) => {

  // Helper function to extract numeric value from CSS dimension
  const extractNumericValue = (value: string | undefined): string => {
    if (!value) return '0';
    const match = value.match(/^(-?\d+(\.\d+)?)/);
    return match ? match[1] : '0';
  };

  // Helper function to extract color value - simplified to avoid crashes
  const extractColorValue = (value: string | undefined): string => {
    if (!value) return '#ffffff';

    // If it's already a hex color, return it
    if (value.startsWith('#')) return value;

    // If it's rgb/rgba, convert to hex or return a default
    if (value.startsWith('rgb')) {
      try {
        // Simple regex to extract RGB values
        const match = value.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*[\d.]+\s*)?\)/i);
        if (match) {
          const r = parseInt(match[1]).toString(16).padStart(2, '0');
          const g = parseInt(match[2]).toString(16).padStart(2, '0');
          const b = parseInt(match[3]).toString(16).padStart(2, '0');
          return `#${r}${g}${b}`;
        }
      } catch (e) {
        console.error('Error parsing RGB color:', e);
      }
    }

    // For any other case, return a default color
    return '#ffffff';
  };

  // Create a map of element ID to properties - simplified to avoid crashes
  const [elementProperties, setElementProperties] = useState<Record<string, ElementProperty[]>>(() => {
    try {
      return selectedElements.reduce((acc, element) => {
        // Default properties for each element
        acc[element.id] = [
          { name: 'MarginX', value: '0', type: 'number', unit: 'px', icon: 'horizontal' },
          { name: 'MarginY', value: '0', type: 'number', unit: 'px', icon: 'vertical' },
          { name: 'PaddingX', value: '0', type: 'number', unit: 'px', icon: 'horizontal' },
          { name: 'PaddingY', value: '0', type: 'number', unit: 'px', icon: 'vertical' },
          { name: 'Background', value: '#ffffff', type: 'color' },
          { name: 'Border radius', value: 'XL', type: 'select', options: ['None', 'XS', 'SM', 'MD', 'LG', 'XL', 'Full'] }
        ];
        return acc;
      }, {} as Record<string, ElementProperty[]>);
    } catch (e) {
      console.error('Error initializing element properties:', e);
      return {};
    }
  });

  // Track which element's advanced section is expanded
  const [expandedAdvanced, setExpandedAdvanced] = useState<Record<string, boolean>>(
    selectedElements.reduce((acc, element) => {
      acc[element.id] = false;
      return acc;
    }, {} as Record<string, boolean>)
  );

  // Update properties when selected elements change - simplified to avoid crashes
  useEffect(() => {
    try {
      if (selectedElements.length === 0) return;

      // Update element properties based on the selected elements' styles
      const newElementProperties = selectedElements.reduce((acc, element) => {
        try {
          // Get styles from the selected element or use defaults
          const styles = element.styles || {};

          // Calculate margin values with error handling
          let marginXValue = '0';
          let marginYValue = '0';
          let paddingXValue = '0';
          let paddingYValue = '0';
          let backgroundColor = '#ffffff';

          try {
            // Calculate average margin values
            marginXValue = Math.round(
              (parseFloat(extractNumericValue(styles.marginLeft)) +
               parseFloat(extractNumericValue(styles.marginRight))) / 2
            ).toString();

            marginYValue = Math.round(
              (parseFloat(extractNumericValue(styles.marginTop)) +
               parseFloat(extractNumericValue(styles.marginBottom))) / 2
            ).toString();

            // Calculate average padding values
            paddingXValue = Math.round(
              (parseFloat(extractNumericValue(styles.paddingLeft)) +
               parseFloat(extractNumericValue(styles.paddingRight))) / 2
            ).toString();

            paddingYValue = Math.round(
              (parseFloat(extractNumericValue(styles.paddingTop)) +
               parseFloat(extractNumericValue(styles.paddingBottom))) / 2
            ).toString();

            // Extract background color
            backgroundColor = extractColorValue(styles.backgroundColor);
          } catch (e) {
            console.error('Error calculating style values:', e);
          }

          // Set properties with values from the selected element
          acc[element.id] = [
            { name: 'MarginX', value: marginXValue, type: 'number', unit: 'px', icon: 'horizontal' },
            { name: 'MarginY', value: marginYValue, type: 'number', unit: 'px', icon: 'vertical' },
            { name: 'PaddingX', value: paddingXValue, type: 'number', unit: 'px', icon: 'horizontal' },
            { name: 'PaddingY', value: paddingYValue, type: 'number', unit: 'px', icon: 'vertical' },
            { name: 'Background', value: backgroundColor, type: 'color' },
            { name: 'Border radius', value: 'XL', type: 'select', options: ['None', 'XS', 'SM', 'MD', 'LG', 'XL', 'Full'] },
            { name: 'Text Content', value: element.textContent || '', type: 'text' }
          ];
        } catch (e) {
          console.error('Error processing element:', e);
          // Provide default properties if there's an error
          acc[element.id] = [
            { name: 'MarginX', value: '0', type: 'number', unit: 'px', icon: 'horizontal' },
            { name: 'MarginY', value: '0', type: 'number', unit: 'px', icon: 'vertical' },
            { name: 'PaddingX', value: '0', type: 'number', unit: 'px', icon: 'horizontal' },
            { name: 'PaddingY', value: '0', type: 'number', unit: 'px', icon: 'vertical' },
            { name: 'Background', value: '#ffffff', type: 'color' },
            { name: 'Border radius', value: 'XL', type: 'select', options: ['None', 'XS', 'SM', 'MD', 'LG', 'XL', 'Full'] },
            { name: 'Text Content', value: element.textContent || '', type: 'text' }
          ];
        }
        return acc;
      }, {} as Record<string, ElementProperty[]>);

      setElementProperties(newElementProperties);

      // Reset expanded state for new elements
      const newExpandedAdvanced = selectedElements.reduce((acc, element) => {
        acc[element.id] = expandedAdvanced[element.id] || false;
        return acc;
      }, {} as Record<string, boolean>);

      setExpandedAdvanced(newExpandedAdvanced);
    } catch (e) {
      console.error('Error in useEffect:', e);
    }
  }, [selectedElements]);

  // Track advanced CSS for each element - simplified to avoid crashes
  const [advancedCSS, setAdvancedCSS] = useState<Record<string, string>>(() => {
    try {
      return selectedElements.reduce((acc, element) => {
        acc[element.id] = 'min-h: screen flex items: center justify: center bg: gradient-to: br from: slate-50 to: slate-100 bg: gray-800';
        return acc;
      }, {} as Record<string, string>);
    } catch (e) {
      console.error('Error initializing advanced CSS state:', e);
      return {};
    }
  });

  // Track which color picker is open
  const [openColorPicker, setOpenColorPicker] = useState<{
    elementId: string;
    propertyIndex: number;
  } | null>(null);

  // Handle property change for a specific element
  const handlePropertyChange = (elementId: string, propertyIndex: number, value: string) => {
    // Get the property being updated
    const property = elementProperties[elementId][propertyIndex];
    const updatedProperty = { ...property, value };

    // Update local state (optimized to avoid unnecessary re-renders)
    setElementProperties(prev => {
      const updated = { ...prev };
      const elementProps = [...updated[elementId]];
      elementProps[propertyIndex] = updatedProperty;
      updated[elementId] = elementProps;
      return updated;
    });

    // Notify parent component about the change immediately
    // This happens before the state update to make it feel more responsive
    onPropertyChange(elementId, updatedProperty);
  };

  // Toggle advanced section for a specific element
  const toggleAdvanced = (elementId: string) => {
    setExpandedAdvanced(prev => ({
      ...prev,
      [elementId]: !prev[elementId]
    }));
  };

  // Handle advanced CSS change for a specific element
  const handleAdvancedCSSChange = (elementId: string, value: string) => {
    // Update local state
    setAdvancedCSS(prev => ({
      ...prev,
      [elementId]: value
    }));

    // Notify parent component about the change immediately
    onPropertyChange(elementId, {
      name: 'advancedCSS',
      value: value,
      type: 'text'
    });
  };

  // Add a function to handle asking AI to modify the element
  const handleAskAI = () => {
    // Let the parent component handle the AI request
    onAskAI(selectedElements[0].id);
  };

  // Safety check - if no elements are selected, show a message
  if (!selectedElements || selectedElements.length === 0) {
    return (
      <div className={styles.chatPropertiesContainer}>
        <div className={styles.header}>
          <div className={styles.elementInfo}>
            <div className={styles.elementTag}>
              <span className={styles.tagIcon}></span>
              <span className={styles.tagName}>No element selected</span>
            </div>
          </div>
          <div className={styles.headerActions}>
            <button className={`${styles.actionButton} ${styles.closeButton}`} onClick={onCancel} title="Close">
              <FiX size={18} />
            </button>
          </div>
        </div>
        <div className={styles.elementsContainer}>
          <div className={styles.noElementsMessage}>
            Please select an element to edit its properties.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.chatPropertiesContainer}>
      <div className={styles.header}>
        <div className={styles.elementInfo}>
          <div className={styles.elementTag}>
            <span className={styles.tagIcon}></span>
            <span className={styles.tagName}>{selectedElements[0]?.tagName || 'div'}</span>
          </div>
        </div>
        <div className={styles.headerActions}>
          <button className={styles.actionButton} onClick={onCancel} title="Discard changes">
            <FiTrash2 size={16} />
          </button>
          <button className={`${styles.actionButton} ${styles.closeButton}`} onClick={onCancel} title="Close">
            <FiX size={18} />
          </button>
        </div>
      </div>

      <div className={styles.elementsContainer}>
        {selectedElements.map(element => (
          <div key={element.id} className={styles.elementSection}>
            <div className={styles.propertiesContainer}>
              {/* Group margin properties */}
              <div className={styles.propertyGroup}>
                <label className={styles.propertyLabel}>Margin</label>
                <div className={styles.propertyInputGroup}>
                  {elementProperties[element.id] ?
                    elementProperties[element.id]
                      .filter(prop => prop.name === 'MarginX' || prop.name === 'MarginY')
                      .map((property, index) => (
                      <div key={index} className={styles.propertyInputWithIcon}>
                        <DirectionIcon
                          direction={property.icon || 'horizontal'}
                          className={styles.directionIcon}
                        />
                        <input
                          type="number"
                          value={property.value}
                          onChange={(e) => {
                            const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                            handlePropertyChange(element.id, propIndex, e.target.value);
                          }}
                          onBlur={(e) => {
                            const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                            handlePropertyChange(element.id, propIndex, e.target.value);
                          }}
                          className={styles.numberInput}
                        />
                      </div>
                    )) : <div>Loading properties...</div>}
                </div>
              </div>

              {/* Group padding properties */}
              <div className={styles.propertyGroup}>
                <label className={styles.propertyLabel}>Padding</label>
                <div className={styles.propertyInputGroup}>
                  {elementProperties[element.id] ?
                    elementProperties[element.id]
                      .filter(prop => prop.name === 'PaddingX' || prop.name === 'PaddingY')
                      .map((property, index) => (
                      <div key={index} className={styles.propertyInputWithIcon}>
                        <DirectionIcon
                          direction={property.icon || 'horizontal'}
                          className={styles.directionIcon}
                        />
                        <input
                          type="number"
                          value={property.value}
                          onChange={(e) => {
                            const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                            handlePropertyChange(element.id, propIndex, e.target.value);
                          }}
                          onBlur={(e) => {
                            const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                            handlePropertyChange(element.id, propIndex, e.target.value);
                          }}
                          className={styles.numberInput}
                        />
                      </div>
                    )) : <div>Loading properties...</div>}
                </div>
              </div>

              {/* Background color */}
              {elementProperties[element.id] ?
                elementProperties[element.id]
                  .filter(prop => prop.name === 'Background')
                  .map((property, index) => {
                    const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                    return (
                    <div key={index} className={styles.propertyRow}>
                      <label className={styles.propertyLabel}>{property.name}</label>
                      <div className={styles.colorInputContainer}>
                        <div
                          className={styles.colorPreview}
                          style={{ backgroundColor: property.value }}
                          onClick={() => setOpenColorPicker({ elementId: element.id, propertyIndex: propIndex })}
                        />
                        <input
                          type="text"
                          value={property.value}
                          onChange={(e) => handlePropertyChange(element.id, propIndex, e.target.value)}
                          onBlur={(e) => handlePropertyChange(element.id, propIndex, e.target.value)}
                          onClick={() => setOpenColorPicker({ elementId: element.id, propertyIndex: propIndex })}
                          className={styles.colorText}
                        />

                        {/* Color Picker */}
                        {openColorPicker &&
                         openColorPicker.elementId === element.id &&
                         openColorPicker.propertyIndex === propIndex && (
                          <>
                            <div className={styles.colorPickerBackdrop} onClick={() => setOpenColorPicker(null)} />
                            <div className={styles.colorPickerWrapper}>
                              <ColorPicker
                                value={property.value}
                                onChange={(color) => {
                                  handlePropertyChange(element.id, propIndex, color);
                                  setOpenColorPicker(null);
                                }}
                                onClose={() => setOpenColorPicker(null)}
                              />
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  );
                }) : <div>Loading properties...</div>}

              {/* Border radius */}
              {elementProperties[element.id] ?
                elementProperties[element.id]
                  .filter(prop => prop.name === 'Border radius')
                  .map((property, index) => {
                    const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                    return (
                    <div key={index} className={styles.propertyRow}>
                      <label className={styles.propertyLabel}>{property.name}</label>
                      <select
                        value={property.value}
                        onChange={(e) => handlePropertyChange(element.id, propIndex, e.target.value)}
                        className={styles.selectInput}
                      >
                        {property.options?.map((option) => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                    </div>
                  );
                }) : <div>Loading properties...</div>}

              {/* Text Content */}
              {elementProperties[element.id] ?
                elementProperties[element.id]
                  .filter(prop => prop.name === 'Text Content')
                  .map((property, index) => {
                    const propIndex = elementProperties[element.id].findIndex(p => p.name === property.name);
                    return (
                    <div key={index} className={styles.propertyRow}>
                      <label className={styles.propertyLabel}>{property.name}</label>
                      <textarea
                        value={property.value}
                        onChange={(e) => handlePropertyChange(element.id, propIndex, e.target.value)}
                        onBlur={(e) => handlePropertyChange(element.id, propIndex, e.target.value)}
                        className={styles.textareaInput}
                        rows={3}
                      />
                    </div>
                  );
                }) : <div>Loading properties...</div>}

              {/* Advanced Section */}
              {/* <div className={styles.advancedSection}>
                <div
                  className={styles.advancedHeader}
                  onClick={() => toggleAdvanced(element.id)}
                >
                  {expandedAdvanced[element.id] ? <FiChevronDown /> : <FiChevronRight />}
                  <span className={styles.advancedTitle}>Advanced</span>
                </div>

                {expandedAdvanced[element.id] && advancedCSS[element.id] && (
                  <div className={styles.advancedContent}>
                    <textarea
                      className={styles.cssTextarea}
                      value={advancedCSS[element.id]}
                      onChange={(e) => handleAdvancedCSSChange(element.id, e.target.value)}
                      onBlur={(e) => handleAdvancedCSSChange(element.id, e.target.value)}
                      placeholder="Enter CSS properties (e.g. color: red; font-size: 16px;)"
                      rows={4}
                      spellCheck="false"
                    />
                  </div>
                )}
              </div> */}
            </div>
          </div>
        ))}
      </div>

      <div className={styles.actions}>
        {/* Debug info - remove in production */}
        <div style={{ fontSize: '10px', color: '#666', marginBottom: '5px' }}>
          Element needs functionality: {selectedElements[0]?.needsFunctionality ? 'Yes' : 'No'}
        </div>
        {/* Show Generate Functionality button only if the element needs functionality */}
        {selectedElements[0]?.needsFunctionality && onGenerateFunctionality && (
          <button
            className={`${styles.askAIButton} ${styles.generateFunctionalityButton}`}
            onClick={() => onGenerateFunctionality(selectedElements[0])}
          >
            <FiZap />
            <span>Generate Functionality for {selectedElements[0]?.tagName || 'element'}</span>
          </button>
        )}
        {/* <button className={styles.askAIButton} onClick={handleAskAI}>
          <FiMessageSquare />
          <span>Ask JustPrototype to modify {selectedElements[0]?.tagName || 'element'}</span>
        </button> */}



        {/* Always show button during development for testing */}
        {!selectedElements[0]?.needsFunctionality && onGenerateFunctionality && (
          <button
            className={`${styles.askAIButton} ${styles.generateFunctionalityButton}`}
            style={{ opacity: 0.5 }}
            onClick={() => {
              // Create a copy with needsFunctionality set to true for testing
              const testElement = {...selectedElements[0], needsFunctionality: true, functionalityType: 'button'};
              onGenerateFunctionality(testElement);
            }}
          >
            <FiZap />
            <span>(Test) Generate Functionality</span>
          </button>
        )}
      </div>
    </div>
  );
};
