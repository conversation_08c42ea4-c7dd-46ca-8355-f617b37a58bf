import Ajv from 'ajv/dist/ajv.js';
import addFormats from 'ajv-formats/dist/index.js';
import type { ErrorObject } from 'ajv';
import { UINode } from '../types/uiNode';

let uiNodeSchema: any = null;

async function fetchSchema() {
  if (uiNodeSchema) return uiNodeSchema;
  
  try {
    const response = await fetch('/api/schema/uiNode', {
      credentials: 'include'
    });
    if (!response.ok) throw new Error('Failed to fetch schema');
    uiNodeSchema = await response.json();
    return uiNodeSchema;
  } catch (err) {
    console.error('Error fetching schema:', err);
    throw err;
  }
}

/**
 * Service for validating UI nodes against the JSON schema
 */
class SchemaValidator {
  private ajv: Ajv;
  private initialized: boolean = false;

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      verbose: true,
      strict: true
    });
    
    // Add formats support
    addFormats(this.ajv);
  }

  /**
   * Initialize schema validator by fetching schema
   */
  private async init(): Promise<void> {
    if (this.initialized) return;
    
    try {
      const schema = await fetchSchema();
      this.ajv.addSchema(schema, 'uiNode');
      this.initialized = true;
    } catch (err) {
      console.error('Error initializing schema validator:', err);
      throw err;
    }
  }

  /**
   * Validate a UI node against the schema
   */
  async validateNode(node: UINode): Promise<{ valid: boolean; errors: string[] }> {
    await this.init();
    const validate = this.ajv.getSchema('uiNode');
    
    if (!validate) {
      return {
        valid: false,
        errors: ['Schema not loaded']
      };
    }

    const valid = validate(node);
    const errors = validate.errors
      ? validate.errors.map((err: ErrorObject) => `${err.instancePath} ${err.message}`)
      : [];

    return {
      valid: !!valid,
      errors
    };
  }

  /**
   * Validate an entire AST tree recursively
   */
  async validateTree(node: UINode): Promise<{ valid: boolean; errors: string[] }> {
    const nodeResult = await this.validateNode(node);
    const allErrors = [...nodeResult.errors];

    if (node.children) {
      for (const child of node.children) {
        const childResult = await this.validateTree(child);
        allErrors.push(...childResult.errors);
      }
    }

    return {
      valid: allErrors.length === 0,
      errors: allErrors
    };
  }

  /**
   * Check if a node type is valid
   */
  async isValidNodeType(type: string): Promise<boolean> {
    await this.init();
    const schema = this.ajv.getSchema('uiNode');
    if (!schema) return false;

    const allowedTypes = [
      'Page',
      'Section',
      'Button',
      'Input',
      'Text',
      'Image',
      'Link',
      'Form'
    ];

    return allowedTypes.includes(type);
  }
}

// Export singleton instance
export const schemaValidator = new SchemaValidator();
