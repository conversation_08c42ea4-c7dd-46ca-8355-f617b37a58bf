/**
 * Helper functions for LLM operations
 */
const { useTokensAndLog } = require('./billingService');
const promptDbService = require('./promptDbService');
const { validateStreamedHTML } = require('./htmlValidationService');

/**
 * Helper function to log token usage
 * @param {Object} req - Express request object
 * @param {string} event - Event type (e.g., 'llm_plan', 'llm_code')
 * @param {string} inputText - The input text (e.g., prompt, plan)
 * @param {string} outputText - The output text (e.g., features, code)
 * @param {string} context - Context information
 * @param {boolean} isStreaming - Whether this is a streaming request
 * @returns {Promise<boolean>} - Success status
 */
async function logTokenUsage(req, event, inputText, outputText, context, isStreaming = false) {
  if (!req.user || !req.user.id) {
    console.log(`[TokenLog] ${isStreaming ? '[stream] ' : ''}Skipping token usage logging: No authenticated user`);
    return false;
  }

  // Estimate tokens (1 token per 4 chars as a rough proxy)
  const tokensUsed = Math.ceil(inputText.length / 4) + Math.ceil(outputText.length / 4);
  console.log(`[TokenLog] ${isStreaming ? '[stream] ' : ''}Logging token usage for user ${req.user.id}: ${tokensUsed} tokens, event=${event}`);

  try {
    // Prepare details object based on event type and streaming status
    let details = {};
    if (isStreaming) {
      details = { streamedSample: outputText.slice(0, 200) };
    } else {
      if (event === 'llm_plan') {
        details = { features: outputText };
      } else if (event === 'llm_code') {
        details = { codeSample: outputText.slice(0, 200) };
      } else if (event.includes('modify')) {
        details = { modifiedSample: outputText.slice(0, 200) };
      } else {
        details = { outputSample: outputText.slice(0, 200) };
      }
    }

    const success = await useTokensAndLog({
      userId: req.user.dbId || req.user.id,
      tokensUsed: Math.ceil(tokensUsed),
      event: event,
      context: context,
      details: JSON.stringify(details)
    }, req);

    console.log(`[TokenLog] ${isStreaming ? '[stream] ' : ''}Token usage logged for user ${req.user.id}, success: ${success}`);
    return success;
  } catch (error) {
    console.error(`[TokenLog] Error logging token usage: ${error.message}`);
    console.error(`[TokenLog] Full error:`, error);
    return false;
  }
}

/**
 * Helper function to save prompt and its first iteration
 * @param {Object} req - Express request object
 * @param {string} promptText - The prompt text
 * @param {string} outputText - The output text (e.g., features, code)
 * @param {number} iterationNumber - The iteration number (default: 1)
 * @returns {Promise<number|null>} - Prompt ID or null if failed
 */
async function savePromptAndIteration(req, promptText, outputText, iterationNumber = 1) {
  if (!req.user || !req.user.id) {
    console.log(`[PromptLog] Skipping prompt save: No authenticated user`);
    return null;
  }

  try {
    console.log('[DEBUG] Starting prompt save process');
    const userId = req.user.dbId || req.user.id;
    console.log(`[DEBUG] Using user ID: ${userId} for prompt save`);

    // Make sure userId is a valid integer
    let userIdInt;
    try {
      userIdInt = parseInt(userId, 10);
      if (isNaN(userIdInt)) {
        throw new Error(`Invalid user ID: ${userId}`);
      }
      console.log(`[DEBUG] Converted user ID to integer: ${userIdInt}`);
    } catch (parseError) {
      console.error(`[DEBUG] Error parsing user ID: ${parseError.message}`);
      userIdInt = userId; // Use as-is and let the service handle it
    }

    console.log('[DEBUG] Calling promptDbService.savePrompt with:', {
      user_id: userIdInt,
      prompt_text: promptText.substring(0, 50) + '...' // Log just the beginning for privacy
    });

    // Call the savePrompt function with a timeout to prevent hanging
    const savePromptPromise = promptDbService.savePrompt({
      user_id: userIdInt,
      prompt_text: promptText
    });

    // Set a timeout for the savePrompt operation
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Timeout saving prompt')), 5000);
    });

    // Race the savePrompt operation against the timeout
    const promptId = await Promise.race([savePromptPromise, timeoutPromise]);

    console.log(`[DEBUG] Prompt saved successfully with ID: ${promptId}`);

    // Only proceed with saving the iteration if we have a valid promptId
    if (promptId && promptId > 0) {
      // Save the initial response as the first iteration
      console.log(`[DEBUG] Saving iteration #${iterationNumber} with prompt ID: ${promptId}`);
      await promptDbService.savePromptIteration({
        prompt_id: promptId,
        iteration_number: iterationNumber,
        input_text: promptText,
        output_text: outputText
      });

      console.log(`[PromptLog] Saved prompt (ID: ${promptId}) and iteration #${iterationNumber} to database`);
      return promptId;
    } else {
      console.error(`[PromptLog] Invalid promptId: ${promptId}, skipping iteration save`);
      return null;
    }
  } catch (error) {
    console.error(`[PromptLog] Error saving prompt to database: ${error.message}`);
    console.error('[DEBUG] Full error object:', error);

    // Check if it's a database connection issue
    if (error.code) {
      console.error(`[DEBUG] Database error code: ${error.code}`);
    }

    return null;
  }
}

/**
 * Helper function to save a prompt iteration
 * @param {number} promptId - The prompt ID
 * @param {string} inputText - The input text
 * @param {string} outputText - The output text
 * @param {number} iterationNumber - The iteration number
 * @returns {Promise<number|null>} - Iteration ID or null if failed
 */
async function savePromptIteration(promptId, inputText, outputText, iterationNumber) {
  if (!promptId || promptId <= 0) {
    console.log(`[PromptLog] Skipping iteration save: Invalid prompt ID ${promptId}`);
    return null;
  }

  try {
    console.log(`[DEBUG] Saving iteration #${iterationNumber} for prompt ID: ${promptId}`);
    const iterationId = await promptDbService.savePromptIteration({
      prompt_id: promptId,
      iteration_number: iterationNumber,
      input_text: inputText,
      output_text: outputText
    });

    console.log(`[PromptLog] Saved iteration #${iterationNumber} for prompt ID: ${promptId}, iteration ID: ${iterationId}`);
    return iterationId;
  } catch (error) {
    console.error(`[PromptLog] Error saving prompt iteration: ${error.message}`);
    return null;
  }
}

/**
 * Helper function to set up streaming response
 * @param {Object} res - Express response object
 * @param {string} contentType - Content type (default: 'text/plain')
 * @returns {Function} - Function to capture streamed content
 */
function setupStreamingResponse(res, contentType = 'text/plain') {
  // Remove Content-Length header if it exists to avoid conflicts with Transfer-Encoding
  if (res.hasHeader('Content-Length')) {
    res.removeHeader('Content-Length');
    console.log('[setupStreamingResponse] Removed Content-Length header to avoid conflict with Transfer-Encoding');
  }

  // Set streaming headers
  res.setHeader('Content-Type', `${contentType}; charset=utf-8`);
  res.setHeader('Transfer-Encoding', 'chunked');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  console.log('[setupStreamingResponse] Set up streaming response headers');

  // Set up content capture for logging
  let streamedContent = '';
  const originalWrite = res.write.bind(res);
  res.write = (chunk, ...args) => {
    if (!chunk) return originalWrite.apply(res, args);

    try {
      // Add to our content buffer for logging
      const chunkStr = chunk.toString();
      streamedContent += chunkStr;

      // Write to the response
      return originalWrite.call(res, chunk, ...args);
    } catch (err) {
      console.error('[setupStreamingResponse] Error in write override:', err);
      // Try to continue with original write
      return originalWrite.call(res, chunk, ...args);
    }
  };

  return () => {
    // Validate HTML content if this is an HTML response
    if (contentType === 'text/html') {
      try {
        const validationResults = validateStreamedHTML(streamedContent);

        // Log validation results
        console.log(`[HTML Validation] Results: ${validationResults.isValid ? 'VALID' : 'INVALID'}`);
        if (validationResults.errors.length > 0) {
          console.error(`[HTML Validation] Errors (${validationResults.errors.length}):`);
          validationResults.errors.forEach(error => console.error(`  - ${error}`));
        }
        if (validationResults.warnings.length > 0) {
          console.warn(`[HTML Validation] Warnings (${validationResults.warnings.length}):`);
          validationResults.warnings.forEach(warning => console.warn(`  - ${warning}`));
        }
      } catch (error) {
        console.error('[HTML Validation] Error during validation:', error);
      }
    }
    return streamedContent;
  };
}

module.exports = {
  logTokenUsage,
  savePromptAndIteration,
  savePromptIteration,
  setupStreamingResponse
};
