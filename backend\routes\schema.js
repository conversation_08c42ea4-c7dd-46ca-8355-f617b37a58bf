const express = require('express');
const router = express.Router();
const path = require('path');

/**
 * @swagger
 * /api/schema/uiNode:
 *   get:
 *     summary: Get UI Node JSON Schema
 *     description: Returns the JSON Schema definition for UI nodes
 *     tags: [Schema]
 *     responses:
 *       200:
 *         description: JSON Schema for UI nodes
 *         content:
 *           application/schema+json:
 *             schema:
 *               $ref: '#/components/schemas/UINode'
 */
router.get('/uiNode', (req, res) => {
  res.setHeader('Content-Type', 'application/schema+json');
  res.sendFile(path.join(__dirname, '../schemas/uiNode.schema.json'));
});

module.exports = router;
