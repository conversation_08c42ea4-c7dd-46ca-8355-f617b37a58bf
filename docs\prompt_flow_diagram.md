# JustPrototype Application Flow Diagram

This document illustrates the flow of data through the JustPrototype application, from the UI to the database.

## Prompt Generation and Saving Flow

```mermaid
flowchart TD
    subgraph "Frontend (React)"
        UI[User Interface] --> |User enters prompt| Form
        Form --> |Submit| API_Call[API Call]
        API_Call --> |POST /api/llm/plan| Backend
    end

    subgraph "Backend (Node.js)"
        Backend[Express Server] --> |Route| Controller
        Controller[llmController.js] --> |generatePlan| LLM_Service
        LLM_Service[llmService.js] --> |Call External API| LLM_Provider[LLM Provider API]
        LLM_Provider --> |Return response| LLM_Service
        LLM_Service --> |Return features| Controller
        
        Controller --> |Log token usage| Billing_Service[billingService.js]
        Billing_Service --> |useTokensAndLog| Database
        
        Controller --> |Save prompt| Helper_Module[llmHelpers.js]
        Helper_Module --> |savePromptAndIteration| Prompt_Service[promptDbService.js]
        Prompt_Service --> |savePrompt| Database
        Prompt_Service --> |savePromptIteration| Database
        
        Controller --> |Return response| Backend
    end

    subgraph "Database (PostgreSQL)"
        Database[(PostgreSQL DB)]
        Database --> |users table| Users[Users Data]
        Database --> |prompts table| Prompts[Prompts Data]
        Database --> |prompt_iterations table| Iterations[Prompt Iterations]
        Database --> |usage_log table| Usage[Token Usage Logs]
    end

    Backend --> |JSON response| UI
```

## Streaming vs Non-Streaming Flow

```mermaid
flowchart TD
    Start[User submits prompt] --> Check{Streaming?}
    
    Check -->|Yes| Stream_Setup[Set up streaming response]
    Stream_Setup --> Stream_Call[Call LLM with streaming]
    Stream_Call --> Stream_Response[Stream response to client]
    Stream_Response --> Stream_End[End response]
    Stream_End --> Stream_Log[Log token usage]
    Stream_Log --> Stream_Save[Save prompt and iteration]
    
    Check -->|No| NonStream_Call[Call LLM without streaming]
    NonStream_Call --> NonStream_Response[Get complete response]
    NonStream_Response --> NonStream_Log[Log token usage]
    NonStream_Log --> NonStream_Save[Save prompt and iteration]
    NonStream_Save --> NonStream_Return[Return JSON response]
```

## Token Usage Logging Flow

```mermaid
flowchart TD
    Start[logTokenUsage called] --> Check{User authenticated?}
    
    Check -->|No| Skip[Skip logging]
    Skip --> Return_False[Return false]
    
    Check -->|Yes| Calculate[Calculate tokens used]
    Calculate --> Prepare[Prepare details object]
    Prepare --> Call_DB[Call useTokensAndLog]
    Call_DB --> DB[(Database)]
    Call_DB --> Log[Log result]
    Log --> Return_Result[Return success status]
```

## Prompt Saving Flow

```mermaid
flowchart TD
    Start[savePromptAndIteration called] --> Check{User authenticated?}
    
    Check -->|No| Skip[Skip saving]
    Skip --> Return_Null[Return null]
    
    Check -->|Yes| Parse[Parse user ID]
    Parse --> Call_Save[Call savePrompt]
    Call_Save --> Timeout{Timeout?}
    
    Timeout -->|Yes| Error[Log error]
    Error --> Return_Null2[Return null]
    
    Timeout -->|No| Check_ID{Valid promptId?}
    
    Check_ID -->|No| Error2[Log error]
    Error2 --> Return_Null3[Return null]
    
    Check_ID -->|Yes| Save_Iteration[Save prompt iteration]
    Save_Iteration --> Log[Log success]
    Log --> Return_ID[Return promptId]
```

## Database Schema

```mermaid
erDiagram
    USERS {
        int id PK
        string email
        string display_name
        string google_id
        int quota_tokens
        int token_usage
        int prototype_count
        timestamp created_at
    }
    
    PROMPTS {
        int id PK
        int user_id FK
        text prompt_text
        timestamp created_at
    }
    
    PROMPT_ITERATIONS {
        int id PK
        int prompt_id FK
        int iteration_number
        text input_text
        text output_text
        timestamp created_at
    }
    
    USAGE_LOG {
        int id PK
        int user_id FK
        string event
        int tokens_used
        text context
        jsonb details
        timestamp created_at
    }
    
    USERS ||--o{ PROMPTS : creates
    USERS ||--o{ USAGE_LOG : generates
    PROMPTS ||--o{ PROMPT_ITERATIONS : has
```

## Function Call Sequence Diagram

```mermaid
sequenceDiagram
    participant UI as User Interface
    participant API as API Client
    participant Controller as llmController.js
    participant LLM as llmService.js
    participant Helper as llmHelpers.js
    participant DB as Database
    
    UI->>API: Submit prompt
    API->>Controller: POST /api/llm/plan
    Controller->>LLM: generateFeaturePlan(prompt)
    LLM->>LLM: Call LLM Provider API
    LLM-->>Controller: Return features
    
    Controller->>Helper: logTokenUsage(...)
    Helper->>DB: Log token usage
    DB-->>Helper: Return success status
    Helper-->>Controller: Return success status
    
    Controller->>Helper: savePromptAndIteration(...)
    Helper->>DB: Save prompt
    DB-->>Helper: Return promptId
    Helper->>DB: Save prompt iteration
    DB-->>Helper: Return iterationId
    Helper-->>Controller: Return promptId
    
    Controller-->>API: Return JSON response
    API-->>UI: Update UI with response
```
