import React, { useRef, useEffect, useState, useCallback } from 'react';
import { FiRefreshCw, FiMaximize2, FiMinimize2 } from 'react-icons/fi';

// ============================================================================
// TYPES
// ============================================================================

interface ReadyAIStylePreviewProps {
  htmlContent: string;
  streamingContent: string;
  isGenerating: boolean;
  onElementClick?: (element: any) => void;
  className?: string;
}

// ============================================================================
// READDY.AI STYLE IFRAME PREVIEW COMPONENT
// ============================================================================

export const ReadyAIStylePreview: React.FC<ReadyAIStylePreviewProps> = ({
  htmlContent,
  streamingContent,
  isGenerating,
  onElementClick,
  className = ''
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastProcessedLength, setLastProcessedLength] = useState(0);
  const [isProcessingUpdate, setIsProcessingUpdate] = useState(false);

  // Use streaming content if available, otherwise use stable content
  const currentContent = streamingContent || htmlContent || '';

  // Enhanced content processing for readdy.ai-style seamless updates
  const processContentUpdate = useCallback(async (content: string) => {
    const iframe = iframeRef.current;
    if (!iframe || !content) return;

    const contentLength = content.length;
    
    // Skip if no new content or already processing
    if (contentLength <= lastProcessedLength || isProcessingUpdate) {
      return;
    }

    setIsProcessingUpdate(true);

    try {
      // Clean the content (remove markdown code blocks)
      const cleanContent = content
        .replace(/```html\s*/g, '')
        .replace(/```\s*/g, '')
        .trim();

      if (!cleanContent) {
        setIsProcessingUpdate(false);
        return;
      }

      // Check if iframe document is ready
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        console.log('🎬 ReadyAI: Iframe document not ready, setting srcdoc');
        iframe.srcdoc = cleanContent;
        setLastProcessedLength(contentLength);
        setIsProcessingUpdate(false);
        return;
      }

      // For incremental updates during streaming
      if (isGenerating && contentLength > lastProcessedLength) {
        console.log('🎬 ReadyAI: Processing incremental update', {
          newLength: contentLength,
          lastProcessed: lastProcessedLength,
          isGenerating
        });

        // Try to update content seamlessly like readdy.ai
        await updateIframeContentSeamlessly(iframeDoc, cleanContent);
      } else {
        // Full content replacement
        console.log('🎬 ReadyAI: Full content replacement');
        iframe.srcdoc = cleanContent;
      }

      setLastProcessedLength(contentLength);
    } catch (error) {
      console.error('🎬 ReadyAI: Error processing content update:', error);
      // Fallback to srcdoc update
      iframe.srcdoc = content;
    } finally {
      setIsProcessingUpdate(false);
    }
  }, [lastProcessedLength, isProcessingUpdate, isGenerating]);

  // Seamless iframe content update function (readdy.ai style)
  const updateIframeContentSeamlessly = async (iframeDoc: Document, newContent: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        // Parse new content
        const parser = new DOMParser();
        const newDoc = parser.parseFromString(newContent, 'text/html');
        
        if (!newDoc.body) {
          // Fallback to srcdoc if parsing fails
          if (iframeRef.current) {
            iframeRef.current.srcdoc = newContent;
          }
          resolve();
          return;
        }

        // Get current body content length
        const currentBodyHTML = iframeDoc.body?.innerHTML || '';
        const newBodyHTML = newDoc.body.innerHTML;

        // If new content is significantly longer, it's likely an incremental update
        if (newBodyHTML.length > currentBodyHTML.length && newBodyHTML.includes(currentBodyHTML)) {
          console.log('🎬 ReadyAI: Detected incremental content, updating seamlessly');
          
          // Extract new content
          const newContentPart = newBodyHTML.substring(currentBodyHTML.length);
          
          if (newContentPart.trim()) {
            // Create temporary container for new content
            const tempDiv = iframeDoc.createElement('div');
            tempDiv.innerHTML = newContentPart;
            
            // Append new elements with smooth animation
            const newElements = Array.from(tempDiv.children);
            
            newElements.forEach((element, index) => {
              // Clone the element to avoid moving it
              const clonedElement = element.cloneNode(true) as HTMLElement;
              
              // Add smooth entrance animation
              clonedElement.style.opacity = '0';
              clonedElement.style.transform = 'translateY(10px)';
              clonedElement.style.transition = 'opacity 300ms ease-out, transform 300ms ease-out';
              
              // Append to body
              iframeDoc.body.appendChild(clonedElement);
              
              // Trigger animation after a small delay
              setTimeout(() => {
                clonedElement.style.opacity = '1';
                clonedElement.style.transform = 'translateY(0)';
                
                // Clean up transition after animation
                setTimeout(() => {
                  clonedElement.style.transition = '';
                }, 300);
              }, index * 50); // Stagger animations
            });
          }
        } else {
          // Full replacement with smooth transition
          console.log('🎬 ReadyAI: Full content replacement with transition');
          
          // Fade out current content
          if (iframeDoc.body) {
            iframeDoc.body.style.transition = 'opacity 200ms ease-out';
            iframeDoc.body.style.opacity = '0';
            
            setTimeout(() => {
              // Replace content
              iframeDoc.body.innerHTML = newBodyHTML;
              
              // Fade in new content
              iframeDoc.body.style.opacity = '1';
              
              setTimeout(() => {
                iframeDoc.body.style.transition = '';
              }, 200);
            }, 200);
          }
        }
        
        resolve();
      } catch (error) {
        console.error('🎬 ReadyAI: Error in seamless update:', error);
        // Fallback to srcdoc
        if (iframeRef.current) {
          iframeRef.current.srcdoc = newContent;
        }
        resolve();
      }
    });
  };

  // Process content updates
  useEffect(() => {
    if (currentContent) {
      processContentUpdate(currentContent);
    }
  }, [currentContent, processContentUpdate]);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    console.log('🎬 ReadyAI: Iframe loaded');

    // Set up click handling for element interaction
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (iframeDoc && onElementClick) {
      const handleClick = (event: Event) => {
        const target = event.target as HTMLElement;
        if (!target) return;

        // Check if element needs implementation (similar to readdy.ai)
        const needsImplementation = checkIfElementNeedsImplementation(target);
        if (needsImplementation.needs) {
          event.preventDefault();
          event.stopPropagation();
          
          onElementClick({
            textContent: needsImplementation.text,
            implementationType: needsImplementation.type,
            implementationReason: needsImplementation.reason,
            isInteractive: true,
            outerHTML: target.outerHTML,
            tagName: target.tagName
          });
        }
      };

      iframeDoc.addEventListener('click', handleClick, true);
      
      // Cleanup function
      return () => {
        iframeDoc.removeEventListener('click', handleClick, true);
      };
    }
  }, [onElementClick]);

  // Check if element needs implementation (readdy.ai style)
  const checkIfElementNeedsImplementation = (element: HTMLElement) => {
    const tagName = element.tagName.toLowerCase();
    const text = element.textContent?.trim() || '';

    // Check buttons without onclick handlers
    if (tagName === 'button' || 
        (tagName === 'input' && (element as HTMLInputElement).type === 'button') ||
        element.classList.contains('btn')) {
      
      const hasOnclick = (element as any).onclick || element.getAttribute('onclick');
      if (!hasOnclick) {
        return {
          needs: true,
          type: 'button',
          reason: 'Button needs click functionality',
          text: text
        };
      }
    }

    // Check links without href or with placeholder href
    if (tagName === 'a') {
      const href = element.getAttribute('href');
      if (!href || href === '#' || href === 'javascript:void(0)') {
        return {
          needs: true,
          type: 'link',
          reason: 'Link needs destination',
          text: text
        };
      }
    }

    return { needs: false };
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const refreshPreview = () => {
    const iframe = iframeRef.current;
    if (iframe && currentContent) {
      console.log('🎬 ReadyAI: Refreshing preview');
      setLastProcessedLength(0);
      iframe.srcdoc = currentContent;
    }
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'h-full'} ${className}`}>
      {/* Preview Controls */}
      <div className="absolute top-2 right-2 z-10 flex space-x-2">
        <button
          onClick={refreshPreview}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title="Refresh preview"
        >
          <FiRefreshCw className="w-4 h-4 text-gray-600" />
        </button>
        <button
          onClick={toggleFullscreen}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? (
            <FiMinimize2 className="w-4 h-4 text-gray-600" />
          ) : (
            <FiMaximize2 className="w-4 h-4 text-gray-600" />
          )}
        </button>
      </div>

     

      {/* ReadyAI-style Iframe */}
      <iframe
        ref={iframeRef}
        className="w-full h-full border-0"
        title="ReadyAI Style Preview"
        onLoad={handleIframeLoad}
        sandbox="allow-scripts allow-same-origin allow-forms"
        style={{
          backgroundColor: 'white',
          minHeight: '100%'
        }}
      />

      
    </div>
  );
};

export default ReadyAIStylePreview;
