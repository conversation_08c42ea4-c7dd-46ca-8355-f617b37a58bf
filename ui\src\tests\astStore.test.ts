import { describe, test, expect, beforeEach } from 'vitest';
import { astStore } from '../services/astStore';
import { UINode } from '../types/uiNode';

describe('ASTStore', () => {
  beforeEach(() => {
    // Reset AST store before each test
    const emptyPage = astStore.getCurrentAST();
    astStore.setCurrentAST(emptyPage, false);
  });

  test('creates empty page on initialization', () => {
    const ast = astStore.getCurrentAST();
    expect(ast.type).toBe('Page');
    expect(ast.props.title).toBe('Untitled Page');
    expect(ast.children).toEqual([]);
  });

  test('creates and retrieves snapshots', () => {
    const initialVersion = astStore.getCurrentAST();
    const snapshotId = astStore.saveSnapshot('Test snapshot');
    
    // Modify AST
    const modifiedAst: UINode = {
      ...initialVersion,
      props: { ...initialVersion.props, title: 'Modified Page' }
    };
    astStore.setCurrentAST(modifiedAst);

    // Get snapshot
    const snapshot = astStore.getSnapshot(snapshotId);
    expect(snapshot).toBeDefined();
    expect(snapshot?.ast.props.title).toBe('Untitled Page');
  });

  test('restores snapshots', () => {
    // Create initial state
    const initialAst = astStore.getCurrentAST();
    const snapshotId = astStore.saveSnapshot('Initial state');

    // Modify AST
    const modifiedAst: UINode = {
      ...initialAst,
      props: { ...initialAst.props, title: 'Modified Page' }
    };
    astStore.setCurrentAST(modifiedAst);

    // Verify modification
    expect(astStore.getCurrentAST().props.title).toBe('Modified Page');

    // Restore snapshot
    const restored = astStore.restoreSnapshot(snapshotId);
    expect(restored).toBe(true);
    expect(astStore.getCurrentAST().props.title).toBe('Untitled Page');
  });

  test('adds and finds nodes', () => {
    const pageNode = astStore.getCurrentAST();
    const sectionNode: UINode = {
      id: 'section1',
      type: 'Section',
      props: {
        className: 'test-section',
        layout: 'row'
      }
    };

    // Add section
    const added = astStore.addChild(pageNode.id, sectionNode);
    expect(added).toBe(true);

    // Find section
    const found = astStore.findNodeById('section1');
    expect(found).toBeDefined();
    expect(found?.type).toBe('Section');
    expect(found?.props.className).toBe('test-section');
  });

  test('updates node properties', () => {
    const pageNode = astStore.getCurrentAST();
    const sectionNode: UINode = {
      id: 'section1',
      type: 'Section',
      props: {
        className: 'test-section',
        layout: 'row'
      }
    };

    // Add and update section
    astStore.addChild(pageNode.id, sectionNode);
    const updated = astStore.updateNodeProps('section1', {
      className: 'modified-section',
      layout: 'column'
    });

    expect(updated).toBe(true);
    const node = astStore.findNodeById('section1');
    expect(node?.props.className).toBe('modified-section');
    expect(node?.props.layout).toBe('column');
  });

  test('removes nodes', () => {
    const pageNode = astStore.getCurrentAST();
    const sectionNode: UINode = {
      id: 'section1',
      type: 'Section',
      props: {
        className: 'test-section'
      }
    };

    // Add and remove section
    astStore.addChild(pageNode.id, sectionNode);
    const removed = astStore.removeNode('section1');

    expect(removed).toBe(true);
    const notFound = astStore.findNodeById('section1');
    expect(notFound).toBeUndefined();
  });

  test('maintains correct versioning', () => {
    // Initial state
    const v1 = astStore.saveSnapshot('Version 1');
    
    // Modify AST
    const ast = astStore.getCurrentAST();
    ast.props.title = 'V2';
    astStore.setCurrentAST(ast);
    const v2 = astStore.saveSnapshot('Version 2');

    // List snapshots
    const snapshots = astStore.listSnapshots();
    expect(snapshots).toEqual([v1, v2]);

    // Verify versions are independent
    const version1 = astStore.getSnapshot(v1);
    const version2 = astStore.getSnapshot(v2);
    expect(version1?.ast.props.title).toBe('Untitled Page');
    expect(version2?.ast.props.title).toBe('V2');
  });
});
