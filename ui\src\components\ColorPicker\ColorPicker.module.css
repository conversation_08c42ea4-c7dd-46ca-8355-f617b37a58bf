.colorPickerContainer {
  width: 240px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #ddd;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
}

.closeButton {
  background: none;
  border: none;
  font-size: 20px;
  line-height: 1;
  padding: 0 10px;
  cursor: pointer;
  color: #666;
}

.closeButton:hover {
  color: #333;
}

.tabs {
  display: flex;
  flex: 1;
}

.tab {
  flex: 1;
  padding: 8px;
  text-align: center;
  background: #f8f8f8;
  border: none;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #666;
  transition: background-color 0.2s;
}

.tab:hover {
  background-color: #f0f0f0;
}

.activeTab {
  background-color: white;
  color: #333;
  border-bottom: 2px solid #4a90e2;
}

.content {
  max-height: 250px;
  overflow-y: auto;
  padding: 12px;
  scrollbar-width: thin;
}

.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.content::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.paletteContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.palette {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.paletteName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.colorGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
}

.colorSwatch {
  width: 100%;
  aspect-ratio: 1;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.1s, box-shadow 0.1s;
}

.colorSwatch:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.customColorContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.customColorPreview {
  width: 100%;
  height: 60px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.customColorInputs {
  display: flex;
  gap: 8px;
  align-items: center;
}

.colorInput {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  background: none;
}

.colorText {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.applyButton {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.applyButton:hover {
  background-color: #3a80d2;
}
