# API Endpoint Usage Guide

This document explains when to use the different AST and LLM endpoints in the prototyping platform.

## 🎯 Two Different AST Systems

### 1. **UI AST Pipeline** (`/api/ast/*`)
**Purpose**: Manages the logical UI component structure (Plan → UI Components)

**Use Cases**:
- Convert user prompts into structured UI plans
- Build component hierarchies (Page, Section, Button, etc.)
- Manage UI component state and relationships
- Version control for UI designs
- JSON Patch operations on UI structure

**Endpoints**:
- `POST /api/ast/plan` - Generate UI plan from prompt
- `GET /api/ast/current` - Get current UI AST state
- `POST /api/ast/patch` - Apply changes to UI structure
- `GET /api/ast/snapshots` - List UI design versions
- `GET /api/ast/snapshots/:id` - Get specific UI version

**Data Format**: UINode JSON structure
```json
{
  "id": "uuid",
  "type": "Page|Section|Button|Input|Text|Image|Link|Form",
  "props": { "title": "...", "styles": {...} },
  "children": [...]
}
```

### 2. **Code AST Pipeline** (`/api/llm/v2/*`)
**Purpose**: Manages the actual code syntax and transformations (Plan → HTML/JS/CSS Code)

**Use Cases**:
- Generate HTML/CSS/JavaScript from plans
- Parse and analyze existing code syntax
- Apply code transformations (rename variables, optimize imports)
- Format and prettify code
- Validate syntax and extract metadata
- Code optimizations and refactoring

**Endpoints**:
- `POST /api/llm/v2/generate-with-ast` - Generate code with AST processing
- `POST /api/llm/v2/parse-code` - Parse code into syntax AST
- `POST /api/llm/v2/transform-code` - Apply code transformations
- `POST /api/llm/v2/format-code` - Format code using AST
- `POST /api/llm/v2/optimize-code` - Apply web optimizations
- `GET /api/llm/v2/transformation-hooks` - List available transformations
- `GET /api/llm/v2/supported-languages` - List supported languages

**Data Format**: Babel/TypeScript/CSS syntax ASTs
```javascript
// Example JavaScript AST node
{
  "type": "FunctionDeclaration",
  "id": { "type": "Identifier", "name": "myFunction" },
  "params": [...],
  "body": { "type": "BlockStatement", "body": [...] }
}
```

## 🔄 Complete Workflow

### **Typical User Journey**:

```
1. User Input Prompt
   ↓
2. UI AST Pipeline (/api/ast/plan)
   - Converts prompt to structured UI plan
   - Creates UINode hierarchy
   ↓
3. Code AST Pipeline (/api/llm/v2/generate-with-ast)
   - Converts UI plan to actual HTML/CSS/JS code
   - Parses generated code into syntax AST
   - Applies transformations and formatting
   ↓
4. Final Prototype
   - Rendered HTML/CSS/JS ready for preview
```

## 📋 When to Use Each System

### **Use UI AST Endpoints When**:
- ✅ Building UI wireframes and component structures
- ✅ Managing design system components
- ✅ Version controlling UI designs
- ✅ Applying high-level UI changes (add section, remove component)
- ✅ Working with design tokens and component props
- ✅ Creating UI component libraries

### **Use Code AST Endpoints When**:
- ✅ Generating actual code from designs
- ✅ Refactoring existing code
- ✅ Formatting and prettifying code
- ✅ Analyzing code complexity and dependencies
- ✅ Applying security transformations (sanitize HTML)
- ✅ Optimizing performance (remove dead code, optimize imports)
- ✅ Adding accessibility improvements
- ✅ Validating syntax and catching errors

## 🔧 Integration Examples

### **Frontend Integration**:

```typescript
// 1. Generate UI Plan (UI AST)
import { astStore } from './services/astStore';
const uiPlan = await astStore.generatePlan("Create a login form");

// 2. Generate Code from Plan (Code AST)
import { codeAstService } from './services/codeAstService';
const eventSource = codeAstService.generateCodeWithAST(
  uiPlan.text,
  'openai',
  {
    language: 'html',
    transformations: [
      codeAstService.createTransformations.sanitizeHTML(),
      codeAstService.createTransformations.improveAccessibility()
    ]
  }
);

// 3. Further Code Modifications
const optimized = await codeAstService.optimizeCode(generatedCode, 'html');
```

### **Backend Integration**:

```javascript
// UI AST - Component Structure Management
const astStore = require('./services/ast/astStore');
const uiSnapshot = astStore.getCurrentSnapshot();
astStore.applyPatch(snapshotId, [
  { type: 'addChild', parentId: 'page1', child: buttonComponent }
]);

// Code AST - Syntax Processing
const codeAstService = require('./services/ast/codeAstService');
const htmlAst = codeAstService.parseCode(htmlContent, 'html');
const transformedAst = await codeAstService.transformAST(htmlAst, transformations);
const formattedCode = codeAstService.printCode(transformedAst, 'html');
```

## 🚀 Advanced Use Cases

### **Design System Builder**:
1. Use **UI AST** to define component templates and design tokens
2. Use **Code AST** to generate consistent, accessible code for each component

### **Code Quality Pipeline**:
1. Use **Code AST** to parse existing prototypes
2. Apply transformations for security, accessibility, and performance
3. Use **UI AST** to update component metadata based on code analysis

### **AI-Powered Refactoring**:
1. Use **Code AST** to analyze code complexity and patterns
2. Use **UI AST** to suggest component restructuring
3. Use **Code AST** to apply automated refactoring

## 📊 Performance Considerations

### **UI AST Operations**:
- ⚡ Fast - JSON operations on lightweight component structure
- 💾 Persistent - Stored in memory with versioning
- 🔄 Real-time - Immediate updates via JSON Patch

### **Code AST Operations**:
- 🧮 Compute-intensive - Full syntax parsing and transformation
- 🎯 On-demand - Triggered when code generation/modification needed
- 🔧 Powerful - Deep code analysis and manipulation capabilities

## 🎯 Summary

| Aspect | UI AST (`/api/ast/*`) | Code AST (`/api/llm/v2/*`) |
|--------|----------------------|---------------------------|
| **Purpose** | UI component structure | Code syntax processing |
| **Data** | UINode JSON | Babel/TS/CSS ASTs |
| **Speed** | Fast | Compute-intensive |
| **When** | Design & structure | Code generation & refactoring |
| **Output** | Component hierarchy | Formatted, optimized code |

Both systems work together to provide a complete prototyping pipeline from high-level UI concepts to production-ready code.
