/**
 * Higher-order function that wraps a request handler with SSE support
 */
function withSSE(handler) {
  return async (req, res, next) => {
    try {
      // Set SSE headers
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('X-Accel-Buffering', 'no');

      // Store original methods
      const originalEnd = res.end;
      const originalWrite = res.write;

      // Override write to ensure UTF-8
      res.write = function(chunk, encoding, callback) {
        return originalWrite.call(this, chunk, 'utf8', callback);
      };

      // Override end to ensure proper cleanup
      res.end = function(chunk, encoding, callback) {
        if (chunk) {
          res.write(chunk, encoding);
        }
        return originalEnd.call(this, null, null, callback);
      };

      // Helper to write SSE response
      res.sse = function(event, data) {
        if (typeof data === 'object') {
          data = JSON.stringify(data);
        }

        if (event) {
          res.write(`event: ${event}\n`);
        }
        res.write(`data: ${data}\n\n`);
      };

      // Handle errors
      const handleError = (error) => {
        console.error('SSE Error:', error);
        
        if (!res.headersSent) {
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: error.message }));
        } else {
          res.sse('error', { error: error.message });
          res.end();
        }
      };

      // Call the wrapped handler
      await handler(req, res, next).catch(handleError);

    } catch (error) {
      next(error);
    }
  };
}

module.exports = withSSE;
