/**
 * SPA Router
 * Handles view navigation and routing logic
 */

export class Router {
  private currentView: string | null = null;
  private views = new Map<string, { name: string; content: string }>();
  private beforeNavigateCallbacks: Array<(viewName: string, currentView: string | null) => Promise<boolean | void>> = [];
  private afterNavigateCallbacks: Array<(viewName: string, previousView: string | null) => Promise<void>> = [];

  /**
   * Add a view to the router
   */
  addView(name: string, content: string, title?: string): void {
    this.views.set(name, { 
      name: title || name, 
      content 
    });
    console.log(`📍 Route added: ${name}`);
  }

  /**
   * Navigate to a specific view
   */
  async navigateToView(viewName: string): Promise<boolean> {
    console.log(`🧭 Navigating to view: ${viewName}`);
    
    const view = this.views.get(viewName);
    if (!view) {
      console.warn(`⚠️ View '${viewName}' not found`);
      return false;
    }

    // Execute before navigate callbacks
    for (const callback of this.beforeNavigateCallbacks) {
      const shouldContinue = await callback(viewName, this.currentView);
      if (shouldContinue === false) {
        console.log(`🚫 Navigation to '${viewName}' cancelled by beforeNavigate callback`);
        return false;
      }
    }

    // Update current view
    const previousView = this.currentView;
    this.currentView = viewName;
    
    // Update view container
    const viewContainer = document.getElementById('viewContainer');
    if (viewContainer) {
      await this.renderView(viewContainer, viewName, view);
      
      // Update navigation state
      this.updateNavigationState(viewName);
      
      console.log(`✅ View '${viewName}' loaded`);
    }

    // Execute after navigate callbacks
    for (const callback of this.afterNavigateCallbacks) {
      await callback(viewName, previousView);
    }

    return true;
  }

  /**
   * Render view with transition
   */
  private async renderView(container: HTMLElement, viewName: string, view: { content: string }): Promise<void> {
    return new Promise((resolve) => {
      // Add transition effect
      container.style.opacity = '0.5';
      
      setTimeout(() => {
        container.innerHTML = `
          <section data-view="${viewName}" class="view-transition">
            ${view.content}
          </section>
        `;
        
        // Restore opacity
        container.style.opacity = '1';
        
        resolve();
      }, 150);
    });
  }

  /**
   * Update navigation active state
   */
  private updateNavigationState(activeView: string): void {
    const navItems = document.querySelectorAll('[data-nav]');
    navItems.forEach(item => {
      const viewName = item.getAttribute('data-nav');
      if (viewName === activeView) {
        // Active state styling
        item.classList.add('active', 'text-blue-600', 'border-blue-600');
        item.classList.remove('text-gray-500', 'border-transparent');
      } else {
        // Inactive state styling
        item.classList.remove('active', 'text-blue-600', 'border-blue-600');
        item.classList.add('text-gray-500', 'border-transparent');
      }
    });

    console.log(`🎯 Navigation state updated - active view: ${activeView}`);
  }

  /**
   * Update navigation items in DOM
   */
  updateNavigation(): void {
    const navItems = document.getElementById('navItems');
    if (!navItems) return;

    const navHTML = Array.from(this.views.entries())
      .map(([key, view]) => `
        <button
          data-nav="${key}"
          class="nav-item px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
        >
          ${view.name}
        </button>
      `).join('');

    navItems.innerHTML = navHTML;
  }

  /**
   * Update navigation from LLM-generated HTML
   */
  updateNavigationFromHtml(html: string): void {
    const navItems = document.getElementById('navItems');
    if (!navItems) return;

    // Extract navigation elements from the LLM-generated HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const navElements = tempDiv.querySelectorAll('[data-nav]');
    if (navElements.length > 0) {
      // Use the LLM-generated navigation
      const navHTML = Array.from(navElements)
        .map(el => el.outerHTML)
        .join('');
      navItems.innerHTML = navHTML;
    } else {
      // Fallback to generated navigation
      this.updateNavigation();
    }
  }

  /**
   * Get current view
   */
  getCurrentView(): string | null {
    return this.currentView;
  }

  /**
   * Get view by name
   */
  getView(name: string): { name: string; content: string } | undefined {
    return this.views.get(name);
  }

  /**
   * Update view content
   */
  updateView(name: string, content: string): boolean {
    if (this.views.has(name)) {
      this.views.set(name, { 
        ...this.views.get(name)!, 
        content 
      });
      
      // Re-render if it's the current view
      if (this.currentView === name) {
        this.navigateToView(name);
      }
      
      console.log(`📝 View '${name}' updated`);
      return true;
    }
    return false;
  }

  /**
   * Remove view
   */
  removeView(name: string): boolean {
    if (this.views.has(name)) {
      this.views.delete(name);
      this.updateNavigation();
      
      // Navigate to first available view if current view was removed
      if (this.currentView === name && this.views.size > 0) {
        const firstView = this.views.keys().next().value;
        this.navigateToView(firstView!);
      }
      
      console.log(`🗑️ View '${name}' removed`);
      return true;
    }
    return false;
  }

  /**
   * Add before navigate callback
   */
  onBeforeNavigate(callback: (viewName: string, currentView: string | null) => Promise<boolean | void>): void {
    this.beforeNavigateCallbacks.push(callback);
  }

  /**
   * Add after navigate callback
   */
  onAfterNavigate(callback: (viewName: string, previousView: string | null) => Promise<void>): void {
    this.afterNavigateCallbacks.push(callback);
  }

  /**
   * Get all views
   */
  getAllViews(): Array<{ key: string; name: string; content: string }> {
    return Array.from(this.views.entries()).map(([key, view]) => ({
      key,
      ...view
    }));
  }

  /**
   * Check if view exists
   */
  hasView(name: string): boolean {
    return this.views.has(name);
  }

  /**
   * Check if any views exist
   */
  hasViews(): boolean {
    return this.views.size > 0;
  }
}
