/**
 * Integration tests for the versioning system
 * Tests the version service functionality
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { versionService } from '../services/versionService';

// Mock fetch for API calls
const mockFetch = vi.fn();
(globalThis as any).fetch = mockFetch;

describe('Version Service', () => {
  const mockPrototypeId = 123;
  const mockVersions = [
    {
      id: 1,
      version_number: 3,
      change_description: 'Latest changes',
      operation_type: 'edit' as const,
      created_at: '2024-01-01T12:00:00Z'
    },
    {
      id: 2,
      version_number: 2,
      change_description: 'Generated content',
      operation_type: 'generate' as const,
      created_at: '2024-01-01T11:00:00Z'
    }
  ];

  const mockVersionDetails = {
    ...mockVersions[0],
    html: '<div>Test HTML content</div>',
    css: null,
    user_prompt: 'Test prompt',
    llm_response: 'Test response'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('API Integration', () => {
    test('should fetch version history successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ versions: mockVersions })
      });

      const result = await versionService.getVersionHistory(mockPrototypeId, 10, 0);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/llm/v3/versions/123?limit=10&offset=0',
        expect.objectContaining({
          method: 'GET',
          credentials: 'include'
        })
      );
      expect(result).toEqual(mockVersions);
    });

    test('should fetch specific version successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ version: mockVersionDetails })
      });

      const result = await versionService.getVersion(mockPrototypeId, 3);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/llm/v3/versions/123/3',
        expect.objectContaining({
          method: 'GET',
          credentials: 'include'
        })
      );
      expect(result).toEqual(mockVersionDetails);
    });

    test('should create version successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ versionId: 4 })
      });

      const result = await versionService.createVersion(
        mockPrototypeId,
        '<div>Test</div>',
        undefined,
        'Test change',
        'manual_edit',
        'Test prompt'
      );

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/llm/v3/versions/123',
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
          body: JSON.stringify({
            html: '<div>Test</div>',
            css: undefined,
            changeDescription: 'Test change',
            operationType: 'manual_edit',
            userPrompt: 'Test prompt',
            llmResponse: undefined
          })
        })
      );
      expect(result).toBe(4);
    });

    test('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Internal Server Error'
      });

      await expect(versionService.getVersionHistory(mockPrototypeId))
        .rejects.toThrow('Failed to fetch version history: Internal Server Error');
    });

    test('should handle 404 for specific version', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      });

      const result = await versionService.getVersion(mockPrototypeId, 999);
      expect(result).toBeNull();
    });
  });

  describe('Utility Functions', () => {
    test('should format relative time correctly', () => {
      // Mock current time
      const mockNow = new Date('2024-01-01T12:05:00Z');
      vi.setSystemTime(mockNow);

      const fiveMinutesAgo = '2024-01-01T12:00:00Z';
      const result = versionService.formatRelativeTime(fiveMinutesAgo);
      expect(result).toBe('5 minutes ago');

      vi.useRealTimers();
    });

    test('should return correct operation icons', () => {
      expect(versionService.getOperationIcon('generate')).toBe('🤖');
      expect(versionService.getOperationIcon('edit')).toBe('✏️');
      expect(versionService.getOperationIcon('session_edit')).toBe('✏️');
      expect(versionService.getOperationIcon('implement')).toBe('🔧');
      expect(versionService.getOperationIcon('unknown')).toBe('📝');
    });

    test('should return correct operation names', () => {
      expect(versionService.getOperationName('generate')).toBe('Generated');
      expect(versionService.getOperationName('edit')).toBe('Manual Edit');
      expect(versionService.getOperationName('session_edit')).toBe('Session Edit');
      expect(versionService.getOperationName('implement')).toBe('Implementation');
      expect(versionService.getOperationName('unknown')).toBe('Modified');
    });
  });

  describe('Edge Cases', () => {
    test('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(versionService.getVersionHistory(mockPrototypeId))
        .rejects.toThrow('Network error');
    });

    test('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => { throw new Error('Invalid JSON'); }
      });

      await expect(versionService.getVersionHistory(mockPrototypeId))
        .rejects.toThrow('Invalid JSON');
    });

    test('should handle empty version history', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ versions: [] })
      });

      const result = await versionService.getVersionHistory(mockPrototypeId);
      expect(result).toEqual([]);
    });
  });
});
