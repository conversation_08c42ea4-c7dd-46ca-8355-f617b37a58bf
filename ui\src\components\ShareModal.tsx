import React, { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon, LinkIcon, ClipboardIcon, CheckIcon } from '@heroicons/react/24/outline';
import shareService, { ShareResponse } from '../services/shareService';
import styles from './ShareModal.module.css';
import { trackPrototypeShare } from '../utils/analytics';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  prototypeId: string;
  prototypeName: string;
}

/**
 * Modal for sharing a prototype
 */
const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, prototypeId, prototypeName }) => {
  const [isPublic, setIsPublic] = useState(true);
  const [email, setEmail] = useState('');
  const [accessLevel, setAccessLevel] = useState<'view' | 'comment' | 'edit'>('view');
  const [expiresAt, setExpiresAt] = useState<string | undefined>(undefined);
  const [shares, setShares] = useState<ShareResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [publicShareUrl, setPublicShareUrl] = useState<string | null>(null);

  // Fetch existing shares when the modal opens
  useEffect(() => {
    if (isOpen && prototypeId) {
      fetchShares();
    }
  }, [isOpen, prototypeId]);

  // Reset copied state after 2 seconds
  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => {
        setCopied(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [copied]);

  // Fetch existing shares for this prototype
  const fetchShares = async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedShares = await shareService.getSharesForPrototype(prototypeId);
      setShares(fetchedShares);

      // Check if there's already a public share
      const publicShare = fetchedShares.find(s => s.share.isPublic);
      if (publicShare) {
        setPublicShareUrl(publicShare.shareUrl);
      } else {
        setPublicShareUrl(null);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch shares');
    } finally {
      setLoading(false);
    }
  };

  // Create a new share
  const handleCreateShare = async () => {
    try {
      setLoading(true);
      setError(null);

      // For public shares, we don't need an email
      const shareData = {
        prototypeId,
        isPublic,
        accessLevel,
        expiresAt,
        ...(isPublic ? {} : { sharedWithEmail: email })
      };

      const newShare = await shareService.createShare(shareData);

      // Update the shares list
      await fetchShares();

      // If it's a public share, set the public share URL
      if (isPublic) {
        setPublicShareUrl(newShare.shareUrl);
        // Track public share creation
        trackPrototypeShare(prototypeId, 'create_public_link');
      } else {
        // Track private share creation
        trackPrototypeShare(prototypeId, 'email_share');
      }

      // Reset form for private shares
      if (!isPublic) {
        setEmail('');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create share');
    } finally {
      setLoading(false);
    }
  };

  // Delete a share
  const handleDeleteShare = async (shareId: string) => {
    try {
      setLoading(true);
      setError(null);
      await shareService.deleteShare(shareId);

      // Update the shares list
      await fetchShares();
    } catch (err: any) {
      setError(err.message || 'Failed to delete share');
    } finally {
      setLoading(false);
    }
  };

  // Copy share URL to clipboard
  const copyToClipboard = (url: string) => {
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);

      // Track the share event
      trackPrototypeShare(prototypeId, 'copy_link');
    });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className={styles.modal}>
      <div className={styles.backdrop} aria-hidden="true" />

      <div className={styles.container}>
        <Dialog.Panel className={styles.panel}>
          <div className={styles.header}>
            <Dialog.Title className={styles.title}>
              Share "{prototypeName}"
            </Dialog.Title>
            <button className={styles.closeButton} onClick={onClose}>
              <XMarkIcon className={styles.closeIcon} />
            </button>
          </div>

          <div className={styles.content}>
            {/* Tab selector for share type */}
            <div className={styles.tabs}>
              <button
                className={`${styles.tab} ${isPublic ? styles.activeTab : ''}`}
                onClick={() => setIsPublic(true)}
              >
                Public Link
              </button>
              <button
                className={`${styles.tab} ${!isPublic ? styles.activeTab : ''}`}
                onClick={() => setIsPublic(false)}
              >
                Private Share
              </button>
            </div>

            {/* Public share section */}
            {isPublic && (
              <div className={styles.section}>
                <p className={styles.description}>
                  Create a public link that anyone can use to access this prototype.
                </p>

                {publicShareUrl ? (
                  <div className={styles.shareLink}>
                    <div className={styles.linkContainer}>
                      <LinkIcon className={styles.linkIcon} />
                      <input
                        type="text"
                        value={publicShareUrl}
                        readOnly
                        className={styles.linkInput}
                      />
                    </div>
                    <button
                      className={styles.copyButton}
                      onClick={() => copyToClipboard(publicShareUrl)}
                    >
                      {copied ? (
                        <CheckIcon className={styles.copyIcon} />
                      ) : (
                        <ClipboardIcon className={styles.copyIcon} />
                      )}
                      {copied ? 'Copied!' : 'Copy'}
                    </button>
                  </div>
                ) : (
                  <button
                    className={styles.createButton}
                    onClick={handleCreateShare}
                    disabled={loading}
                  >
                    {loading ? 'Creating...' : 'Create Public Link'}
                  </button>
                )}
              </div>
            )}

            {/* Private share section */}
            {!isPublic && (
              <div className={styles.section}>
                <p className={styles.description}>
                  Share this prototype with specific people by email.
                </p>

                <div className={styles.formGroup}>
                  <label className={styles.label}>Email</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter email address"
                    className={styles.input}
                  />
                </div>

                <div className={styles.formGroup}>
                  <label className={styles.label}>Access Level</label>
                  <select
                    value={accessLevel}
                    onChange={(e) => setAccessLevel(e.target.value as any)}
                    className={styles.select}
                  >
                    <option value="view">View only</option>
                    <option value="comment">Can comment</option>
                    <option value="edit">Can edit</option>
                  </select>
                </div>

                <button
                  className={styles.createButton}
                  onClick={handleCreateShare}
                  disabled={loading || !email}
                >
                  {loading ? 'Sharing...' : 'Share'}
                </button>
              </div>
            )}

            {/* Error message */}
            {error && <p className={styles.error}>{error}</p>}

            {/* Existing shares */}
            {shares.length > 0 && (
              <div className={styles.sharesSection}>
                <h3 className={styles.sharesTitle}>Shared With</h3>
                <ul className={styles.sharesList}>
                  {shares.map((shareItem) => (
                    <li key={shareItem.share.id} className={styles.shareItem}>
                      <div className={styles.shareInfo}>
                        {shareItem.share.isPublic ? (
                          <span className={styles.publicBadge}>Public Link</span>
                        ) : (
                          <span>{shareItem.share.sharedWithEmail}</span>
                        )}
                        <span className={styles.accessLevel}>
                          {shareItem.share.accessLevel === 'view' && 'View only'}
                          {shareItem.share.accessLevel === 'comment' && 'Can comment'}
                          {shareItem.share.accessLevel === 'edit' && 'Can edit'}
                        </span>
                      </div>
                      <div className={styles.shareActions}>
                        <button
                          className={styles.copyLinkButton}
                          onClick={() => copyToClipboard(shareItem.shareUrl)}
                        >
                          {copied ? 'Copied!' : 'Copy Link'}
                        </button>
                        <button
                          className={styles.deleteButton}
                          onClick={() => handleDeleteShare(shareItem.share.id)}
                          disabled={loading}
                        >
                          Remove
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default ShareModal;
