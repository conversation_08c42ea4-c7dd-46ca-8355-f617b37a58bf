// <PERSON>ript to fix the token usage function
const fs = require('fs');
const path = require('path');
const { pool } = require('./services/promptDbService');

async function fixTokenFunction() {
  console.log('Starting token function fix process...');
  
  try {
    // Check if the database is accessible
    const pingResult = await pool.query('SELECT 1 as ping');
    console.log(`Database connection: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    
    // Read and execute the fix_token_usage.sql file
    const sqlPath = path.join(__dirname, 'fix_token_usage.sql');
    if (fs.existsSync(sqlPath)) {
      console.log('Executing fix_token_usage.sql...');
      const sql = fs.readFileSync(sqlPath, 'utf8');
      await pool.query(sql);
      console.log('Successfully executed fix_token_usage.sql');
    } else {
      console.error('fix_token_usage.sql not found!');
    }
    
    // Test the function
    try {
      console.log('Testing use_tokens_and_log function...');
      
      // Get a valid user ID
      const userResult = await pool.query('SELECT id FROM users LIMIT 1');
      if (userResult.rows.length === 0) {
        console.log('No users found in the database. Creating a test user...');
        
        // Create a test user
        const newUserResult = await pool.query(`
          INSERT INTO users (email, display_name, google_id)
          VALUES ('<EMAIL>', 'Test User', 'test123')
          RETURNING id
        `);
        
        const testUserId = newUserResult.rows[0].id;
        console.log(`Created test user with ID: ${testUserId}`);
        
        // Test the function with the new user
        const testResult = await pool.query(`
          SELECT use_tokens_and_log($1, $2, $3, $4, $5) as success
        `, [testUserId, 10, 'test_event', 'Test context', JSON.stringify({ test: 'data' })]);
        
        console.log(`Test result: ${testResult.rows[0].success}`);
      } else {
        const testUserId = userResult.rows[0].id;
        console.log(`Using existing user with ID: ${testUserId}`);
        
        // Test the function with the existing user
        const testResult = await pool.query(`
          SELECT use_tokens_and_log($1, $2, $3, $4, $5) as success
        `, [testUserId, 10, 'test_event', 'Test context', JSON.stringify({ test: 'data' })]);
        
        console.log(`Test result: ${testResult.rows[0].success}`);
      }
    } catch (testError) {
      console.error('Error testing function:', testError);
    }
    
    console.log('Token function fix process completed successfully!');
  } catch (error) {
    console.error('Error fixing token function:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the fix
fixTokenFunction().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
