/**
 * Right Sidebar Component for EditorPageV3
 * Handles the chat interface and AI assistant
 */

import React from 'react';
import ChatInterface from './ChatInterface';

interface RightSidebarProps {
  // Panel state
  chatPanelWidth: number;
  isGenerating: boolean;
  
  // Chat state
  messages: any[];
  input: string;
  elementSelectorActive: boolean;
  
  // Event handlers
  onInputChange: (input: string) => void;
  onSubmit: (message: string) => void;
  onClearInput: () => void;
  onElementSelectorToggle: () => void;
  onMouseDown: (type: 'chat') => (e: React.MouseEvent) => void;
}

export const RightSidebar: React.FC<RightSidebarProps> = ({
  chatPanelWidth,
  isGenerating,
  messages,
  input,
  elementSelectorActive,
  onInputChange,
  onSubmit,
  onClearInput,
  onElementSelectorToggle,
  onMouseDown,
}) => {
  // Hide chat during generation
  if (isGenerating) {
    return null;
  }

  return (
    <>
      {/* Chat Resize Handle */}
      <div
        className="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors flex-shrink-0"
        onMouseDown={onMouseDown('chat')}
        title="Drag to resize chat panel"
      />

      {/* Chat Interface */}
      <div
        className="flex-shrink-0 bg-white border-l border-gray-200"
        style={{ width: chatPanelWidth }}
      >
        <ChatInterface
          messages={messages}
          input={input}
          isGenerating={isGenerating}
          onInputChange={onInputChange}
          onSubmit={onSubmit}
          onToggleSelector={onElementSelectorToggle}
          isSelectorActive={elementSelectorActive}
        />
      </div>
    </>
  );
};

export default RightSidebar;
