/**
 * End-to-End Test using real data from project ID 228
 * Tests the complete editHTML flow with actual database content
 */

const service = require('../services/llmServiceV3');

// Mock response for testing
class MockResponse {
  constructor() {
    this.events = [];
    this.ended = false;
    this.eventLog = [];
  }
  
  write(data) {
    this.events.push(data);
    // Parse SSE events
    const lines = data.split('\n');
    for (const line of lines) {
      if (line.startsWith('event:')) {
        const event = line.substring(6);
        this.eventLog.push({ type: 'event', value: event });
        console.log(`📡 SSE Event: ${event}`);
      } else if (line.startsWith('data:')) {
        const data = line.substring(5);
        if (data.trim()) {
          this.eventLog.push({ type: 'data', value: data });
          console.log(`📡 SSE Data: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
        }
      }
    }
  }
  
  end() {
    this.ended = true;
    console.log('🏁 Response ended');
  }
  
  getEventSummary() {
    const events = this.eventLog.filter(e => e.type === 'event').map(e => e.value);
    const dataEvents = this.eventLog.filter(e => e.type === 'data' && e.value.trim());
    return { 
      events, 
      hasData: dataEvents.length > 0,
      dataCount: dataEvents.length,
      ended: this.ended 
    };
  }
}

async function getProjectData(projectId) {
  try {
    // Try to get session data for the project
    const sessionService = require('../services/sessionService');
    
    console.log(`🔍 Looking for sessions for project ID: ${projectId}`);
    
    // Get all sessions for this project
    const sessions = await sessionService.getSessionsByPrototypeId(projectId);
    
    if (!sessions || sessions.length === 0) {
      console.log('❌ No sessions found for this project');
      return null;
    }
    
    console.log(`✅ Found ${sessions.length} sessions for project ${projectId}`);
    
    // Get the most recent session with HTML content
    const sessionWithHtml = sessions.find(s => s.page_html && s.page_html.trim().length > 0);
    
    if (!sessionWithHtml) {
      console.log('❌ No sessions with HTML content found');
      return null;
    }
    
    console.log(`✅ Found session with HTML: ${sessionWithHtml.id}`);
    console.log(`📄 Page title: ${sessionWithHtml.page_url}`);
    console.log(`📏 HTML length: ${sessionWithHtml.page_html.length} chars`);
    
    return {
      sessionId: sessionWithHtml.id,
      pageTitle: sessionWithHtml.page_url,
      htmlContent: sessionWithHtml.page_html,
      projectId: projectId
    };
    
  } catch (error) {
    console.error('❌ Error getting project data:', error.message);
    return null;
  }
}

async function testEndToEnd() {
  console.log('🧪 Starting End-to-End Test with Real Data\n');
  
  try {
    // Get real data from project 228
    const projectData = await getProjectData(228);
    
    if (!projectData) {
      console.log('❌ Could not get project data. Testing with sample data instead.');
      return testWithSampleData();
    }
    
    console.log('✅ Successfully retrieved project data');
    console.log(`📊 Project: ${projectData.projectId}`);
    console.log(`📄 Page: ${projectData.pageTitle}`);
    console.log(`📏 HTML: ${projectData.htmlContent.length} chars`);
    console.log(`📄 HTML preview: ${projectData.htmlContent.substring(0, 200)}...\n`);
    
    // Test scenarios with real data
    const testScenarios = [
      {
        name: "Change Header Color",
        prompt: "change the header background color to red",
        expectFragment: true
      },
      {
        name: "Add Menu Item", 
        prompt: "add a new menu item called 'Settings'",
        expectFragment: true
      },
      {
        name: "Change Title Text",
        prompt: "change the main title to 'Admin Dashboard'",
        expectFragment: true
      }
    ];
    
    for (const scenario of testScenarios) {
      console.log(`🔬 Testing: ${scenario.name}`);
      console.log(`📝 Prompt: "${scenario.prompt}"`);
      
      const mockRes = new MockResponse();
      
      try {
        console.log('🚀 Starting editHTML call...');
        
        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Test timeout after 60 seconds')), 60000);
        });
        
        // Create completion promise
        const completionPromise = new Promise((resolve) => {
          const originalEnd = mockRes.end.bind(mockRes);
          mockRes.end = () => {
            originalEnd();
            resolve();
          };
        });
        
        // Call the actual editHTML method with real data
        const editPromise = service.editHTML(
          projectData.htmlContent,
          scenario.prompt,
          mockRes,
          null, // provider (auto-select)
          null, // elementSelector (let method determine)
          [], // conversationHistory
          { 
            projectId: projectData.projectId,
            sessionId: projectData.sessionId,
            testMode: true 
          }
        );
        
        // Wait for completion or timeout
        await Promise.race([
          Promise.all([editPromise, completionPromise]),
          timeoutPromise
        ]);
        
        console.log('✅ editHTML call completed successfully');
        
        // Analyze the response
        const summary = mockRes.getEventSummary();
        console.log('📊 Response Summary:');
        console.log(`   Events: ${summary.events.join(', ')}`);
        console.log(`   Data Events: ${summary.dataCount}`);
        console.log(`   Has Data: ${summary.hasData}`);
        console.log(`   Ended: ${summary.ended}`);
        
        // Check for success indicators
        const hasStart = summary.events.includes('start');
        const hasEnd = summary.events.includes('end');
        const hasError = summary.events.includes('error');
        const hasDiff = summary.events.includes('diff');
        
        console.log(`📡 Event Analysis:`);
        console.log(`   ✅ Start Event: ${hasStart}`);
        console.log(`   ✅ End Event: ${hasEnd}`);
        console.log(`   ❌ Error Event: ${hasError}`);
        console.log(`   🔄 Diff Event: ${hasDiff}`);
        
        if (hasStart && hasEnd && !hasError) {
          console.log('🎉 End-to-End test PASSED');
          
          if (hasDiff) {
            console.log('✅ Diff generation working');
          } else {
            console.log('⚠️ No diff event detected - may indicate full HTML replacement');
          }
        } else {
          console.log('❌ End-to-End test FAILED');
          if (hasError) {
            console.log('   Error detected in response');
          }
        }
        
      } catch (error) {
        console.error('❌ Test Error:', error.message);
        
        // Check if this is a configuration error
        if (error.message.includes('LiteLLM') || error.message.includes('API key') || error.message.includes('timeout')) {
          console.log('ℹ️  Expected error - API configuration or timeout');
        } else {
          console.log('❌ Unexpected error in method logic');
        }
      }
      
      console.log('─'.repeat(80));
    }
    
  } catch (error) {
    console.error('❌ End-to-End test failed:', error.message);
  }
}

async function testWithSampleData() {
  console.log('🧪 Testing with sample dashboard data\n');
  
  const sampleHtml = `<div id="app" class="min-h-screen bg-gray-100">
  <header class="bg-blue-600 text-white p-4">
    <h1 class="text-2xl font-bold">Dashboard</h1>
  </header>
  <main class="p-6">
    <div class="grid grid-cols-3 gap-4">
      <div class="bg-white p-4 rounded shadow">
        <h2 class="text-lg font-semibold">Users</h2>
        <p class="text-2xl">1,234</p>
      </div>
    </div>
  </main>
</div>`;

  const mockRes = new MockResponse();
  
  try {
    await service.editHTML(
      sampleHtml,
      "change the header background color to red",
      mockRes,
      null,
      null,
      [],
      { testMode: true }
    );
  } catch (error) {
    console.log('Expected error with sample data:', error.message);
  }
}

// Run the test
testEndToEnd().then(() => {
  console.log('\n🏁 End-to-End testing completed');
}).catch(error => {
  console.error('Test execution failed:', error);
});
