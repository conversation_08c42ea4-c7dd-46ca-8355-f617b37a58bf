import React, { useEffect, useRef, useState } from 'react';

interface ReadyAIStyleRendererProps {
  htmlContent?: string;
  streamingContent?: string;
  isGenerating?: boolean;
  onElementClick?: (element: any) => void;
  className?: string;
  maxElements?: number;
}

const SeamlessProgressiveRenderer: React.FC<ReadyAIStyleRendererProps> = ({
  htmlContent = '',
  streamingContent = '',
  isGenerating = false,
  onElementClick,
  className = '',
  maxElements = 100,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const lastProcessedLengthRef = useRef<number>(0);
  const isProcessingRef = useRef<boolean>(false);
  const processedElementsRef = useRef<Set<string>>(new Set());
  const [stylesLoaded, setStylesLoaded] = useState(false);
  const [hasRenderedFirstElement, setHasRenderedFirstElement] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    const ensureStylesLoaded = async () => {
      if (!document.querySelector('script[src*="tailwindcss"]')) {
        const tailwindScript = document.createElement('script');
        tailwindScript.src = 'https://cdn.tailwindcss.com';
        tailwindScript.async = true;
        document.head.appendChild(tailwindScript);

        await new Promise((resolve) => {
          tailwindScript.onload = resolve;
          tailwindScript.onerror = resolve;
        });
      }

      if (!document.querySelector('#readyai-style-renderer-css')) {
        const customStyles = document.createElement('style');
        customStyles.id = 'readyai-style-renderer-css';
        customStyles.textContent = `
          .progressive-renderer {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 400px;
            position: relative;
          }

          .progressive-element {
            opacity: 0;
            transform: translateY(4px);
            transition: opacity 0.2s ease-out, transform 0.2s ease-out;
          }

          .progressive-element.visible {
            opacity: 1;
            transform: translateY(0);
          }

          .progressive-renderer * {
            box-sizing: border-box;
          }

          .progressive-renderer .dynamic-content {
            animation: forceStyleRecalc 0.1s ease-out;
          }

          .progressive-renderer.fade-in {
            opacity: 0;
            animation: fadeInWrapper 0.4s ease-in forwards;
          }

          .mask-fade-bottom {
            position: absolute;
            bottom: 0;
            height: 50px;
            width: 100%;
            background: linear-gradient(to bottom, transparent, white);
            pointer-events: none;
          }

          @keyframes fadeInWrapper {
            to { opacity: 1; }
          }

          @keyframes forceStyleRecalc {
            0% { opacity: 0.99; }
            100% { opacity: 1; }
          }
        `;
        document.head.appendChild(customStyles);
      }

      setStylesLoaded(true);
    };

    ensureStylesLoaded();
  }, []);

  useEffect(() => {
    if (!stylesLoaded || isProcessingRef.current) return;

    const container = containerRef.current;
    if (!container) return;

    const currentContent = isGenerating ? streamingContent : htmlContent;
    if (!currentContent?.trim()) return;

    const currentLength = currentContent.length;
    const lastProcessedLength = lastProcessedLengthRef.current;
    if (currentLength <= lastProcessedLength) return;

    isProcessingRef.current = true;

    const processNewContent = async () => {
      try {
        const newContent = currentContent.substring(lastProcessedLength);
        const completeElements = extractCompleteElementsImmediately(newContent);

        for (const element of completeElements) {
          if (!container.isConnected) break;

          const elementHash = generateElementHash(element);
          if (processedElementsRef.current.has(elementHash)) continue;

          processedElementsRef.current.add(elementHash);
          await renderElementSeamlessly(element, container);

          if (!hasRenderedFirstElement) setHasRenderedFirstElement(true);
          if (container.children.length > maxElements) {
            container.removeChild(container.firstChild as Node);
          }
        }

        lastProcessedLengthRef.current = currentLength;
        if (!isGenerating) setIsComplete(true);
      } catch (error) {
        console.error('🎬 Error in progressive rendering:', error);
      } finally {
        isProcessingRef.current = false;
      }
    };

    processNewContent();
  }, [htmlContent, streamingContent, isGenerating, stylesLoaded]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (
        isGenerating &&
        streamingContent.length > lastProcessedLengthRef.current &&
        stylesLoaded &&
        !isProcessingRef.current
      ) {
        const container = containerRef.current;
        if (container) {
          const newContent = streamingContent.substring(lastProcessedLengthRef.current);
          const completeElements = extractCompleteElementsImmediately(newContent);

          completeElements.forEach(async (element) => {
            const elementHash = generateElementHash(element);
            if (processedElementsRef.current.has(elementHash)) return;

            processedElementsRef.current.add(elementHash);
            await renderElementSeamlessly(element, container);
            if (!hasRenderedFirstElement) setHasRenderedFirstElement(true);

            if (container.children.length > maxElements) {
              container.removeChild(container.firstChild as Node);
            }
          });

          lastProcessedLengthRef.current = streamingContent.length;
        }
      }
    }, 150);

    return () => clearInterval(interval);
  }, [streamingContent, isGenerating, stylesLoaded]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const currentContent = isGenerating ? streamingContent : htmlContent;
    if (currentContent.length < lastProcessedLengthRef.current * 0.5) {
      container.innerHTML = '';
      lastProcessedLengthRef.current = 0;
      setHasRenderedFirstElement(false);
      setIsComplete(false);
    }
  }, [htmlContent, streamingContent, isGenerating]);

  const generateElementHash = (element: HTMLElement): string => {
    const content = element.outerHTML.substring(0, 100);
    const tag = element.tagName.toLowerCase();
    const textContent = element.textContent?.trim().substring(0, 50) || '';
    return `${tag}-${content.length}-${textContent.length}-${content.charCodeAt(0) || 0}`;
  };

  const extractCompleteElementsImmediately = (content: string): HTMLElement[] => {
    const elements: HTMLElement[] = [];
    const range = document.createRange();
    const frag = range.createContextualFragment(content);
    frag.childNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE && isValidCompleteElement(node as HTMLElement)) {
        elements.push(node as HTMLElement);
      }
    });
    return elements;
  };

  const isValidCompleteElement = (element: HTMLElement): boolean => {
    if (!element || !element.tagName) return false;
    const hasContent = !!(element.textContent?.trim() || element.children.length > 0);
    return hasContent;
  };

  const renderElementSeamlessly = async (element: HTMLElement, container: HTMLDivElement): Promise<void> => {
    return new Promise((resolve) => {
      const clonedElement = element.cloneNode(true) as HTMLElement;
      clonedElement.classList.add('progressive-element', 'dynamic-content');
      forceStyleApplication(clonedElement);

      if (onElementClick) {
        clonedElement.addEventListener('click', (e) => {
          e.preventDefault();
          onElementClick({
            tagName: clonedElement.tagName.toLowerCase(),
            textContent: clonedElement.textContent?.trim() || '',
            className: clonedElement.className || '',
            id: clonedElement.id || '',
            outerHTML: clonedElement.outerHTML,
          });
        });
      }

      container.appendChild(clonedElement);
      clonedElement.offsetHeight;

      requestAnimationFrame(() => {
        clonedElement.classList.add('visible');
        setTimeout(resolve, 200);
      });
    });
  };

  const forceStyleApplication = (element: HTMLElement) => {
    element.style.display = element.style.display || '';
    const allElements = element.querySelectorAll('*');
    allElements.forEach((child) => {
      (child as HTMLElement).style.display = (child as HTMLElement).style.display || '';
    });
  };

  if (!stylesLoaded) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading styles...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {!hasRenderedFirstElement && (
        <div className="animate-pulse space-y-4 px-4">
          <div className="h-6 bg-gray-300 rounded w-1/2"></div>
          <div className="h-4 bg-gray-300 rounded w-full"></div>
          <div className="h-4 bg-gray-300 rounded w-5/6"></div>
        </div>
      )}
      <div
        ref={containerRef}
        className={`progressive-renderer ${hasRenderedFirstElement ? 'fade-in' : ''} ${className}`}
        style={{ minHeight: '100%' }}
      />
      <div className="mask-fade-bottom" />
      {isComplete && (
        <div className="text-center text-green-600 mt-4 animate-fadeIn text-sm">✅ Rendering complete</div>
      )}
    </div>
  );
};

export default SeamlessProgressiveRenderer;
