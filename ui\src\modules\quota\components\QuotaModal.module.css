.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modalHeader {
  padding: 1.5rem 1.5rem 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.modalTitle {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.closeButton:hover {
  color: #111827;
}

.modalBody {
  padding: 1.5rem;
}

.modalText {
  color: #4b5563;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.quotaBarContainer {
  margin-bottom: 1.5rem;
}

.benefitsBox {
  background-color: rgba(79, 70, 229, 0.05);
  border: 1px solid rgba(79, 70, 229, 0.2);
  border-radius: 0.5rem;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.benefitsTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #4f46e5;
  margin: 0 0 1rem 0;
}

.benefitsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitItem {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #4b5563;
  font-size: 0.875rem;
}

.benefitItem::before {
  content: "✓";
  color: #4f46e5;
  font-weight: bold;
  margin-right: 0.5rem;
}

.modalFooter {
  padding: 1rem 1.5rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.secondaryButton {
  padding: 0.5rem 1rem;
  background-color: white;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.secondaryButton:hover {
  background-color: #f9fafb;
}

.primaryButton {
  padding: 0.5rem 1rem;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.primaryButton:hover {
  background-color: #4338ca;
}

.loadingButton {
  opacity: 0.7;
  cursor: wait;
}
