const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';

/**
 * Interface for share data
 */
export interface ShareData {
  id: string;
  prototypeId: string;
  ownerId: string;
  sharedWithEmail?: string;
  sharedWithUserId?: string;
  accessLevel: 'view' | 'comment' | 'edit';
  accessToken: string;
  isPublic: boolean;
  isActive: boolean;
  expiresAt?: string;
  hasAccessed: boolean;
  firstAccessedAt?: string;
  lastAccessedAt?: string;
  accessCount: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface for share creation request
 */
export interface CreateShareRequest {
  prototypeId: string;
  sharedWithEmail?: string;
  isPublic?: boolean;
  accessLevel?: 'view' | 'comment' | 'edit';
  expiresAt?: string;
}

/**
 * Interface for share update request
 */
export interface UpdateShareRequest {
  accessLevel?: 'view' | 'comment' | 'edit';
  isActive?: boolean;
  expiresAt?: string;
}

/**
 * Interface for share response
 */
export interface ShareResponse {
  share: ShareData;
  shareUrl: string;
}

/**
 * Interface for access validation response
 */
export interface AccessValidationResponse {
  hasAccess: boolean;
  accessLevel?: 'view' | 'comment' | 'edit';
  isPublic?: boolean;
}

/**
 * Service for managing prototype sharing
 */
const shareService = {
  /**
   * Create a new share for a prototype
   * @param shareData - Data for the new share
   * @returns Promise with the created share and share URL
   */
  async createShare(shareData: CreateShareRequest): Promise<ShareResponse> {
    const response = await fetch(`${API_BASE}/share`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(shareData),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create share');
    }

    return await response.json();
  },

  /**
   * Get all shares for a prototype
   * @param prototypeId - ID of the prototype
   * @returns Promise with an array of shares and their URLs
   */
  async getSharesForPrototype(prototypeId: string): Promise<ShareResponse[]> {
    const response = await fetch(`${API_BASE}/share/prototype/${prototypeId}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get shares');
    }

    return await response.json();
  },

  /**
   * Get all prototypes shared with the current user
   * @returns Promise with an array of shared prototypes
   */
  async getSharedWithMe(): Promise<any[]> {
    const response = await fetch(`${API_BASE}/share/shared-with-me`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get shared prototypes');
    }

    return await response.json();
  },

  /**
   * Update a share
   * @param shareId - ID of the share to update
   * @param updateData - Data to update
   * @returns Promise with the updated share and share URL
   */
  async updateShare(shareId: string, updateData: UpdateShareRequest): Promise<ShareResponse> {
    const response = await fetch(`${API_BASE}/share/${shareId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update share');
    }

    return await response.json();
  },

  /**
   * Delete a share
   * @param shareId - ID of the share to delete
   * @returns Promise with success status
   */
  async deleteShare(shareId: string): Promise<{ success: boolean }> {
    const response = await fetch(`${API_BASE}/share/${shareId}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete share');
    }

    return await response.json();
  },

  /**
   * Get a prototype by share token
   * @param accessToken - The access token
   * @returns Promise with the prototype and share information
   */
  async getPrototypeByShareToken(accessToken: string): Promise<any> {
    const response = await fetch(`${API_BASE}/share/access/${accessToken}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Invalid or expired share link');
    }

    return await response.json();
  },

  /**
   * Validate if a user has access to a prototype
   * @param prototypeId - ID of the prototype
   * @param email - Email of the user (optional)
   * @param accessToken - Access token (optional)
   * @returns Promise with access information
   */
  async validateAccess(
    prototypeId: string,
    email?: string,
    accessToken?: string
  ): Promise<AccessValidationResponse> {
    const response = await fetch(`${API_BASE}/share/validate-access`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prototypeId,
        email,
        accessToken,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to validate access');
    }

    return await response.json();
  },

  /**
   * Generate a shareable link for a prototype
   * @param accessToken - The access token
   * @returns The shareable link
   */
  generateShareableLink(accessToken: string): string {
    // Use the current origin to ensure the link works in any environment
    const baseUrl = window.location.origin;
    return `${baseUrl}/shared/${accessToken}`;
  },
};

export default shareService;
