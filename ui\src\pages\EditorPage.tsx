import { useEffect, useRef, useState, useCallback } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { streamGeneratedCode, streamModifyElement, streamModifyContent, createPrototype, streamGenerateFunctionality } from '../services/planService';
import { FiCode, FiEye, FiCheckCircle, FiHome, FiSend, FiLoader, FiCheck, FiSave, FiCrosshair, FiX } from 'react-icons/fi';
import { withFinalChunkCallback } from '../services/streamingUtils';
import ShareButton from '../components/ShareButton';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { SelectedElement } from '../components/ElementSelector/SimpleElementSelector';
import { SimpleElementSelector } from '../components/ElementSelector/SimpleElementSelector';
import { ChatElementProperties } from '../components/ChatElementProperties/ChatElementProperties';
import { applyStyles, applyBorderRadius } from '../utils/styleUtils';
import { updateElementStyleInHtml } from '../utils/htmlUtils';
import { UnimplementedElement } from '../components/FunctionalityGenerator';
import { getPrototypeById, updatePrototype } from '../services/prototypeService';
import { LoadingSpinner } from '../components/LoadingSpinner/LoadingSpinner';
import { trackPrototypeCreation, trackPrototypeEdit, trackFeatureUsage } from '../utils/analytics';

// Declare global function and variables for element selection
declare global {
  interface Window {
    handleElementSelected: (data: any) => void;
    selectionModeActive: boolean;
  }
}
// Returns the full HTML for the iframe, using a template approach
function getIframeHtml(content: string): string {
  //console.log('getIframeHtml called with content length:', content?.length || 0);

  // Ensure content is a string and not undefined/null
  const safeContent = content || '';

  try {
    // Create a safe version of the content with scripts properly tagged
    const processedContent = safeContent.replace(/<script>/g, '<script data-exec="inline">');

    return `
    <!DOCTYPE html>
  <html lang="en">
  <head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RM Assistant - Commercial Banking</title>
  <!-- Base tag to ensure all relative links stay within the iframe -->
  <base target="_self">
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script>tailwind.config={theme:{extend:{colors:{primary:'#0047AB',secondary:'#4682B4'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
  <script>
    // This will execute all inline scripts after DOMContentLoaded (needed for charts)
    document.addEventListener('DOMContentLoaded', function() {
      var scripts = document.querySelectorAll('script[data-exec="inline"]');
      scripts.forEach(function(script) {
        try {
          // eslint-disable-next-line no-eval
          eval(script.textContent);
        } catch (e) {
          console.error('Error executing inline script:', e);
        }
      });
    });

  </script>
  <style>
  :where([class^="ri-"])::before { content: "\f3c2"; }
  body {
  font-family: 'Inter', sans-serif;
  background-color: #f9fafb;
  }

  /* Custom scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* For Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }
  .sidebar-item:hover {
  background-color: rgba(0, 71, 171, 0.05);
  }
  .sidebar-item.active {
  background-color: rgba(0, 71, 171, 0.1);
  border-left: 3px solid #0047AB;
  }
  .custom-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  }
  .custom-switch input {
  opacity: 0;
  width: 0;
  height: 0;
  }
  .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
  }
  .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  }
  input:checked + .slider {
  background-color: #0047AB;
  }
  input:checked + .slider:before {
  transform: translateX(20px);
  }
  .custom-checkbox {
  position: relative;
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  }
  .custom-checkbox.checked {
  background-color: #0047AB;
  border-color: #0047AB;
  }
  .custom-checkbox.checked::after {
  content: "";
  width: 10px;
  height: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='3'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M5 13l4 4L19 7' /%3E%3C/svg%3E");
  background-size: cover;
  }
  .custom-radio {
  position: relative;
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  }
  .custom-radio.checked {
  border-color: #0047AB;
  }
  .custom-radio.checked::after {
  content: "";
  width: 10px;
  height: 10px;
  background-color: #0047AB;
  border-radius: 50%;
  }
  .custom-range {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 5px;
  outline: none;
  }
  .custom-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #0047AB;
  border-radius: 50%;
  cursor: pointer;
  }
  .custom-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #0047AB;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  }
  .notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  }
  .tab-button {
  position: relative;
  }
  .tab-button::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #0047AB;
  transition: width 0.3s;
  }
  .tab-button.active::after {
  width: 100%;
  }
  .agent-card {
  transition: all 0.3s;
  }
  .agent-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
  .client-card {
  transition: all 0.3s;
  }
  .client-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
  }
  </style>
  </head>

    <body>
      ${processedContent}
    </body>
    </html>
    `;
  } catch (error) {
    console.error('Error in getIframeHtml:', error);

    // Return a fallback HTML with error message
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Error</title>
      <style>
        body { font-family: sans-serif; padding: 20px; color: #333; }
        .error { color: #e53e3e; border: 1px solid #e53e3e; padding: 15px; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="error">
        <h2>Error rendering content</h2>
        <p>There was an error processing the content. Please try again.</p>
        <p>Error details: ${error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    </body>
    </html>
    `;
  }
}

type ViewMode = 'code' | 'preview';
type EditorMode = 'view' | 'edit';

type ChatMessage = {
  role: 'user' | 'assistant';
  content: string;
};

// Helper function to identify section headings (copied from PlanReviewPage.tsx)
function isSectionHeading(line: string) {
  // Heuristic: heading if all uppercase, or matches "1. Layout", "2. Components", etc.
  return (
    /^[A-Z][A-Za-z\s]+$/.test(line) ||
    /^\d+\.\s*[A-Za-z\s]+$/.test(line)
  );
}

// Custom scrollbar styles and elegant border styles
const scrollbarStyles = `
  /* For Webkit browsers like Chrome/Safari/Edge */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* For Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }

  /* Chat message scrollbar - more subtle */
  .chat-message-container::-webkit-scrollbar {
    width: 4px;
  }

  .chat-message-container::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
  }

  .chat-message-container::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.8);
  }

  /* Elegant border and glow effects */
  .elegant-border {
    border: 1px solid rgba(224, 231, 255, 0.6);
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.15), inset 0 0 3px rgba(255, 255, 255, 0.8);
  }

  .elegant-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
  }

  .gradient-border {
    position: relative;
  }

  .gradient-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(to right, rgba(192, 132, 252, 0.2), rgba(99, 102, 241, 0.2), rgba(147, 197, 253, 0.2));
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
`;

const extractTitleFromPrompt = (prompt: string) => {
  if (!prompt) return 'Untitled Prototype';
  // Use the first non-empty line, trimmed - no arbitrary character limits
  const firstLine = prompt.split('\n').find(line => line.trim())?.trim() || 'Untitled Prototype';
  // Only truncate if extremely long (over 200 chars), but preserve meaningful titles
  return firstLine.length > 200 ? firstLine.slice(0, 200) + '...' : firstLine;
};

export function EditorPage() {
  const location = useLocation();
  const { id } = useParams<{ id?: string }>();
  const plan: string = location.state?.plan || '';
  const initialPrototypeTitle: string = location.state?.prototypeTitle || '';
  const [code, setCode] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('preview');
  const [editorMode, setEditorMode] = useState<EditorMode>('view');
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [prototypeId, setPrototypeId] = useState<string | null>(id || null);
  const [prototypeTitle, setPrototypeTitle] = useState<string>(() => initialPrototypeTitle || 'Untitled Prototype');
  // Loading state is used in loadExistingPrototype
  const [, setIsLoading] = useState(!!id);
  const [prototype, setPrototype] = useState<any>(null);
  // Refs to track state without triggering re-renders
  const prevCodeRef = useRef<string>('');
  const lastIframeUpdateRef = useRef<number>(0);

  // Use a key to force iframe remounting when needed
  const [iframeKey, setIframeKey] = useState<number>(0);

  // State management for streaming and loading
  // streaming: true when content is being streamed from the API
  const [streaming, setStreaming] = useState(false);

  // streamingComplete: true when streaming has finished
  const [streamingComplete, setStreamingComplete] = useState(false);

  // iframeLoaded: true when the iframe has loaded content
  const [iframeLoaded, setIframeLoaded] = useState(false);

  // We don't need generationComplete state anymore

  // streamingProgress: tracks the progress of streaming (0-100)
  const [, setStreamingProgress] = useState(0);

  // streamingType: the type of content being streamed
  const [streamingType, setStreamingType] = useState<'code' | 'element' | 'content' | 'functionality'>('code');

  // State for tracking preview ready notification visibility
  const [showPreviewReady, setShowPreviewReady] = useState(false);

  // Store selected elements
  const [selectedElements, setSelectedElements] = useState<SelectedElement[]>([]);


  // Show element properties in chat
  const [showElementProperties, setShowElementProperties] = useState(false);

  // Chat state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>(() => {
    // Initialize with the plan as the first message if available
    const initialMessages: ChatMessage[] = [];

    if (plan && plan.trim()) {
      // Add plan directly without "Here is my plan:" message
      initialMessages.push({
        role: 'assistant',
        content: plan
      });
    }

    // Add welcome message
    initialMessages.push({
      role: 'assistant',
      content: 'How can I help you with your prototype today?'
    });

    return initialMessages;
  });
  const [chatInput, setChatInput] = useState('');

  // Ref for chat container to scroll to bottom
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Element selector state
  const [chatSelectorActive, setChatSelectorActive] = useState(false);

  // Effect to scroll chat to bottom when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Auto-save related states
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Auto update related states
  const [autoUpdateEnabled, setAutoUpdateEnabled] = useState(false);
  const [isCheckingUpdates, setIsCheckingUpdates] = useState(false);
  const autoUpdateTimerRef = useRef<NodeJS.Timeout | null>(null);
  const AUTO_UPDATE_INTERVAL = 30000; // 30 seconds

  // Load existing prototype or generate code on initial mount
  useEffect(() => {
    console.log('Editor mode changed:', editorMode);

    if (editorMode === 'edit') {
      // When entering edit mode, ensure element selection is active
      if (!chatSelectorActive) {
        console.log('Activating element selection for edit mode');
        setChatSelectorActive(true);
      }

      // Also ensure the iframe knows selection mode is active
      if (iframeRef.current?.contentWindow) {
        console.log('Sending toggleSelectionMode=true message to iframe');
        iframeRef.current.contentWindow.postMessage({
          type: 'toggleSelectionMode',
          active: true
        }, '*');
      }
    } else {
      // When exiting edit mode, deactivate element selection
      if (chatSelectorActive) {
        console.log('Deactivating element selection when exiting edit mode');
        setChatSelectorActive(false);

        // Clear selections and hide properties panel
        setSelectedElements([]);
        setShowElementProperties(false);

        // Clear any selections in the iframe and turn off selection mode
        if (iframeRef.current?.contentWindow) {
          console.log('Sending clearSelections and toggleSelectionMode=false messages to iframe');
          iframeRef.current.contentWindow.postMessage({
            type: 'clearSelections'
          }, '*');

          iframeRef.current.contentWindow.postMessage({
            type: 'toggleSelectionMode',
            active: false
          }, '*');
        }
      }
    }
  }, [editorMode, chatSelectorActive, setChatSelectorActive, setSelectedElements, setShowElementProperties, iframeRef]);

  // Functionality generator state - these states are used in handleGenerateFunctionality
  // Using underscore prefix to indicate they're used but linter doesn't detect it
  const [_unimplementedElement, setUnimplementedElement] = useState<UnimplementedElement | null>(null);
  const [_functionalityPrompt, setFunctionalityPrompt] = useState('');
  const [_isGeneratingFunctionality, setIsGeneratingFunctionality] = useState(false);

  // No debounced function needed as we update code directly

  // Load existing prototype or generate code on initial mount
  useEffect(() => {
    if (id) {
      loadExistingPrototype();
    } else if (plan) {
      generateCode();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, plan]);

  // When plan or initialPrototypeTitle changes (new prototype), update the title
  useEffect(() => {
    if (initialPrototypeTitle) setPrototypeTitle(initialPrototypeTitle);
    else setPrototypeTitle('Untitled Prototype');
  }, [initialPrototypeTitle]);

  // Function to load an existing prototype
  const loadExistingPrototype = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      setError(null);

      // Reset iframe states
      setStreamingComplete(false);
      setIframeLoaded(false);

      // Set initial load flag BEFORE any code is set
      isInitialLoadRef.current = true;

      const loadedPrototype = await getPrototypeById(parseInt(id, 10));
      console.log('Loaded prototype:', loadedPrototype);
      setPrototype(loadedPrototype);

      // Set the code
      setCode(loadedPrototype.html);

      // Set the prototype title from loaded prototype
      if (loadedPrototype.title) {
        setPrototypeTitle(loadedPrototype.title);
      } else {
        setPrototypeTitle('Untitled Prototype');
      }

      // Set plan from description if available
      if (loadedPrototype.description) {
        setChatMessages([
          {
            role: 'assistant',
            content: 'Here is your prototype. You can continue to modify it by sending messages.'
          }
        ]);
      }

      // Mark streaming as complete
      setStreamingComplete(true);
    } catch (err: any) {
      console.error('Error loading prototype:', err);
      setError(`Failed to load prototype: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // We don't need a helper function to update the iframe content anymore
  // The iframe will load content automatically based on the srcDoc condition

  // Clean, reliable state management for streaming and iframe loading

  // Effect 1: Handle view mode changes with a more robust approach
  useEffect(() => {
    if (viewMode === 'preview') {
      console.log('Switched to preview mode');

      // If we already have completed streaming and the iframe isn't loaded yet,
      // force a remount of the iframe
      if (streamingComplete && code && !streaming) {
        console.log('In preview mode with content available');

        // Only force reload if not already loaded
        if (!iframeLoaded) {
          console.log('Iframe not loaded, forcing remount');

          // Force iframe remount by incrementing the key
          setIframeKey(prevKey => prevKey + 1);
        }
      }
    }
  }, [viewMode, streamingComplete, code, streaming, iframeLoaded]);

  // Effect 2: Handle streaming state changes with a more robust approach
  useEffect(() => {
    if (streaming) {
      // When streaming starts, reset all states
      console.log('Streaming started, resetting states');
      setIframeLoaded(false);
      setStreamingComplete(false);
      setShowPreviewReady(false); // Hide preview ready notification when streaming starts

      // Reset the previous code ref to force a fresh comparison
      prevCodeRef.current = '';
    }
    // We don't need to handle streaming end here anymore
    // The final chunk callback will set streamingComplete to true
  }, [streaming]);

  // Effect 3: Show preview ready toast only when all conditions are met
  useEffect(() => {
    // Only show toast if not initial load (i.e., after streaming/generation)
    if (!isInitialLoadRef.current && streamingComplete && code && !streaming && iframeLoaded) {
      setShowPreviewReady(true);
    }
  }, [streamingComplete, code, streaming, iframeLoaded]);

  // Effect 4: Auto-hide preview ready notification after a timeout
  useEffect(() => {
    if (showPreviewReady) {
      const timeoutId = setTimeout(() => {
        setShowPreviewReady(false);
      }, 5000); // Auto-hide after 5 seconds

      return () => clearTimeout(timeoutId); // Clean up on unmount or when showPreviewReady changes
    }
  }, [showPreviewReady]);

  // We don't need a separate message handler for iframe events
  // The element selector component already has its own message handler for selection events

  const generateCode = () => {
    // Clear existing code and set streaming state
    setCode('');
    setError(null);

    // Reset all states and start streaming
    setStreaming(true);
    setStreamingProgress(0);
    setStreamingComplete(false);
    setIframeLoaded(false);
    setStreamingType('code');

    console.log('Starting code generation...');

    let cancelled = false;
    let totalChunks = 0;

    (async () => {
      try {
        console.log('Initializing code generation stream...');
        let streamed = '';

        // No timeout promise - let the final chunk callback handle completion
        // The Deepseek LLM provider stops responding after ~3.2 seconds even when the connection remains open
        // We'll rely on the withFinalChunkCallback utility to detect when the final chunk is received

        // Create a streaming function that can be raced with the timeout
        const streamingPromise = async () => {
          let lastUpdateTime = Date.now();
          const updateInterval = 300; // Update every 300ms to avoid excessive re-renders

          // Use the withFinalChunkCallback utility to detect when the final chunk is received
          const streamWithCallback = withFinalChunkCallback(
            streamGeneratedCode(plan),
            () => {
              console.log('Final chunk received from LLM service');

              // Set streaming to false and streamingComplete to true immediately
              // This will load the iframe with the final content
              setStreaming(false);
              setStreamingComplete(true);
            }
          );

          for await (const chunk of streamWithCallback) {
            if (cancelled) break;
            totalChunks++;

            // Log every 5 chunks to avoid console spam
            if (totalChunks % 5 === 0) {
             // console.log(`Received code chunk #${totalChunks}, size: ${chunk.length} bytes`);
            }

            streamed += chunk;

            // Only update the UI periodically to avoid excessive re-renders
            const now = Date.now();
            if (now - lastUpdateTime > updateInterval) {
              // Only update code if it has changed
              if (streamed !== code) {
                setCode(streamed);
              }

              // Update progress - we'll estimate based on chunk count and size
              const estimatedProgress = Math.min(95, Math.floor((streamed.length / 10000) * 100));
              setStreamingProgress(estimatedProgress);

              lastUpdateTime = now;
            }
          }

          // Final update to ensure we have the complete code
          if (streamed !== code) {
            setCode(streamed);
          }
          setStreamingProgress(100);
          console.log('Code stream completed successfully, total chunks:', totalChunks);
        };

        // Just await the streaming promise directly - no race with timeout
        await streamingPromise();
        console.log('Code stream processing complete');
        // setGenerationComplete(true);
      } catch (err: any) {
        console.error('Error generating code:', err);

        // Check if this is a quota exceeded error
        if (err.message && err.message.includes('QUOTA_EXCEEDED')) {
          setError('Quota exceeded: You\'ve reached your prototype limit. Please upgrade your plan to continue.');

          // Add a message to the chat about quota being exceeded
          setChatMessages(prev => [
            ...prev,
            {
              role: 'assistant',
              content: 'You\'ve reached your prototype limit. Please upgrade your plan to continue creating prototypes.'
            }
          ]);
        } else {
          // For other errors, don't show the error message to the user, but log it for debugging
          // setError('Failed to stream generated code. Please try again.');
        }

        // Try to recover by using any code that was successfully streamed
        if (code) {
          console.log('Using partially streamed code for recovery');

          // We already have the code from the streaming process

          // In case of error, still stop streaming and show the content
          setStreaming(false);
          setStreamingComplete(true);

          // Show a user-friendly error message
          const errorMessage = err instanceof Error ? err.message : 'Unknown error';
          console.log('Showing error toast to user:', errorMessage);

          // You could add a toast notification here if you have a toast component
          // toast.error(`Code generation encountered an issue: ${errorMessage}`);
        } else {
          // If we have no streamed content at all, this is a more serious error
          console.error('No code was streamed before the error occurred');
          setStreaming(false);

          // You could add a more serious error notification here
          // toast.error('Code generation failed completely. Please try again.');
        }
      }
    })();

    return () => {
      cancelled = true;
    };
  };

  // State for tracking element modification in progress
  const [modifyingElement, setModifyingElement] = useState(false);

  // Handle chat send
  const handleChatSend = () => {
    if (!chatInput.trim()) return;

    // Add user message
    const userMessage = chatInput.trim();
    setChatMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setChatInput('');

    // Check if we have a selected element and the selector is active
    if (selectedElements.length > 0 && chatSelectorActive) {
      // Show loading state
      setModifyingElement(true);
      setChatMessages(prev => [
        ...prev,
        {
          role: 'assistant',
          content: 'Modifying the selected element...'
        }
      ]);

      // Use the code from React state instead of getting HTML from iframe
      const selectedElement = selectedElements[0];

      // Enhance the prompt with element description if available
      let enhancedPrompt = userMessage;
      if (selectedElement.description) {
        enhancedPrompt = `${userMessage}\n\nElement description: ${selectedElement.description}`;
        console.log('Enhanced prompt with element description:', selectedElement.description);
      }

      // Create a request object for element modification using the code state
      const request = {
        htmlContent: code, // Use the code state directly
        elementSelector: selectedElement.ref,
        prompt: enhancedPrompt // Use the enhanced prompt with element description
      };

      console.log('Using code from React state for element modification with selector:', selectedElement.ref);

      let cancelled = false;

      // Reset states before starting
      setStreamingProgress(0);
      setStreamingComplete(false);
      setIframeLoaded(false);
      setStreaming(true);
      setStreamingType('element');

      (async () => {
        try {
          console.log('Starting element modification stream...');
          let streamed = '';
          let totalChunks = 0;

          // No timeout promise - let the final chunk callback handle completion
          // The Deepseek LLM provider stops responding after ~3.2 seconds even when the connection remains open
          // We'll rely on the withFinalChunkCallback utility to detect when the final chunk is received

          // Create a streaming function that can be raced with the timeout
          const streamingPromise = async () => {
            let lastUpdateTime = Date.now();
            const updateInterval = 300; // Update every 300ms to avoid excessive re-renders

            // Use the withFinalChunkCallback utility to detect when the final chunk is received
            const streamWithCallback = withFinalChunkCallback(
              streamModifyElement(request),
              () => {
                console.log('Final element modification chunk received from LLM service');

                // Set streaming to false and streamingComplete to true immediately
                // This will load the iframe with the final content
                setStreaming(false);
                setStreamingComplete(true);
              }
            );

            for await (const chunk of streamWithCallback) {
              if (cancelled) break;
              totalChunks++;
              streamed += chunk;

              // Only update the UI periodically to avoid excessive re-renders
              const now = Date.now();
              if (now - lastUpdateTime > updateInterval) {
                setCode(streamed);

                // Update progress - we'll estimate based on chunk count and size
                const estimatedProgress = Math.min(95, Math.floor((streamed.length / 5000) * 100));
                setStreamingProgress(estimatedProgress);

                lastUpdateTime = now;
              }
            }

            // Final update to ensure we have the complete code
            setCode(streamed);
            setStreamingProgress(100);
            console.log('Element modification stream completed successfully, total chunks:', totalChunks);

            // We'll set streamingComplete to true when streaming state changes
            // This will trigger the iframe to load content
          };

          // Just await the streaming promise directly - no race with timeout
          await streamingPromise();

          console.log('Element modification stream processing complete');

          // Show success message
          setChatMessages(prev => {
            // Remove the loading message
            const messages = prev.slice(0, -1);
            // Add success message
            return [
              ...messages,
              {
                role: 'assistant',
                content: `I've modified the ${selectedElement.tagName} element according to your request. Is there anything else you'd like to change?`
              }
            ];
          });

          // Set completion state
          // setGenerationComplete(true);
        } catch (err: any) {
          console.error('Error modifying element:', err);

          // Check if this is a quota exceeded error
          if (err.message && err.message.includes('QUOTA_EXCEEDED')) {
            // Show quota exceeded message
            setChatMessages(prev => {
              // Remove the loading message
              const messages = prev.slice(0, -1);
              // Add quota exceeded message
              return [
                ...messages,
                {
                  role: 'assistant',
                  content: `You've reached your prototype limit. Please upgrade your plan to continue creating and modifying prototypes.`
                }
              ];
            });

            // Set error state
            setError('Quota exceeded: You\'ve reached your prototype limit. Please upgrade your plan to continue.');
          }
          // Try to recover by using any HTML that was successfully streamed
          else if (code) {
            console.log('Using partially streamed HTML for element modification');
            // In case of error, still stop streaming and show the content
            setStreaming(false);
            setStreamingComplete(true);

            // Show a more user-friendly message
            setChatMessages(prev => {
              // Remove the loading message
              const messages = prev.slice(0, -1);
              // Add a more helpful message
              return [
                ...messages,
                {
                  role: 'assistant',
                  content: `I've applied some changes to the ${selectedElement.tagName} element. Is there anything specific you'd like me to adjust further?`
                }
              ];
            });

            // We'll let the iframe's onLoad event handler set generationComplete
          } else {
            // If we have no code at all, show a generic message
            setChatMessages(prev => {
              // Remove the loading message
              const messages = prev.slice(0, -1);
              // Add a generic message
              return [
                ...messages,
                {
                  role: 'assistant',
                  content: `I'm having trouble modifying that element right now. Could you try a different approach or be more specific about what you'd like to change?`
                }
              ];
            });
          }
        } finally {
          setModifyingElement(false);
          // Don't set streaming to false here, it's handled by the final chunk callback
          // In case of error, the catch block will set it
        }
      })();

      return () => {
        cancelled = true;
      };
    } else {
      // No element selected - modify the entire content based on the prompt
      setModifyingElement(true);

      // Show loading state
      setChatMessages(prev => [
        ...prev,
        {
          role: 'assistant',
          content: 'Modifying the content...'
        }
      ]);

      // Create a request object for content modification using the code state
      const request = {
        htmlContent: code, // Use the code state directly
        prompt: userMessage
      };

      console.log('Using code from React state for content modification');

      let cancelled = false;

      // Reset states before starting
      setStreamingProgress(0);
      setStreamingComplete(false);
      setIframeLoaded(false);
      setStreaming(true);
      setStreamingType('content');

      (async () => {
        try {
          console.log('Starting content modification stream...');
          let streamed = '';
          let totalChunks = 0;

          // No timeout promise - let the final chunk callback handle completion
          // The Deepseek LLM provider stops responding after ~3.2 seconds even when the connection remains open
          // We'll rely on the withFinalChunkCallback utility to detect when the final chunk is received

          // Create a streaming function that can be raced with the timeout
          const streamingPromise = async () => {
            let lastUpdateTime = Date.now();
            const updateInterval = 300; // Update every 300ms to avoid excessive re-renders

            // Use the withFinalChunkCallback utility to detect when the final chunk is received
            const streamWithCallback = withFinalChunkCallback(
              streamModifyContent(request),
              () => {
                console.log('Final content modification chunk received from LLM service');

                // Set streaming to false and streamingComplete to true immediately
                // This will load the iframe with the final content
                setStreaming(false);
                setStreamingComplete(true);
              }
            );

            for await (const chunk of streamWithCallback) {
              if (cancelled) break;
              totalChunks++;
              streamed += chunk;

              // Only update the UI periodically to avoid excessive re-renders
              const now = Date.now();
              if (now - lastUpdateTime > updateInterval) {
                setCode(streamed);

                // Update progress - we'll estimate based on chunk count and size
                const estimatedProgress = Math.min(95, Math.floor((streamed.length / 5000) * 100));
                setStreamingProgress(estimatedProgress);

                lastUpdateTime = now;
              }
            }

            // Final update to ensure we have the complete code
            setCode(streamed);
            setStreamingProgress(100);
           // console.log('Content modification stream completed successfully, total chunks:', totalChunks);

            // We'll set streamingComplete to true when streaming state changes
            // This will trigger the iframe to load content
          };

          // Just await the streaming promise directly - no race with timeout
          await streamingPromise();

          console.log('Content modification stream processing complete');

          // Show success message
          setChatMessages(prev => {
            // Remove the loading message
            const messages = prev.slice(0, -1);
            // Add success message
            return [
              ...messages,
              {
                role: 'assistant',
                content: `I've modified the content according to your request. Is there anything else you'd like to change?`
              }
            ];
          });

          // Set completion state
          // setGenerationComplete(true);
        } catch (err: any) {
          console.error('Error modifying content:', err);

          // Check if this is a quota exceeded error
          if (err.message && err.message.includes('QUOTA_EXCEEDED')) {
            // Show quota exceeded message
            setChatMessages(prev => {
              // Remove the loading message
              const messages = prev.slice(0, -1);
              // Add quota exceeded message
              return [
                ...messages,
                {
                  role: 'assistant',
                  content: `You've reached your prototype limit. Please upgrade your plan to continue creating and modifying prototypes.`
                }
              ];
            });

            // Set error state
            setError('Quota exceeded: You\'ve reached your prototype limit. Please upgrade your plan to continue.');
          }
          // Try to recover by using any HTML that was successfully streamed
          else if (code) {
            console.log('Using partially streamed HTML for content modification');
            // In case of error, still stop streaming and show the content
            setStreaming(false);
            setStreamingComplete(true);

            // Show a more user-friendly message
            setChatMessages(prev => {
              // Remove the loading message
              const messages = prev.slice(0, -1);
              // Add a more helpful message
              return [
                ...messages,
                {
                  role: 'assistant',
                  content: `I've applied some changes to the content. Is there anything specific you'd like me to adjust further?`
                }
              ];
            });

            // We'll let the iframe's onLoad event handler set generationComplete
          } else {
            // If we have no code at all, show a generic message
            setChatMessages(prev => {
              // Remove the loading message
              const messages = prev.slice(0, -1);
              // Add a generic message
              return [
                ...messages,
                {
                  role: 'assistant',
                  content: `I'm having trouble modifying the content right now. Could you try a different approach or be more specific about what you'd like to change?`
                }
              ];
            });
          }
        } finally {
          setModifyingElement(false);
          // Don't set streaming to false here, it's handled by the final chunk callback
          // In case of error, the catch block will set it
        }
      })();

      return () => {
        cancelled = true;
      };
    }
  };

  // Save the prototype to the database
  const savePrototype = async () => {
    if (!code || streaming) {
      return;
    }

    try {
      setSaving(true);
      setError(null);

      // Extract CSS from the code if available
      let css = '';
      const styleTagMatch = code.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      if (styleTagMatch && styleTagMatch[1]) {
        css = styleTagMatch[1].trim();
      }

      // Always trim title to 250 chars
      const trimmedTitle = (prototypeTitle || extractTitleFromPrompt(plan)).slice(0, 250);

      // If we have a prototype ID, update the existing prototype
      if (prototypeId) {
        // Use the existing prototype's title and description if available
        const title = trimmedTitle;
        const description = prototype?.description || plan || 'Generated prototype';

        const updatedPrototype = await updatePrototype(parseInt(prototypeId, 10), {
          title,
          description,
          html: code,
          css
        });

        setPrototype(updatedPrototype);
        setSaveSuccess(true);

        // Track prototype edit
        trackPrototypeEdit(prototypeId, title);

        // Show success message in chat
        setChatMessages(prev => [
          ...prev,
          {
            role: 'assistant',
            content: 'Your prototype has been updated successfully!'
          }
        ]);
      }
      // Otherwise create a new prototype
      else {
        // Use prototypeTitle for new prototype
        const title = trimmedTitle;
        const description = plan || 'Generated prototype';

        const result = await createPrototype({
          title,
          description,
          html: code,
          css
        });

        if (result.success) {
          const newPrototypeId = result.prototype?.id?.toString() || null;
          setPrototypeId(newPrototypeId);
          setSaveSuccess(true);

          // Track prototype creation
          if (newPrototypeId) {
            trackPrototypeCreation(newPrototypeId, title);
          }

          // Show success message in chat
          setChatMessages(prev => [
            ...prev,
            {
              role: 'assistant',
              content: 'Your prototype has been saved successfully!'
            }
          ]);
        } else if (result.quotaExceeded) {
          setError('Quota exceeded: You\'ve reached your prototype limit. Please upgrade your plan to continue.');
          return;
        } else {
          setError('Failed to save prototype. Please try again.');
          return;
        }
      }

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (err: any) {
      console.error('Error saving prototype:', err);
      setError(`Failed to save prototype: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  // Handle generating functionality for an element selected through the element selector
  const handleElementFunctionality = (element: SelectedElement) => {
    if (!element) return;

    // Create a default prompt based on the element type
    let defaultPrompt = `Implement functionality for this ${element.tagName}`;

    // Convert the SelectedElement to UnimplementedElement format
    const unimplementedElement: UnimplementedElement = {
      id: element.id,
      tagName: element.tagName,
      elementId: element.elementId,
      className: element.className,
      ref: element.ref,
      description: element.description,
      type: element.tagName,
      context: ''
    };

    // Track feature usage
    trackFeatureUsage('element_functionality', {
      element_type: element.tagName,
      has_description: !!element.description
    });

    // Call the functionality generator
    handleGenerateFunctionality(unimplementedElement, defaultPrompt);
  };

  // Handle generating functionality for unimplemented elements
  const handleGenerateFunctionality = (element: UnimplementedElement, prompt: string) => {
    // Don't allow if streaming or not in preview mode
    if (streaming || viewMode !== 'preview' || !code) {
      return;
    }

    // Set state for the modal
    setUnimplementedElement(element);
    setFunctionalityPrompt(prompt);

    // Show loading state in chat
    setChatMessages(prev => [
      ...prev,
      {
        role: 'user',
        content: `Generate functionality for ${element.type}: ${element.description || element.ref}`
      },
      {
        role: 'assistant',
        content: `Generating functionality for the ${element.type}...`
      }
    ]);

    // Start generating functionality
    setIsGeneratingFunctionality(true);

    // Reset states before starting
    setStreamingProgress(0);
    setStreamingComplete(false);
    setIframeLoaded(false);
    setStreaming(true);
    setStreamingType('functionality');

    // Create a request object for functionality generation
    const request = {
      htmlContent: code,
      elementSelector: element.ref,
      elementType: element.type,
      elementContext: element.context || '',
      prompt: prompt
    };

    let cancelled = false;

    (async () => {
      try {
        console.log('Starting functionality generation stream...');
        let streamed = '';
        let totalChunks = 0;

        // No timeout promise - let the final chunk callback handle completion
        // The Deepseek LLM provider stops responding after ~3.2 seconds even when the connection remains open
        // We'll rely on the withFinalChunkCallback utility to detect when the final chunk is received

        // Create a streaming function that can be raced with the timeout
        const streamingPromise = async () => {
          let lastUpdateTime = Date.now();
          const updateInterval = 300; // Update every 300ms to avoid excessive re-renders

          // Use the withFinalChunkCallback utility to detect when the final chunk is received
          const streamWithCallback = withFinalChunkCallback(
            streamGenerateFunctionality(request),
            () => {
              console.log('Final functionality generation chunk received from LLM service');

              // Set streaming to false and streamingComplete to true immediately
              // This will load the iframe with the final content
              setStreaming(false);
              setStreamingComplete(true);
            }
          );

          for await (const chunk of streamWithCallback) {
            if (cancelled) break;
            totalChunks++;
            streamed += chunk;

            // Only update the UI periodically to avoid excessive re-renders
            const now = Date.now();
            if (now - lastUpdateTime > updateInterval) {
              setCode(streamed);

              // Update progress - we'll estimate based on chunk count and size
              const estimatedProgress = Math.min(95, Math.floor((streamed.length / 5000) * 100));
              setStreamingProgress(estimatedProgress);

              lastUpdateTime = now;
            }
          }

          // Final update to ensure we have the complete code
          setCode(streamed);
          setStreamingProgress(100);
          console.log('Functionality generation stream completed successfully, total chunks:', totalChunks);

          // We'll set streamingComplete to true when streaming state changes
          // This will trigger the iframe to load content
        };

        // Just await the streaming promise directly - no race with timeout
        await streamingPromise();

        console.log('Functionality generation stream processing complete');

        // Show success message
        setChatMessages(prev => {
          // Remove the loading message
          const messages = prev.slice(0, -1);
          // Add success message
          return [
            ...messages,
            {
              role: 'assistant',
              content: `I've implemented functionality for the ${element.type}. Try it out in the preview!`
            }
          ];
        });

        // Clear the unimplemented element
        setUnimplementedElement(null);

        // We'll set generationComplete to true when the iframe is loaded
        // This is handled in the iframe's onLoad event handler
      } catch (err) {
        console.error('Error generating functionality:', err);

        // Try to recover by using any HTML that was successfully streamed
        if (code) {
          console.log('Using partially streamed HTML for functionality generation');
          // In case of error, still stop streaming and show the content
          setStreaming(false);
          setStreamingComplete(true);

          // Show a more user-friendly message
          setChatMessages(prev => {
            // Remove the loading message
            const messages = prev.slice(0, -1);
            // Add a more helpful message
            return [
              ...messages,
              {
                role: 'assistant',
                content: `I've implemented some functionality for the ${element.type}. Is there anything specific you'd like me to adjust?`
              }
            ];
          });
        } else {
          // If we have no code at all, show a generic message
          setChatMessages(prev => {
            // Remove the loading message
            const messages = prev.slice(0, -1);
            // Add a generic message
            return [
              ...messages,
              {
                role: 'assistant',
                content: `I'm having trouble implementing functionality for that element right now. Could you try a different approach?`
              }
            ];
          });
        }

        // Clear the unimplemented element
        setUnimplementedElement(null);
      } finally {
        setIsGeneratingFunctionality(false);
        // Don't set streaming to false here, it's handled by the final chunk callback
        // In case of error, the catch block will set it
      }
    })();

    return () => {
      cancelled = true;
    };
  };

  // Global function to handle element selection from iframe
  useEffect(() => {
    // Add a global function to handle element selection
    window.handleElementSelected = (data) => {
      console.log('Global handleElementSelected called with data:', data);

      if (!data) return;

      // Create a selected element object
      const element = {
        id: data.uniqueId || `el-${Date.now().toString(36)}-${Math.floor(Math.random() * 10000).toString(36)}`,
        tagName: data.tagName,
        elementId: data.id || undefined,
        className: data.className || undefined,
        ref: data.selector,
        description: data.description || undefined,
        textContent: data.textContent || undefined,
        styles: data.styles || undefined
      };

      console.log('Created element object from global handler:', element);

      // Update the selected elements state
      setSelectedElements([element]);

      // Show the properties panel
      setShowElementProperties(true);

      // Make sure edit mode is active
      setEditorMode('edit');
    };

    return () => {
      // Clean up the global function
      window.handleElementSelected = undefined as any;
    };
  }, []);

  // Memoized toggle element selector function to prevent unnecessary re-renders
  const toggleChatElementSelector = useCallback(() => {
    // Don't allow toggling if streaming, not in preview mode, or iframe not loaded
    if (streaming || viewMode !== 'preview' || !iframeLoaded || !streamingComplete || !code) {
      console.log('Cannot toggle element selector:');
      console.log('- streaming:', streaming);
      console.log('- viewMode:', viewMode);
      console.log('- iframeLoaded:', iframeLoaded);
      console.log('- streamingComplete:', streamingComplete);
      console.log('- code exists:', !!code);
      return;
    }

    // If we're activating the selector, make sure we're in preview mode
    const newSelectorState = !chatSelectorActive;

    if (newSelectorState && viewMode !== 'preview') {
      // Switch to preview mode first
      setViewMode('preview');
    }

    console.log('Toggling chat element selector:', newSelectorState);

    // Set the chat selector active state
    setChatSelectorActive(newSelectorState);

    // Also set the editor mode to edit when activating the selector
    // or to view when deactivating it
    setEditorMode(newSelectorState ? 'edit' : 'view');

    // Set the global window.selectionModeActive based on newSelectorState
    if (typeof window !== 'undefined') {
      window.selectionModeActive = newSelectorState;
      console.log('Set window.selectionModeActive to:', newSelectorState);
    }

    // If turning off selection mode, close the advanced property panel and clear selections
    if (!newSelectorState) {
      console.log('Element selector toggled off, closing advanced property panel');
      setShowElementProperties(false);
      setSelectedElements([]);

      // Clean up any highlights in the iframe
      if (iframeRef.current?.contentWindow) {
        console.log('Sending clearSelections message to iframe');
        iframeRef.current.contentWindow.postMessage({
          type: 'clearSelections'
        }, '*');
      }
    }
  }, [
    streaming,
    viewMode,
    iframeLoaded,
    streamingComplete,
    code,
    chatSelectorActive,
    setViewMode,
    setChatSelectorActive,
    setEditorMode,
    setShowElementProperties,
    setSelectedElements,
    iframeRef
  ]);

  // Track if the code was loaded from server (edit) or generated (streaming)
  const isInitialLoadRef = useRef(true);

  // Effect to track code changes and trigger auto-save
  useEffect(() => {
    // Skip if streaming is in progress or if code is empty
    if (streaming || !code) return;

    // Only set unsaved changes if this is NOT the initial load
    if (!isInitialLoadRef.current && prevCodeRef.current !== code) {
      setHasUnsavedChanges(true);
    }

    // Update the previous code reference
    prevCodeRef.current = code;
  }, [code, streaming]);

  // Effect to mark initial load as complete after first code set
  useEffect(() => {
    if (isInitialLoadRef.current && code) {
      isInitialLoadRef.current = false;
    }
  }, [code]);

  // Utility to clean up LLM continuation markers and "nothing more" messages
  function cleanLLMContinuationMarkers(html: string): string {
    return html
      .replace(/Continue\s+EXACTLY\s+from\s+where\s+you\s+left\s+off\.?/gi, '')
      .replace(/There is nothing more to continue\.? The HTML document is already complete and properly closed\.?/gi, '')
      .replace(/^\s*Continue\s*$/gim, '')
      .trim();
  }

  // Effect to auto-save after LLM streaming completes
  useEffect(() => {
    // Check if we should auto-save when streaming completes
    // Prevent multiple auto-saves by using a ref
    const autoSaveTriggeredRef = (window as any).__autoSaveTriggeredRef || { current: false };
    (window as any).__autoSaveTriggeredRef = autoSaveTriggeredRef;

    // Fix: Check for LLM continuation marker in code before auto-saving
    const containsContinuationMarker =
      typeof code === 'string' &&
      (
        /Continue\s+EXACTLY\s+from\s+where\s+you\s+left\s+off/i.test(code) ||
        /^\s*Continue\s*$/im.test(code)
      );

    if (containsContinuationMarker) {
      // Optionally, show a warning to the user here
      console.warn(
        '[LLM Output] Detected continuation marker ("Continue" or similar) in HTML output. Auto-save is blocked until full output is received.'
      );
      // Optionally, you can set an error state or toast here
      return;
    }

    if (!streaming && streamingComplete && autoSaveEnabled && hasUnsavedChanges && !saving) {
      if (!autoSaveTriggeredRef.current) {
        autoSaveTriggeredRef.current = true;
        console.log('Auto-saving after LLM response completed');
        savePrototype();
      }
    } else {
      autoSaveTriggeredRef.current = false;
    }
  }, [streaming, streamingComplete, autoSaveEnabled, hasUnsavedChanges, saving, code]);

  // Effect to handle auto update feature
  useEffect(() => {
    // Only set up auto update if enabled and we have a prototype ID
    if (!autoUpdateEnabled || !prototypeId || streaming) {
      return;
    }

    // Function to check for updates
    const checkForUpdates = async () => {
      if (saving || streaming || isCheckingUpdates) return;

      try {
        console.log('Checking for remote updates');
        setIsCheckingUpdates(true);

        // Fetch the latest prototype version
        const latestPrototype = await getPrototypeById(parseInt(prototypeId, 10));

        // Compare with current version - only update if:
        // 1. Remote version is newer (check updated_at timestamp)
        // 2. User doesn't have unsaved changes
        const remoteUpdateTime = new Date(latestPrototype.updated_at).getTime();
        const localUpdateTime = lastSaved ? lastSaved.getTime() : 0;

        if (remoteUpdateTime > localUpdateTime && !hasUnsavedChanges) {
          console.log('Remote update detected, applying changes');

          // Update the local prototype data
          setPrototype(latestPrototype);
          setCode(latestPrototype.html);

          // Add a message in chat
          setChatMessages(prev => [
            ...prev,
            {
              role: 'assistant',
              content: 'The prototype was updated from the server with latest changes.'
            }
          ]);

          // Update the last saved timestamp
          setLastSaved(new Date());
        } else {
          console.log('No remote updates or local changes pending');
        }
      } catch (error) {
        console.error('Error checking for updates:', error);
      } finally {
        setIsCheckingUpdates(false);
      }
    };

    // Set up interval for checking updates
    const intervalId = setInterval(checkForUpdates, AUTO_UPDATE_INTERVAL);

    // Clean up on unmount or when feature is disabled
    return () => {
      clearInterval(intervalId);
    };
  }, [autoUpdateEnabled, prototypeId, streaming, saving, isCheckingUpdates, hasUnsavedChanges, lastSaved]);

  // Update lastSaved timestamp on successful save
  useEffect(() => {
    if (saveSuccess) {
      console.log('Save successful, updating lastSaved timestamp');
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    }
  }, [saveSuccess]);

  // Layout logic
  const showCode = viewMode === 'code';
  const showPreview = viewMode === 'preview';

  return (
    <>
      <style>{scrollbarStyles}</style>
      <div className="flex flex-col w-screen h-[calc(100vh-56px)] overflow-hidden bg-gray-50 font-sans elegant-glow z-0">
        <div className="flex justify-between items-center px-4 py-3 bg-white border-b border-gray-200 h-14 shadow-sm min-h-[56px]">
        <div className="flex items-center gap-3">
          <button
            className="bg-transparent border-none text-gray-600 text-xl cursor-pointer p-2 rounded-full transition-all duration-200 hover:bg-gray-100 hover:text-gray-800 flex items-center justify-center"
            onClick={() => window.history.back()}
            title="Home"
          >
            <FiHome />
          </button>
          <div className="font-semibold text-base text-gray-900">
            {prototypeTitle}
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex justify-center">
            <div className="flex relative bg-gray-100 rounded-full p-1 border border-gray-200 w-[220px]">
              <button
                className={`flex items-center justify-center gap-1.5 py-1.5 px-3 rounded-full border-none bg-transparent text-gray-500 text-sm font-medium cursor-pointer transition-all duration-200 hover:text-gray-900 z-10 flex-1 ${viewMode === 'code' ? 'text-gray-900' : ''}`}
                onClick={() => setViewMode('code')}
                title="Code View"
              >
                <FiCode />
                <span>Code</span>
              </button>
              <button
                className={`flex items-center justify-center gap-1.5 py-1.5 px-3 rounded-full border-none bg-transparent text-gray-500 text-sm font-medium cursor-pointer transition-all duration-200 hover:text-gray-900 z-10 flex-1 ${viewMode === 'preview' ? 'text-gray-900' : ''}`}
                onClick={() => setViewMode('preview')}
                title="Preview"
              >
                <FiEye />
                <span>Preview</span>
              </button>
              <div
                className={`absolute top-1 h-[calc(100%-8px)] w-[calc(50%-4px)] bg-white rounded-full transition-all duration-300 shadow-sm ${viewMode === 'preview' ? 'left-[calc(50%+0px)]' : 'left-1'}`}
              ></div>
            </div>
          </div>
          <button
            className={`py-1.5 px-4 rounded-full border ${editorMode === 'edit' ? 'bg-indigo-600 border-indigo-600 text-white' : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'} text-sm font-medium cursor-pointer transition-all duration-200`}
            onClick={() => {
              // Toggle edit mode
              const newMode = editorMode === 'edit' ? 'view' : 'edit';
              setEditorMode(newMode);
              if (typeof window !== 'undefined') {
                window.selectionModeActive = newMode === 'edit';
              }
              if (newMode === 'view') {
                const overlay = document.querySelector('div[style*="cursor: crosshair"]');
                if (overlay && overlay.parentNode) {
                  overlay.parentNode.removeChild(overlay);
                }
                if (iframeRef.current) {
                  const iframeDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
                  if (iframeDocument) {
                    const highlightedElements = iframeDocument.querySelectorAll('.element-hover-highlight, .element-selected-highlight, .element-multi-selected');
                    highlightedElements.forEach((el: Element) => {
                      el.classList.remove('element-hover-highlight');
                      el.classList.remove('element-selected-highlight');
                      el.classList.remove('element-multi-selected');
                    });
                  }
                }
              }
            }}
            title={editorMode === 'edit' ? 'Exit Edit Mode' : 'Edit Elements'}
            disabled={streaming || !code}
          >
            {editorMode === 'edit' ? 'Exit Edit Mode' : 'Edit Elements'}
          </button>
          {prototypeId && (
            <ShareButton
              prototypeId={prototypeId}
              prototypeName={prototype?.title || plan.split('\n')[0]?.trim() || 'My Prototype'}
              className="mr-2"
            />
          )}
          <button
            className={`py-1.5 px-4 rounded-full border-none ${!code || streaming || saving ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700 cursor-pointer'} text-white text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1.5`}
            disabled={!code || streaming || saving}
            onClick={savePrototype}
          >
            {saving ? (
              <>
                <FiLoader className="animate-spin" />
                Saving...
              </>
            ) : saveSuccess ? (
              <>
                <FiCheck />
                Saved!
              </>
            ) : prototypeId ? (
              <>
                <FiSave className="mr-1" />
                Update Prototype
              </>
            ) : (
              <>
                <FiSave className="mr-1" />
                Save Prototype
              </>
            )}
          </button>
        </div>
      </div>

      <div className="flex flex-1 h-[calc(100vh-70px)] w-full overflow-hidden box-border">
        <div className="w-[30%] min-w-[300px] max-w-[30%] bg-white border-r border-gray-200 flex flex-col h-full overflow-hidden relative box-border shadow-sm elegant-border gradient-border rounded-r-xl">
          <div ref={chatContainerRef} className="flex-1 overflow-y-auto overflow-x-hidden p-4 pt-6 flex flex-col gap-4 absolute top-0 left-0 right-0 bottom-[180px] w-full box-border scrollbar-thin chat-message-container">
            {chatMessages.map((msg, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg shadow-sm w-[95%] ${index === 0 && plan && plan.trim()
                  ? 'bg-gray-50 text-gray-900 border border-gray-100 w-full'
                  : msg.role === 'user'
                    ? 'bg-white border border-gray-200'
                    : 'bg-gray-100 text-gray-800 border border-gray-200'}`}
              >
                <div className="text-xs font-semibold mb-2 flex items-center">
                  {msg.role === 'user' ? (
                    <>
                      <div className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2 text-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-700">You</span>
                    </>
                  ) : (
                    <>
                      <div className="w-6 h-6 rounded-full bg-indigo-500 flex items-center justify-center mr-2 text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13 6a3 3 3 0 11-6 0 3 3 3 0 016 0zm-7 9a7 7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-700">JustPrototype</span>
                    </>
                  )}
                </div>
                <div className={`text-sm leading-relaxed whitespace-pre-wrap break-words ${index === 0 && plan && plan.trim() ? 'plan-content space-y-2' : ''}`}>
                  {msg.role === 'assistant' && msg.content.includes('\n') ? (
                    // Format multi-line content with proper line breaks and formatting
                    (() => {
                      const lines = msg.content.split('\n');
                      let inSection = false;

                      // Check if this is a plan message (the first message when plan exists)
                      const isPlanMessage = index === 0 && plan && plan.trim();

                      // If this is a plan message, wrap it in a special container
                      if (isPlanMessage) {
                        // Track if we're inside a section for proper styling
                        let inPlanSection = false;

                        return (
                          <div className="plan-message-container bg-white rounded-lg p-4 shadow-sm border border-gray-200 w-full">
                            {lines.map((line, i) => {
                              // Special handling for plan content with emoji icons
                              // Check for section headers with emoji (e.g., "🧩 Task")
                              const emojiHeaderMatch = line.trim().match(/^([\p{Emoji}\u200d]+)\s+(.+)$/u);

                              if (emojiHeaderMatch) {
                                // Set inPlanSection to true when we encounter an emoji header
                                inPlanSection = true;
                                return (
                                  <div key={i} className="text-base font-semibold text-gray-800 mt-5 mb-3 flex items-center border-b border-gray-100 pb-2">
                                    <span className="text-gray-600 mr-2 text-xl">{emojiHeaderMatch[1]}</span>
                                    <span>{emojiHeaderMatch[2]}</span>
                                  </div>
                                );
                              }

                              // Check for bullet points
                              if (line.trim().startsWith('-') || line.trim().startsWith('*')) {
                                return (
                                  <div key={i} className="flex items-start gap-2.5 mb-2 ml-2 list-disc list-inside">
                                    <span className="w-2 h-2 bg-gray-500 rounded-full mt-2 flex-shrink-0"></span>
                                    <span className="text-gray-700 leading-relaxed font-medium">{line.substring(1).trim()}</span>
                                  </div>
                                );
                              }

                              // Check for section headers (e.g., "Layout", "Components")
                              if (isSectionHeading(line.trim())) {
                                // Set inPlanSection to true when we encounter a section heading
                                inPlanSection = true;
                                return (
                                  <div key={i} className="text-base font-semibold text-gray-800 mt-4 mb-2">
                                    {line.trim()}
                                  </div>
                                );
                              }

                              // Regular text or empty line
                              if (line.trim()) {
                                // Apply numbered bullet styling to regular text under section headers
                                // This mimics the checkbox items in PlanReviewPage but with numbers
                                if (inPlanSection) {
                                  // Use bullet point instead of number
                                  return (
                                    <div key={i} className="flex items-start gap-2.5 mb-2 ml-2">
                                      <span className="w-2 h-2 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></span>
                                      <span className="text-gray-700 leading-relaxed font-normal">{line}</span>
                                    </div>
                                  );
                                } else {
                                  // Regular text not under a section
                                  return <div key={i} className="text-gray-700 mb-2 ml-2">{line}</div>;
                                }
                              } else {
                                return <div key={i} className="h-2"></div>;
                              }
                            })}
                          </div>
                        );
                      }

                      // For non-plan messages, use the original formatting logic

                      return lines.map((line, i) => {
                        // Check for numbered section headers (e.g., "1. Section Title")
                        const numberedSectionMatch = line.trim().match(/^(\d+)\.\s+(.+)$/);

                        if (numberedSectionMatch) {
                          inSection = true;

                          return (
                            <div key={i} className="text-base font-semibold text-gray-800 mt-5 mb-3 flex items-center border-b border-gray-100 pb-2">
                              <span className="text-indigo-600 mr-2">{numberedSectionMatch[1]}.</span>
                              <span>{numberedSectionMatch[2]}</span>
                            </div>
                          );
                        }
                        // Check for bullet points
                        else if (line.trim().startsWith('-') || line.trim().startsWith('*')) {
                          return (
                            <div key={i} className="flex items-start gap-2.5 mb-2 ml-5 list-disc list-inside">
                              <span className="w-2 h-2 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></span>
                              <span className="text-gray-700 leading-relaxed font-medium">{line.substring(1).trim()}</span>
                            </div>
                          );
                        }
                        // Check for regular headings
                        else if (line.trim().startsWith('#')) {
                          // Set inSection to true
                          inSection = true;
                          return (
                            <strong key={i} className="block text-base font-semibold text-gray-800 mt-3 mb-2 border-b border-gray-100 pb-1">
                              {line.substring(1).trim()}
                            </strong>
                          );
                        }
                        // Regular text within a section should be styled properly
                        else if (line.trim() && inSection) {
                          // Apply bullet styling to regular text under section headers
                          return (
                            <div key={i} className="flex items-start gap-2.5 mb-2 ml-5">
                              <span className="w-2 h-2 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></span>
                              <span className="text-gray-700 leading-relaxed font-normal">{line}</span>
                            </div>
                          );
                        }
                        // Regular text or empty line
                        else if (line.trim()) {
                          return <div key={i}>{line}</div>;
                        } else {
                          return <br key={i} />;
                        }
                      });
                    })()
                  ) : (
                    msg.content
                  )}
                </div>
              </div>
            ))}
          </div>
          {showElementProperties && selectedElements.length === 1 && (
            <>
              {/* Semi-transparent backdrop */}
              <div
                className="fixed inset-0 z-40"
                onClick={() => {
                  // Close the properties panel
                  setShowElementProperties(false);

                  // Clear selected elements
                  setSelectedElements([]);

                  // Clear selections in the iframe without deactivating the selector
                  if (iframeRef.current?.contentWindow) {
                    iframeRef.current.contentWindow.postMessage({
                      type: 'clearSelections'
                    }, '*');
                  }

                  // Keep the element selector active (don't change chatSelectorActive)
                }}
              ></div>

              {/* Properties panel overlay */}
              <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-[90%] max-w-md max-h-[80vh] overflow-y-auto bg-white rounded-lg shadow-xl scrollbar-thin elegant-border gradient-border">
                {/* <div className="p-3 border-b border-gray-100 bg-gray-50">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-semibold text-gray-800">
                      {selectedElements[0].tagName} Properties
                    </h3>
                    <button
                      onClick={() => setShowElementProperties(false)}
                      className="text-gray-500 hover:text-gray-800 p-2 rounded-full hover:bg-gray-200 flex items-center justify-center transition-all duration-200"
                      aria-label="Close properties panel"
                    >
                      <FiX size={20} />
                    </button>
                  </div>
                </div> */}
                <ChatElementProperties
                  selectedElements={selectedElements}
                  onPropertyChange={(_, property) => {
                    // Get the selector for the element
                    const selector = selectedElements[0].ref;

                  console.log('Updating element style in code:', selector, property);

                  // Update the element style in the original code content
                  const updatedCode = updateElementStyleInHtml(code, selector, property);

                  console.log('Code updated successfully');

                  // Update the code state with the modified content
                  setCode(updatedCode);

                  // Also apply the change to the iframe for immediate visual feedback
                  if (iframeRef.current && iframeRef.current.contentDocument) {
                    const doc = iframeRef.current.contentDocument;
                    const element = doc.querySelector(selector) as HTMLElement;

                    if (element) {
                      // Apply the property efficiently to the live iframe
                      switch (property.name) {
                        case 'MarginX':
                          element.style.marginLeft = `${property.value}${property.unit || 'px'}`;
                          element.style.marginRight = `${property.value}${property.unit || 'px'}`;
                          break;
                        case 'MarginY':
                          element.style.marginTop = `${property.value}${property.unit || 'px'}`;
                          element.style.marginBottom = `${property.value}${property.unit || 'px'}`;
                          break;
                        case 'PaddingX':
                          element.style.paddingLeft = `${property.value}${property.unit || 'px'}`;
                          element.style.paddingRight = `${property.value}${property.unit || 'px'}`;
                          break;
                        case 'PaddingY':
                          element.style.paddingTop = `${property.value}${property.unit || 'px'}`;
                          element.style.paddingBottom = `${property.value}${property.unit || 'px'}`;
                          break;
                        case 'Background':
                          element.style.backgroundColor = property.value;
                          break;
                        case 'Border radius':
                          applyBorderRadius(element, property.value);
                          break;
                        case 'Text Content':
                          // Update the text content of the element
                          element.textContent = property.value;
                          break;
                        case 'advancedCSS':
                          // Use the optimized utility to apply CSS
                          applyStyles(element, property.value);
                          break;
                      }
                    }
                  }
                }}
                onAskAI={(_) => {
                  // Add a message to the conversation asking AI to modify the element
                  let message = `Please modify the ${selectedElements[0].tagName} element with selector "${selectedElements[0].ref}"`;

                  // Include element description if available
                  if (selectedElements[0].description) {
                    message += `\nElement description: ${selectedElements[0].description}`;
                  }

                  setChatInput(message);

                  // Close the properties panel
                  setShowElementProperties(false);
                }}
                onGenerateFunctionality={handleElementFunctionality}
                onCancel={() => {
                  // Close the properties panel
                  setShowElementProperties(false);

                  // Clear selected elements
                  setSelectedElements([]);

                  // Clear selections in the iframe without deactivating the selector
                  if (iframeRef.current?.contentWindow) {
                    iframeRef.current.contentWindow.postMessage({
                      type: 'clearSelections'
                    }, '*');
                  }

                  // Keep the element selector active (don't change chatSelectorActive)
                }}
              />
            </div>
          </>
          )}

          <form
            className="border-t border-gray-200 bg-white absolute bottom-0 left-0 right-0 z-10 w-full box-border min-h-[140px] max-h-[240px] flex p-0 shadow-md"
            onSubmit={(e) => {
              e.preventDefault();
              handleChatSend();
            }}
            style={{ display: 'flex', flex: 1 }}
          >
            <div className="flex flex-col bg-white p-0 gap-2 flex-1 border border-gray-100 rounded-lg m-3 overflow-hidden relative shadow-sm elegant-border gradient-border">
              <textarea
                className="w-full p-4 border-none bg-transparent text-sm outline-none transition-all duration-200 min-h-[60px] max-h-[150px] resize-none overflow-y-auto leading-relaxed font-inherit flex-1 box-border block h-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
                placeholder="Ask about your prototype..."
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                disabled={streaming}
                rows={3}
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#d1d5db transparent'
                }}
                onInput={(e) => {
                  // Auto-resize the textarea based on content
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = `${Math.min(target.scrollHeight, 150)}px`;

                  // Always show scrollbar for multiline input
                  target.style.overflowY = 'auto';
                }}
                onKeyDown={(e) => {
                  // Submit form on Ctrl+Enter or Cmd+Enter
                  if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                    e.preventDefault();
                    if (chatInput.trim() && !streaming && !modifyingElement) {
                      handleChatSend();
                    }
                  }
                }}
              />
              <div className="flex items-center gap-2 h-12 justify-between p-1 px-3 border-t border-gray-100">

                <button
                  className={`flex items-center gap-1.5 cursor-pointer transition-all duration-200 relative rounded-full p-1.5 px-3 h-9 ${chatSelectorActive ? 'text-indigo-600 bg-indigo-50 border border-indigo-100' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'} ${(viewMode !== 'preview' || streaming || !iframeLoaded || !streamingComplete || !code) ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => {
                    console.log('Edit button clicked');
                    console.log('viewMode:', viewMode);
                    console.log('streaming:', streaming);
                    console.log('iframeLoaded:', iframeLoaded);
                    console.log('streamingComplete:', streamingComplete);
                    console.log('code exists:', !!code);
                    toggleChatElementSelector();
                  }}
                  disabled={viewMode !== 'preview' || streaming || !iframeLoaded || !streamingComplete || !code}
                  title={
                    viewMode !== 'preview'
                      ? 'Switch to preview mode to select elements'
                      : streaming
                        ? 'Wait for streaming to complete'
                        : !streamingComplete
                          ? 'Wait for streaming to complete'
                          : !iframeLoaded
                            ? 'Wait for preview to load completely'
                            : 'Select elements'
                  }
                >
                  <FiCrosshair className={`text-lg ${chatSelectorActive ? 'text-indigo-600' : 'text-gray-500'}`} />
                  <span className={`text-sm font-medium ${chatSelectorActive ? 'text-indigo-600' : 'text-gray-500'}`}>
                    Selector
                  </span>
                </button>

                <div className="hidden">
                  <SimpleElementSelector
                    iframeRef={iframeRef}
                    onElementSelect={(element: SelectedElement) => {
                      console.log('Chat element selector onElementSelect called with element:', element?.tagName || 'none');

                      // Check if this is a deselection (empty id)
                      if (!element.id) {
                        console.log('Deselecting element (empty id)');
                        // Clear selected elements and hide properties panel
                        setSelectedElements([]);
                        setShowElementProperties(false);
                        return;
                      }

                      // Log that we're processing a valid element selection
                      console.log('Processing valid element selection in EditorPage');

                      // Verify the element can be found in the iframe
                      if (iframeRef.current?.contentDocument) {
                        const foundElement = iframeRef.current.contentDocument.querySelector(element.ref);
                        console.log('Element found in iframe:', !!foundElement);
                        if (!foundElement) {
                          console.error('Could not find element with selector:', element.ref);
                        }
                      }

                      // Replace any existing selected element with the new one
                      // This ensures only one element is selected at a time
                      console.log('Setting selectedElements state with new element:', element.tagName);
                      setSelectedElements([element]);

                      // Automatically show properties when an element is selected
                      console.log('Showing element properties panel');
                      setShowElementProperties(true);

                      // Make sure selection mode remains active
                      if (!chatSelectorActive) {
                        console.log('Selection mode was deactivated, reactivating it');
                        setChatSelectorActive(true);
                      }

                      // Ensure edit mode is maintained
                      if (editorMode !== 'edit') {
                        console.log('Edit mode was deactivated, reactivating it');
                        setEditorMode('edit');
                      }

                      console.log('Element selection updated, properties panel shown');
                    }}
                    onGenerateFunctionality={handleElementFunctionality}
                    disabled={streaming || !code || viewMode !== 'preview' || !streamingComplete || !iframeLoaded}
                    initialActive={chatSelectorActive}
                    onSelectionStateChange={(isActive: boolean) => {
                      console.log('Chat selector state changed from component:', isActive);

                      // Always keep selection active in edit mode
                      if (editorMode === 'edit' && !isActive) {
                        console.log('Selection was deactivated but edit mode is active, keeping selection active');
                        // Don't update the state to keep selection active
                        return;
                      }

                      setChatSelectorActive(isActive);
                    }}
                    viewMode={viewMode}
                    streamingComplete={streamingComplete}
                    iframeLoaded={iframeLoaded}
                  />
                </div>
                <button
                  type="submit"
                  className={`flex items-center justify-center bg-indigo-600 text-white border-none rounded-full w-10 h-10 cursor-pointer transition-all duration-200 hover:bg-indigo-700 ${!chatInput.trim() || streaming || modifyingElement ? 'bg-indigo-300 cursor-not-allowed' : ''}`}
                  disabled={!chatInput.trim() || streaming || modifyingElement}
                >
                  {modifyingElement ? <FiLoader className="animate-spin" /> : <FiSend className="text-lg" />}
                </button>
              </div>
            </div>
          </form>
        </div>

        <div className="flex-1 flex flex-col overflow-hidden h-full w-[70%] max-w-[70%] box-border elegant-glow">
          {error && error !== 'Failed to stream generated code. Please try again.' && (
            <div className="text-red-700 bg-red-50 border border-red-200 rounded-lg p-3 m-3 text-sm flex-shrink-0 shadow-sm">
              {error}
            </div>
          )}
          <div className="flex-1 flex overflow-hidden h-full w-full box-border p-3 ">
            {showCode && (
              <div className="flex-1 bg-gray-900 h-full max-w-full relative box-border flex flex-col rounded-lg shadow-md overflow-hidden elegant-border gradient-border">
                <SyntaxHighlighter
                  language="html"
                  style={vscDarkPlus}
                  showLineNumbers
                  wrapLongLines
                  customStyle={{
                    fontSize: '1em',
                    borderRadius: 8,
                    minHeight: 320,
                    height: '100%',
                    background: '#1e1e1e',
                    overflow: 'auto',
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#4a5568 #1e1e1e',
                  }}
                >
                  {code || (streaming ? 'Generating code...' : 'No code yet.')}
                </SyntaxHighlighter>
                {streaming && (
                  <div className="py-3 text-center text-gray-600 text-sm font-medium bg-gray-100 bg-opacity-90 border-t border-gray-200 flex-shrink-0 absolute bottom-0 left-0 right-0 z-50 shadow-sm">
                    <div className="flex items-center justify-center gap-2">
                      <FiLoader className="animate-spin text-indigo-600" />
                      Streaming code...
                    </div>
                  </div>
                )}
              </div>
            )}
            {showPreview && (
              <div className="flex-1 bg-white h-full max-w-full relative box-border flex flex-col rounded-lg shadow-md overflow-hidden elegant-border gradient-border">
                <div className="relative w-full h-full">
                  {/*
                    Two-layer approach:
                    1. Bottom layer: iframe (only visible when not streaming)
                    2. Top layer: spinner (only visible when streaming)
                  */}

                  {/* Layer 1: iframe (only loaded and visible when not streaming) */}
                  <div className={`absolute top-0 left-0 w-full h-full transition-opacity duration-300 ease-in-out z-[1] ${!streaming && streamingComplete ? 'opacity-100 visible' : 'opacity-0 invisible'}`}>
                    <iframe
                      key={iframeKey} // Use key to force remounting when needed
                      ref={iframeRef}
                      title="Live Preview"
                      className="w-full h-full border-none bg-white"
                      sandbox="allow-scripts allow-same-origin allow-popups allow-forms allow-top-navigation-by-user-activation"
                      srcDoc={!streaming && code ? getIframeHtml(code) : ''} // Simplified condition, only need code and not streaming
                      onLoad={() => {
                        console.log('Iframe onLoad event fired, key:', iframeKey);
                        console.log('Iframe load conditions - code:', !!code, 'streamingComplete:', streamingComplete, 'streaming:', streaming);

                        // Set iframe as loaded if we have content, regardless of streaming state
                        // This ensures the iframe is marked as loaded even if streaming state hasn't updated yet
                        if (code) {
                          console.log('Iframe loaded successfully with content');
                          // Update the previous code ref to prevent infinite updates
                          prevCodeRef.current = code;
                          // Set iframe as loaded
                          setIframeLoaded(true);

                          // Force visibility by ensuring streamingComplete is true when we have content
                          if (!streamingComplete && !streaming) {
                            console.log('Forcing streamingComplete to true');
                            setStreamingComplete(true);
                          }

                          // Show the preview ready notification ONLY if streaming is complete
                          if (!streaming) {
                            setShowPreviewReady(true);
                          }
                        }
                      }}
                    />
                  </div>

                  {/* Layer 2: Spinner (only visible when streaming) */}
                  {streaming && (
                    <div className="absolute top-0 left-0 w-full h-full z-10 bg-gray-50">
                      {streamingType === 'code' ? (
                        <LoadingSpinner
                          message="Generating preview..."
                          subMessage="Please wait while we prepare your content"
                        />
                      ) : streamingType === 'element' ? (
                        <LoadingSpinner
                          message="Modifying element..."
                          subMessage="Please wait while we update the selected element"
                        />
                      ) : streamingType === 'content' ? (
                        <LoadingSpinner
                          message="Updating content..."
                          subMessage="Please wait while we modify the content based on your request"
                        />
                      ) : (
                        <LoadingSpinner
                          message="Adding functionality..."
                          subMessage="Please wait while we implement the requested functionality"
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {editorMode === 'edit' && (
        <div className="absolute top-0 left-[30%] right-0 bottom-0 z-100 pointer-events-none">
          {/* <div className="absolute top-3 right-3 z-[1000]">
            <div className="flex items-center bg-indigo-600 py-2 px-4 rounded-full shadow-md elegant-border gradient-border">
              <FiCrosshair className="text-white text-lg" />
              <span className="ml-2 text-sm font-medium text-white">
                Element Selection Mode
              </span>
            </div>
          </div> */}

          <SimpleElementSelector
            iframeRef={iframeRef}
            onElementSelect={(element: SelectedElement) => {
              console.log('Editor overlay element selector onElementSelect called with element:', element?.tagName || 'none');

              // Check if this is a deselection (empty id)
              if (!element.id) {
                console.log('Deselecting element (empty id)');
                // Clear selected elements and hide properties panel
                setSelectedElements([]);
                setShowElementProperties(false);
                return;
              }

              // Log that we're processing a valid element selection
              console.log('Processing valid element selection in EditorPage (editor overlay)');

              // Verify the element can be found in the iframe
              if (iframeRef.current?.contentDocument) {
                const foundElement = iframeRef.current.contentDocument.querySelector(element.ref);
                console.log('Element found in iframe:', !!foundElement);
                if (!foundElement) {
                  console.error('Could not find element with selector:', element.ref);
                }
              }

              // Replace any existing selected element with the new one
              // This ensures only one element is selected at a time
              console.log('Setting selectedElements state with new element:', element.tagName);
              setSelectedElements([element]);

              // Automatically show properties when an element is selected
              console.log('Showing element properties panel');
              setShowElementProperties(true);

              // Make sure edit mode remains active
              if (editorMode !== 'edit') {
                console.log('Edit mode was deactivated, reactivating it');
                setEditorMode('edit');
              }

              // Ensure selection mode is maintained
              console.log('Ensuring selection mode remains active for subsequent selections');

              console.log('Element selection updated, properties panel shown');
            }}
            onGenerateFunctionality={handleElementFunctionality}
            disabled={streaming || !code || viewMode !== 'preview' || !streamingComplete || !iframeLoaded}
            initialActive={editorMode === 'edit'}
            onSelectionStateChange={(isActive: boolean) => {
              console.log('Editor overlay selector active:', isActive);

              // Only change to view mode if explicitly deactivated by user
              // Don't change mode if deactivated programmatically
              if (!isActive && editorMode === 'edit') {
                console.log('Selection mode deactivated, checking if it was user-initiated');

                // Always keep edit mode active when selection is deactivated programmatically
                console.log('Keeping edit mode active and re-activating selection');

                // Force the selection mode back to active after a short delay
                setTimeout(() => {
                  console.log('Re-activating selection mode');
                  // This will re-activate the selection mode without changing the editor mode
                  if (iframeRef.current?.contentWindow) {
                    iframeRef.current.contentWindow.postMessage({
                      type: 'toggleSelectionMode',
                      active: true
                    }, '*');
                  }
                }, 100);
              }
            }}
            viewMode={viewMode}
            streamingComplete={streamingComplete}
            iframeLoaded={iframeLoaded}
          />
        </div>
      )}

      {/* Show "Preview Ready" message when everything is loaded */}
      {!streaming && code && showPreviewReady && (
        <div className="absolute bottom-4 right-4 z-[1000]">
          <div className="flex items-center justify-between py-2 px-4 bg-green-50 border border-green-200 rounded-full text-green-800 text-sm font-medium shadow-md min-w-[150px] elegant-border gradient-border" title="Preview Ready">
            <div className="flex items-center">
              <FiCheckCircle className="text-green-500 mr-2" />
              Preview Ready
            </div>
            <button
              onClick={() => setShowPreviewReady(false)}
              className="bg-transparent border-none cursor-pointer ml-2 p-0.5 flex items-center justify-center rounded-full text-green-700 text-base hover:bg-green-100"
              title="Close"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
    </>
  );
}
