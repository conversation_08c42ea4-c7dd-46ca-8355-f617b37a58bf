.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-sans, system-ui, sans-serif);
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button variants */
.primary {
  background-color: #4f46e5;
  color: white;
}

.primary:hover:not(:disabled) {
  background-color: #4338ca;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: none;
}

.secondary {
  background-color: #f3f4f6;
  color: #111827;
  border: 1px solid #e5e7eb;
}

.secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.secondary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: none;
}

/* Button sizes */
.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.medium {
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

.large {
  padding: 0.625rem 1.25rem;
  font-size: 1.125rem;
}

/* Animation styles */
.animated {
  transition: all 0.3s ease;
}

.animated:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.animated:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: none;
}

/* Ripple effect */
.animated::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 0.8s;
}

.animated:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* Primary button with gradient on hover */
.primary.animated:hover:not(:disabled) {
  background-image: linear-gradient(to right, #4f46e5, #6366f1);
}
