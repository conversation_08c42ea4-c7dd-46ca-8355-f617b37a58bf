// elementExtractor.test.js
// Integration tests for the Element Extraction Service

const { extractElements, generateSelector, validateAndSanitize } = require('../services/elementExtractor');

describe('Element Extraction Service', () => {
  test('extractElements returns an array', () => {
    const html = '<div><button>Click</button></div>';
    const elements = extractElements(html);
    expect(Array.isArray(elements)).toBe(true);
  });

  test('generateSelector returns a string', () => {
    const element = { tagName: 'button', id: 'myBtn', classNames: ['btn'] };
    const selector = generateSelector(element);
    expect(typeof selector).toBe('string');
  });

  test('validateAndSanitize returns the element or null', () => {
    const element = { tagName: 'div' };
    const sanitized = validateAndSanitize(element);
    expect(sanitized === null || typeof sanitized === 'object').toBe(true);
  });
});
