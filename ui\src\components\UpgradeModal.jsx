import {
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography,
  Box,
  LinearProgress
} from '@mui/material';

/**
 * Modal shown when a user has exceeded their prototype quota
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Function} props.onUpgrade - Function to handle upgrade
 * @param {Object} props.quota - User's quota information
 * @param {string} props.quota.plan - User's current plan
 * @param {number} props.quota.usedCount - Number of prototypes used
 * @param {number} props.quota.totalCount - Total number of prototypes allowed
 * @param {number} props.quota.remainingCount - Number of prototypes remaining
 */
function UpgradeModal({ open, onClose, onUpgrade, quota = {} }) {
  const { 
    plan = 'free', 
    usedCount = 3, 
    totalCount = 3, 
    remainingCount = 0 
  } = quota;
  
  const percentUsed = Math.min(100, Math.round((usedCount / totalCount) * 100));
  const isPro = plan !== 'free';

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
        }
      }}
    >
      <DialogTitle sx={{ 
        textAlign: 'center', 
        pt: 3,
        pb: 1,
        fontWeight: 700,
        color: '#1F2937'
      }}>
        {remainingCount <= 0 ? 'Prototype Limit Reached' : 'Prototype Quota Status'}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            {remainingCount <= 0 
              ? `You've reached your ${isPro ? 'pro' : 'free'} prototype limit. ${!isPro ? 'Upgrade to create more prototypes and unlock additional features.' : 'Contact support to increase your limit.'}`
              : `You have ${remainingCount} prototype${remainingCount !== 1 ? 's' : ''} remaining on your ${isPro ? 'pro' : 'free'} plan.`
            }
          </Typography>
          
          {/* Quota progress bar */}
          <Box sx={{ mb: 3, px: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                {usedCount} used
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {totalCount} total
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={percentUsed} 
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: 'rgba(209, 213, 219, 0.5)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: remainingCount <= 0 ? '#EF4444' : '#8B5CF6',
                }
              }}
            />
          </Box>
          
          {!isPro && (
            <Box sx={{ 
              p: 3, 
              bgcolor: 'rgba(139, 92, 246, 0.05)', 
              borderRadius: 2,
              mb: 2,
              border: '1px solid rgba(139, 92, 246, 0.2)'
            }}>
              <Typography variant="subtitle1" color="#8B5CF6" sx={{ fontWeight: 600, mb: 1 }}>
                Pro Plan Benefits
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • Up to 50 prototypes (vs. 3 on free plan)
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • Advanced design options
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • Export to code
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Priority support
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, pt: 0, justifyContent: 'center' }}>
        <Button 
          onClick={onClose}
          variant="outlined"
          sx={{ 
            borderColor: '#E5E7EB',
            color: '#4B5563',
            '&:hover': {
              borderColor: '#D1D5DB',
              bgcolor: 'rgba(75, 85, 99, 0.04)'
            },
            mr: 1,
            fontWeight: 600
          }}
        >
          {remainingCount > 0 ? 'Close' : 'Maybe Later'}
        </Button>
        {!isPro && (
          <Button 
            onClick={onUpgrade}
            variant="contained"
            sx={{ 
              bgcolor: '#8B5CF6',
              '&:hover': {
                bgcolor: '#7C3AED'
              },
              fontWeight: 600
            }}
          >
            Upgrade Now
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
}

export default UpgradeModal;
