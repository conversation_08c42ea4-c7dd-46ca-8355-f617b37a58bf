.container {
  max-width: 100%;
  min-height: 100vh;
  padding: 2rem 0;
  background-color: #f9fafb;
  display: flex;
  justify-content: center;
}

.content {
  max-width: 800px;
  width: 100%;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.lastUpdated {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 2rem;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.section p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #4b5563;
}

.section ul {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.section li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  color: #4b5563;
}

.section a {
  color: #2563eb;
  text-decoration: none;
}

.section a:hover {
  text-decoration: underline;
}

.footer {
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: center;
}

.backLink {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.375rem;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.2s;
}

.backLink:hover {
  background-color: #1d4ed8;
}

@media (max-width: 768px) {
  .content {
    padding: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .section h2 {
    font-size: 1.25rem;
  }
  
  .section h3 {
    font-size: 1.125rem;
  }
}
