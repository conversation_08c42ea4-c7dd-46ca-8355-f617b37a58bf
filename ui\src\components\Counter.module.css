.container {
  padding: 1.5rem;
  max-width: 24rem;
  margin: 0 auto;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #1f2937;
}

.count {
  font-size: 3rem;
  font-weight: 700;
  color: #2563eb;
}

.buttonGroup {
  display: flex;
  gap: 0.75rem;
}

.decrementButton {
  padding: 0.5rem 1rem;
  background-color: #ef4444;
  color: white;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.decrementButton:hover {
  background-color: #dc2626;
}

.incrementButton {
  padding: 0.5rem 1rem;
  background-color: #10b981;
  color: white;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.incrementButton:hover {
  background-color: #059669;
}

.resetButton {
  padding: 0.5rem 1rem;
  background-color: #d1d5db;
  color: #1f2937;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.resetButton:hover {
  background-color: #9ca3af;
}

.themeInfo {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Dark mode styles */
.darkContainer {
  background-color: #1f2937;
}

.darkTitle {
  color: white;
}

.darkCount {
  color: #3b82f6;
}

.darkResetButton {
  background-color: #4b5563;
  color: white;
}

.darkResetButton:hover {
  background-color: #374151;
}

.darkThemeInfo {
  color: #9ca3af;
}
