import { ReactNode } from 'react';
import styles from './Section.module.css';

interface SectionProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  id?: string;
  className?: string;
  background?: 'light' | 'dark';
}

export function Section({
  children,
  title,
  subtitle,
  id,
  className = '',
  background = 'light',
}: SectionProps) {
  return (
    <section
      id={id}
      className={`${styles.section} ${styles[background]} ${className}`}
    >
      <div className={styles.container}>
        {(title || subtitle) && (
          <div className={styles.header}>
            {title && <h2 className={styles.title}>{title}</h2>}
            {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
          </div>
        )}
        <div className={styles.content}>{children}</div>
      </div>
    </section>
  );
}
