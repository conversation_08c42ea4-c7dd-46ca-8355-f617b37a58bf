import React from 'react';
import { Sparkles, <PERSON>, Sun, Layout, Settings, Home } from 'lucide-react';

type Page = 'home' | 'templates' | 'settings';

interface HeaderProps {
  theme: 'light' | 'dark';
  onToggleTheme: () => void;
  currentPage: Page;
  onNavigate: (page: Page) => void;
}

const Header: React.FC<HeaderProps> = ({ theme, onToggleTheme, currentPage, onNavigate }) => {
  const navItems = [
    { id: 'home' as Page, label: 'Generator', icon: Home },
    { id: 'templates' as Page, label: 'Templates', icon: Layout },
    { id: 'settings' as Page, label: 'Settings', icon: Settings },
  ];

  const getButtonClasses = (isActive: boolean) => {
    if (isActive) {
      return 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg';
    }
    
    return theme === 'dark'
      ? 'text-gray-300 hover:text-white hover:bg-gray-700/50'
      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100/80';
  };

  return (
    <header className={`border-b transition-colors duration-300 ${
      theme === 'dark' 
        ? 'bg-gray-800/50 border-gray-700 backdrop-blur-lg' 
        : 'bg-white/70 border-gray-200 backdrop-blur-lg'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                AI HTML Generator
              </h1>
            </div>
          </div>

          <nav className="hidden md:flex items-center space-x-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => onNavigate(item.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 font-medium ${
                    getButtonClasses(isActive)
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </nav>

          <div className="flex items-center space-x-2">
            {/* Mobile navigation */}
            <div className="md:hidden flex items-center space-x-1">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => onNavigate(item.id)}
                    className={`p-2.5 rounded-lg transition-all duration-300 ${
                      getButtonClasses(isActive)
                    }`}
                    title={item.label}
                  >
                    <Icon className="w-4 h-4" />
                  </button>
                );
              })}
            </div>

            <button
              onClick={onToggleTheme}
              className={`p-2.5 rounded-lg transition-colors duration-300 ${
                theme === 'dark'
                  ? 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100/80'
              }`}
            >
              {theme === 'dark' ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;