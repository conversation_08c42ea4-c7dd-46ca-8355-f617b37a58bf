.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.clickable {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.clickable:hover {
  transform: translateY(-1px);
}

.free {
  background-color: #e0f2fe;
  color: #0369a1;
}

.pro {
  background-color: #e0e7ff;
  color: #4f46e5;
}

.warning {
  background-color: #fef3c7;
  color: #92400e;
}

.exceeded {
  background-color: #fee2e2;
  color: #b91c1c;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upgradeText {
  background-color: #ef4444;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
