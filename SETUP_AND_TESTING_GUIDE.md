# Setup and Testing Guide

This guide provides step-by-step instructions for setting up and testing the new project management and pages functionality.

## 🚀 Quick Setup

### Prerequisites
- Existing JustPrototype application running
- PostgreSQL database with existing schema
- Node.js and npm/yarn installed
- User authentication working

### 1. Database Setup

#### Option A: Use Existing Data
If you have existing prototypes, the new functionality will work immediately.

#### Option B: Create Sample Data
```sql
-- Connect to your PostgreSQL database
psql -U your_username -d your_database

-- Create sample sessions for testing
\i backend/db_sample_sessions.sql

-- Run the sample data generation function
SELECT create_sample_sessions();

-- Verify sample data was created
SELECT 
  p.title as project_title,
  ps.page_url,
  ps.session_state,
  ps.created_at
FROM prototype_sessions ps
JOIN prototypes p ON ps.prototype_id = p.id
ORDER BY p.title, ps.created_at DESC
LIMIT 10;
```

#### Option C: Performance Enhancements (Optional)
```sql
-- Add performance indexes and utility functions
\i backend/db_project_enhancements.sql
```

### 2. Backend Setup

No additional setup required - the new routes are automatically loaded when the server starts.

**Verify Backend Routes**:
```bash
# Check if server starts without errors
cd backend
npm start

# Should see log message about routes being loaded
# Look for: "Routes loaded: /api/page_gen"
```

### 3. Frontend Setup

No additional setup required - new components are automatically included in the build.

**Verify Frontend Build**:
```bash
# Check if frontend builds without errors
cd ui
npm run build

# Should complete without TypeScript errors
```

## 🧪 Testing Checklist

### 1. Basic Navigation Testing

#### Test Project List View
1. Navigate to `http://localhost:5173/prompt-v3`
2. Click "My Projects" button in the header
3. **Expected**: Should see a grid of projects with pagination
4. **Verify**: Projects display with titles, descriptions, and dates

#### Test Project Creation
1. From project list, click "New Project" button
2. Fill in project title: "Test Project"
3. Select a template (Landing Page recommended)
4. Click "Create Project"
5. **Expected**: Should navigate to editor with new project
6. **Verify**: Project appears in project list

#### Test Page List View
1. From project list, click on any existing project
2. **Expected**: Should show pages/sessions within that project
3. **Verify**: 
   - Header shows "Pages - [Project Name]"
   - Back button is visible
   - Pages display with status colors

### 2. API Testing

#### Test Project List API
```bash
# Test project listing (replace with your session cookie)
curl -X POST http://localhost:3001/api/page_gen/project/list \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"page":{"pageNum":1,"pageSize":12}}'

# Expected Response:
# {
#   "projects": [...],
#   "totalCount": number,
#   "page": {"pageNum": 1, "pageSize": 12, "totalPages": number}
# }
```

#### Test Session List API
```bash
# Test session listing (replace projectId with actual ID)
curl -X POST http://localhost:3001/api/page_gen/session/list \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"projectId":"1","page":{"pageSize":30,"pageNum":1}}'

# Expected Response:
# {
#   "sessions": [...],
#   "totalCount": number,
#   "page": {"pageNum": 1, "pageSize": 30, "totalPages": number}
# }
```

### 3. Database Testing

#### Verify Session Data
```sql
-- Check if sessions exist
SELECT COUNT(*) as total_sessions FROM prototype_sessions;

-- Check sessions per project
SELECT 
  p.title,
  COUNT(ps.id) as session_count,
  ps.session_state
FROM prototypes p
LEFT JOIN prototype_sessions ps ON p.id = ps.prototype_id
GROUP BY p.id, p.title, ps.session_state
ORDER BY p.title;

-- Check session states
SELECT 
  session_state,
  COUNT(*) as count
FROM prototype_sessions
GROUP BY session_state;
```

### 4. Error Handling Testing

#### Test Authentication
1. Open browser in incognito mode
2. Navigate to `http://localhost:5173/prompt-v3`
3. Try to click "My Projects"
4. **Expected**: Should redirect to login or show authentication error

#### Test Empty States
1. Create a new user account (or use account with no projects)
2. Navigate to project list
3. **Expected**: Should show "No projects yet" empty state
4. Create a project with no sessions
5. View project pages
6. **Expected**: Should show "No pages yet" empty state

#### Test Pagination
1. If you have many projects/sessions, test pagination controls
2. Click "Next" and "Previous" buttons
3. **Expected**: Should load different pages of data
4. Page numbers should update correctly

### 5. UI/UX Testing

#### Test Responsive Design
1. Test on different screen sizes:
   - Desktop (1920x1080)
   - Tablet (768x1024)
   - Mobile (375x667)
2. **Verify**: Grid layouts adjust appropriately
3. **Verify**: Navigation remains usable on all sizes

#### Test Status Colors
1. View page list with different session states
2. **Verify Status Colors**:
   - Active: Green background
   - Editing: Yellow background
   - Completed: Blue background
   - Expired: Gray background

#### Test Loading States
1. Throttle network in browser dev tools
2. Navigate between views
3. **Expected**: Should see loading spinners
4. **Verify**: No broken states during loading

## 🐛 Troubleshooting

### Common Issues

#### "Sessions not loading"
**Symptoms**: Page list shows empty or loading forever
**Solutions**:
1. Check if `prototype_sessions` table exists:
   ```sql
   \dt prototype_sessions
   ```
2. Verify foreign key relationships:
   ```sql
   SELECT * FROM prototype_sessions WHERE prototype_id = 1 LIMIT 1;
   ```
3. Check server logs for database errors

#### "Projects not displaying"
**Symptoms**: Project list shows empty
**Solutions**:
1. Verify user has prototypes:
   ```sql
   SELECT * FROM prototypes WHERE user_id = YOUR_USER_ID;
   ```
2. Check authentication cookies in browser dev tools
3. Verify API endpoints are responding:
   ```bash
   curl http://localhost:3001/api/page_gen/project/list
   ```

#### "TypeScript errors in frontend"
**Symptoms**: Build fails with type errors
**Solutions**:
1. Clear node_modules and reinstall:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```
2. Check if all new files are properly imported
3. Verify TypeScript configuration

#### "Database connection errors"
**Symptoms**: API returns 500 errors
**Solutions**:
1. Check database connection in backend logs
2. Verify database credentials in environment variables
3. Ensure PostgreSQL is running

### Performance Issues

#### "Slow page loading"
**Solutions**:
1. Add database indexes:
   ```sql
   \i backend/db_project_enhancements.sql
   ```
2. Reduce page sizes in frontend components
3. Check database query performance:
   ```sql
   EXPLAIN ANALYZE SELECT * FROM prototypes WHERE user_id = 1;
   ```

#### "High memory usage"
**Solutions**:
1. Implement pagination properly (check page sizes)
2. Clear browser cache
3. Monitor React component re-renders in dev tools

## ✅ Success Criteria

### Functional Requirements
- [ ] Users can view their projects in a paginated grid
- [ ] Users can create new projects with templates
- [ ] Users can view pages within each project
- [ ] Users can navigate between projects and pages
- [ ] Pagination works correctly for both projects and pages
- [ ] Status indicators display correctly for pages

### Technical Requirements
- [ ] All API endpoints respond correctly
- [ ] Database queries are efficient
- [ ] Frontend builds without errors
- [ ] Authentication works properly
- [ ] Error handling displays appropriate messages

### UI/UX Requirements
- [ ] Responsive design works on all screen sizes
- [ ] Loading states provide good user feedback
- [ ] Empty states guide users to take action
- [ ] Navigation is intuitive and consistent
- [ ] Status colors are clearly distinguishable

## 📞 Support

If you encounter issues not covered in this guide:

1. **Check Server Logs**: Look for error messages in backend console
2. **Check Browser Console**: Look for JavaScript errors in dev tools
3. **Check Database Logs**: Look for PostgreSQL error messages
4. **Verify Environment**: Ensure all environment variables are set correctly

### Debug Mode
Enable debug logging by setting environment variable:
```bash
DEBUG=justprototype:* npm start
```

This will provide detailed logging for troubleshooting.

---

Following this guide should result in a fully functional project management and pages system that matches the Readdy.ai reference implementation.
