import { useState } from 'react';
import { Button } from './Button';
import styles from './PromptModal.module.css';

const EXAMPLES = [
  "Create a basic login form",
  "Design a responsive landing page with a hero section",
  "Build a dashboard with sidebar navigation and charts",
  "Generate a contact page with a form and map",
];

interface PromptModalProps {
  open: boolean;
  onClose: () => void;
  onGenerate: (prompt: string) => void;
}

export function PromptModal({ open, onClose, onGenerate }: PromptModalProps) {
  const [prompt, setPrompt] = useState('');

  if (!open) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <button className={styles.closeBtn} onClick={onClose} aria-label="Close">&times;</button>
        <h2 className={styles.heading}>Describe Your Prototype</h2>
        <textarea
          className={styles.input}
          placeholder="Describe the prototype you'd like to create (e.g., 'A simple homepage with a header, navigation, and a footer')."
          value={prompt}
          onChange={e => setPrompt(e.target.value)}
          rows={6}
          autoFocus
        />
        <div className={styles.examples}>
          <div className={styles.examplesLabel}>Examples:</div>
          <ul>
            {EXAMPLES.map((ex, i) => (
              <li key={i} className={styles.example} onClick={() => setPrompt(ex)}>
                {ex}
              </li>
            ))}
          </ul>
        </div>
        <Button
          size="large"
          animated
          className={styles.generateBtn}
          onClick={() => onGenerate(prompt)}
          disabled={!prompt.trim()}
        >
          Generate
        </Button>
      </div>
    </div>
  );
}
