import React, { useState, useRef, useEffect } from 'react';
import { FiRefreshCw, FiMaximize2, FiMinimize2 } from 'react-icons/fi';
import { SeamlessHtmlRenderer } from '../AnimatedHtmlRenderer';
import { ViewMode } from '../../types/editor';
import { CodeEditor } from '../CodeEditor';

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const extractHtmlFromResponse = (response: string): string => {
  if (!response) return '';
  
  // Remove any markdown code blocks
  const withoutCodeBlocks = response.replace(/```html\n?|```\n?/g, '');
  
  // Remove any explanatory text before the HTML
  const htmlMatch = withoutCodeBlocks.match(/<!DOCTYPE html>[\s\S]*<\/html>|<html[\s\S]*<\/html>|<div[\s\S]*<\/div>/i);
  if (htmlMatch) {
    return htmlMatch[0];
  }
  
  return withoutCodeBlocks.trim();
};

// ============================================================================
// TYPES
// ============================================================================

interface SeamlessPreviewPanelProps {
  htmlContent: string;
  streamingContent: string;
  stableIframeContent: string;
  viewMode: ViewMode;
  isGenerating: boolean;
  onViewModeChange: (mode: ViewMode) => void;
  onElementClick?: (element: any) => void;
  onHtmlChange?: (html: string) => void;
  className?: string;
}

interface ViewModeToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

// ============================================================================
// COMPONENTS
// ============================================================================

const ViewModeToggle: React.FC<ViewModeToggleProps> = ({ viewMode, onViewModeChange }) => (
  <div className="flex bg-gray-100 rounded-lg p-1">
    <button
      onClick={() => onViewModeChange('preview')}
      className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
        viewMode === 'preview'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      Preview
    </button>
    <button
      onClick={() => onViewModeChange('code')}
      className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
        viewMode === 'code'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      Code
    </button>
  </div>
);

interface SeamlessPreviewProps {
  content: string;
  streamingContent: string;
  isGenerating: boolean;
  onElementClick?: (element: any) => void;
}

const SeamlessPreview: React.FC<SeamlessPreviewProps> = ({
  content,
  streamingContent,
  isGenerating,
  onElementClick
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Use streaming content if available, otherwise use stable content
  const displayContent = streamingContent || content;

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const refreshPreview = () => {
    // Force re-render by clearing and setting content
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
      // Trigger a re-render
      setTimeout(() => {
        // The SeamlessHtmlRenderer will handle the re-rendering
      }, 100);
    }
  };

  // Handle element clicks for readdy.ai-style interaction
  useEffect(() => {
    const handleClick = (event: Event) => {
      const target = event.target as HTMLElement;
      if (!target) return;

      // Check if element needs implementation
      const needsImplementation = checkIfElementNeedsImplementation(target);
      if (needsImplementation.needs && onElementClick) {
        event.preventDefault();
        event.stopPropagation();
        
        onElementClick({
          textContent: needsImplementation.text,
          implementationType: needsImplementation.type,
          implementationReason: needsImplementation.reason,
          isInteractive: true
        });
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('click', handleClick, true);
      return () => container.removeEventListener('click', handleClick, true);
    }
  }, [onElementClick]);

  const checkIfElementNeedsImplementation = (element: HTMLElement) => {
    const tagName = element.tagName.toLowerCase();
    const text = element.textContent?.trim() || '';

    // Check buttons without onclick handlers
    if (tagName === 'button' || 
        (tagName === 'input' && (element as HTMLInputElement).type === 'button') ||
        element.classList.contains('btn')) {
      
      const hasOnclick = (element as any).onclick || element.getAttribute('onclick');
      if (!hasOnclick) {
        return {
          needs: true,
          type: 'button',
          reason: 'Button needs click functionality',
          text: text
        };
      }
    }

    // Check links without href or with placeholder href
    if (tagName === 'a') {
      const href = element.getAttribute('href');
      if (!href || href === '#' || href === 'javascript:void(0)') {
        return {
          needs: true,
          type: 'link',
          reason: 'Link needs destination',
          text: text
        };
      }
    }

    return { needs: false };
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'h-full'}`}>
      {/* Preview Controls */}
      <div className="absolute top-2 right-2 z-10 flex space-x-2">
        <button
          onClick={refreshPreview}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title="Refresh preview"
        >
          <FiRefreshCw className="w-4 h-4 text-gray-600" />
        </button>
        <button
          onClick={toggleFullscreen}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? (
            <FiMinimize2 className="w-4 h-4 text-gray-600" />
          ) : (
            <FiMaximize2 className="w-4 h-4 text-gray-600" />
          )}
        </button>
      </div>

     
      {/* Seamless HTML Renderer */}
      <div 
        ref={containerRef}
        className="w-full h-full overflow-auto bg-white p-4"
        style={{ 
          minHeight: '100%',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}
      >
        <SeamlessHtmlRenderer
          html={displayContent}
          className="w-full"
          onAnimationComplete={() => {
            console.log('🎬 Seamless rendering completed');
          }}
        />
      </div>
    </div>
  );
};

interface CodeViewProps {
  content: string;
  isGenerating: boolean;
  onContentChange?: (content: string) => void;
}

const CodeView: React.FC<CodeViewProps> = ({ content, isGenerating, onContentChange }) => {
  return (
    <div className="relative h-full">
      {/* Loading Overlay */}
      {isGenerating && (
        <div className="absolute inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="flex items-center space-x-3 bg-gray-800 rounded-lg px-4 py-3">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-400 border-t-transparent"></div>
            <span className="text-sm text-gray-300">Generating code...</span>
          </div>
        </div>
      )}

      {/* Enhanced Code Editor */}
      <CodeEditor
        value={content || ''}
        onChange={onContentChange || (() => {})}
        language="html"
        placeholder="<!-- Generated HTML will appear here -->"
        className="h-full"
        autoFormat={true}
      />
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const SeamlessPreviewPanel: React.FC<SeamlessPreviewPanelProps> = ({
  htmlContent,
  streamingContent,
  stableIframeContent,
  viewMode,
  isGenerating,
  onViewModeChange,
  onElementClick,
  onHtmlChange,
  className = ''
}) => {
  // Get current content for display
  const rawContent = streamingContent || htmlContent;
  const cleanHtmlContent = extractHtmlFromResponse(rawContent);

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between bg-white border-b border-gray-200 px-6 py-4">
        <h2 className="text-lg font-semibold text-gray-900">
          Preview <span className="text-sm text-blue-600 font-normal">(Seamless)</span>
        </h2>
        <ViewModeToggle viewMode={viewMode} onViewModeChange={onViewModeChange} />
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? (
          <SeamlessPreview
            content={cleanHtmlContent}
            streamingContent={streamingContent}
            isGenerating={isGenerating}
            onElementClick={onElementClick}
          />
        ) : (
          <CodeView
            content={cleanHtmlContent}
            isGenerating={isGenerating}
            onContentChange={onHtmlChange}
          />
        )}
      </div>
    </div>
  );
};

export default SeamlessPreviewPanel;
