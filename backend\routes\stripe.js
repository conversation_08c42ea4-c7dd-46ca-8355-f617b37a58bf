let stripe;
try {
  stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
} catch (e) {
  // Stripe not installed, suppress error for now
  stripe = null;
}

const express = require("express");
const router = express.Router();

// Middleware: require authentication (assumes req.user is set if logged in)
function requireAuth(req, res, next) {
  if (!req.user) {
    return res.status(401).json({ error: "Authentication required" });
  }
  next();
}

// POST /api/stripe/create-checkout-session
router.post("/create-checkout-session", requireAuth, async (req, res) => {
  if (!stripe) {
    // Stripe not installed, suppress error and return dummy URL
    return res.json({ url: (process.env.FRONTEND_URL || "http://localhost:5174") + "/?checkout=not-implemented" });
  }
  try {
    // You may want to use req.user.id or req.user.email for Stripe metadata
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      line_items: [
        {
          price: process.env.STRIPE_PRICE_ID, // Set this in your .env
          quantity: 1,
        },
      ],
      customer_email: req.user.email,
      metadata: {
        userId: req.user.id,
      },
      success_url: `${process.env.FRONTEND_URL || "http://localhost:5174"}/?checkout=success`,
      cancel_url: `${process.env.FRONTEND_URL || "http://localhost:5174"}/?checkout=cancel`,
    });
    res.json({ url: session.url });
  } catch (err) {
    console.error("Stripe checkout error:", err);
    res.status(500).json({ error: "Could not create Stripe session" });
  }
});

module.exports = router;
