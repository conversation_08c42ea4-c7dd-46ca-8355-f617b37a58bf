// <PERSON>ript to run the fix_token_usage.sql file
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function runFixTokenUsage() {
  console.log('Starting to fix token usage function...');
  
  try {
    // Check if the database is accessible
    const pingResult = await pool.query('SELECT 1 as ping');
    console.log(`Database connection: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    
    // Read and execute the fix_token_usage.sql file
    const sqlPath = path.join(__dirname, 'fix_token_usage.sql');
    if (fs.existsSync(sqlPath)) {
      console.log('Reading fix_token_usage.sql...');
      const sql = fs.readFileSync(sqlPath, 'utf8');
      
      console.log('Executing SQL...');
      await pool.query(sql);
      console.log('Successfully executed fix_token_usage.sql');
      
      // Test the function
      console.log('Testing the fixed function...');
      
      // Get a valid user ID
      const userResult = await pool.query('SELECT id FROM users LIMIT 1');
      
      if (userResult.rows.length > 0) {
        const userId = userResult.rows[0].id;
        console.log(`Found user with ID: ${userId}`);
        
        // Test the function
        const testResult = await pool.query(`
          SELECT use_tokens_and_log($1, $2, $3, $4, $5) as success
        `, [userId, 10, 'test_event', 'Test context', JSON.stringify({ test: 'data' })]);
        
        console.log(`Test result: ${testResult.rows[0].success}`);
        
        if (testResult.rows[0].success) {
          console.log('Function is working correctly!');
        } else {
          console.log('Function returned false. This should not happen with the fixed function.');
        }
      } else {
        console.log('No users found in the database. Cannot test the function.');
      }
    } else {
      console.error(`fix_token_usage.sql not found at ${sqlPath}`);
    }
    
    console.log('Fix token usage process completed.');
  } catch (error) {
    console.error('Error fixing token usage function:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the script
runFixTokenUsage().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
