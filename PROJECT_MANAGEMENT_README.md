# Project Management & Pages Implementation

This document provides a comprehensive overview of the project management and pages functionality implemented for the JustPrototype application, based on the Readdy.ai reference API and UI patterns.

## 🎯 Overview

The implementation adds a complete project management system with pages functionality to the `/prompt-v3` route, allowing users to:
- View and manage their projects in a paginated grid
- Create new projects from templates
- View pages/sessions within each project
- Navigate seamlessly between projects and pages
- Create and edit individual pages within projects

## 📋 Features Implemented

### 1. Project Management
- **Project List View**: Paginated grid display of user projects
- **Project Creation**: Template-based project creation (Basic, Landing Page, Dashboard)
- **Project Navigation**: Click-to-view project pages

### 2. Pages Management
- **Page List View**: Paginated display of pages/sessions within a project
- **Page Status Tracking**: Visual status indicators (active, editing, completed, expired)
- **Page Navigation**: Click-to-edit individual pages
- **Page Creation**: Create new pages within projects

### 3. API Compatibility
- **Readdy.ai API Structure**: Exact match with reference API endpoints
- **Pagination Support**: Configurable page sizes and navigation
- **Authentication**: Secure user-based access control

## 🏗️ Architecture

### Backend Structure
```
backend/
├── routes/
│   └── pageGen.js              # New API routes for project/session management
├── services/
│   ├── prototypeService.js     # Enhanced with pagination methods
│   └── sessionService.js       # Enhanced with project-based queries
└── db_*.sql                    # Database schemas and sample data
```

### Frontend Structure
```
ui/src/
├── components/
│   ├── ProjectList.tsx         # Project grid component
│   ├── CreateProject.tsx       # Project creation form
│   └── PageList.tsx           # Page/session grid component
├── services/
│   ├── pageGenService.ts       # Project management API calls
│   └── sessionService.ts      # Session/page management API calls
└── pages/
    └── PromptInputPageV3.tsx   # Enhanced with project/page views
```

## 🔧 Implementation Details

### API Endpoints

#### Project Management
```typescript
// List projects with pagination
POST /api/page_gen/project/list
Body: { page: { pageNum: number, pageSize: number } }
Response: { projects: [], totalCount: number, page: {...} }

// Create new project
POST /api/page_gen/project/create
Body: { title: string, description?: string, template?: string }
Response: { success: boolean, project: {...} }
```

#### Session/Page Management
```typescript
// List sessions for a project
POST /api/page_gen/session/list
Body: { projectId: string, page: { pageNum: number, pageSize: number } }
Response: { sessions: [], totalCount: number, page: {...} }
```

### Database Schema

#### Enhanced Prototypes Table
```sql
-- Existing table with added indexes for performance
CREATE INDEX IF NOT EXISTS idx_prototypes_user_created_desc 
ON prototypes(user_id, created_at DESC);
```

#### Sessions Table
```sql
-- Existing prototype_sessions table supports page functionality
CREATE TABLE prototype_sessions (
    id VARCHAR(36) PRIMARY KEY,
    prototype_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    page_url VARCHAR(500) NOT NULL,
    page_html TEXT NOT NULL,
    session_state VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- ... additional fields
);
```

### Frontend Components

#### ProjectList Component
- **Purpose**: Display paginated grid of user projects
- **Features**: 
  - Responsive grid layout (1-4 columns based on screen size)
  - Project cards with preview images, titles, descriptions
  - Pagination controls
  - Empty state with call-to-action
- **Props**: `onProjectSelect`, `onCreateNew`

#### CreateProject Component
- **Purpose**: Project creation form with template selection
- **Features**:
  - Form validation
  - Template selection (Basic, Landing Page, Dashboard)
  - Visual template previews
  - Error handling
- **Props**: `onProjectCreated`, `onCancel`

#### PageList Component
- **Purpose**: Display pages/sessions within a project
- **Features**:
  - Paginated grid of page cards
  - Status indicators with color coding
  - Creation and last accessed timestamps
  - Breadcrumb navigation
- **Props**: `project`, `onPageSelect`, `onCreatePage`, `onBack`

## 🚀 Usage Guide

### Navigation Flow
1. **Start**: `/prompt-v3` shows prompt input with navigation options
2. **Projects**: Click "My Projects" → View paginated project grid
3. **Project Pages**: Click project → View pages within that project
4. **Edit Page**: Click page → Open in editor with session context
5. **Create**: Use "New Project" or "New Page" buttons at any level

### View Modes
The PromptInputPageV3 component supports four view modes:
- `prompt`: Default prompt input interface
- `projects`: Project list view
- `create`: Project creation form
- `pages`: Page list for selected project

### API Integration
```typescript
// Example: Fetch project list
const response = await getProjectList(1, 12); // page 1, 12 items
console.log(response.projects); // Array of projects
console.log(response.totalCount); // Total number of projects

// Example: Fetch pages for a project
const sessions = await getSessionList(projectId, 1, 30); // page 1, 30 items
console.log(sessions.sessions); // Array of sessions/pages
```

## 🔧 Configuration

### Environment Variables
No additional environment variables required. Uses existing:
- `VITE_API_BASE_URL`: Frontend API base URL
- Database connection settings from existing configuration

### Pagination Settings
```typescript
// Default pagination settings (configurable)
const PROJECT_PAGE_SIZE = 12;  // Projects per page
const SESSION_PAGE_SIZE = 30;  // Sessions per page (matches Readdy.ai)
```

## 🧪 Testing

### Sample Data Generation
```sql
-- Run to create sample sessions for testing
SELECT create_sample_sessions();

-- Verify sample data
SELECT ps.id, p.title, ps.page_url, ps.session_state
FROM prototype_sessions ps
JOIN prototypes p ON ps.prototype_id = p.id
ORDER BY p.title, ps.created_at DESC;
```

### Manual Testing Checklist
- [ ] Project list loads with pagination
- [ ] Project creation works with all templates
- [ ] Project selection shows pages
- [ ] Page list displays with correct status colors
- [ ] Page selection navigates to editor
- [ ] Back navigation works correctly
- [ ] Pagination controls function properly
- [ ] Empty states display correctly

## 🔍 API Reference

### Request/Response Examples

#### Project List Request
```bash
curl -X POST http://localhost:3001/api/page_gen/project/list \
  -H "Content-Type: application/json" \
  -d '{"page":{"pageNum":1,"pageSize":12}}' \
  --cookie "session=..."
```

#### Session List Request (matches Readdy.ai exactly)
```bash
curl -X POST http://localhost:3001/api/page_gen/session/list \
  -H "Content-Type: application/json" \
  -d '{"projectId":"123","page":{"pageSize":30,"pageNum":1}}' \
  --cookie "session=..."
```

### Response Formats
```typescript
// Project List Response
{
  projects: Project[],
  totalCount: number,
  page: {
    pageNum: number,
    pageSize: number,
    totalPages: number
  }
}

// Session List Response
{
  sessions: Session[],
  totalCount: number,
  page: {
    pageNum: number,
    pageSize: number,
    totalPages: number
  }
}
```

## 🚨 Error Handling

### Backend Error Responses
- `400`: Bad Request (missing required fields)
- `401`: Unauthorized (authentication required)
- `404`: Not Found (project/session not found)
- `500`: Internal Server Error

### Frontend Error Handling
- Network errors: Retry buttons and error messages
- Loading states: Spinners and skeleton screens
- Empty states: Call-to-action buttons
- Form validation: Real-time field validation

## 🔄 Migration Guide

### Database Migrations
1. Ensure `prototype_sessions` table exists (from existing migrations)
2. Run `backend/db_project_enhancements.sql` for performance indexes
3. Optionally run `backend/db_sample_sessions.sql` for test data

### Frontend Integration
1. New components are self-contained
2. Existing routes and components unchanged
3. Only `/prompt-v3` route enhanced with new functionality

## 📝 Notes

### Design Decisions
- **Pagination**: Matches Readdy.ai API structure exactly
- **Navigation**: Breadcrumb-style with back buttons
- **Status Colors**: Standard UI patterns (green=active, yellow=editing, etc.)
- **Responsive Design**: Mobile-first approach with grid breakpoints

### Performance Considerations
- **Database Indexes**: Added for efficient pagination queries
- **Frontend Pagination**: Prevents loading large datasets
- **Component Optimization**: React.memo and useCallback where appropriate

### Security
- **Authentication**: All endpoints require user authentication
- **Authorization**: Users can only access their own projects/sessions
- **Input Validation**: Server-side validation for all inputs

## 🔮 Future Enhancements

### Potential Improvements
- Search and filtering for projects/pages
- Bulk operations (delete multiple projects)
- Project sharing and collaboration
- Advanced status management
- Real-time updates with WebSockets
- Project templates marketplace

### Scalability Considerations
- Database partitioning for large user bases
- CDN integration for project previews
- Caching strategies for frequently accessed data
- Background job processing for heavy operations

---

This implementation provides a solid foundation for project and page management that can be extended and customized based on specific requirements.
