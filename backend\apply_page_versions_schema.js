const { pool } = require('./services/promptDbService');
const fs = require('fs');
const path = require('path');

/**
 * Apply page versions schema to the database
 * This extends the existing versioning system to support page-level versioning
 */

async function applyPageVersionsSchema() {
  try {
    console.log('🔄 Applying page versions schema...');
    
    // Read the schema file
    const schemaPath = path.join(__dirname, 'db_page_versions_schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema
    await pool.query(schema);
    
    console.log('✅ Page versions schema applied successfully!');
    
    // Test the functions
    console.log('🧪 Testing database functions...');
    
    // Test get_next_page_version_number function (using a test page ID)
    const testResult = await pool.query('SELECT get_next_page_version_number(1) as next_version');
    console.log('📊 Next version number for page 1:', testResult.rows[0].next_version);
    
    console.log('✅ All tests passed!');
    
  } catch (error) {
    console.error('❌ Error applying page versions schema:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  applyPageVersionsSchema()
    .then(() => {
      console.log('🎉 Page versions schema migration completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { applyPageVersionsSchema };
