import React from "react";

export interface BillingTabProps {
  plan: "Free" | "Pay-as-you-go" | "Pro";
  prototypeCount: number;
  prototypeLimit: number;
  tokenUsage?: number;
  tokenLimit?: number;
  onBuyCredits?: () => void;
  onUpgrade?: () => void;
}

export const BillingTab: React.FC<BillingTabProps> = ({
  plan,
  prototypeCount,
  prototypeLimit,
  tokenUsage,
  tokenLimit,
  onBuyCredits,
  onUpgrade,
}) => {
  return (
    <div className="max-w-lg mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow p-6 flex flex-col gap-6 border border-zinc-200 dark:border-zinc-800">
      <div>
        <h2 className="text-xl font-bold mb-2">Billing & Usage</h2>
        <div className="flex items-center gap-2 mb-2">
          <span className="font-semibold text-zinc-700 dark:text-zinc-200">
            Plan:
          </span>
          <span className="capitalize px-2 py-1 rounded bg-blue-100 text-blue-700 text-xs font-semibold">
            {plan}
          </span>
        </div>
        <div className="flex flex-col gap-2">
          <div>
            <span className="text-sm text-zinc-600 dark:text-zinc-300">
              Prototypes used:
            </span>
            <span className="ml-2 font-medium">
              {prototypeCount} / {prototypeLimit}
            </span>
          </div>
          {typeof tokenUsage === "number" && typeof tokenLimit === "number" && (
            <div>
              <span className="text-sm text-zinc-600 dark:text-zinc-300">
                Token usage:
              </span>
              <span className="ml-2 font-medium">
                {tokenUsage} / {tokenLimit}
              </span>
            </div>
          )}
        </div>
      </div>
      <div className="flex flex-col gap-3">
        <button
          className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded transition"
          onClick={onBuyCredits}
        >
          Buy credits
        </button>
        <button
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded transition"
          onClick={onUpgrade}
        >
          Upgrade to Pro
        </button>
      </div>
      <div className="text-xs text-zinc-500 mt-2">
        Need more?{" "}
        <a
          href="#"
          className="text-blue-600 underline hover:text-blue-800"
          onClick={onUpgrade}
        >
          View Plans
        </a>
      </div>
    </div>
  );
};
