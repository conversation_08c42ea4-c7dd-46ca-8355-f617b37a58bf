import { ReactNode, useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import styles from './ErrorBoundary.module.css';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback: React.ComponentType<{ error: Error; reset: () => void }>;
}

export function ErrorBoundary({ children, fallback: Fallback }: ErrorBoundaryProps) {
  const [error, setError] = useState<Error | null>(null);

  if (error) {
    return <Fallback error={error} reset={() => setError(null)} />;
  }

  return (
    <ErrorBoundaryInternal onError={setError}>
      {children}
    </ErrorBoundaryInternal>
  );
}

interface ErrorBoundaryInternalProps {
  children: ReactNode;
  onError: (error: Error) => void;
}

function ErrorBoundaryInternal({ children, onError }: ErrorBoundaryInternalProps) {
  try {
    return <>{children}</>;
  } catch (e) {
    if (e instanceof Error) {
      onError(e);
    } else {
      onError(new Error(String(e)));
    }
    return null;
  }
}

// Default fallback component
export function DefaultErrorFallback({ error, reset }: { error: Error; reset: () => void }) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className={`${styles.errorContainer} ${isDark ? styles.darkErrorContainer : ''}`}>
      <div className={`${styles.errorTitle} ${isDark ? styles.darkErrorTitle : ''}`}>
        Something went wrong
      </div>
      <div className={`${styles.errorMessageContainer} ${isDark ? styles.darkErrorMessageContainer : ''}`}>
        <p className={styles.errorMessageLabel}>Error message:</p>
        <pre className={`${styles.errorMessageContent} ${isDark ? styles.darkErrorMessageContent : ''}`}>
          {error.message}
        </pre>
      </div>
      <button
        onClick={reset}
        className={styles.resetButton}
      >
        Try again
      </button>
    </div>
  );
}
