.section {
  padding: 1.75rem 1rem; /* Further reduced from 2.5rem to 1.75rem */
  width: 100%;
}

.light {
  background-color: #ffffff;
}

.dark {
  background-color: #f9fafb;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 1rem; /* Further reduced from 1.5rem to 1rem */
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.375rem; /* Further reduced from 0.5rem to 0.375rem */
}

.subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.content {
  width: 100%;
}

/* Dark mode styles */
:global(.dark) .light {
  background-color: #111827;
}

:global(.dark) .dark {
  background-color: #1f2937;
}

:global(.dark) .title {
  color: #f9fafb;
}

:global(.dark) .subtitle {
  color: #d1d5db;
}

/* Responsive styles */
@media (max-width: 768px) {
  .section {
    padding: 1.25rem 1rem; /* Further reduced from 1.75rem to 1.25rem */
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .section {
    padding: 1rem 0.75rem; /* Further reduced from 1.25rem to 1rem */
  }
  
  .title {
    font-size: 1.25rem;
  }
}
