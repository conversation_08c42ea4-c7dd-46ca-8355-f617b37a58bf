// Simulate the extractFragment method with debug logging

function extractFragmentDebug(htmlContent, selector) {
  try {
    console.log(`🔍 extractFragment called with selector: "${selector}"`);
    
    // Simple regex-based extraction for common selectors
    if (!selector || !htmlContent) {
      console.log('❌ No selector or htmlContent provided');
      return null;
    }

    // Handle multiple selectors (comma-separated)
    const selectors = selector.split(',').map(s => s.trim());
    console.log('📋 Selectors array:', selectors);
    
    for (const singleSelector of selectors) {
      console.log(`\n🔍 Processing selector: "${singleSelector}"`);
      let fragment = null;

      // Handle class selectors (.classname)
      if (singleSelector.startsWith('.')) {
        console.log('🏷️ Detected class selector');
        const className = singleSelector.substring(1);
        const classRegex = new RegExp(`<[^>]*class[^>]*["'\\s]${className}["'\\s][^>]*>.*?</[^>]*>`, 'gis');
        const match = htmlContent.match(classRegex);
        if (match && match[0]) {
          console.log(`✅ Found class fragment: ${className}`);
          return match[0];
        }
        console.log(`❌ No match for class: ${className}`);
      }

      // Handle ID selectors (#idname)
      else if (singleSelector.startsWith('#')) {
        console.log('🆔 Detected ID selector');
        const id = singleSelector.substring(1);
        const idRegex = new RegExp(`<[^>]*id[^>]*["']${id}["'][^>]*>.*?</[^>]*>`, 'gis');
        const match = htmlContent.match(idRegex);
        if (match && match[0]) {
          console.log(`✅ Found ID fragment: ${id}`);
          return match[0];
        }
        console.log(`❌ No match for ID: ${id}`);
      }

      // Handle attribute selectors ([attr*="value"])
      else if (singleSelector.includes('[') && singleSelector.includes(']')) {
        console.log('📋 Detected attribute selector');
        const attrMatch = singleSelector.match(/\[([^*=]+)\*?=?"?([^"]*)"?\]/);
        if (attrMatch) {
          const [, attr, value] = attrMatch;
          const attrRegex = new RegExp(`<[^>]*${attr}[^>]*${value}[^>]*>.*?</[^>]*>`, 'gis');
          const match = htmlContent.match(attrRegex);
          if (match && match[0]) {
            console.log(`✅ Found attribute fragment: ${attr}*="${value}"`);
            return match[0];
          }
          console.log(`❌ No match for attribute: ${attr}*="${value}"`);
        }
      }

      // Handle simple tag selectors (tagname)
      else if (/^[a-z]+$/i.test(singleSelector)) {
        console.log('🏷️ Detected simple tag selector');
        const tag = singleSelector.toLowerCase();
        console.log(`🔍 Looking for tag: ${tag}`);
        const tagRegex = new RegExp(`<${tag}[^>]*>.*?</${tag}>`, 'gis');
        console.log(`🔍 Using regex: ${tagRegex}`);
        const match = htmlContent.match(tagRegex);
        console.log(`🔍 Match result:`, match);
        if (match && match[0]) {
          console.log(`✅ Found tag fragment: ${tag}`);
          return match[0];
        }
        console.log(`❌ No match for tag: ${tag}`);
      } else {
        console.log(`❓ Unknown selector type: ${singleSelector}`);
      }
    }

    console.log(`⚠️ Could not extract fragment for selector: ${selector}`);
    return null;

  } catch (error) {
    console.error('❌ Error extracting fragment:', error);
    return null;
  }
}

const sampleHTML = `
<div id="app">
  <header class="bg-blue-600">
    <nav class="nav">
      <h1>Dashboard</h1>
      <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
      </ul>
    </nav>
  </header>
  <main>
    <div id="chart-container">Chart here</div>
  </main>
</div>
`;

console.log('🧪 Testing h1 extraction with debug:\n');
const result = extractFragmentDebug(sampleHTML, 'h1');
console.log('\n🏁 Final result:', result);
