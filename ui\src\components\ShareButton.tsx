import React, { useState } from 'react';
import { ShareIcon } from '@heroicons/react/24/outline';
import ShareModal from './ShareModal';
import styles from './ShareButton.module.css';

interface ShareButtonProps {
  prototypeId: string;
  prototypeName: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'text';
  size?: 'small' | 'medium' | 'large';
}

/**
 * <PERSON><PERSON> that opens the share modal
 */
const ShareButton: React.FC<ShareButtonProps> = ({
  prototypeId,
  prototypeName,
  className = '',
  variant = 'primary',
  size = 'medium'
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const openShareModal = () => {
    setIsModalOpen(true);
  };
  
  const closeShareModal = () => {
    setIsModalOpen(false);
  };
  
  return (
    <>
      <button
        className={`${styles.shareButton} ${styles[variant]} ${styles[size]} ${className}`}
        onClick={openShareModal}
        aria-label="Share prototype"
      >
        <ShareIcon className={styles.shareIcon} />
        <span>Share</span>
      </button>
      
      <ShareModal
        isOpen={isModalOpen}
        onClose={closeShareModal}
        prototypeId={prototypeId}
        prototypeName={prototypeName}
      />
    </>
  );
};

export default ShareButton;
