.pricingCard {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 2rem;
  width: 100%;
  max-width: 350px;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.pricingCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.featured {
  border-color: #4f46e5;
  transform: scale(1.05);
}

.featured:hover {
  transform: translateY(-5px) scale(1.05);
}

.currentPlan {
  border-color: #10b981;
}

.badge {
  position: absolute;
  top: -12px;
  right: 20px;
  background-color: #4f46e5;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.planHeader {
  margin-bottom: 1.5rem;
}

.planName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.planDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.pricing {
  margin-bottom: 1.5rem;
}

.priceContainer {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.price {
  font-size: 3rem;
  font-weight: 700;
  color: #111827;
  line-height: 1;
  margin: 0 0.25rem;
}

.period {
  font-size: 1rem;
  color: #6b7280;
}

.billedYearly {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.quota {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 8px;
}

.quotaItem {
  text-align: center;
}

.quotaValue {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.quotaLabel {
  font-size: 0.875rem;
  color: #6b7280;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  flex-grow: 1;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.featureCheck {
  color: #10b981;
  font-weight: bold;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.featureIcon {
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.featureIconComponent {
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.featureName {
  line-height: 1.5;
}

.featureDescription {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.highlightedFeature {
  font-weight: 600;
  color: #111827;
}

.cardFooter {
  margin-top: auto;
}

.actionButton {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.actionButton:hover {
  background-color: #4338ca;
}

.actionButton:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4);
}

.currentPlanButton {
  background-color: #10b981;
  cursor: default;
}

.currentPlanButton:hover {
  background-color: #10b981;
}

.loadingButton {
  opacity: 0.7;
  cursor: wait;
}

.disabledButton {
  background-color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
}

.disabledButton:hover {
  background-color: #9ca3af;
}

@media (max-width: 768px) {
  .pricingCard {
    max-width: 100%;
  }

  .featured {
    transform: none;
  }

  .featured:hover {
    transform: translateY(-5px);
  }
}
