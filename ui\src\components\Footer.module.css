.footer {
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 0.5rem 1rem; /* Single line footer - minimal padding */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: row; /* Single horizontal line */
  align-items: center;
  justify-content: space-between; /* Spread items across the line */
  gap: 1rem;
}

.logo {
  font-size: 1rem; /* Reduced from 1.5rem to 1rem */
  font-weight: 600; /* Reduced from 700 to 600 */
  background: linear-gradient(to right, #4f46e5, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.links {
  display: flex;
  gap: 1rem; /* Reduced from 1.25rem to 1rem */
  flex-wrap: wrap;
  justify-content: center;
}

.link {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
  font-size: 0.75rem; /* Reduced from 0.875rem to 0.75rem */
}

.link:hover {
  color: #4f46e5;
}

.copyright {
  color: #9ca3af;
  font-size: 0.75rem; /* Reduced from 0.875rem to 0.75rem */
  text-align: center;
}

/* Dark mode styles */
:global(.dark) .footer {
  background-color: #111827;
  border-top-color: #374151;
}

:global(.dark) .link {
  color: #d1d5db;
}

:global(.dark) .link:hover {
  color: #818cf8;
}

:global(.dark) .copyright {
  color: #9ca3af;
}

/* Responsive styles */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }

  .footer {
    padding: 0.4rem 1rem;
  }

  .links {
    gap: 0.75rem;
  }

  .logo {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .links {
    gap: 0.5rem;
  }

  .link {
    font-size: 0.7rem;
  }

  .copyright {
    font-size: 0.7rem;
  }
}
