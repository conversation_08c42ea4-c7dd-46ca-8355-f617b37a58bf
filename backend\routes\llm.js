const express = require('express');
const router = express.Router();
const llmController = require('../controllers/llmController');
const { ensureAuthenticated } = require('../auth/googleAuth');

// Authentication debugging middleware
router.use((req, res, next) => {
  console.log(`[Auth Debug] Route: ${req.method} ${req.path}`);
  console.log(`[Auth Debug] isAuthenticated: ${req.isAuthenticated ? req.isAuthenticated() : 'function not available'}`);
  console.log(`[Auth Debug] req.user: ${req.user ? JSON.stringify(req.user) : 'undefined'}`);
  console.log(`[Auth Debug] Session ID: ${req.session ? req.session.id : 'no session'}`);
  console.log(`[Auth Debug] Session Cookie: ${req.headers.cookie ? 'present' : 'absent'}`);

  // If user is not authenticated, create a temporary user for token tracking
  if (!req.user) {
    console.log('[Auth Debug] Creating temporary user for token tracking');
    req.user = {
      id: 'anonymous-' + (req.session ? req.session.id : Date.now()),
      isTemporary: true
    };
    console.log(`[Auth Debug] Temporary user created: ${JSON.stringify(req.user)}`);
  }

  next();
});

/**
 * @swagger
 * /llm/plan:
 *   post:
 *     summary: Generate a feature plan from a prompt
 *     tags:
 *       - LLM
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               prompt:
 *                 type: string
 *                 example: "Build a dashboard with sidebar navigation and charts"
 *     responses:
 *       200:
 *         description: Feature plan generated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 features:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.post('/plan', llmController.generatePlan);

/**
 * @swagger
 * /llm/generate:
 *   post:
 *     summary: Generate HTML/CSS/JS code from a plan
 *     tags:
 *       - LLM
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               plan:
 *                 type: string
 *                 example: "Layout\n- Responsive grid\nComponents\n- Hero section\n..."
 *     responses:
 *       200:
 *         description: Generated code
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
router.post('/generate', llmController.generateCode);

/**
 * @swagger
 * /llm/modify-element:
 *   post:
 *     summary: Modify a specific element in HTML content based on a prompt
 *     tags:
 *       - LLM
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               htmlContent:
 *                 type: string
 *                 description: The full HTML content
 *               elementSelector:
 *                 type: string
 *                 description: CSS selector for the element to modify
 *                 example: "#header"
 *               prompt:
 *                 type: string
 *                 description: Natural language prompt describing the desired changes
 *                 example: "Change the background color to blue and add a shadow"
 *     responses:
 *       200:
 *         description: Modified HTML content
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
router.post('/modify-element', llmController.modifyElement);

/**
 * @swagger
 * /llm/modify-content:
 *   post:
 *     summary: Modify the entire HTML content based on a prompt
 *     tags:
 *       - LLM
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               htmlContent:
 *                 type: string
 *                 description: The full HTML content
 *               prompt:
 *                 type: string
 *                 description: Natural language prompt describing the desired changes
 *                 example: "Add a footer section with copyright information"
 *     responses:
 *       200:
 *         description: Modified HTML content
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
router.post('/modify-content', llmController.modifyContent);

/**
 * @swagger
 * /llm/generate-functionality:
 *   post:
 *     summary: Generate functionality for an unimplemented interactive element
 *     tags:
 *       - LLM
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               htmlContent:
 *                 type: string
 *                 description: The full HTML content
 *               elementSelector:
 *                 type: string
 *                 description: CSS selector for the element to implement
 *                 example: "#login-button"
 *               elementType:
 *                 type: string
 *                 description: Type of element (button, link, form, etc.)
 *                 example: "button"
 *               elementContext:
 *                 type: string
 *                 description: Context or purpose of the element
 *                 example: "Login button on a login form"
 *               prompt:
 *                 type: string
 *                 description: Additional instructions for the functionality
 *                 example: "Implement login functionality that validates the form and shows success message"
 *     responses:
 *       200:
 *         description: Modified HTML content with implemented functionality
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
router.post('/generate-functionality', llmController.generateFunctionality);

module.exports = router;
