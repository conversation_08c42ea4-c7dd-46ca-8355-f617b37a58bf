import { useState } from 'react';
import styles from './PlanSummary.module.css';

interface PlanSummaryProps {
  features: string[];
}

export function PlanSummary({ features }: PlanSummaryProps) {
  const [expanded, setExpanded] = useState(false);

  const visibleFeatures = expanded ? features : features.slice(0, 2);

  return (
    <div className={styles.planSummary}>
      <div className={styles.title}>Plan:</div>
      <ul className={styles.featureList}>
        {visibleFeatures.map((f, i) => (
          <li key={i} className={styles.featureItem}>• {f}</li>
        ))}
      </ul>
      {!expanded && features.length > 2 && (
        <button className={styles.readMore} onClick={() => setExpanded(true)}>
          Read more
        </button>
      )}
    </div>
  );
}
