// <PERSON>ript to check the database schema
const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkDbSchema() {
  console.log('Starting database schema check...');
  
  try {
    // Check if the database is accessible
    const pingResult = await pool.query('SELECT 1 as ping');
    console.log(`Database connection: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    
    // Check if tables exist
    const tables = ['users', 'prompts', 'prompt_iterations', 'usage_log'];
    for (const table of tables) {
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        ) as exists
      `, [table]);
      
      console.log(`Table '${table}' exists: ${tableCheck.rows[0].exists}`);
      
      if (tableCheck.rows[0].exists) {
        // Check table structure
        const tableStructure = await pool.query(`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns
          WHERE table_schema = 'public' AND table_name = $1
          ORDER BY ordinal_position
        `, [table]);
        
        console.log(`\nStructure of table '${table}':`);
        tableStructure.rows.forEach(row => {
          console.log(`  ${row.column_name} (${row.data_type}, ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
        });
        
        // Check if there are any rows in the table
        const rowCount = await pool.query(`
          SELECT COUNT(*) as count FROM ${table}
        `);
        
        console.log(`  Row count: ${rowCount.rows[0].count}`);
        
        // If it's the users table, check a sample user
        if (table === 'users' && rowCount.rows[0].count > 0) {
          const userSample = await pool.query(`
            SELECT * FROM users LIMIT 1
          `);
          
          console.log('\nSample user:');
          const user = userSample.rows[0];
          Object.keys(user).forEach(key => {
            // Don't log sensitive information
            if (!['password', 'token'].includes(key)) {
              console.log(`  ${key}: ${user[key]}`);
            }
          });
        }
      }
      
      console.log(''); // Add a blank line for readability
    }
    
    // Check if functions exist
    const functions = ['use_tokens_and_log', 'increment_prototype_count', 'get_latest_iteration_number'];
    for (const func of functions) {
      const funcCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM pg_proc
          WHERE proname = $1
        ) as exists
      `, [func]);
      
      console.log(`Function '${func}' exists: ${funcCheck.rows[0].exists}`);
      
      if (funcCheck.rows[0].exists) {
        // Get function definition
        const funcDef = await pool.query(`
          SELECT pg_get_functiondef(oid) as definition
          FROM pg_proc
          WHERE proname = $1
        `, [func]);
        
        console.log(`\nDefinition of function '${func}':`);
        console.log(funcDef.rows[0].definition);
      }
      
      console.log(''); // Add a blank line for readability
    }
    
    console.log('Database schema check completed.');
  } catch (error) {
    console.error('Error checking database schema:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the script
checkDbSchema().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
