const { parse: parseJS } = require('@babel/parser');
const { parse: parseTS } = require('@typescript-eslint/parser');
const recast = require('recast');
const prettier = require('prettier');
const csstree = require('css-tree');
const { parse: parseHTML } = require('node-html-parser');

class CodeASTService {
  constructor() {
    this.transformationHooks = new Map();
    this.supportedLanguages = ['javascript', 'typescript', 'html', 'css'];
  }

  /**
   * Parse code into a syntax AST based on language
   */
  parseCode(code, language = 'javascript') {
    if (!code || typeof code !== 'string') {
      throw new Error('Code must be a non-empty string');
    }

    language = language.toLowerCase();

    try {
      switch (language) {
        case 'javascript':
        case 'js':
          return this.parseJavaScript(code);
        
        case 'typescript':
        case 'ts':
          return this.parseTypeScript(code);
        
        case 'html':
          return this.parseHTML(code);
        
        case 'css':
          return this.parseCSS(code);
        
        default:
          throw new Error(`Unsupported language: ${language}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse ${language} code: ${error.message}`);
    }
  }

  /**
   * Parse JavaScript code using Babel parser
   */
  parseJavaScript(code) {
    return parseJS(code, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript']
    });
  }

  /**
   * Parse TypeScript code
   */
  parseTypeScript(code) {
    return parseTS(code, {
      ecmaVersion: 2020,
      sourceType: 'module',
      ecmaFeatures: {
        jsx: true
      }
    });
  }

  /**
   * Parse HTML code with safe serialization
   */
  parseHTML(code) {
    const root = parseHTML(code, {
      lowerCaseTagName: false,
      comment: true,
      blockTextElements: {
        script: true,
        noscript: true,
        style: true,
        pre: true
      }
    });

    // Create a simplified AST without circular references
    const simplifyNode = (node) => {
      if (!node) return null;

      const simplified = {
        type: 'element',
        tag: node.tagName,
        rawText: node.rawText,
        nodeType: node.nodeType,
        attributes: {},
        childNodes: []
      };

      // Convert attributes to a simple object
      if (node.attributes) {
        for (const [key, value] of Object.entries(node.attributes)) {
          simplified.attributes[key] = value;
        }
      }

      // Recursively simplify child nodes
      if (node.childNodes) {
        simplified.childNodes = node.childNodes.map(child => simplifyNode(child));
      }

      return simplified;
    };

    return {
      type: 'html',
      root: simplifyNode(root),
      scripts: [],
      styles: []
    };
  }

  /**
   * Parse CSS code
   */
  parseCSS(code) {
    return csstree.parse(code, {
      parseValue: true,
      parseCustomProperty: true
    });
  }

  /**
   * Transform AST using registered transformation hooks
   */
  async transformAST(ast, transformations = [], language = 'javascript') {
    if (!ast) {
      throw new Error('AST is required for transformation');
    }

    let transformedAST = ast;

    for (const transformation of transformations) {
      const { type, config } = transformation;
      
      if (this.transformationHooks.has(type)) {
        const hook = this.transformationHooks.get(type);
        try {
          transformedAST = await hook(transformedAST, config, language);
        } catch (error) {
          console.error(`Transformation ${type} failed:`, error);
          throw new Error(`Transformation ${type} failed: ${error.message}`);
        }
      }
    }

    return transformedAST;
  }

  /**
   * Print code from AST with formatting
   */
  printCode(ast, language = 'javascript', options = {}) {
    if (!ast) {
      throw new Error('AST is required for code printing');
    }

    try {
      let code = '';

      switch (language.toLowerCase()) {
        case 'javascript':
        case 'js':
        case 'typescript':
        case 'ts':
          code = recast.print(ast).code;
          break;
        
        case 'html':
          code = this.printHTML(ast);
          break;
        
        case 'css':
          code = csstree.generate(ast);
          break;
        
        default:
          throw new Error(`Unsupported language for printing: ${language}`);
      }

      // Format the code
      if (options.format !== false && code) {
        try {
          code = prettier.format(String(code), {
            parser: language === 'css' ? 'css' : language === 'html' ? 'html' : 'babel',
            semi: true,
            singleQuote: true,
            ...options.prettier
          });
        } catch (formatError) {
          console.warn('Failed to format code:', formatError);
        }
      }

      return code;
    } catch (error) {
      throw new Error(`Failed to print ${language} code: ${error.message}`);
    }
  }

  /**
   * Print HTML from simplified AST
   */
  printHTML(ast) {
    if (!ast || !ast.root) return '';

    const printNode = (node) => {
      if (!node) return '';

      // Text node
      if (node.nodeType === 3) {
        return node.rawText || '';
      }

      // Comment node
      if (node.nodeType === 8) {
        return `<!--${node.rawText}-->`;
      }

      // Element node
      const tag = node.tag;
      if (!tag) return '';

      // Build attributes string
      const attrs = Object.entries(node.attributes || {})
        .map(([key, value]) => ` ${key}="${value}"`)
        .join('');

      // Handle void elements
      const voidElements = ['img', 'br', 'hr', 'input', 'meta', 'link'];
      if (voidElements.includes(tag.toLowerCase())) {
        return `<${tag}${attrs}>`;
      }

      // Handle normal elements with children
      const children = (node.childNodes || [])
        .map(child => printNode(child))
        .join('');

      return `<${tag}${attrs}>${children}</${tag}>`;
    };

    return printNode(ast.root);
  }

  registerTransformationHook(type, hook) {
    if (typeof hook !== 'function') {
      throw new Error('Transformation hook must be a function');
    }
    this.transformationHooks.set(type, hook);
  }

  getTransformationHooks() {
    return Array.from(this.transformationHooks.keys());
  }

  getSupportedLanguages() {
    return [...this.supportedLanguages];
  }
}

module.exports = new CodeASTService();
