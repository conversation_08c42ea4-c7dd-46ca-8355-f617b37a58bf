{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "uiNode", "type": "object", "title": "UI Node Schema", "description": "Schema for validating UI component tree nodes", "required": ["id", "type", "props"], "additionalProperties": false, "definitions": {"uiNode": {"$ref": "#"}}, "properties": {"id": {"type": "string", "description": "Unique identifier for the node"}, "type": {"type": "string", "enum": ["Page", "Section", "<PERSON><PERSON>", "Input", "Text", "Image", "Link", "Form"], "description": "Type of UI component"}, "props": {"type": "object", "additionalProperties": true, "description": "Component properties"}, "children": {"type": "array", "items": {"$ref": "#"}, "description": "Child nodes"}}}