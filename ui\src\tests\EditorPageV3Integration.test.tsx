/**
 * Integration tests for EditorPageV3Refactored
 * Tests complete workflows and user interactions
 */

import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import React from 'react';
import EditorPageV3Refactored from '../pages/EditorPageV3Refactored';

// Mock fetch for API calls
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock EventSource for streaming
class MockEventSource {
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onopen: ((event: Event) => void) | null = null;
  readyState = 1;
  url = '';
  
  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 0);
  }
  
  close() {
    this.readyState = 2;
  }
  
  simulateMessage(data: string) {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data }));
    }
  }
}

global.EventSource = MockEventSource as any;

// Mock router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ projectId: '123' }),
    useLocation: () => ({ 
      state: { 
        prompt: 'Create a landing page',
        projectId: 123 
      }, 
      pathname: '/editor-v3-refactored' 
    }),
    useNavigate: () => vi.fn(),
  };
});

// Test wrapper
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('EditorPageV3Refactored Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock default API responses
    mockFetch.mockImplementation((url: string) => {
      if (url.includes('/api/page_gen/project/123/pages')) {
        return Promise.resolve({
          ok: true,
          json: async () => ({ pages: [] }),
        });
      }
      
      if (url.includes('/api/page_gen/projects')) {
        return Promise.resolve({
          ok: true,
          json: async () => ({ 
            projects: [
              { id: 123, name: 'Test Project', description: 'Test project description' }
            ],
            total: 1 
          }),
        });
      }
      
      if (url.includes('/api/llm/v3/generate-html')) {
        return Promise.resolve({
          ok: true,
          json: async () => ({ 
            sessionId: 'test-session-123',
            pageTitle: 'Landing Page'
          }),
        });
      }
      
      return Promise.resolve({
        ok: true,
        json: async () => ({}),
      });
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Complete Page Creation Workflow', () => {
    test('should create a new page from prompt to completion', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });

      // Enter a prompt
      const promptInput = screen.getByRole('textbox');
      await user.type(promptInput, 'Create a modern landing page with hero section');

      // Submit the prompt
      const submitButton = screen.getByRole('button', { name: /create|generate/i });
      await user.click(submitButton);

      // Should start generation
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/llm/v3/generate-html'),
          expect.objectContaining({
            method: 'POST',
            body: expect.stringContaining('Create a modern landing page with hero section'),
          })
        );
      });

      // Should show loading state
      expect(screen.getByText(/generating|creating/i)).toBeInTheDocument();
    });

    test('should handle plan generation and approval workflow', async () => {
      const user = userEvent.setup();
      
      // Mock plan generation response
      mockFetch.mockImplementationOnce((url: string) => {
        if (url.includes('/api/llm/v3/plan')) {
          return Promise.resolve({
            ok: true,
            json: async () => ({
              plan: {
                title: 'Landing Page',
                description: 'A modern landing page with hero section',
                features: ['Hero section', 'Call-to-action buttons', 'Responsive design']
              }
            }),
          });
        }
        return Promise.resolve({ ok: true, json: async () => ({}) });
      });

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Enter prompt and generate plan
      const promptInput = screen.getByRole('textbox');
      await user.type(promptInput, 'Create a landing page');

      const planButton = screen.getByRole('button', { name: /plan/i });
      await user.click(planButton);

      // Should show plan review
      await waitFor(() => {
        expect(screen.getByText(/plan/i)).toBeInTheDocument();
      });

      // Approve plan and generate
      const generateButton = screen.getByRole('button', { name: /generate/i });
      await user.click(generateButton);

      // Should start HTML generation
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/llm/v3/generate-html'),
          expect.any(Object)
        );
      });
    });
  });

  describe('Page Management Workflow', () => {
    test('should load existing pages and allow selection', async () => {
      const user = userEvent.setup();
      
      // Mock pages response
      mockFetch.mockImplementationOnce((url: string) => {
        if (url.includes('/api/page_gen/project/123/pages')) {
          return Promise.resolve({
            ok: true,
            json: async () => ({
              pages: [
                { id: 1, name: 'Home Page', content: '<div>Home content</div>' },
                { id: 2, name: 'About Page', content: '<div>About content</div>' },
              ]
            }),
          });
        }
        return Promise.resolve({ ok: true, json: async () => ({}) });
      });

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Wait for pages to load
      await waitFor(() => {
        expect(screen.getByText('Home Page')).toBeInTheDocument();
        expect(screen.getByText('About Page')).toBeInTheDocument();
      });

      // Click on a page
      await user.click(screen.getByText('Home Page'));

      // Should load page content
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/page_gen/session/'),
          expect.any(Object)
        );
      });
    });

    test('should handle page rename workflow', async () => {
      const user = userEvent.setup();
      
      // Mock pages and update responses
      mockFetch
        .mockImplementationOnce((url: string) => {
          if (url.includes('/api/page_gen/project/123/pages')) {
            return Promise.resolve({
              ok: true,
              json: async () => ({
                pages: [
                  { id: 1, name: 'Old Page Name', content: '<div>Content</div>' }
                ]
              }),
            });
          }
          return Promise.resolve({ ok: true, json: async () => ({}) });
        })
        .mockImplementationOnce((url: string) => {
          if (url.includes('/api/page_gen/page/1')) {
            return Promise.resolve({
              ok: true,
              json: async () => ({
                id: 1,
                name: 'New Page Name',
                content: '<div>Content</div>'
              }),
            });
          }
          return Promise.resolve({ ok: true, json: async () => ({}) });
        });

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Wait for page to load
      await waitFor(() => {
        expect(screen.getByText('Old Page Name')).toBeInTheDocument();
      });

      // Find and click rename button (this would be in a dropdown or context menu)
      const pageElement = screen.getByText('Old Page Name');
      await user.hover(pageElement);
      
      // Look for rename option (implementation depends on UI structure)
      const renameButton = screen.queryByRole('button', { name: /rename/i });
      if (renameButton) {
        await user.click(renameButton);

        // Enter new name
        const nameInput = screen.getByDisplayValue('Old Page Name');
        await user.clear(nameInput);
        await user.type(nameInput, 'New Page Name');

        // Confirm rename
        const confirmButton = screen.getByRole('button', { name: /save|confirm/i });
        await user.click(confirmButton);

        // Should call update API
        await waitFor(() => {
          expect(mockFetch).toHaveBeenCalledWith(
            '/api/page_gen/page/1',
            expect.objectContaining({
              method: 'PUT',
              body: JSON.stringify({ name: 'New Page Name' }),
            })
          );
        });
      }
    });

    test('should handle page deletion workflow', async () => {
      const user = userEvent.setup();
      
      // Mock pages and delete responses
      mockFetch
        .mockImplementationOnce((url: string) => {
          if (url.includes('/api/page_gen/project/123/pages')) {
            return Promise.resolve({
              ok: true,
              json: async () => ({
                pages: [
                  { id: 1, name: 'Page to Delete', content: '<div>Content</div>' }
                ]
              }),
            });
          }
          return Promise.resolve({ ok: true, json: async () => ({}) });
        })
        .mockImplementationOnce((url: string) => {
          if (url.includes('/api/page_gen/page/1')) {
            return Promise.resolve({
              ok: true,
              json: async () => ({ success: true }),
            });
          }
          return Promise.resolve({ ok: true, json: async () => ({}) });
        });

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Wait for page to load
      await waitFor(() => {
        expect(screen.getByText('Page to Delete')).toBeInTheDocument();
      });

      // Find and click delete button
      const pageElement = screen.getByText('Page to Delete');
      await user.hover(pageElement);
      
      const deleteButton = screen.queryByRole('button', { name: /delete/i });
      if (deleteButton) {
        await user.click(deleteButton);

        // Confirm deletion
        const confirmButton = screen.getByRole('button', { name: /confirm|delete/i });
        await user.click(confirmButton);

        // Should call delete API
        await waitFor(() => {
          expect(mockFetch).toHaveBeenCalledWith(
            '/api/page_gen/page/1',
            expect.objectContaining({
              method: 'DELETE',
            })
          );
        });
      }
    });
  });

  describe('Error Handling Workflows', () => {
    test('should handle API errors gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock API error
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      // Try to create a page
      const promptInput = screen.getByRole('textbox');
      await user.type(promptInput, 'Create a page');

      const submitButton = screen.getByRole('button', { name: /create|generate/i });
      await user.click(submitButton);

      // Should show error state or handle gracefully
      await waitFor(() => {
        // Component should not crash and should show some error indication
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
    });

    test('should handle generation timeout', async () => {
      const user = userEvent.setup();
      
      // Mock slow response
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => ({ sessionId: 'test-session' })
          }), 5000)
        )
      );

      render(
        <TestWrapper>
          <EditorPageV3Refactored />
        </TestWrapper>
      );

      const promptInput = screen.getByRole('textbox');
      await user.type(promptInput, 'Create a page');

      const submitButton = screen.getByRole('button', { name: /create|generate/i });
      await user.click(submitButton);

      // Should show loading state
      expect(screen.getByText(/generating|creating/i)).toBeInTheDocument();
    });
  });
});
