import React, { useState, useEffect, useCallback, useRef, memo } from 'react';

export interface SelectedElement {
  id: string;
  tagName: string;
  elementId?: string;
  className?: string;
  ref: string;
  description?: string;
  textContent?: string;
  needsFunctionality?: boolean;
  functionalityType?: string;
  functionalityContext?: string;
  styles?: {
    marginLeft?: string;
    marginRight?: string;
    marginTop?: string;
    marginBottom?: string;
    paddingLeft?: string;
    paddingRight?: string;
    paddingTop?: string;
    paddingBottom?: string;
    backgroundColor?: string;
  };
}

interface SimpleElementSelectorProps {
  iframeRef: React.RefObject<HTMLIFrameElement>;
  onElementSelect?: (element: SelectedElement) => void;
  onGenerateFunctionality?: (element: SelectedElement) => void;
  disabled?: boolean;
  initialActive?: boolean;
  onSelectionStateChange?: (isActive: boolean) => void;
  viewMode?: string;
  streamingComplete?: boolean;
  iframeLoaded?: boolean;
}

// Create an empty element object for deselection
const EMPTY_ELEMENT: SelectedElement = {
  id: '',
  tagName: '',
  ref: ''
};

export const SimpleElementSelector = memo<SimpleElementSelectorProps>(({
  iframeRef,
  onElementSelect,
  onGenerateFunctionality,
  disabled = false,
  initialActive = false,
  onSelectionStateChange,
  viewMode = 'preview',
  streamingComplete = true,
  iframeLoaded = true
}) => {
  // Single state for selection mode
  const [isSelecting, setIsSelecting] = useState(initialActive);

  // Use a ref instead of state for script injection to avoid re-renders
  const scriptInjectedRef = useRef(false);

  // Ref to track previous viewMode for comparison
  const prevViewModeRef = useRef(viewMode);

  // Ref to track if we need to handle initialActive changes
  const initialActiveHandledRef = useRef(false);

  // No longer need the toggleSelectionMode function as we handle this in the effects

  // Handle messages from iframe - memoized handler for better performance
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Early return if no data or no handlers
      if (!event.data || (!onElementSelect && !onGenerateFunctionality)) return;

      // Log all incoming messages for debugging
      console.log('Received message from iframe:', JSON.stringify(event.data));

      const { type, data } = event.data;

      if (type === 'elementSelected' && data) {
        console.log('Received elementSelected message from iframe:', data.tagName);
        console.log('Element selector:', data.selector);
        console.log('Full element data:', JSON.stringify(data));

        // Make sure selection mode is still active in the parent component
        if (!isSelecting) {
          console.log('Selection mode was deactivated, reactivating it');
          setIsSelecting(true);
        }

        // Create a selected element object with a stable ID generation
        const newElement: SelectedElement = {
          id: `el-${Date.now().toString(36)}-${Math.floor(Math.random() * 10000).toString(36)}`,
          tagName: data.tagName,
          elementId: data.id || undefined,
          className: data.className || undefined,
          ref: data.selector,
          description: data.description || undefined, // Include the element description
          textContent: data.textContent || undefined, // Include the element text content
          styles: data.styles || undefined
        };

        console.log('Created new element object with ID:', newElement.id);

        // Check if the element needs functionality
        if (iframeRef.current && (iframeRef.current as any).checkElementNeedsFunctionality) {
          try {
            // Get the element in the iframe
            const iframe = iframeRef.current;
            const doc = iframe.contentDocument;
            if (doc) {
              const element = doc.querySelector(data.selector);
              if (element) {
                // Check if the element needs functionality
                const result = (iframeRef.current as any).checkElementNeedsFunctionality(element);
                if (result && result.needsFunctionality) {
                  // Add functionality info to the element
                  newElement.needsFunctionality = true;
                  newElement.functionalityType = result.details?.type;
                  newElement.functionalityContext = result.details?.context;
                }
              }
            }
          } catch (error) {
            console.error('Error checking if element needs functionality:', error);
          }
        }

        // Notify parent component
        if (onElementSelect) {
          console.log('Notifying parent about element selection:', newElement.tagName);
          console.log('Calling onElementSelect with element ID:', newElement.id);

          try {
            // Call the onElementSelect callback with the new element
            onElementSelect(newElement);
            console.log('onElementSelect callback completed successfully');
          } catch (error) {
            console.error('Error in onElementSelect callback:', error);
          }
        } else {
          console.warn('No onElementSelect callback provided');
        }

        // Log the selected element for debugging
        console.log('Selected element object:', JSON.stringify(newElement));
      } else if (type === 'elementDeselected') {
        console.log('Received elementDeselected message from iframe');

        // Notify parent component about deselection using the constant
        if (onElementSelect) {
          console.log('Notifying parent about element deselection');
          onElementSelect(EMPTY_ELEMENT);
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [onElementSelect, onGenerateFunctionality, iframeRef]);

  // CSS styles for element selection - defined outside to prevent recreation
  const SELECTION_STYLES = `
    /* Add debugger to track when styles are applied */
    @keyframes debug-animation {
      0% { opacity: 1; }
      100% { opacity: 1; }
    }

    .element-hover {
      outline: 3px dashed #2196f3 !important;
      outline-offset: 2px !important;
      z-index: 9999 !important;
      cursor: pointer !important;
      animation: debug-animation 0.1s;
    }

    .element-selected {
      outline: 2px solid #2196f3!important;
      outline-offset: 2px !important;
      // background-color: rgba(255, 64, 129, 0.1) !important;
      z-index: 9999 !important;
      position: relative !important;
      // box-shadow: 0 0 10px rgba(255, 64, 129, 0.5) !important;
      animation: debug-animation 0.1s;
    }

    /* Add a more visible highlight for selected elements */
    .element-selected::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      border: 2px solid #2196f3;
      z-index: 9999;
    }


  `;

  // Element selector script - simplified version
  const SELECTION_SCRIPT = `
    (function() {
      console.log('Element selector script starting');

      // Make sure jQuery is available
      if (typeof jQuery === 'undefined') {
        console.error('jQuery not available');
        return;
      }

      // Use jQuery
      const $ = jQuery;

      // Use the global window.selectionModeActive variable
      // This is set by the toggleChatElementSelector function in EditorPage.tsx
      // If it doesn't exist yet, initialize it to false
      // if (typeof window.selectionModeActive === 'undefined') {
      //   window.selectionModeActive = false;
      // }

      // Function to get a unique selector for an element
      function getUniqueSelector(el) {
        if (!el) return '';
        if (el === document.body) return 'body';
        if (el === document.documentElement) return 'html';

        // Start with the tag name
        const tagName = el.tagName.toLowerCase();

        // If element has an ID, use it (most specific)
        if (el.id) {
          return '#' + el.id;
        }

        // If element has classes, use the first class
        if (el.className && typeof el.className === 'string') {
          const classes = el.className.trim().split(/\\s+/);
          if (classes.length > 0 && classes[0]) {
            // Try with tag name and class
            const selector = tagName + '.' + classes[0];
            // Check if this selector is unique enough
            if (document.querySelectorAll(selector).length === 1) {
              return selector;
            }
          }
        }

        // Try to create a selector based on the element's position among siblings
        const parent = el.parentElement;
        if (parent) {
          const siblings = Array.from(parent.children);
          const index = siblings.indexOf(el);

          // Use nth-child
          const nthSelector = tagName + ':nth-child(' + (index + 1) + ')';
          const parentSelector = parent === document.body ? 'body' :
                              parent === document.documentElement ? 'html' :
                              getUniqueSelector(parent);

          return parentSelector + ' > ' + nthSelector;
        }

        // Fallback to a data attribute
        const uniqueId = 'sel-' + Math.random().toString(36).substring(2, 10);
        el.setAttribute('data-selector-id', uniqueId);
        return '[data-selector-id="' + uniqueId + '"]';
      }

      // Handle element click
      $(document).on('click', function(e) {
      debugger;
        // Only proceed if selection mode is active
        if (!window.parent.selectionModeActive) {
          console.log('Selection mode not active, ignoring click');
          return;
        }

        console.log('Element clicked in selection mode');

        // Prevent default behavior
        e.preventDefault();
        e.stopPropagation();

        // If clicking on body or html, deselect all
        if (e.target === document.body || e.target === document.documentElement) {
          console.log('Clicked on body/html, deselecting all elements');
          $('.element-selected').removeClass('element-selected');
          window.parent.postMessage({
            type: 'elementDeselected',
            data: {}
          }, '*');
          return;
        }

        console.log('Clicked on element:', e.target.tagName);

        // First, deselect any previously selected elements
        $('.element-selected').removeClass('element-selected');

        // Select the clicked element
        $(e.target).addClass('element-selected');

        // Get computed styles
        const computedStyle = window.getComputedStyle(e.target);

        // Extract styles
        const styles = {
          marginLeft: computedStyle.marginLeft,
          marginRight: computedStyle.marginRight,
          marginTop: computedStyle.marginTop,
          marginBottom: computedStyle.marginBottom,
          paddingLeft: computedStyle.paddingLeft,
          paddingRight: computedStyle.paddingRight,
          paddingTop: computedStyle.paddingTop,
          paddingBottom: computedStyle.paddingBottom,
          backgroundColor: computedStyle.backgroundColor
        };

        // Generate a unique selector
        const selector = getUniqueSelector(e.target);
        console.log('Generated selector:', selector);

        // Create a unique ID
        const uniqueId = 'el-' + Date.now() + '-' + Math.floor(Math.random() * 10000);

        // Get text content
        const textContent = e.target.textContent || '';

        // Create message data
        const data = {
          type: 'elementSelected',
          data: {
            selector: selector,
            tagName: e.target.tagName.toLowerCase(),
            id: e.target.id || '',
            className: e.target.className || '',
            styles: styles,
            description: e.target.tagName.toLowerCase(),
            textContent: textContent,
            uniqueId: uniqueId
          }
        };

        // Send message to parent
        window.parent.postMessage(data, '*');
      });

      // Handle element hover
      $(document).on('mouseover', function(e) {
        // Only proceed if selection mode is active
        if (!window.parent.selectionModeActive) return;

        // Remove hover class from all elements except the current target
        $('.element-hover').not(e.target).removeClass('element-hover');

        // Add hover class to current target
        $(e.target).addClass('element-hover');
      });

      // Handle element hover out
      $(document).on('mouseout', function(e) {
        // Only proceed if selection mode is active
        if (!window.parent.selectionModeActive) return;

        // Remove hover class from the target
        $(e.target).removeClass('element-hover');
      });

      // Simple function to update UI based on selection mode
      function updateUI() {
        if (window.parent.selectionModeActive) {
          $('body').addClass('selection-mode-active').removeClass('selection-mode-inactive');
        } else {
          $('body').addClass('selection-mode-inactive').removeClass('selection-mode-active');
        }
      }

      // Initialize UI
      updateUI();

      // Listen for messages from parent
      window.addEventListener('message', function(event) {
        if (!event.data || !event.data.type) return;

        const { type, active } = event.data;

        if (type === 'toggleSelectionMode') {
          console.log('Received toggleSelectionMode message:', active);

          // Simply set the selection mode active/inactive based on the toggle message
          // selectionModeActive = active;
          // window.selectionModeActive = active;

          // Update the UI
          updateUI();

          // Only clear selections if turning off selection mode
          if (!window.parent.selectionModeActive) {
            $('.element-hover, .element-selected').removeClass('element-hover element-selected');
          }

          console.log('Selection mode set to:', window.parent.selectionModeActive);
        } else if (type === 'clearSelections') {
          console.log('Clearing all selections');
          $('.element-hover, .element-selected').removeClass('element-hover element-selected');
        }
      });

      console.log('Element selector initialized successfully');
    })();
  `;

  // Function to inject scripts into iframe
  const injectScripts = useCallback(() => {
    if (!iframeRef.current) return false;

    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return false;

    const doc = iframe.contentDocument;

    // Check if script is already injected to avoid duplicate injection
    if (doc.querySelector('#element-selector-script')) return true;

    try {
      // Add selection styles
      const style = doc.createElement('style');
      style.id = 'element-selector-styles';
      style.textContent = SELECTION_STYLES;
      doc.head.appendChild(style);

      // Create a two-step injection process

      // Step 1: Inject jQuery
      const jQueryScript = doc.createElement('script');
      jQueryScript.id = 'jquery-script';
      jQueryScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';

      // Step 2: After jQuery loads, inject the selection script
      jQueryScript.onload = () => {
        console.log('jQuery loaded successfully');

        // Small delay to ensure jQuery is fully initialized
        setTimeout(() => {
          try {
            // Create a separate script element for the selection script
            const selectorScript = doc.createElement('script');
            selectorScript.id = 'element-selector-script';

            // Use the SELECTION_SCRIPT defined above
            selectorScript.textContent = SELECTION_SCRIPT;

            // Append to body
            if (doc.body) {
              doc.body.appendChild(selectorScript);
              console.log('Element selector script injected successfully');
            } else {
              console.error('Document body not available for script injection');
            }
          } catch (err) {
            console.error('Error injecting selection script:', err);
          }
        }, 100);
      };

      // Add error handling
      jQueryScript.onerror = () => {
        console.error('Failed to load jQuery');
      };

      // Append jQuery script to head
      doc.head.appendChild(jQueryScript);
      console.log('jQuery script injection started');

      console.log('Element selector scripts injection process started');
      return true;
    } catch (error) {
      console.error('Failed to inject element selector scripts:', error);
      return false;
    }
  }, [iframeRef, SELECTION_STYLES, SELECTION_SCRIPT]);

  // Single effect to handle script injection and selection mode
  useEffect(() => {
    // Skip if iframe ref is not available
    if (!iframeRef.current) return;

    console.log('Setting up element selector with iframe');

    // Function to check if scripts are already injected
    const areScriptsInjected = () => {
      if (!iframeRef.current?.contentDocument) return false;
      return !!iframeRef.current.contentDocument.querySelector('#element-selector-script');
    };

    // Function to inject scripts if needed and return success status
    const ensureScriptsInjected = () => {
      // If scripts are already injected, return true
      if (scriptInjectedRef.current || areScriptsInjected()) {
        scriptInjectedRef.current = true;
        return true;
      }

      // Otherwise, inject scripts
      const success = injectScripts();
      if (success) {
        scriptInjectedRef.current = true;
      }
      return success;
    };

    // Function to update selection mode in iframe
    const updateSelectionMode = (active: boolean) => {
      if (!iframeRef.current?.contentWindow) return;

      // Ensure scripts are injected before toggling selection mode
      if (ensureScriptsInjected()) {
        iframeRef.current.contentWindow.postMessage({
          type: 'toggleSelectionMode',
          active
        }, '*');
      }
    };

    // Handle iframe load event
    const handleIframeLoad = () => {
      console.log('Iframe load event fired');

      // Reset script injected status
      scriptInjectedRef.current = false;

      // Always inject scripts when iframe loads, regardless of streaming state
      console.log('Iframe loaded, injecting scripts');
      const success = ensureScriptsInjected();

      // If scripts were successfully injected and selection is active, update selection mode
      if (success && isSelecting) {
        updateSelectionMode(true);
      }
    };

    // Add load event listener
    const currentIframe = iframeRef.current;
    currentIframe.addEventListener('load', handleIframeLoad);

    // If iframe is already loaded, ensure scripts are injected
    if (currentIframe.contentDocument?.readyState === 'complete') {
      console.log('Iframe already loaded, ensuring scripts are injected');
      ensureScriptsInjected();

      // If selection is active, update selection mode
      if (isSelecting) {
        updateSelectionMode(true);
      }
    }

    // Cleanup function
    return () => {
      currentIframe.removeEventListener('load', handleIframeLoad);
    };
  }, [iframeRef, injectScripts, isSelecting]);

  // Effect to handle initialActive - runs ONLY once on mount
  useEffect(() => {
    console.log('Setting initial selection state:', initialActive);

    // Set isSelecting state based on initialActive (only on mount)
    setIsSelecting(initialActive);

    // Mark as handled
    initialActiveHandledRef.current = true;

    // No dependency on initialActive or isSelecting to prevent re-runs
  }, []);

  // Effect to handle viewMode changes
  useEffect(() => {
    // Only run when viewMode changes
    if (viewMode !== prevViewModeRef.current) {
      console.log('ViewMode changed from', prevViewModeRef.current, 'to', viewMode);
      prevViewModeRef.current = viewMode;

      // If changing to preview mode and selection is active, ensure scripts are injected
      if (viewMode === 'preview' && isSelecting && iframeRef.current) {
        // Small delay to ensure the iframe is fully rendered
        const timer = setTimeout(() => {
          // Check if scripts are already injected
          if (!scriptInjectedRef.current && iframeRef.current?.contentDocument) {
            const alreadyInjected = iframeRef.current.contentDocument.querySelector('#element-selector-script');

            // Always inject scripts when view mode changes to preview
            if (!alreadyInjected) {
              console.log('Injecting scripts after viewMode change to preview');
              const success = injectScripts();

              if (success) {
                scriptInjectedRef.current = true;

                // Update selection mode
                if (iframeRef.current.contentWindow) {
                  iframeRef.current.contentWindow.postMessage({
                    type: 'toggleSelectionMode',
                    active: true
                  }, '*');
                }
              }
            } else if (alreadyInjected) {
              scriptInjectedRef.current = true;

              // Update selection mode
              if (iframeRef.current.contentWindow) {
                iframeRef.current.contentWindow.postMessage({
                  type: 'toggleSelectionMode',
                  active: true
                }, '*');
              }
            }
          }
        }, 100);

        return () => clearTimeout(timer);
      }
    }
  }, [viewMode, isSelecting, iframeRef, injectScripts]);

  // Track previous isSelecting value to detect actual changes
  const prevIsSelectingRef = useRef(isSelecting);

  // Helper function to clear selections in the iframe
  const clearSelectionsInIframe = useCallback(() => {
    if (!iframeRef.current?.contentWindow) return;

    console.log('Clearing all selections in iframe via postMessage');
    iframeRef.current.contentWindow.postMessage({
      type: 'clearSelections'
    }, '*');
  }, [iframeRef]);

  // Effect to handle isSelecting changes
  useEffect(() => {
    // Skip if iframe ref is not available
    if (!iframeRef.current?.contentWindow) return;

    // Only proceed if isSelecting actually changed
    if (isSelecting !== prevIsSelectingRef.current) {
      console.log('isSelecting changed from', prevIsSelectingRef.current, 'to', isSelecting);
      prevIsSelectingRef.current = isSelecting;

      // Always inject scripts when selection is activated
      if (isSelecting && !scriptInjectedRef.current) {
        console.log('Injecting scripts for element selection');
        const success = injectScripts();
        if (success) {
          scriptInjectedRef.current = true;
        }
      }

      // Only send messages if scripts have been injected
      if (scriptInjectedRef.current) {
        // Update selection mode in iframe
        iframeRef.current.contentWindow.postMessage({
          type: 'toggleSelectionMode',
          active: isSelecting
        }, '*');
      }

      // Notify parent about selection state change - but only if the callback exists
      // and we're not in the initial mount phase
      if (onSelectionStateChange && initialActiveHandledRef.current) {
        console.log('Notifying parent about selection state change:', isSelecting);
        onSelectionStateChange(isSelecting);
      }

      // If turning off selection mode, notify parent component
      if (!isSelecting && onElementSelect) {
        onElementSelect(EMPTY_ELEMENT);
      }

      // If turning on selection mode, clear any existing selections
      if (isSelecting) {
        console.log('Selection mode activated, clearing any existing selections');
        clearSelectionsInIframe();
      }
    }

    // Cleanup function to ensure selection mode is disabled when component unmounts
    return () => {
      if (iframeRef.current?.contentWindow) {
        // Force selection mode off when unmounting
        iframeRef.current.contentWindow.postMessage({
          type: 'toggleSelectionMode',
          active: false
        }, '*');
      }
    };
  }, [isSelecting, iframeRef, injectScripts, onElementSelect, onSelectionStateChange, clearSelectionsInIframe]);

  // This component doesn't render a visible UI, it just handles the selection logic
  return null;
});
