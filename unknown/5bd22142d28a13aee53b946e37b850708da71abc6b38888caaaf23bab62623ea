<!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>
        Interactive Dashboard Prototype
      </title>
      <style>
        :root {            --primary: #2563eb;            --secondary: #10b981;            --dark: #1e293b;            --light: #f8fafc;            --gray-100: #f1f5f9;            --gray-200: #e2e8f0;            --gray-300: #cbd5e1;            --gray-400: #94a3b8;            --gray-500: #64748b;            --gray-600: #475569;            --shadow-sm: 0 1px 2px 0 rgba(0,0,0,0.05);            --shadow: 0 1px 3px 0 rgba(0,0,0,0.1), 0 1px 2px -1px rgba(0,0,0,0.1);            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -2px rgba(0,0,0,0.1);            --rounded-sm: 0.25rem;            --rounded: 0.5rem;            --rounded-md: 0.75rem;        }        * {            margin: 0;            padding: 0;            box-sizing: border-box;        }        body {            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;            background-color: var(--gray-100);            color: var(--dark);            line-height: 1.5;        }        .container {            max-width: 1200px;            margin: 0 auto;            padding: 1rem;            display: grid;            grid-template-columns: 240px 1fr;            gap: 1.5rem;            min-height: 100vh;        }        /* Sidebar */        .sidebar {            background-color: white;            border-radius: var(--rounded-md);            padding: 1.5rem;            box-shadow: var(--shadow);            height: fit-content;        }        .logo {            font-size: 1.5rem;            font-weight: 700;            color: var(--primary);            margin-bottom: 2rem;            display: flex;            align-items: center;            gap: 0.5rem;        }        .nav {            display: flex;            flex-direction: column;            gap: 0.5rem;        }        .nav-item {            padding: 0.75rem 1rem;            border-radius: var(--rounded-sm);            display: flex;            align-items: center;            gap: 0.75rem;            color: var(--gray-600);            text-decoration: none;            transition: all 0.2s;        }        .nav-item:hover {            background-color: var(--gray-100);            color: var(--primary);        }        .nav-item.active {            background-color: var(--gray-100);            color: var(--primary);            font-weight: 500;        }        .nav-item i {            width: 24px;            height: 24px;            display: flex;            align-items: center;            justify-content: center;        }        /* Main Content */        .main {            display: flex;            flex-direction: column;            gap: 1.5rem;        }        .header {            display: flex;            justify-content: space-between;            align-items: center;            background-color: white;            padding: 1rem 1.5rem;            border-radius: var(--rounded-md);            box-shadow: var(--shadow);        }        .header h1 {            font-size: 1.5rem;            font-weight: 600;        }        .user-menu {            display: flex;            align-items: center;            gap: 1rem;        }        .user-avatar {            width: 40px;            height: 40px;            border-radius: 50%;            background-color: var(--primary);            color: white;            display: flex;            align-items: center;            justify-content: center;            font-weight: 600;            cursor: pointer;            transition: transform 0.2s;        }        .user-avatar:hover {            transform: scale(1.05);        }        /* Stats Grid */        .stats-grid {            display: grid;            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));            gap: 1.5rem;        }        .stat-card {            background-color: white;            border-radius: var(--rounded-md);            padding: 1.5rem;            box-shadow: var(--shadow);            display: flex;            flex-direction: column;            gap: 0.5rem;        }        .stat-card h3 {            font-size: 0.875rem;            color: var(--gray-500);            font-weight: 500;        }        .stat-card .value {            font-size: 2rem;            font-weight: 700;            color: var(--dark);        }        .stat-card .change {            display: flex;            align-items: center;            gap: 0.25rem;            font-size: 0.875rem;        }        .change.positive {            color: var(--secondary);        }        .change.negative {            color: #ef4444;        }        /* Charts Section */        .charts-section {            display: grid;            grid-template-columns: 2fr 1fr;            gap: 1.5rem;        }        .chart-card {            background-color: white;            border-radius: var(--rounded-md);            padding: 1.5rem;            box-shadow: var(--shadow);        }        .chart-card h2 {            font-size: 1.25rem;            font-weight: 600;            margin-bottom: 1rem;        }        .chart-placeholder {            height: 300px;            background-color: var(--gray-100);            border-radius: var(--rounded-sm);            display: flex;            align-items: center;            justify-content: center;            color: var(--gray-400);            font-weight: 500;        }        /* Recent Activity */        .activity-card {            background-color: white;            border-radius: var(--rounded-md);            padding: 1.5rem;            box-shadow: var(--shadow);        }        .activity-card h2 {            font-size: 1.25rem;            font-weight: 600;            margin-bottom: 1rem;        }        .activity-list {            display: flex;            flex-direction: column;            gap: 1rem;        }        .activity-item {            display: flex;            gap: 1rem;            padding: 0.75rem;            border-radius: var(--rounded-sm);            transition: all 0.2s;        }        .activity-item:hover {            background-color: var(--gray-100);        }        .activity-avatar {            width: 40px;            height: 40px;            border-radius: 50%;            background-color: var(--gray-200);            display: flex;            align-items: center;            justify-content: center;            flex-shrink: 0;        }        .activity-content {            flex: 1;        }        .activity-title {            font-weight: 500;            margin-bottom: 0.25rem;        }        .activity-time {            font-size: 0.875rem;            color: var(--gray-500);        }        /* Buttons */        .btn {            padding: 0.5rem 1rem;            border-radius: var(--rounded-sm);            border: none;            font-weight: 500;            cursor: pointer;            transition: all 0.2s;            display: inline-flex;            align-items: center;            gap: 0.5rem;        }        .btn-primary {            background-color: var(--primary);            color: white;        }        .btn-primary:hover {            background-color: #1d4ed8;        }        .btn-secondary {            background-color: var(--gray-200);            color: var(--gray-700);        }        .btn-secondary:hover {            background-color: var(--gray-300);        }        /* Modal Trigger */        .modal-trigger {            position: relative;        }        .modal-trigger::after {            content: "⚡";            position: absolute;            top: -8px;            right: -8px;            font-size: 0.75rem;            color: var(--primary);        }        /* Modal Styles */        .hidden {            display: none;        }        /* Responsive */        @media (max-width: 768px) {            .container {                grid-template-columns: 1fr;            }            .charts-section {                grid-template-columns: 1fr;            }        }
      </style>
    </head>

    <body>

      <div class="container">
        <!-- Sidebar -->
          <aside class="sidebar">
            <div class="logo">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z">
                </path>
                <polyline points="9 22 9 12 15 12 15 22">
                </polyline>
              </svg>
              <span>Acme Inc</span>
            </div>
            <nav class="nav">
              <a href="#" class="nav-item active">
                <i>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z">
                    </path>
                    <polyline points="9 22 9 12 15 12 15 22">
                    </polyline>
                  </svg>
                </i>
                Dashboard
              </a>
              <a href="#" class="nav-item">
                <i>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5z">
                    </path>
                    <path d="M2 17l10 5 10-5">
                    </path>
                    <path d="M2 12l10 5 10-5">
                    </path>
                  </svg>
                </i>
                Projects
              </a>
              <a href="#" class="nav-item">
                <i>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="3">
                    </circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
                    </path>
                  </svg>
                </i>
                Settings
              </a>
              <a href="#" class="nav-item">
                <i>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2">
                    </path>
                    <circle cx="9" cy="7" r="4">
                    </circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87">
                    </path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75">
                    </path>
                  </svg>
                </i>
                Team
              </a>
              <a href="#" class="nav-item">
                <i>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2">
                    </path>
                  </svg>
                </i>
                Reports
              </a>
            </nav>
          </aside>
          <!-- Main Content -->
            <main class="main">
              <!-- Header -->
                <header class="header">
                  <h1>
                    Dashboard Overview
                  </h1>
                  <div class="user-menu">
                    <button class="btn btn-secondary modal-trigger">
                      New Project
                    </button>
                    <div class="user-avatar" onclick="openModal('userProfileModal')">
                      JD
                    </div>
                  </div>
                </header>
                <!-- Stats Grid -->
                  <div class="stats-grid">
                    <div class="stat-card">
                      <h3>
                        Total Revenue
                      </h3>
                      <div class="value">
                        $24,780
                      </div>
                      <div class="change positive">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="22 7 13.5 15.5 8.5 10.5 2 17">
                          </polyline>
                          <polyline points="16 7 22 7 22 13">
                          </polyline>
                        </svg>
                        +12.5% from last month
                      </div>
                    </div>
                    <div class="stat-card">
                      <h3>
                        Active Users
                      </h3>
                      <div class="value">
                        1,429
                      </div>
                      <div class="change positive">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="22 7 13.5 15.5 8.5 10.5 2 17">
                          </polyline>
                          polyline points="16