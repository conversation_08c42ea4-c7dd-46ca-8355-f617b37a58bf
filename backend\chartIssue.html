<









di
vNvgaon

id

n
"
ss
=
"
b-y-900xwhn-4>fxjuf-eit-cnter
"


clas
<
v
"
chjts-between items-center
=
"
fxm-rcx
"
>
class

<
s
s
 spac

"
w-8h8blu4
""

f
=
"
e
"<

sok
=
"
unCrext-blue-400
"
fill
vwBx
=
"
002424
"
s
m
=
"
htp//www.w3.org//svg
"
>

 
<
path
 "
w3/k-ie
=
"
ond
"
 
strokji
=
"
rund
"
"
ok-w
=
"
2
"

d
=
"
M912l224-4m5.68-4.16A11.9551.95 011.944a11.955 11.95  1-8.618.4A12.2 12.2 319c025.591l3.824210.29 9211.622 5.176-1.33249-6.03-9-11.62210-1.042-.133-2.052-.382-3.016zA11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z
"
              color: '#0d9488'
            },
            smooth: true
          }]

      
</
svg
>

      
<
h1
 
class
=
"
text-xl font-bold
"
>
Dashing CRM
</
h1
>

    
</
div
>

    
<
div
 
class
=
"
flex space-x-4
"
>

      
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showPage
(
'home'
)
"
 
class
=
"
hover:text-blue-300
"
>
Dashboard
</
a
>

      
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showPage
(
'pipeline'
)
"
 
class
=
"
hover:text-blue-300
"
>
Pipeline
</
a
>

      
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showPage
(
'insights'
)
"
 
class
=
"
hover:text-blue-300
"
>
Insights
</
a
>

      
<
div
 
class
=
"
unimplemented
"
 
data-feature
=
"
navigation-to-team
"
>

        
<
a
 
href
=
"
javascript:void(0)
"
 
onclick
=
"
showUnimplementedFeature
(
this
)
"
 
class
=
"
hover:text-blue-300
"
>
Team
</
a
>

      
</
div
>

    
</
div
>

    
<
div
 
class
=
"
relative
"
>

      
<
input
 
type
=
"
text
"
 
placeholder
=
"
Quick search...
"
 
class
=
"
bg-gray-800 text-white px-4 py-2 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-blue-500
"
>

    
</
div
>

  
</
nav
>

  
<!-- Home Page -->

  
<
div
 
class
=
"
page
"
 
id
=
"
page-home
"
>

    
<
div
 
class
=
"
p-6 grid grid-cols-1 md:grid-cols-3 gap-6
"
>

      
<!-- Pipeline Overview -->

      
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6 col-span-2
"
>

        
<
div
 
class
=
"
flex justify-between items-center mb-4
"
>

          
<
h2
 
class
=
"
text-xl font-bold text-gray-800
"
>
Pipeline Overview
</
h2
>

          
<
button
 
class
=
"
text-blue-600 hover:text-blue-800
"
>
View All
</
button
>

        
</
div
>

        
<
div
 
class
=
"
flex space-x-4 overflow-x-auto pb-4
"
>

          
<
div
 
class
=
"
min-w-[200px] bg-blue-50 p-4 rounded-lg
"
>

            
<
h3
 
class
=
"
font-medium text-blue-800
"
>
Prospects
</
h3
>

            
<
p
 
class
=
"
text-2xl font-bold mt-2
"
>
24
</
p
>

          
</
div
>

          
<
div
 
class
=
"
min-w-[200px] bg-blue-100 p-4 rounded-lg
"
>

            
<
h3
 
class
=
"
font-medium text-blue-800
"
>
Qualified
</
h3
>

            
<
p
 
class
=
"
text-2xl font-bold mt-2
"
>
18
</
p
>

          
</
div
>

          
<
div
 
class
=
"
min-w-[200px] bg-blue-200 p-4 rounded-lg
"
>

            
<
h3
 
class
=
"
font-medium text-blue-800
"
>
Proposal
</
h3
>

            
<
p
 
class
=
"
text-2xl font-bold mt-2
"
>
12
</
p
>

          
</
div
>

          
<
div
 
class
=
"
min-w-[200px] bg-blue-300 p-4 rounded-lg
"
>

            
<
h3
 
class
=
"
font-medium text-blue-800
"
>
Negotiation
</
h3
>

            
<
p
 
class
=
"
text-2xl font-bold mt-2
"
>
8
</
p
>

          
</
div
>

          
<
div
 
class
=
"
min-w-[200px] bg-blue-400 p-4 rounded-lg text-white
"
>

            
<
h3
 
class
=
"
font-medium
"
>
Closed Won
</
h3
>

            
<
p
 
class
=
"
text-2xl font-bold mt-2
"
>
5
</
p
>

          
</
div
>

        
</
div
>

        
<
div
 
id
=
"
chart1
"
 
class
=
"
h-64 w-full mt-4
"
>
</
div
>

      
</
div
>

      
<!-- Deal Spotlight -->

      
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6
"
>

        
<
h2
 
class
=
"
text-xl font-bold text-gray-800 mb-4
"
>
Deal Spotlight
</
h2
>

        
<
div
 
class
=
"
space-y-4
"
>

          
<
div
 
class
=
"
border-l-4 border-coral-500 pl-4 py-2
"
>

            
<
h3
 
class
=
"
font-bold
"
>
Acme Corp Expansion
</
h3
>

            
<
p
 
class
=
"
text-sm text-gray-600
"
>
$120K • 80% probability
</
p
>

            
<
div
 
class
=
"
flex items-center mt-2 text-sm
"
>

              
<
span
 
class
=
"
bg-blue-100 text-blue-800 px-2 py-1 rounded
"
>
Final Review
</
span
>

              
<
span
 
class
=
"
ml-auto text-gray-500
"
>
2 days left
</
span
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
border-l-4 border-orange-400 pl-4 py-2
"
>

            
<
h3
 
class
=
"
font-bold
"
>
Beta LLC Renewal
</
h3
>

            
<
p
 
class
=
"
text-sm text-gray-600
"
>
$75K • 65% probability
</
p
>

            
<
div
 
class
=
"
flex items-center mt-2 text-sm
"
>

              
<
span
 
class
=
"
bg-blue-100 text-blue-800 px-2 py-1 rounded
"
>
Contract Sent
</
span
>

              
<
span
 
class
=
"
ml-auto text-gray-500
"
>
5 days left
</
span
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
border-l-4 border-yellow-300 pl-4 py-2
"
>

            
<
h3
 
class
=
"
font-bold
"
>
Gamma Inc Pilot
</
h3
>

            
<
p
 
class
=
"
text-sm text-gray-600
"
>
$45K • 50% probability
</
p
>

            
<
div
 
class
=
"
flex items-center mt-2 text-sm
"
>

              
<
span
 
class
=
"
bg-blue-100 text-blue-800 px-2 py-1 rounded
"
>
Discovery
</
span
>

              
<
span
 
class
=
"
ml-auto text-gray-500
"
>
7 days left
</
span
>

            
</
div
>

          
</
div
>

        
</
div
>

        
<
button
 
class
=
"
mt-4 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg
"
>
Add New Deal
</
button
>

      
</
div
>

      
<!-- Team Pulse -->

      
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6 col-span-1
"
>

        
<
h2
 
class
=
"
text-xl font-bold text-gray-800 mb-4
"
>
Team Pulse
</
h2
>

        
<
div
 
class
=
"
space-y-4
"
>

          
<
div
 
class
=
"
flex items-start
"
>

            
<
div
 
class
=
"
bg-blue-100 p-2 rounded-full mr-3
"
>

              
<
svg
 
class
=
"
w-5 h-5 text-blue-600
"
 
fill
=
"
none
"
 
stroke
=
"
currentColor
"
 
viewBox
=
"
0 0 24 24
"
 
xmlns
=
"
http://www.w3.org/2000/svg
"
>

                
<
path
 
stroke-linecap
=
"
round
"
 
stroke-linejoin
=
"
round
"
 
stroke-width
=
"
2
"
 
d
=
"
M5 13l4 4L19 7
"
>
</
path
>

              
</
svg
>

            
</
div
>

            
<
div
>

              
<
p
 
class
=
"
font-medium
"
>
Alex closed Beta LLC
</
p
>

              
<
p
 
class
=
"
text-sm text-gray-600
"
>
$75K • 2 hours ago
</
p
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
flex items-start
"
>

            
<
div
 
class
=
"
bg-green-100 p-2 rounded-full mr-3
"
>

              
<
svg
 
class
=
"
w-5 h-5 text-green-600
"
 
fill
=
"
none
"
 
stroke
=
"
currentColor
"
 
viewBox
=
"
0 0 24 24
"
 
xmlns
=
"
http://www.w3.org/2000/svg
"
>

                
<
path
 
stroke-linecap
=
"
round
"
 
stroke-linejoin
=
"
round
"
 
stroke-width
=
"
2
"
 
d
=
"
M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z
"
>
</
path
>

              
</
svg
>

            
</
div
>

            
<
div
>

              
<
p
 
class
=
"
font-medium
"
>
Jamie scheduled 5 demos
</
p
>

              
<
p
 
class
=
"
text-sm text-gray-600
"
>
Today
</
p
>

            
</
div
>

          
</
div
>

          
<
div
 
class
=
"
flex items-start
"
>

            
<
div
 
class
=
"
bg-red-100 p-2 rounded-full mr-3
"
>

              
<
svg
 
class
=
"
w-5 h-5 text-red-600
"
 
fill
=
"
none
"
 
stroke
=
"
currentColor
"
 
viewBox
=
"
0 0 24 24
"
 
xmlns
=
"
http://www.w3.org/2000/svg
"
>

                
<
path
 
stroke-linecap
=
"
round
"
 
stroke-linejoin
=
"
round
"
 
stroke-width
=
"
2
"
 
d
=
"
M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z
"
>
</
path
>

              
</
svg
>

            
</
div
>

            
<
div
>

              
<
p
 
class
=
"
font-medium
"
>
Taylor needs help with pricing
</
p
>

              
<
p
 
class
=
"
text-sm text-gray-600
"
>
1 hour ago
</
p
>

            
</
div
>

          
</
div
>

        
</
div
>

      
</
div
>

      
<!-- Smart Insights -->

      
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6 col-span-2
"
>

        
<
h2
 
class
=
"
text-xl font-bold text-gray-800 mb-4
"
>
Smart Insights
</
h2
>

        
<
div
 
class
=
"
grid grid-cols-1 md:grid-cols-2 gap-4
"
>

          
<
div
 
class
=
"
bg-blue-50 p-4 rounded-lg
"
>

            
<
h3
 
class
=
"
font-medium text-blue-800
"
>
Win Rate Trend
</
h3
>

            
<
p
 
class
=
"
mt-2
"
>
Your Q2 win rate is 
<
span
 
class
=
"
font-bold text-green-600
"
>
12% higher
</
span
>
 than Q1
</
p
>

            
<
div
 
id
=
"
chart2
"
 
class
=
"
h-48 w-full mt-2
"
>
</
div
>

          
</
div
>

          
<
div
 
class
=
"
bg-blue-50 p-4 rounded-lg
"
>

            
<
h3
 
class
=
"
font-medium text-blue-800
"
>
Deal Velocity
</
h3
>

            
<
p
 
class
=
"
mt-2
"
>
Deals move 
<
span
 
class
=
"
font-bold text-blue-600
"
>
2 days faster
</
span
>
 when you send proposals within 48 hours
</
p
>

            
<
div
 
id
=
"
chart3
"
 
class
=
"
h-48 w-full mt-2
"
>
</
div
>

          
</
div
>

        
</
div
>

      
</
div
>

    
</
div
>

  
</
div
>

  
<!-- Pipeline Page -->

  
<
div
 
class
=
"
page
"
 
id
=
"
page-pipeline
"
 
style
=
"
display
:
none
"
>

    
<
div
 
class
=
"
p-6
"
>

      
<
div
 
class
=
"
flex justify-between items-center mb-6
"
>

        
<
h2
 
class
=
"
text-2xl font-bold text-gray-800
"
>
Pipeline Management
</
h2
>

        
<
button
 
class
=
"
bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg
"
>
New Deal
</
button
>

      
</
div
>

      
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6
"
>

        
<
div
 
class
=
"
overflow-x-auto
"
>

          
<
table
 
class
=
"
min-w-full divide-y divide-gray-200
"
>

            
<
thead
 
class
=
"
bg-gray-50
"
>

              
<
tr
>

                
<
th
 
class
=
"
px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
"
>
Deal
</
th
>

                
<
th
 
class
=
"
px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
"
>
Value
</
th
>

                
<
th
 
class
=
"
px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
"
>
Stage
</
th
>

                
<
th
 
class
=
"
px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
"
>
Owner
</
th
>

                
<
th
 
class
=
"
px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
"
>
Next Step
</
th
>

              
</
tr
>

            
</
thead
>

            
<
tbody
 
class
=
"
bg-white divide-y divide-gray-200
"
>

              
<
tr
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap font-medium
"
>
Acme Corp Expansion
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
$120,000
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>

                  
<
span
 
class
=
"
px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm
"
>
Final Review
</
span
>

                
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
Alex
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
Send contract
</
td
>

              
</
tr
>

              
<
tr
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap font-medium
"
>
Beta LLC Renewal
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
$75,000
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>

                  
<
span
 
class
=
"
px-2 py-1 bg-blue-200 text-blue-800 rounded-full text-sm
"
>
Negotiation
</
span
>

                
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
Jamie
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
Schedule call
</
td
>

              
</
tr
>

              
<
tr
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap font-medium
"
>
Gamma Inc Pilot
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
$45,000
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>

                  
<
span
 
class
=
"
px-2 py-1 bg-blue-300 text-blue-800 rounded-full text-sm
"
>
Discovery
</
span
>

                
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
Taylor
</
td
>

                
<
td
 
class
=
"
px-6 py-4 whitespace-nowrap
"
>
Send proposal
</
td
>

              
</
tr
>

            
</
tbody
>

          
</
table
>

        
</
div
>

      
</
div
>

    
</
div
>

    
<
button
 
class
=
"
fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 z-50
"
 
onclick
=
"
showPage
(
'home'
)
"
 
title
=
"
Back to Home
"
>

      
<
svg
 
class
=
"
w-6 h-6
"
 
fill
=
"
none
"
 
stroke
=
"
currentColor
"
 
viewBox
=
"
0 0 24 24
"
 
xmlns
=
"
http://www.w3.org/2000/svg
"
>

        
<
path
 
stroke-linecap
=
"
round
"
 
stroke-linejoin
=
"
round
"
 
stroke-width
=
"
2
"
 
d
=
"
M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6
"
>
</
path
>

      
</
svg
>

    
</
button
>

  
</
div
>

  
<!-- Insights Page -->

  
<
div
 
class
=
"
page
"
 
id
=
"
page-insights
"
 
style
=
"
display
:
none
"
>

    
<
div
 
class
=
"
p-6
"
>

      
<
h2
 
class
=
"
text-2xl font-bold text-gray-800 mb-6
"
>
Sales Insights
</
h2
>

      
<
div
 
class
=
"
grid grid-cols-1 md:grid-cols-2 gap-6
"
>

        
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6
"
>

          
<
h3
 
class
=
"
text-lg font-bold text-gray-800 mb-4
"
>
Win Rate by Stage
</
h3
>

          
<
div
 
id
=
"
chart4
"
 
class
=
"
h-64 w-full
"
>
</
div
>

        
</
div
>

        
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6
"
>

          
<
h3
 
class
=
"
text-lg font-bold text-gray-800 mb-4
"
>
Deal Velocity
</
h3
>

          
<
div
 
id
=
"
chart5
"
 
class
=
"
h-64 w-full
"
>
</
div
>

        
</
div
>

        
<
div
 
class
=
"
bg-white rounded-xl shadow-md p-6 col-span-2
"
>

          
<
h3
 
class
=
"
text-lg font-bold text-gray-800 mb-4
"
>
Quarterly Performance
</
h3
>

          
<
div
 
id
=
"
chart6
"
 
class
=
"
h-64 w-full
"
>
</
div
>

        
</
div
>

      
</
div
>

    
</
div
>

    
<
button
 
class
=
"
fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 z-50
"
 
onclick
=
"
showPage
(
'home'
)
"
 
title
=
"
Back to Home
"
>

      
<
svg
 
class
=
"
w-6 h-6
"
 
fill
=
"
none
"
 
stroke
=
"
currentColor
"
 
viewBox
=
"
0 0 24 24
"
 
xmlns
=
"
http://www.w3.org/2000/svg
"
>

        
<
path
 
stroke-linecap
=
"
round
"
 
stroke-linejoin
=
"
round
"
 
stroke-width
=
"
2
"
 
d
=
"
M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6
"
>
</
path
>

      
</
svg
>

    
</
button
>

  
</
div
>

  
<
script
 
data-exec
=
"
inline
"
>

    
// SPA Navigation

    
const
 pages 
=
 
document
.
querySelectorAll
(
'.page'
)
;

    
function
 
showPage
(
pageId
)
 
{

      
const
 trigger 
=
 event
?.
target 
||
 
null
;

      
const
 target 
=
 
document
.
getElementById
(
'page-'
 
+
 pageId
)
;

      
if
 
(
target
)
 
{

        pages
.
forEachpages
.
forEach
(
p
 
=>
 p
.
style
.
display
 
=
 
'none'
)
;

        target
.
style
.
display
 
=
 
'block'
;

      
}
 
else
 
if
 
(
trigger
)
 
{

        
try
 
{

          
const
 wrapper 
=
 
document
.
createElement
(
'div'
)
;

          wrapper
.
className
 
=
 
'unimplemented border-2 border-dashed border-red-500 p-2'
;

          wrapper
.
setAttribute
(
'data-feature'
,
 
'navigation-to-'
 
+
 pageId
)
;

          
const
 button 
=
 
document
.
createElement
(
'button'
)
;

          button
.
textContent
 
=
 
'Implement: navigation-to-'
 
+
 pageId
;

          button
.
className
 
=
 
'mt-2 text-red-600 underline block'
;

          trigger
.
parentNode
.
replaceChild
(
wrapper
,
 trigger
)
;

          wrapper
.
appendChild
(
trigger
)
;

          wrapper
.
appendChild
(
button
)
;

          
setTimeout
(
(
)
 
=>
 
{

            wrapper
.
replaceWith
(
trigger
)
;

          
}
,
 
3000
)
;

        
}
 
catch
 
(
e
)
 
{

          
console
.
error
(
'Failed to mark unimplemented navigation:'
,
 e
)
;

        
}

      
}

    
}

    
document
.
addEventListener
(
'DOMContentLoaded'
,
 
(
)
 
=>
 
{

      
showPage
(
'home'
)
;

      
// Initialize Charts

      
try
 
{

        echarts
.
init
(
document
.
getElementById
(
'chart1'
)
)
.
setOption
(
{

          
tooltip
:
 
{
 
trigger
:
 
'axis'
 
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
data
:
 
[
'Prospect'
,
 
'Qualified'
,
 
'Proposal'
,
 
'Negotiation'
,
 
'Closed Won'
]

          
}
,

          
yAxis
:
 
{
 
type
:
 
'value'
 
}
,

          
series
:
 
[
{

            
data
:
 
[
120
,
 
80
,
 
60
,
 
30
,
 
15
]
,

            
type
:
 
'bar'
,

            
itemStyle
:
 
{

              
color
:
 
'#0369a1'

            
}

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart1 failed:'
,
 e
)
;

      
}

      
try
 
{

        echarts
.
init
(
document
.
getElementById
(
'chart2'
)
)
.
setOption
(
{

          
tooltip
:
 
{
 
trigger
:
 
'item'
 
}
,

          
series
:
 
[
{

            
type
:
 
'pie'
,

            
radius
:
 
[
'40%'
,
 
'70%'
]
,

            
data
:
 
[

              
{
 
value
:
 
35
,
 
name
:
 
'New Deals'
 
}
,

              
{
 
value
:
 
25
,
 
name
:
 
'Follow-ups'
 
}
,

              
{
 
value
:
 
20
,
 
name
:
 
'Meetings'
 
}
,

              
{
 
value
:
 
15
,
 
name
:
 
'Proposals'
 
}
,

              
{
 
value
:
 
5
,
 
name
:
 
'Closures'
 
}

            
]
,

            
itemStyle
:
 
{

              
color
:
 
'#4f46e5'

            
}

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart2 failed:'
,
 e
)
;

      
}

      
try
 
{

        echarts
.
init
(
document
.
getElementById
(
'chart3'
)
)
.
setOption
(
{

          
tooltip
:
 
{
 
trigger
:
 
'axis'
 
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
data
:
 
[
'Jan'
,
 
'Feb'
,
 
'Mar'
,
 
'Apr'
,
 
'May'
,
 
'Jun'
]

          
}
,

          
yAxis
:
 
{
 
type
:
 
'value'
 
}
,

          
series
:
 
[
{

            
data
:
 
[
10000
,
 
15000
,
 
20000
,
 
25000
,
 
30000
,
 
35000
]
,

            
type
:
 
'line'
,

            
areaStyle
:
 
{

              
color
:
 
'#0d9488'

            
}
,

            
smooth
:
 
true

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart3 failed:'
,
 e
)
;

      
}

      
try
 
{

        echarts
.
init
(
document
.
getElementById
(
'chart4'
)
)
.
setOption
(
{

          
tooltip
:
 
{
 
trigger
:
 
'axis'
 
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
data
:
 
[
'Prospect'
,
 
'Qualified'
,
 
'Proposal'
,
 
'Closed'
]

          
}
,

          
yAxis
:
 
{
 
type
:
 
'value'
 
}
,

          
series
:
 
[
{

            
data
:
 
[
20
,
 
45
,
 
65
,
 
85
]
,

            
type
:
 
'line'
,

            
areaStyle
:
 
{

              
color
:
 
'#7c3aed'

            
}
,

            
smooth
:
 
true

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart4 failed:'
,
 e
)
;

      
}

      
try
 
{

        echarts
.
init
(
document
.
getElementById
(
'chart5'
)
)
.
setOption
(
{

          
tooltip
:
 
{
 
trigger
:
 
'axis'
 
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
data
:
 
[
'<7 days'
,
 
'7-14 days'
,
 
'14-30 days'
,
 
'30+ days'
]

          
}
,

          
yAxis
:
 
{
 
type
:
 
'value'
 
}
,

          
series
:
 
[
{

            
data
:
 
[
15
,
 
25
,
 
30
,
 
10
]
,

            
type
:
 
'bar'
,

            
itemStyle
:
 
{

              
color
:
 
'#0891b2'

            
}

          
}
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart5 failed:'
,
 e
)
;

      
}

      
try
 
{

        echarts
.
init
(
document
.
getElementById
(
'chart6'
)
)
.
setOption
(
{

          
tooltip
:
 
{
 
trigger
:
 
'axis'
 
}
,

          
xAxis
:
 
{

            
type
:
 
'category'
,

            
data
:
 
[
'Q1'
,
 
'Q2'
,
 
'Q3'
,
 
'Q4'
]

          
}
,

          
yAxis
:
 
{
 
type
:
 
'value'
 
}
,

          
series
:
 
[

            
{

              
name
:
 
'Target'
,

              
type
:
 
'line'
,

              
data
:
 
[
120
,
 
132
,
 
101
,
 
134
]
,

              
itemStyle
:
 
{

                
color
:
 
'#dc2626'

              
}

            
}
,

            
{

              
name
:
 
'Actual'
,

              
type
:
 
'bar'
,

              
data
:
 
[
100
,
 
120
,
 
90
,
 
110
]
,

              
itemStyle
:
 
{

                
color
:
 
'#16a34a'

              
}

            
}

          
]

        
}
)
;

      
}
 
catch
 
(
e
)
 
{

        
console
.
error
(
'Chart6 failed:'
,
 e
)
;

      
}

    
}
)
;

  
</
script
>

</
div
>
JustPrototype
Contact
Discord
Privacy Policy
© 2025 JustPrototype. All rights reserved.