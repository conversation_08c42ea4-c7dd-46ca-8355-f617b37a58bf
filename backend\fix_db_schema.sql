-- Comprehensive SQL file to fix database schema issues
-- This file ensures all required tables and functions exist and have the correct structure

-- First, make sure the users table exists with required columns
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    google_id VARCHAR(64),
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name <PERSON><PERSON><PERSON><PERSON>(255),
    photo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_admin BOOLEAN DEFAULT FALSE,
    token_usage INTEGER DEFAULT 0 CHECK (token_usage >= 0),
    prototype_count INTEGER DEFAULT 3 CHECK (prototype_count >= 0),
    plan VARCHAR(32) DEFAULT 'free',
    quota_tokens INTEGER DEFAULT 10000 CHECK (quota_tokens >= 0),
    quota_prototypes INTEGER DEFAULT 3 CHECK (quota_prototypes >= 0),
    password_hash VARCHAR(255),
    facebook_id VARCHAR(64),
    twitter_id VARCHAR(64),
    github_id VARCHAR(64),
    auth_provider VARCHAR(32),
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(128),
    reset_token VARCHAR(128),
    reset_token_expires TIMESTAMP WITH TIME ZONE
);

-- Make sure the prompts table exists with required columns
CREATE TABLE IF NOT EXISTS prompts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    prompt_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Make sure the prompt_iterations table exists with required columns
CREATE TABLE IF NOT EXISTS prompt_iterations (
    id SERIAL PRIMARY KEY,
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    iteration_number INTEGER NOT NULL,
    input_text TEXT NOT NULL,
    output_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Make sure the prototypes table exists with required columns
CREATE TABLE IF NOT EXISTS prototypes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    html TEXT NOT NULL,
    css TEXT,
    preview_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Make sure the usage_log table exists with required columns
CREATE TABLE IF NOT EXISTS usage_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    event VARCHAR(64),
    tokens_used INTEGER CHECK (tokens_used >= 0),
    context TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_prompt_iterations_prompt_id ON prompt_iterations(prompt_id);
CREATE INDEX IF NOT EXISTS idx_prototypes_user_created_at ON prototypes(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_prototypes_prompt_id ON prototypes(prompt_id);
CREATE INDEX IF NOT EXISTS idx_usage_log_user_id ON usage_log(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_log_event ON usage_log(event);

-- Create or replace the use_tokens_and_log function
CREATE OR REPLACE FUNCTION use_tokens_and_log(
    p_user_id INTEGER,
    p_tokens_used INTEGER,
    p_event VARCHAR,
    p_context TEXT DEFAULT NULL,
    p_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    -- Skip token quota check for now, just log the usage
    INSERT INTO usage_log (user_id, event, tokens_used, context, details)
    VALUES (p_user_id, p_event, p_tokens_used, p_context, p_details);

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the increment_prototype_count function
CREATE OR REPLACE FUNCTION increment_prototype_count(p_user_id INTEGER) RETURNS BOOLEAN AS $$
DECLARE
    user_plan VARCHAR;
    current_count INTEGER;
    quota INTEGER;
BEGIN
    -- Get the user's current plan, prototype count, and quota
    SELECT plan, prototype_count, quota_prototypes INTO user_plan, current_count, quota
    FROM users WHERE id = p_user_id FOR UPDATE;

    -- Set default quota if null
    IF quota IS NULL THEN
        quota := CASE WHEN user_plan = 'free' THEN 3 ELSE 50 END;
    END IF;

    -- Check if the user has reached their quota
    IF current_count >= quota THEN
        RETURN FALSE; -- Quota exceeded
    END IF;

    -- Increment the prototype count
    UPDATE users
    SET prototype_count = prototype_count + 1
    WHERE id = p_user_id;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the get_user_quota function
CREATE OR REPLACE FUNCTION get_user_quota(p_user_id INTEGER)
RETURNS TABLE (
    plan VARCHAR,
    prototype_count INTEGER,
    quota_prototypes INTEGER,
    remaining_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.plan,
        COALESCE(u.prototype_count, 0) as prototype_count,
        COALESCE(u.quota_prototypes, CASE WHEN u.plan = 'free' THEN 3 ELSE 50 END) as quota_prototypes,
        GREATEST(0, COALESCE(u.quota_prototypes, CASE WHEN u.plan = 'free' THEN 3 ELSE 50 END) - COALESCE(u.prototype_count, 0)) as remaining_count
    FROM users u
    WHERE u.id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Create a test user if none exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM users LIMIT 1) THEN
        INSERT INTO users (email, display_name, google_id)
        VALUES ('<EMAIL>', 'Test User', 'test123');
    END IF;
END $$;
