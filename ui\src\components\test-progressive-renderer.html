<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progressive Renderer Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Seamless progressive rendering styles */
        .progressive-renderer {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .progressive-element {
            opacity: 0;
            transform: translateY(4px);
            transition: opacity 0.2s ease-out, transform 0.2s ease-out;
        }
        
        .progressive-element.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Ensure all Tailwind classes work immediately */
        .progressive-renderer * {
            box-sizing: border-box;
        }
        
        /* Force style recalculation for dynamic content */
        .progressive-renderer .dynamic-content {
            animation: forceStyleRecalc 0.1s ease-out;
        }
        
        @keyframes forceStyleRecalc {
            0% { opacity: 0.99; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Progressive Renderer Test</h1>
        
        <div class="grid grid-cols-2 gap-6">
            <!-- Test Container -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Immediate Rendering Test</h2>
                <div id="test-container" class="progressive-renderer border-2 border-gray-200 rounded p-4 min-h-[300px]">
                    <!-- Elements will be rendered here -->
                </div>
                <button id="start-test" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Start Progressive Test
                </button>
                <button id="clear-test" class="mt-4 ml-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                    Clear
                </button>
            </div>
            
            <!-- Log Container -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Rendering Log</h2>
                <div id="log-container" class="bg-gray-50 rounded p-4 h-[300px] overflow-y-auto text-sm font-mono">
                    <!-- Logs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test HTML elements to render progressively
        const testElements = [
            '<div class="bg-blue-500 text-white p-4 rounded mb-2">Complete Element 1</div>',
            '<p class="text-gray-700 mb-2">This is a complete paragraph element.</p>',
            '<button class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Complete Button</button>',
            '<div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-2">Alert Box</div>',
            '<img src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'100\' height=\'50\' viewBox=\'0 0 100 50\'%3E%3Crect width=\'100\' height=\'50\' fill=\'%23e5e7eb\'/%3E%3Ctext x=\'50\' y=\'25\' text-anchor=\'middle\' dy=\'.3em\' fill=\'%236b7280\'%3EImage%3C/text%3E%3C/svg%3E" class="rounded mb-2" alt="Test Image" />',
            '<div class="flex space-x-2 mb-2"><span class="bg-purple-500 text-white px-2 py-1 rounded text-sm">Tag 1</span><span class="bg-purple-500 text-white px-2 py-1 rounded text-sm">Tag 2</span></div>'
        ];

        let processedElements = new Set();
        
        function log(message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function generateElementHash(element) {
            const content = element.outerHTML.substring(0, 100);
            const tag = element.tagName.toLowerCase();
            const textContent = element.textContent?.trim().substring(0, 50) || '';
            return `${tag}-${content.length}-${textContent.length}-${content.charCodeAt(0) || 0}`;
        }

        function isValidCompleteElement(element) {
            if (!element || !element.tagName) return false;
            const hasContent = !!(element.textContent?.trim() || element.children.length > 0);
            return hasContent;
        }

        function extractCompleteElementsImmediately(content) {
            if (!content.trim()) return [];

            const completeElements = [];
            
            try {
                // Strategy 1: Find complete self-closing tags
                const selfClosingPattern = /<(\w+)([^>]*?)\/>/g;
                let match;
                
                while ((match = selfClosingPattern.exec(content)) !== null) {
                    try {
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = match[0];
                        const element = tempDiv.firstElementChild;
                        
                        if (element && isValidCompleteElement(element)) {
                            completeElements.push(element);
                        }
                    } catch (e) {
                        // Skip invalid elements
                    }
                }
                
                // Strategy 2: Find complete paired tags
                const pairedTagPattern = /<(\w+)([^>]*?)>(.*?)<\/\1>/gs;
                pairedTagPattern.lastIndex = 0; // Reset regex
                
                while ((match = pairedTagPattern.exec(content)) !== null) {
                    try {
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = match[0];
                        const element = tempDiv.firstElementChild;
                        
                        if (element && isValidCompleteElement(element)) {
                            completeElements.push(element);
                        }
                    } catch (e) {
                        // Skip invalid elements
                    }
                }
                
                log(`Extracted ${completeElements.length} complete elements from ${content.length} chars`);
                
            } catch (error) {
                log(`Error extracting elements: ${error.message}`);
            }
            
            return completeElements;
        }

        async function renderElementSeamlessly(element, container) {
            return new Promise((resolve) => {
                const clonedElement = element.cloneNode(true);
                
                // Add progressive rendering classes
                clonedElement.classList.add('progressive-element', 'dynamic-content');
                
                // Force style application
                clonedElement.style.display = clonedElement.style.display || '';
                
                // Append element (initially invisible)
                container.appendChild(clonedElement);
                
                // Force reflow
                clonedElement.offsetHeight;
                
                // Trigger smooth fade-in animation
                requestAnimationFrame(() => {
                    clonedElement.classList.add('visible');
                    setTimeout(resolve, 200);
                });
            });
        }

        async function processNewContent(content) {
            const container = document.getElementById('test-container');
            
            log(`Processing new content: ${content.length} chars`);
            
            // IMMEDIATE PROCESSING: Extract and render complete elements right away
            const completeElements = extractCompleteElementsImmediately(content);
            
            if (completeElements.length > 0) {
                log(`Found ${completeElements.length} complete elements - rendering immediately`);
                
                // Render each complete element immediately without delays
                for (const element of completeElements) {
                    const elementHash = generateElementHash(element);
                    
                    // Skip if already processed (avoid duplicates)
                    if (processedElements.has(elementHash)) {
                        continue;
                    }
                    
                    // Mark as processed
                    processedElements.add(elementHash);
                    
                    // Render immediately
                    await renderElementSeamlessly(element, container);
                    log(`Rendered: ${element.tagName} - ${element.textContent?.substring(0, 30) || 'No text'}...`);
                }
                
                log(`Immediately rendered ${completeElements.length} complete elements`);
            } else {
                log('No complete elements found');
            }
        }

        // Test progressive rendering
        async function startProgressiveTest() {
            const container = document.getElementById('test-container');
            container.innerHTML = '';
            processedElements.clear();
            
            log('Starting progressive rendering test...');
            
            // Simulate streaming by adding elements one by one
            for (let i = 0; i < testElements.length; i++) {
                const element = testElements[i];
                log(`Streaming element ${i + 1}: ${element.substring(0, 50)}...`);
                
                // Process immediately when complete element is available
                await processNewContent(element);
                
                // Small delay to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('Progressive rendering test completed!');
        }

        function clearTest() {
            const container = document.getElementById('test-container');
            const logContainer = document.getElementById('log-container');
            container.innerHTML = '';
            logContainer.innerHTML = '';
            processedElements.clear();
            log('Test cleared');
        }

        // Event listeners
        document.getElementById('start-test').addEventListener('click', startProgressiveTest);
        document.getElementById('clear-test').addEventListener('click', clearTest);
        
        log('Progressive Renderer Test Ready');
    </script>
</body>
</html>
