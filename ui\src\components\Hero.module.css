.hero {
  padding: 3rem 1rem; /* Further reduced from 4rem to 3rem */
  background-color: #f9fafb;
  background-image: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 50vh; /* Further reduced from 60vh to 50vh */
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 0.75rem; /* Further reduced from 1rem to 0.75rem */
  color: #4f46e5;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.5rem;
  color: #6b7280;
  margin-bottom: 1.5rem; /* Further reduced from 2rem to 1.5rem */
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Dark mode styles */
:global(.dark) .hero {
  background-color: #111827;
  background-image: linear-gradient(135deg, #111827 0%, #1f2937 100%);
}

:global(.dark) .title {
  color: #6366f1;
}

:global(.dark) .subtitle {
  color: #d1d5db;
}

/* Responsive styles */
@media (max-width: 768px) {
  .hero {
    padding: 2.5rem 1rem; /* Further reduced from 3rem to 2.5rem */
    min-height: 40vh; /* Further reduced from 50vh to 40vh */
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 1.5rem 1rem; /* Further reduced from 2rem to 1.5rem */
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
  
  .actions {
    flex-direction: column;
    gap: 0.75rem;
  }
}
