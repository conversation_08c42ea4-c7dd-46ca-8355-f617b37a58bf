const { ChatOpenAI } = require('@langchain/openai');
const { ChatAnthropic } = require('@langchain/anthropic');

class LLMFactory {
  constructor() {
    this.supportedProviders = ['openai', 'anthropic'];
  }

  validateProvider(provider) {
    if (!provider) {
      return process.env.DEFAULT_LLM_PROVIDER || 'openai';
    }

    provider = provider.toLowerCase();
    if (!this.supportedProviders.includes(provider)) {
      throw new Error(`Unsupported LLM provider: ${provider}`);
    }

    return provider;
  }

  supportsStreaming(provider) {
    return true; // All current providers support streaming
  }

  createLLM(provider, callbacks = undefined) {
    provider = this.validateProvider(provider);

    const baseConfig = {
      maxConcurrency: 5,
      verbose: true,
      streaming: true
    };

    // Only add callbacks if they exist
    if (callbacks) {
      baseConfig.callbacks = [
        {
          handleLLMNewToken: async (token) => {
            try {
              await callbacks.handleLLMNewToken?.(token);
            } catch (error) {
              console.error('Token callback error:', error);
            }
          },
          handleLLMEnd: async () => {
            try {
              await callbacks.handleLLMEnd?.();
            } catch (error) {
              console.error('End callback error:', error);
            }
          },
          handleLLMError: async (error) => {
            try {
              await callbacks.handleLLMError?.(error);
            } catch (err) {
              console.error('Error callback error:', err);
            }
          }
        }
      ];
    }

    switch (provider) {
      case 'openai':
        return new ChatOpenAI({
          ...baseConfig,
          modelName: process.env.OPENAI_MODEL_NAME || 'gpt-4',
          openAIApiKey: process.env.OPENAI_API_KEY,
          temperature: 0.7,
          maxTokens: 4000,
          streaming: true
        });

      case 'anthropic':
        return new ChatAnthropic({
          ...baseConfig,
          modelName: process.env.ANTHROPIC_MODEL_NAME || 'claude-2.1',
          anthropicApiKey: process.env.ANTHROPIC_API_KEY,
          temperature: 0.7,
          maxTokens: 4000,
          streaming: true
        });

      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Create callback handlers with custom token processor
   */
  createCallbackHandlers(onToken, onComplete, onError) {
    return {
      handleLLMNewToken: async (token) => {
        if (onToken) {
          try {
            await onToken(token);
          } catch (error) {
            console.error('Token handler error:', error);
          }
        }
      },
      handleLLMEnd: async () => {
        if (onComplete) {
          try {
            await onComplete();
          } catch (error) {
            console.error('Complete handler error:', error);
          }
        }
      },
      handleLLMError: async (error) => {
        if (onError) {
          try {
            await onError(error);
          } catch (err) {
            console.error('Error handler error:', err);
          }
        }
      }
    };
  }

  getSupportedProviders() {
    return [...this.supportedProviders];
  }
}

module.exports = new LLMFactory();
