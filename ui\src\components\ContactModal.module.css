.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.modalTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}

.modalText {
  font-size: 1rem;
  color: #4b5563;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.emailContainer {
  display: flex;
  align-items: center;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
}

.email {
  font-size: 1rem;
  color: #111827;
  font-weight: 500;
  flex: 1;
}

.copyButton {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
}

.copyButton:hover {
  background-color: #e5e7eb;
  color: #111827;
}

.feedbackContainer {
  position: relative;
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

.feedbackButton {
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.feedbackButton:hover {
  background-color: #e5e7eb;
}

.feedbackDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  width: 300px;
  z-index: 100;
  overflow: hidden;
}

.dropdownItem {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.dropdownItem:last-child {
  border-bottom: none;
}

.dropdownLabel {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.dropdownContent {
  display: flex;
  align-items: center;
}

.discordContainer {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
}

.discordLink {
  display: flex;
  align-items: center;
  color: #5865F2;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.discordLink:hover {
  color: #4752c4;
  text-decoration: underline;
}

.discordIcon {
  margin-right: 0.5rem;
}
