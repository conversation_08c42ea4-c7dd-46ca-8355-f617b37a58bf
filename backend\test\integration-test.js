/**
 * Integration test for the edit strategy
 * Tests the complete flow with real scenarios
 */

// Mock response for testing
class MockResponse {
  constructor() {
    this.events = [];
    this.ended = false;
  }
  
  write(data) {
    this.events.push(data);
    console.log('📡 SSE:', data.trim());
  }
  
  end() {
    this.ended = true;
    console.log('🏁 Response ended');
  }
}

// Test scenarios based on user requirements
const testScenarios = [
  {
    name: "Change Color",
    prompt: "change the header background color to red",
    html: `<div id="app"><header class="bg-blue-600"><h1>Dashboard</h1></header></div>`,
    expectFragmentEdit: true
  },
  {
    name: "Add Menu Item", 
    prompt: "add a new menu item called 'Services'",
    html: `<div id="app"><nav class="nav"><ul><li><a href="#home">Home</a></li></ul></nav></div>`,
    expectFragmentEdit: true
  },
  {
    name: "Add Chart",
    prompt: "add a bar chart showing monthly sales", 
    html: `<div id="app"><div id="chart-container">Chart placeholder</div></div>`,
    expectFragmentEdit: true
  },
  {
    name: "Change Title",
    prompt: "change the title to 'Admin Panel'",
    html: `<div id="app"><h1>Dashboard</h1></div>`,
    expectFragmentEdit: true
  },
  {
    name: "Global Redesign",
    prompt: "redesign the entire layout with a modern look",
    html: `<div id="app"><header><h1>Old Layout</h1></header></div>`,
    expectFragmentEdit: false
  }
];

async function runIntegrationTest() {
  console.log('🧪 Running Integration Tests for Edit Strategy\n');
  
  try {
    const service = require('../services/llmServiceV3');
    console.log('✅ LLMServiceV3 loaded successfully\n');
    
    let passedTests = 0;
    
    for (const scenario of testScenarios) {
      console.log(`🔬 Testing: ${scenario.name}`);
      console.log(`📝 Prompt: "${scenario.prompt}"`);
      console.log(`📄 HTML length: ${scenario.html.length} chars`);
      
      try {
        // Test prompt analysis
        const analysis = await service.analyzePromptIntent(scenario.prompt, scenario.html);
        
        const isFragmentStrategy = analysis.isTargeted && !!analysis.elementSelector;
        const strategyMatch = isFragmentStrategy === scenario.expectFragmentEdit;

        console.log(`🎯 Expected: ${scenario.expectFragmentEdit ? 'FRAGMENT' : 'FULL'}`);
        console.log(`🎯 Actual: ${isFragmentStrategy ? 'FRAGMENT' : 'FULL'}`);
        console.log(`🔍 Debug: isFragmentStrategy=${isFragmentStrategy}, expectFragmentEdit=${scenario.expectFragmentEdit}, match=${strategyMatch}`);
        console.log(`✅ Strategy Match: ${strategyMatch ? 'PASS' : 'FAIL'}`);

        console.log(`📊 Analysis Details:`, {
          isTargeted: analysis.isTargeted,
          hasSelector: !!analysis.elementSelector,
          changeType: analysis.changeType,
          confidence: analysis.confidence
        });
        
        if (isFragmentStrategy) {
          console.log(`🔧 Selector: ${analysis.elementSelector}`);
          
          // Test fragment extraction
          const fragment = service.extractFragment(scenario.html, analysis.elementSelector);
          console.log(`📦 Fragment: ${fragment ? 'EXTRACTED' : 'FAILED'}`);
          
          if (fragment) {
            console.log(`📏 Fragment length: ${fragment.length} chars`);
          }
        }
        
        if (strategyMatch) {
          passedTests++;
          console.log('🎉 Test PASSED');
        } else {
          console.log('❌ Test FAILED');
        }
        
      } catch (error) {
        console.error('❌ Test Error:', error.message);
      }
      
      console.log('─'.repeat(60));
    }
    
    console.log(`\n📊 Results: ${passedTests}/${testScenarios.length} tests passed`);
    console.log(`📈 Success Rate: ${Math.round((passedTests/testScenarios.length) * 100)}%`);
    
    if (passedTests === testScenarios.length) {
      console.log('🎉 All tests passed! Edit strategy is working correctly.');
      console.log('✅ The system will avoid full HTML regeneration for targeted changes.');
    } else {
      console.log('⚠️ Some tests failed. The edit strategy needs refinement.');
    }
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
  }
}

// Run the test
runIntegrationTest();
