.planSummary {
  background: #f3f4f6;
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;
  margin-bottom: 1.25rem;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.04);
  font-size: 1rem;
  animation: fadeIn 0.4s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px);}
  to { opacity: 1; transform: translateY(0);}
}

.title {
  font-weight: 600;
  color: #6366f1;
  margin-bottom: 0.5rem;
}

.featureList {
  margin: 0;
  padding: 0 0 0 0.5rem;
}

.featureItem {
  color: #111827;
  margin-bottom: 0.25rem;
  list-style: none;
}

.readMore {
  background: none;
  border: none;
  color: #4f46e5;
  font-weight: 600;
  cursor: pointer;
  font-size: 0.98rem;
  margin-top: 0.25rem;
  padding: 0;
  transition: color 0.2s;
}

.readMore:hover,
.readMore:focus {
  color: #3730a3;
}

/* Dark mode styles */
:global(.dark) .planSummary {
  background: #374151;
  color: #f9fafb;
}

:global(.dark) .title {
  color: #a5b4fc;
}

:global(.dark) .featureItem {
  color: #f9fafb;
}

:global(.dark) .readMore {
  color: #a5b4fc;
}

:global(.dark) .readMore:hover,
:global(.dark) .readMore:focus {
  color: #fff;
}
