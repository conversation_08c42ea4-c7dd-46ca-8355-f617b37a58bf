const llmService = require('../services/llmService');
const { incrementPrototypeCount } = require('../services/billingService');
const prototypeService = require('../services/prototypeService');

/**
 * POST /api/prototype/plan
 * Request body: { prompt: string }
 * Response: { features: string[] }
 */
async function generatePlan(req, res) {
  try {
    const { prompt } = req.body;
    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({ error: 'Prompt is required.' });
    }

    // Call LLM service to get plan/features (implement this in llmService)
    const features = await llmService.generateFeaturePlan(prompt);

    res.json({ features });
  } catch (err) {
    console.error('Error generating plan:', err);
    res.status(500).json({ error: 'Failed to generate plan.' });
  }
}

/**
 * POST /api/prototype/create
 * Request body: { title, description, html, css?, preview_image_url?, prompt_id? }
 * Response: { success, prototype, quotaExceeded, remainingCount, plan, reason? }
 */
async function createPrototype(req, res) {
  try {
    const {
      title, description, html, css, preview_image_url, prompt_id
    } = req.body;

    if (!title || typeof title !== 'string' || !html || typeof html !== 'string') {
      return res.status(400).json({ error: 'Prototype title and HTML are required.' });
    }

    // Trim title to 250 characters if it exceeds the limit
    const trimmedTitle = title.length > 250 ? title.substring(0, 250) : title;

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required to create a prototype.' });
    }
    // Get DB user id
    const userId = req.user.dbId || req.user.id;

    // Quota enforcement
    const userQuota = await getUserQuota(userId);
    if (userQuota.remainingCount <= 0) {
      return res.json({
        success: false,
        prototype: null,
        quotaExceeded: true,
        remainingCount: 0,
        plan: userQuota.plan,
        reason: 'QUOTA_CHECK'
      });
    }
    // Increment prototype count (enforced at the DB level)
    const incremented = await incrementPrototypeCount(userId, req);
    if (!incremented) {
      return res.json({
        success: false,
        prototype: null,
        quotaExceeded: true,
        remainingCount: 0,
        plan: userQuota.plan,
        reason: 'QUOTA_EXCEEDED'
      });
    }

    // Persist new prototype
    const prototype = await prototypeService.createPrototype({
      user_id: userId,
      title: trimmedTitle,
      description,
      html,
      css,
      preview_image_url,
      prompt_id
    });

    res.json({
      success: true,
      prototype,
      quotaExceeded: false,
      remainingCount: userQuota.remainingCount - 1,
      plan: userQuota.plan,
      titleTrimmed: title.length > 250 ? title.length - 250 : 0
    });
  } catch (err) {
    console.error('Error creating prototype:', err);
    res.status(500).json({ error: 'Failed to create prototype.' });
  }
}

/**
 * GET /api/prototype/quota
 * Response: { plan: string, totalCount: number, usedCount: number, remainingCount: number }
 *
 * Gets the user's prototype quota information
 */
async function getPrototypeQuota(req, res) {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required to get quota information.' });
    }

    // Get the user's quota information
    const quota = await getUserQuota(req.user.dbId || req.user.id);

    res.json(quota);
  } catch (err) {
    console.error('Error getting prototype quota:', err);
    res.status(500).json({ error: 'Failed to get prototype quota.' });
  }
}

/**
 * Helper function to get a user's quota information
 * @param {string|number} userId - The user's ID
 * @returns {Promise<Object>} - The user's quota information
 */
async function getUserQuota(userId) {
  try {
    // Get the user's database ID
    const { pool } = require('../services/promptDbService');

    // Use the get_user_quota function to get accurate quota information
    const result = await pool.query(
      `SELECT * FROM get_user_quota($1)`,
      [userId]
    );

    if (result.rows.length === 0) {
      return {
        plan: 'free',
        totalCount: 3,
        usedCount: 0,
        remainingCount: 3
      };
    }

    const quota = result.rows[0];

    return {
      plan: quota.plan || 'free',
      totalCount: quota.quota_prototypes,
      usedCount: quota.prototype_count,
      remainingCount: quota.remaining_count
    };
  } catch (error) {
    console.error('Error getting user quota:', error);

    try {
      // Fallback to direct query if the function doesn't exist yet
      const { pool } = require('../services/promptDbService');

      const result = await pool.query(
        `SELECT
          plan,
          prototype_count,
          quota_prototypes
        FROM users
        WHERE id = $1`,
        [userId]
      );

      if (result.rows.length === 0) {
        return {
          plan: 'free',
          totalCount: 3,
          usedCount: 0,
          remainingCount: 3
        };
      }

      const user = result.rows[0];
      const totalCount = user.quota_prototypes || (user.plan === 'free' ? 3 : 50);
      const usedCount = user.prototype_count || 0;
      const remainingCount = Math.max(0, totalCount - usedCount);

      return {
        plan: user.plan || 'free',
        totalCount,
        usedCount,
        remainingCount
      };
    } catch (fallbackError) {
      console.error('Error in fallback quota query:', fallbackError);
      // Default to free plan with 3 prototypes if there's an error
      return {
        plan: 'free',
        totalCount: 3,
        usedCount: 0,
        remainingCount: 3
      };
    }
  }
}

/**
 * GET /api/prototype
 * Returns all prototypes for the current user.
 */
async function listPrototypes(req, res) {
  try {
    // Set CORS headers directly to ensure cross-origin requests work
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept, Cache-Control');
    res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

    console.log('[Prototype Debug] List prototypes request from:', req.headers.origin);

    if (!req.user || !req.user.id) {
      // Set CORS headers even for error responses
      return res.status(401).json({ error: 'Authentication required.' });
    }

    const userId = req.user.dbId || req.user.id;
    const prototypes = await prototypeService.getPrototypesByUser(userId);

    res.json({ prototypes });
  } catch (err) {
    console.error('Error listing prototypes:', err);

    // Set CORS headers even for error responses
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');

    res.status(500).json({ error: 'Failed to list prototypes.' });
  }
}

/**
 * GET /api/prototype/:id
 * Returns a single prototype (owned by this user).
 */
async function getPrototype(req, res) {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required.' });
    }
    const userId = req.user.dbId || req.user.id;
    const protoId = parseInt(req.params.id, 10);
    if (isNaN(protoId)) {
      return res.status(400).json({ error: 'Invalid prototype id.' });
    }
    const prototype = await prototypeService.getPrototypeById(protoId, userId);
    if (!prototype) {
      return res.status(404).json({ error: 'Prototype not found.' });
    }
    res.json({ prototype });
  } catch (err) {
    console.error('Error fetching prototype:', err);
    res.status(500).json({ error: 'Failed to fetch prototype.' });
  }
}

/**
 * PUT /api/prototype/:id
 * Edits an existing prototype for the user (partial update, only allowed fields).
 */
async function updatePrototype(req, res) {
  try {
    // Set CORS headers directly to ensure cross-origin requests work
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept, Cache-Control');
    res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

    console.log('[Prototype Debug] Update prototype request from:', req.headers.origin);
    
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required.' });
    }
    const userId = req.user.dbId || req.user.id;
    const protoId = parseInt(req.params.id, 10);
    if (isNaN(protoId)) {
      return res.status(400).json({ error: 'Invalid prototype id.' });
    }
    const allowed = ['title', 'description', 'html', 'css', 'preview_image_url'];
    const updates = {};
    for (const key of allowed) {
      if (key in req.body) updates[key] = req.body[key];
    }
    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ error: 'No valid fields to update.' });
    }
    // Handle title trimming in updates
    if (updates.title && updates.title.length > 250) {
      updates.title = updates.title.substring(0, 250);
    }
    
    const prototype = await prototypeService.updatePrototype(protoId, userId, updates);
    if (!prototype) {
      return res.status(404).json({ error: 'Prototype not found or not owned by user.' });
    }
    res.json({ 
      prototype,
      titleTrimmed: updates.title && updates.title.length > 250 ? updates.title.length - 250 : 0 
    });
  } catch (err) {
    console.error('Error updating prototype:', err);
    
    // Set CORS headers even for error responses
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');
    
    res.status(500).json({ error: 'Failed to update prototype.' });
  }
}

/**
 * DELETE /api/prototype/:id
 * Deletes a single prototype owned by the user.
 */
async function deletePrototype(req, res) {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required.' });
    }
    const userId = req.user.dbId || req.user.id;
    const protoId = parseInt(req.params.id, 10);
    if (isNaN(protoId)) {
      return res.status(400).json({ error: 'Invalid prototype id.' });
    }
    const deleted = await prototypeService.deletePrototype(protoId, userId);
    if (!deleted) {
      return res.status(404).json({ error: 'Prototype not found or not owned by user.' });
    }
    res.json({ success: true });
  } catch (err) {
    console.error('Error deleting prototype:', err);
    res.status(500).json({ error: 'Failed to delete prototype.' });
  }
}

module.exports = {
  generatePlan,
  createPrototype,
  listPrototypes,
  getPrototype,
  updatePrototype,
  deletePrototype,
  getPrototypeQuota
};
