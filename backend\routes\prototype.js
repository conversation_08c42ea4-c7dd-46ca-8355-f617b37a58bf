const express = require('express');
const router = express.Router();
const prototypeController = require('../controllers/prototypeController');
const { ensureAuthenticated } = require('../auth/googleAuth');

// Debug middleware for prototype routes
router.use((req, _res, next) => {
  //console.log('[Prototype Debug] Request received:', req.method, req.path);
  console.log('[Prototype Debug] Origin:', req.headers.origin);
  //console.log('[Prototype Debug] Cookies:', req.headers.cookie);
  next();
});

// Handle OPTIONS requests for routes with IDs explicitly
router.options('/:id', (req, res) => {
  console.log('[Prototype Debug] OPTIONS request for ID path:', req.params.id);
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept, Cache-Control');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');
  res.status(204).end();
});

// Generate prototype plan from prompt (can be public)
router.post('/plan', prototypeController.generatePlan);

// List all prototypes for the user
router.get('/', ensureAuthenticated, prototypeController.listPrototypes);

// Create a new prototype
router.post('/create', ensureAuthenticated, prototypeController.createPrototype);

// Get user's prototype quota information
router.get('/quota', ensureAuthenticated, prototypeController.getPrototypeQuota);

// Get a single prototype by id
router.get('/:id', ensureAuthenticated, prototypeController.getPrototype);

// Update a prototype (partial update) - using PUT (might have CORS issues)
router.put('/:id', ensureAuthenticated, prototypeController.updatePrototype);

// Update a prototype (partial update) - using POST to avoid CORS issues
router.post('/update/:id', ensureAuthenticated, prototypeController.updatePrototype);

// Delete a prototype
router.delete('/:id', ensureAuthenticated, prototypeController.deletePrototype);

// Session-based editing (Readdy.ai Phase 2)
router.post('/edit-session', async (req, res) => {
  const llmServiceV3 = require('../services/llmServiceV3');

  try {
    const { sessionId, intentId, userQuery } = req.body;

    // Validate required fields
    if (!sessionId || !userQuery) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'Missing required fields: sessionId, userQuery'
        }
      });
    }

    // Set up SSE headers for streaming
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': req.headers.origin || '*',
      'Access-Control-Allow-Credentials': 'true'
    });

    // Create intent data object (if intentId provided, could fetch from database)
    const intentData = {
      userIntent: userQuery,
      suggestion: userQuery,
      implementationType: 'inline'
    };

    // Use session-based editing
    await llmServiceV3.editHTMLFromSession(
      sessionId,
      userQuery,
      intentData,
      res
    );

  } catch (error) {
    console.error('Error in session-based editing:', error);

    // Send error event if response hasn't been ended
    if (!res.headersSent) {
      res.writeHead(500, {
        'Content-Type': 'application/json'
      });
    }

    if (!res.writableEnded) {
      res.write(`event:error\n`);
      res.write(`data:${JSON.stringify({ error: error.message })}\n\n`);
      res.end();
    }
  }
});

module.exports = router;
