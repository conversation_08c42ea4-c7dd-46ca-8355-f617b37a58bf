import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { QuotaInfo } from './QuotaModule';

interface QuotaContextType {
  /**
   * Quota information
   */
  quota: QuotaInfo;
  
  /**
   * Function to refresh quota information
   */
  refreshQuota: () => Promise<void>;
  
  /**
   * Whether quota information is loading
   */
  isLoading: boolean;
  
  /**
   * Error message if quota fetch failed
   */
  error: string | null;
}

interface QuotaProviderProps {
  /**
   * Children components
   */
  children: ReactNode;
  
  /**
   * Function to fetch quota information
   */
  fetchQuota: () => Promise<QuotaInfo>;
  
  /**
   * Optional refresh interval in milliseconds (default: 30000)
   */
  refreshInterval?: number;
  
  /**
   * Optional default quota values
   */
  defaultQuota?: Partial<QuotaInfo>;
}

// Create context
const QuotaContext = createContext<QuotaContextType | undefined>(undefined);

/**
 * Hook to use quota context
 */
export const useQuota = () => {
  const context = useContext(QuotaContext);
  if (!context) {
    throw new Error('useQuota must be used within a QuotaProvider');
  }
  return context;
};

/**
 * Provider component for quota information
 */
export const QuotaProvider: React.FC<QuotaProviderProps> = ({
  children,
  fetchQuota,
  refreshInterval = 30000,
  defaultQuota = {}
}) => {
  const [quota, setQuota] = useState<QuotaInfo>({
    plan: 'free',
    totalCount: 3,
    usedCount: 0,
    remainingCount: 3,
    ...defaultQuota
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const refreshQuota = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchQuota();
      setQuota(data);
    } catch (err) {
      console.error('Error fetching quota:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch quota');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch quota on mount and set up refresh interval
  useEffect(() => {
    refreshQuota();
    
    // Set up interval to refresh quota
    const intervalId = setInterval(() => {
      refreshQuota();
    }, refreshInterval);
    
    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval]);
  
  return (
    <QuotaContext.Provider value={{ quota, refreshQuota, isLoading, error }}>
      {children}
    </QuotaContext.Provider>
  );
};
