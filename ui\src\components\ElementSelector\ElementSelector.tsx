import { useEffect, useState, useRef } from 'react';
import styles from './ElementSelector.module.css';
import { FiEdit, FiTrash, FiEye, FiCopy, FiX } from 'react-icons/fi';

interface ElementInfo {
  element: HTMLElement;
  id: string;
  tagName: string;
  className?: string;
  styles: Record<string, string>;
}

interface ElementSelectorProps {
  iframeRef: React.RefObject<HTMLIFrameElement>;
  onElementUpdate?: (element: HTMLElement, updates: Record<string, string>) => void;
}

export function ElementSelector({ iframeRef, onElementUpdate }: ElementSelectorProps) {
  const [selectedElements, setSelectedElements] = useState<ElementInfo[]>([]);
  const [activeElementIndex, setActiveElementIndex] = useState<number>(-1);
  const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartPos, setDragStartPos] = useState<{ x: number, y: number } | null>(null);
  const [dragCurrentPos, setDragCurrentPos] = useState<{ x: number, y: number } | null>(null);
  const [elementStyles, setElementStyles] = useState<Record<string, string>>({});
  const overlayRef = useRef<HTMLDivElement>(null);

  // Initialize the selector
  useEffect(() => {
    if (!iframeRef.current) return;

    const iframe = iframeRef.current;
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDocument) return;

    // Add custom styles to the iframe document for highlighting
    const styleEl = iframeDocument.createElement('style');
    styleEl.textContent = `
      .element-hover-highlight {
        outline: 2px dashed #2196f3 !important;
        outline-offset: 2px !important;
      }
      .element-selected-highlight {
        outline: 2px solid #4caf50 !important;
        outline-offset: 2px !important;
      }
      .element-multi-selected {
        background-color: rgba(76, 175, 80, 0.2) !important;
        outline: 2px solid #4caf50 !important;
        outline-offset: 2px !important;
      }
      .selection-box {
        position: absolute;
        border: 1px dashed #2196f3;
        background-color: rgba(33, 150, 243, 0.1);
        pointer-events: none;
        z-index: 9999;
      }
    `;
    iframeDocument.head.appendChild(styleEl);

    // Clean up
    return () => {
      styleEl.remove();
    };
  }, [iframeRef]);

  // Generate a unique ID for an element
  const generateElementId = (element: HTMLElement): string => {
    const randomId = Math.random().toString(36).substring(2, 10);
    return `${element.tagName.toLowerCase()}-${randomId}`;
  };

  // Create element info object
  const createElementInfo = (element: HTMLElement): ElementInfo => {
    // Get computed styles
    const computedStyles = window.getComputedStyle(element);
    const styles: Record<string, string> = {};

    // Extract important styles
    ['width', 'height', 'color', 'background-color', 'font-size', 'font-family',
     'margin', 'padding', 'border', 'border-radius', 'display', 'position'].forEach(prop => {
      styles[prop] = computedStyles.getPropertyValue(prop);
    });

    return {
      element,
      id: element.id || generateElementId(element),
      tagName: element.tagName.toLowerCase(),
      className: element.className || undefined,
      styles
    };
  };

  // Check if element is already selected
  const isElementSelected = (element: HTMLElement): boolean => {
    return selectedElements.some(info => info.element === element);
  };

  // Add element to selection
  const addElementToSelection = (element: HTMLElement) => {
    if (!isElementSelected(element)) {
      // Remove hover highlight if present
      element.classList.remove('element-hover-highlight');

      // Add selection highlight
      element.classList.add('element-multi-selected');

      const elementInfo = createElementInfo(element);
      setSelectedElements(prev => [...prev, elementInfo]);
      setActiveElementIndex(selectedElements.length);
      setElementStyles(elementInfo.styles);
    }
  };

  // Remove element from selection
  const removeElementFromSelection = (elementId: string) => {
    const elementIndex = selectedElements.findIndex(info => info.id === elementId);
    if (elementIndex !== -1) {
      const element = selectedElements[elementIndex].element;

      // Remove all highlight classes
      element.classList.remove('element-multi-selected');
      element.classList.remove('element-selected-highlight');
      element.classList.remove('element-hover-highlight');

      setSelectedElements(prev => prev.filter((_, index) => index !== elementIndex));

      if (activeElementIndex === elementIndex) {
        // If we're removing the active element, set a new active element
        if (selectedElements.length > 1) {
          setActiveElementIndex(Math.max(0, elementIndex - 1));
          setElementStyles(selectedElements[Math.max(0, elementIndex - 1)].styles);
        } else {
          setActiveElementIndex(-1);
          setElementStyles({});
        }
      } else if (activeElementIndex > elementIndex) {
        // Adjust active index if we're removing an element before it
        setActiveElementIndex(activeElementIndex - 1);
      }
    }
  };

  // Clear all selections
  const clearSelections = () => {
    // Remove highlight classes from all selected elements
    selectedElements.forEach(info => {
      info.element.classList.remove('element-multi-selected');
      info.element.classList.remove('element-selected-highlight');
      info.element.classList.remove('element-hover-highlight');
    });

    setSelectedElements([]);
    setActiveElementIndex(-1);
    setElementStyles({});
  };

  // Reset selection state when selection mode changes
  useEffect(() => {
    if (!isSelecting) {
      // Reset drag state
      setIsDragging(false);
      setDragStartPos(null);
      setDragCurrentPos(null);
    }
  }, [isSelecting]);

  // Handle mouse events for element selection
  useEffect(() => {
    if (!iframeRef.current || !isSelecting) {
      // Clean up any existing selection box when exiting selection mode
      if (!isSelecting && iframeRef.current) {
        const iframeDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
        if (iframeDocument) {
          const existingBox = iframeDocument.querySelector('.selection-box');
          if (existingBox && existingBox.parentNode) {
            existingBox.parentNode.removeChild(existingBox);
          }
        }
      }
      return;
    }

    const iframe = iframeRef.current;
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDocument) return;

    // Remove any existing selection box
    const existingBox = iframeDocument.querySelector('.selection-box');
    if (existingBox && existingBox.parentNode) {
      existingBox.parentNode.removeChild(existingBox);
    }

    // Create selection box in the iframe
    const selectionBox = iframeDocument.createElement('div');
    selectionBox.className = 'selection-box';
    selectionBox.style.display = 'none';
    iframeDocument.body.appendChild(selectionBox);

    // Track if mouse is pressed
    let isMouseDown = false;

    const handleMouseOver = (e: MouseEvent) => {
      if (isDragging) return;

      e.stopPropagation();
      const target = e.target as HTMLElement;

      // Remove highlight from previous hovered element
      if (hoveredElement && !isElementSelected(hoveredElement)) {
        hoveredElement.classList.remove('element-hover-highlight');
      }

      // Add highlight to current hovered element
      if (!isElementSelected(target)) {
        target.classList.add('element-hover-highlight');
        setHoveredElement(target);
      }
    };

    const handleMouseOut = (e: MouseEvent) => {
      if (isDragging) return;

      e.stopPropagation();
      const target = e.target as HTMLElement;

      if (!isElementSelected(target)) {
        target.classList.remove('element-hover-highlight');
      }

      setHoveredElement(null);
    };

    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault();

      isMouseDown = true;

      // Start drag selection
      setIsDragging(true);

      const rect = iframe.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setDragStartPos({ x, y });
      setDragCurrentPos({ x, y });

      selectionBox.style.left = `${x}px`;
      selectionBox.style.top = `${y}px`;
      selectionBox.style.width = '0px';
      selectionBox.style.height = '0px';
      selectionBox.style.display = 'block';

      // If not using Ctrl/Cmd key, clear previous selections
      if (!(e.ctrlKey || e.metaKey)) {
        clearSelections();
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isMouseDown || !dragStartPos) return;

      setIsDragging(true);

      const rect = iframe.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setDragCurrentPos({ x, y });

      // Update selection box
      const left = Math.min(dragStartPos.x, x);
      const top = Math.min(dragStartPos.y, y);
      const width = Math.abs(x - dragStartPos.x);
      const height = Math.abs(y - dragStartPos.y);

      selectionBox.style.left = `${left}px`;
      selectionBox.style.top = `${top}px`;
      selectionBox.style.width = `${width}px`;
      selectionBox.style.height = `${height}px`;

      // Find elements under the selection box
      if (width > 5 || height > 5) {
        // Find all elements in the selection box
        const elements = iframeDocument.querySelectorAll('*');
        elements.forEach(el => {
          const element = el as HTMLElement;
          const rect = element.getBoundingClientRect();
          const iframeRect = iframe.getBoundingClientRect();

          // Convert element coordinates to iframe coordinates
          const elLeft = rect.left - iframeRect.left;
          const elTop = rect.top - iframeRect.top;
          const elRight = elLeft + rect.width;
          const elBottom = elTop + rect.height;

          // Check if element is in selection box
          if (
            elLeft < left + width &&
            elRight > left &&
            elTop < top + height &&
            elBottom > top
          ) {
            // Add temporary highlight
            if (!isElementSelected(element)) {
              element.classList.add('element-hover-highlight');
            }
          } else if (!isElementSelected(element)) {
            // Remove temporary highlight
            element.classList.remove('element-hover-highlight');
          }
        });
      }
    };

    const handleMouseUp = (e: MouseEvent) => {
      if (!isMouseDown) return;

      isMouseDown = false;

      if (isDragging) {
        // Finish drag selection
        selectionBox.style.display = 'none';

        if (dragStartPos && dragCurrentPos) {
          // Calculate the width and height of the selection box
          const width = Math.abs(dragCurrentPos.x - dragStartPos.x);
          const height = Math.abs(dragCurrentPos.y - dragStartPos.y);

          // If it's a small drag (more like a click), just select the target
          if (width < 5 && height < 5) {
            const target = e.target as HTMLElement;
            if (!isElementSelected(target)) {
              addElementToSelection(target);
            }
          } else {
            // Find all elements in the selection box
            const elements = iframeDocument.querySelectorAll('*');
            elements.forEach(el => {
              const element = el as HTMLElement;

              // If element has the hover highlight, select it
              if (element.classList.contains('element-hover-highlight') && !isElementSelected(element)) {
                element.classList.remove('element-hover-highlight');
                addElementToSelection(element);
              }
            });
          }
        }
      }

      setIsDragging(false);
      setDragStartPos(null);
      setDragCurrentPos(null);

      // Don't exit selection mode automatically
      // We want to keep selection mode active until the button is toggled
    };

    // Add event listeners
    iframeDocument.body.addEventListener('mouseover', handleMouseOver);
    iframeDocument.body.addEventListener('mouseout', handleMouseOut);
    iframeDocument.body.addEventListener('mousedown', handleMouseDown);
    iframeDocument.body.addEventListener('mousemove', handleMouseMove);
    iframeDocument.body.addEventListener('mouseup', handleMouseUp);

    // Clean up
    return () => {
      iframeDocument.body.removeEventListener('mouseover', handleMouseOver);
      iframeDocument.body.removeEventListener('mouseout', handleMouseOut);
      iframeDocument.body.removeEventListener('mousedown', handleMouseDown);
      iframeDocument.body.removeEventListener('mousemove', handleMouseMove);
      iframeDocument.body.removeEventListener('mouseup', handleMouseUp);

      if (selectionBox.parentNode) {
        selectionBox.parentNode.removeChild(selectionBox);
      }
    };
  }, [iframeRef, isSelecting, isDragging, dragStartPos, dragCurrentPos, hoveredElement, selectedElements]);

  // Handle style updates
  const updateElementStyle = (property: string, value: string) => {
    if (activeElementIndex === -1 || selectedElements.length === 0) return;

    const activeElement = selectedElements[activeElementIndex].element;
    activeElement.style[property as any] = value;

    // Update styles in state
    const updatedStyles = { ...elementStyles, [property]: value };
    setElementStyles(updatedStyles);

    // Update element info
    setSelectedElements(prev => {
      const updated = [...prev];
      updated[activeElementIndex] = {
        ...updated[activeElementIndex],
        styles: updatedStyles
      };
      return updated;
    });

    if (onElementUpdate) {
      onElementUpdate(activeElement, { [property]: value });
    }
  };

  return (
    <div className={styles.elementSelector}>
      <div className={styles.selectorHeader}>
        <h3>Element Selector</h3>
        <button
          className={`${styles.selectorButton} ${isSelecting ? styles.active : ''}`}
          onClick={() => {
            // If we're turning off selection mode, clear all temporary highlights
            if (isSelecting) {
              // Remove hover highlights from all elements in the iframe
              if (iframeRef.current) {
                const iframeDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
                if (iframeDocument) {
                  const elements = iframeDocument.querySelectorAll('.element-hover-highlight');
                  elements.forEach(el => {
                    el.classList.remove('element-hover-highlight');
                  });
                }
              }

              // Clear hover state
              if (hoveredElement) {
                hoveredElement.classList.remove('element-hover-highlight');
                setHoveredElement(null);
              }
            }

            // Toggle selection mode
            setIsSelecting(!isSelecting);
          }}
        >
          {isSelecting ? 'Cancel Selection' : 'Select Element'}
        </button>
      </div>

      {/* Selected Elements Pills */}
      {selectedElements.length > 0 && (
        <div className={styles.selectedElementsPills}>
          {selectedElements.map((info, index) => (
            <div
              key={info.id}
              className={`${styles.elementPill} ${index === activeElementIndex ? styles.activePill : ''}`}
              onClick={() => setActiveElementIndex(index)}
            >
              <span className={styles.pillText}>
                {info.tagName}
                {info.className && `.${info.className.split(' ')[0]}`}
                {info.id && `#${info.id}`}
              </span>
              <button
                className={styles.pillCloseBtn}
                onClick={(e) => {
                  e.stopPropagation();
                  removeElementFromSelection(info.id);
                }}
              >
                <FiX size={14} />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Element Details */}
      {selectedElements.length > 0 && activeElementIndex >= 0 && (
        <div className={styles.elementInfo}>
          <div className={styles.elementPath}>
            <span className={styles.tagName}>
              {selectedElements[activeElementIndex].tagName}
            </span>
            {selectedElements[activeElementIndex].id && (
              <span className={styles.elementId}>
                #{selectedElements[activeElementIndex].id}
              </span>
            )}
            {selectedElements[activeElementIndex].className && (
              <span className={styles.elementClass}>
                .{selectedElements[activeElementIndex].className.split(' ')[0]}
              </span>
            )}
          </div>

          <div className={styles.actionButtons}>
            <button className={styles.actionButton} title="Edit Element">
              <FiEdit />
            </button>
            <button className={styles.actionButton} title="Delete Element">
              <FiTrash />
            </button>
            <button className={styles.actionButton} title="Duplicate Element">
              <FiCopy />
            </button>
            <button className={styles.actionButton} title="Toggle Visibility">
              <FiEye />
            </button>
          </div>

          <div className={styles.styleEditor}>
            <h4>Styles</h4>
            <div className={styles.styleGrid}>
              {Object.entries(elementStyles).map(([property, value]) => (
                <div key={property} className={styles.styleRow}>
                  <label>{property}:</label>
                  <input
                    type="text"
                    value={value}
                    onChange={(e) => updateElementStyle(property, e.target.value)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {selectedElements.length === 0 && !isSelecting && (
        <div className={styles.noSelection}>
          Click "Select Element" to begin editing
        </div>
      )}

      {isSelecting && (
        <div className={styles.selectionMode}>
          <p>Press and hold mouse button to select elements</p>
          <p>Drag to select multiple elements</p>
          <p>Hold Ctrl/Cmd for multiple selections</p>
          <p>Click "Cancel Selection" when done</p>
        </div>
      )}

      <div ref={overlayRef} className={styles.selectorOverlay} style={{ display: isSelecting ? 'block' : 'none' }} />
    </div>
  );
}
