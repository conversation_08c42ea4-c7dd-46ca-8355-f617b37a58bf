import React from "react";

export interface BillingUsageBarProps {
  plan: "Free" | "Pay-as-you-go" | "Pro";
  prototypeCount: number;
  prototypeLimit: number;
  tokenUsage?: number;
  tokenLimit?: number;
}

export const BillingUsageBar: React.FC<BillingUsageBarProps> = ({
  plan,
  prototypeCount,
  prototypeLimit,
  tokenUsage,
  tokenLimit,
}) => {
  const prototypesLeft = Math.max(0, prototypeLimit - prototypeCount);
  const isFree = plan === "Free";
  const prototypePercent = Math.min(100, (prototypeCount / prototypeLimit) * 100);

  return (
    <div className="rounded-lg bg-white/80 dark:bg-zinc-900/80 shadow p-4 flex flex-col gap-2 mb-4 border border-zinc-200 dark:border-zinc-800">
      <div className="flex items-center justify-between">
        <span className="font-semibold text-zinc-700 dark:text-zinc-200">
          Plan: <span className="capitalize">{plan}</span>
        </span>
        {isFree && (
          <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-semibold">
            You have {prototypeLimit} free prototypes
          </span>
        )}
      </div>
      <div>
        <div className="flex items-center justify-between mb-1">
          <span className="text-sm text-zinc-600 dark:text-zinc-300">
            Prototypes used
          </span>
          <span className="text-sm font-medium">
            {prototypeCount} / {prototypeLimit}
          </span>
        </div>
        <div className="w-full bg-zinc-200 dark:bg-zinc-800 rounded h-2 overflow-hidden">
          <div
            className={`h-2 rounded transition-all duration-300 ${
              prototypePercent >= 100
                ? "bg-red-500"
                : prototypePercent >= 66
                ? "bg-yellow-400"
                : "bg-blue-500"
            }`}
            style={{ width: `${prototypePercent}%` }}
          />
        </div>
        {isFree && prototypesLeft === 1 && (
          <div className="mt-2 text-xs text-yellow-700 bg-yellow-100 rounded px-2 py-1">
            1 free prototype left!
          </div>
        )}
        {isFree && prototypesLeft === 0 && (
          <div className="mt-2 text-xs text-red-700 bg-red-100 rounded px-2 py-1">
            <b>You've reached your free limit.</b> Buy more credits or upgrade to unlock unlimited prototyping.
          </div>
        )}
      </div>
      {typeof tokenUsage === "number" && typeof tokenLimit === "number" && (
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-zinc-600 dark:text-zinc-300">
              Token usage
            </span>
            <span className="text-sm font-medium">
              {tokenUsage} / {tokenLimit}
            </span>
          </div>
          <div className="w-full bg-zinc-200 dark:bg-zinc-800 rounded h-2 overflow-hidden">
            <div
              className="h-2 rounded bg-green-500 transition-all duration-300"
              style={{
                width: `${Math.min(100, (tokenUsage / tokenLimit) * 100)}%`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};
