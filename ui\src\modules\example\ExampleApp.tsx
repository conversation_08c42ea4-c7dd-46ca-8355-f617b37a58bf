import React from 'react';
import { PricingModule, PricingPlan } from '../pricing';
import { QuotaProvider, QuotaModule, QuotaInfo } from '../quota';
import { getPrototypeQuota } from '../../services/planService';

/**
 * Example app showing how to use the pricing and quota modules
 */
export const ExampleApp: React.FC = () => {
  // Define pricing plans
  const plans: PricingPlan[] = [
    {
      id: "free",
      name: "Free",
      description: "Perfect for trying out the platform",
      price: 0,
      buttonText: "Get Started",
      features: [
        { name: "3 prototypes" },
        { name: "Basic design options" },
        { name: "HTML/CSS output" },
        { name: "Community support" }
      ],
      quota: {
        prototypeLimit: 3
      }
    },
    {
      id: "pro",
      name: "Pro",
      description: "For serious prototype creators",
      price: 5,
      buttonText: "Upgrade to Pro",
      isFeatured: true,
      badge: "Most Popular",
      checkoutUrl: "https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa",
      features: [
        { name: "50 prototypes", isHighlighted: true },
        { name: "Advanced design options", isHighlighted: true },
        { name: "Clean HTML/CSS output" },
        { name: "No token tracking" },
        { name: "Priority build speed" },
        { name: "Priority support" }
      ],
      quota: {
        prototypeLimit: 50
      }
    }
  ];
  
  // Define FAQ items
  const faqItems = [
    {
      question: "Can I cancel anytime?",
      answer: "Yes, subscriptions are month-to-month and can be canceled at any time."
    },
    {
      question: "Do you support teams?",
      answer: "Team plans are coming soon. Stay tuned for updates!"
    },
    {
      question: "Is there a free trial?",
      answer: "Yes — you get 3 free prototypes to get started with no credit card required."
    }
  ];
  
  // Function to fetch quota (using the existing service)
  const fetchQuota = async (): Promise<QuotaInfo> => {
    const data = await getPrototypeQuota();
    return {
      plan: data.plan,
      totalCount: data.totalCount,
      usedCount: data.usedCount,
      remainingCount: data.remainingCount
    };
  };
  
  return (
    <QuotaProvider fetchQuota={fetchQuota}>
      <ExampleContent plans={plans} faqItems={faqItems} />
    </QuotaProvider>
  );
};

interface ExampleContentProps {
  plans: PricingPlan[];
  faqItems: Array<{ question: string; answer: string }>;
}

/**
 * Example content component that uses the quota context
 */
const ExampleContent: React.FC<ExampleContentProps> = ({ plans, faqItems }) => {
  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
      <h1>Example App</h1>
      
      {/* Quota display in the header */}
      <div style={{ marginBottom: '2rem' }}>
        <QuotaConsumer />
      </div>
      
      {/* Pricing module */}
      <PricingModule
        plans={plans}
        faqItems={faqItems}
        yearlyDiscountPercentage={20}
      />
    </div>
  );
};

/**
 * Example component that consumes the quota context
 */
const QuotaConsumer: React.FC = () => {
  // Use the quota hook to access quota information
  const { quota, refreshQuota, isLoading } = useQuota();
  
  return (
    <div>
      <h2>Your Quota</h2>
      {isLoading ? (
        <p>Loading quota information...</p>
      ) : (
        <QuotaModule
          quota={quota}
          refreshQuota={refreshQuota}
          upgradeUrl="https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa"
          displayMode="full"
        />
      )}
    </div>
  );
};

// Import the hook to avoid TypeScript errors
import { useQuota } from '../quota';

export default ExampleApp;
