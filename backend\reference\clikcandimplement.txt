curl ^"https://readdy.ai/api/page_gen/generate_intent^" ^
  -H ^"accept: */*^" ^
  -H ^"accept-language: en-US,en;q=0.9^" ^
  -H ^"authorization: Bearer eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDIyMkFBQSIsImtpZCI6Imluc18ycWtRbmFmNnRNcW9DQVZRMjRsWEwzRzRDcnQiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************.B380tQQxgWa-110EakCNSay1WQ6gqxU-XsyUnGnI9NhwT9OE59NeWQVTswSrgQSk2XOPMfuVkJ9aG8XudmLEg94zt73niOPO6vHcUwB3dX8rOndo4QrE5DqnpxDFJcyqwrVn-1QskuffzrW65EpyPirAJr6dbWsX5giakHWud22IYYSkyPt8FmCPdBswuQ47wJ2PZOHS1pVpCrr5buPLD_ZM5SWhGyV4h74oUnFbOJfEUV-B88d-6iGP182UaYzbGkunXkvCoIBCUnOzSjXZ-2QH5qH__x56vIp5_sQUpMLRYpBqVdel5DtNQACiQTxL7OLHhHTd82zanriGkjFN-A^" ^
  -H ^"cache-control: no-cache^" ^
  -H ^"content-type: text/plain;charset=UTF-8^" ^
  -b ^"_ga=GA1.1.1465294481.1745988234; __client_uat=1745988267; __client_uat_ABo4F7rL=1745988267; featurebase-anon-id=01968502-cb3b-71da-a7b3-8040ad5e954f; _ga_MHDZ46BL5J=GS2.1.s1746973142^$o2^$g0^$t1746973142^$j0^$l0^$h0; featurebase-access-67554c8c97bd6ab5bb7e9290=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODBiMTcxZjY3MTA2MTVkODQwOGE0NTIiLCJ1dCI6ImN1c3RvbWVyIiwiaWF0IjoxNzQ4MDEzODM3LCJleHAiOjE3NTA2MDU4MzcsIm5iZiI6MTc0ODAxMzcxNywiaXNzIjoicHJvZHVjdGlvbjpiYWNrZW5kIiwiYXVkIjoic2VzcyIsInR0IjoiYWNjZXNzIiwidHYiOjAsIm9pZCI6IjY3NTU0YzhjOTdiZDZhYjViYjdlOTI5MCIsInNyYyI6InNzbyJ9.g6zibhU-DMfB_OranjVjAUOaAiUCOofJmtPTevXyUKg; ph_phc_V7JMHB0fVJGRu8UHyrsj6pSL1BS76P5zD8qCi7lrTTV_posthog=^%^7B^%^22distinct_id^%^22^%^3A^%^22user_2wCtEse7SEtbCnPCarydaaRSgfU^%^22^%^2C^%^22^%^24sesid^%^22^%^3A^%^5B1748067475393^%^2C^%^22019700f0-6e91-72c8-81a0-a6d0f7b87b8f^%^22^%^2C1748067446416^%^5D^%^2C^%^22^%^24epp^%^22^%^3Atrue^%^2C^%^22^%^24initial_person_info^%^22^%^3A^%^7B^%^22r^%^22^%^3A^%^22https^%^3A^%^2F^%^2Freaddy.ai^%^2Fsignin^%^22^%^2C^%^22u^%^22^%^3A^%^22https^%^3A^%^2F^%^2Freaddy.ai^%^2Fhome^%^22^%^7D^%^7D; __session=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __session_ABo4F7rL=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************^" ^
  -H ^"origin: https://readdy.ai^" ^
  -H ^"pragma: no-cache^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://readdy.ai/home/<USER>/d47de6a6-a5fe-4661-8513-b349d01fa833^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"134^\^", ^\^"Not:A-Brand^\^";v=^\^"24^\^", ^\^"Google Chrome^\^";v=^\^"134^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?0^" ^
  -H ^"sec-ch-ua-platform: ^\^"Windows^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36^" ^
  --data-raw ^"^{^\^"recordId^\^":2207890,^\^"elementCode^\^":^\^"^<button class=^\^\^\^"bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center^\^\^\^"^>^\^\n^<div class=^\^\^\^"w-5 h-5 flex items-center justify-center mr-2^\^\^\^"^>^\^\n^<i class=^\^\^\^"ri-customer-service-2-line^\^\^\^"^>^</i^>^\^\n^</div^>^\^\nGet Support^\^\n^</button^>^\^"^}^"






  {
    "code": "OK",
    "data": {
        "canGenerate": false,
        "userIntent": "The user clicked the \"Get Support\" button in the welcome banner section. This suggests they need customer support or assistance with their banking services but the current page doesn't have a support dialog implementation.",
        "suggestion": "When the user clicks the \"Get Support\" button, a support dialog modal should appear, similar to the existing connect-modal. This dialog should include support options like live chat with a customer service representative, phone support, email support, and a form to submit support tickets. The dialog should have fields for the user to describe their issue, select a category, and attach any relevant files. It should also display estimated wait times for different support channels."
    },
    "meta": {
        "time": *************,
        "request_id": "bbaae62d-0996-4e86-a063-b5c8baccfd31",
        "message": "",
        "detail": null
    }
}




curl ^"https://readdy.ai/api/page_gen/edit^" ^
  -H ^"accept: text/event-stream^" ^
  -H ^"accept-language: en-US,en;q=0.9^" ^
  -H ^"authorization: Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************^" ^
  -H ^"cache-control: no-cache^" ^
  -H ^"content-type: application/json^" ^
  -b ^"_ga=GA1.1.1465294481.1745988234; __client_uat=1745988267; __client_uat_ABo4F7rL=1745988267; featurebase-anon-id=01968502-cb3b-71da-a7b3-8040ad5e954f; _ga_MHDZ46BL5J=GS2.1.s1746973142^$o2^$g0^$t1746973142^$j0^$l0^$h0; featurebase-access-67554c8c97bd6ab5bb7e9290=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODBiMTcxZjY3MTA2MTVkODQwOGE0NTIiLCJ1dCI6ImN1c3RvbWVyIiwiaWF0IjoxNzQ4MDEzODM3LCJleHAiOjE3NTA2MDU4MzcsIm5iZiI6MTc0ODAxMzcxNywiaXNzIjoicHJvZHVjdGlvbjpiYWNrZW5kIiwiYXVkIjoic2VzcyIsInR0IjoiYWNjZXNzIiwidHYiOjAsIm9pZCI6IjY3NTU0YzhjOTdiZDZhYjViYjdlOTI5MCIsInNyYyI6InNzbyJ9.g6zibhU-DMfB_OranjVjAUOaAiUCOofJmtPTevXyUKg; __session=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __session_ABo4F7rL=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; ph_phc_V7JMHB0fVJGRu8UHyrsj6pSL1BS76P5zD8qCi7lrTTV_posthog=^%^7B^%^22distinct_id^%^22^%^3A^%^22user_2wCtEse7SEtbCnPCarydaaRSgfU^%^22^%^2C^%^22^%^24sesid^%^22^%^3A^%^5B1748067486931^%^2C^%^22019700f0-6e91-72c8-81a0-a6d0f7b87b8f^%^22^%^2C1748067446416^%^5D^%^2C^%^22^%^24epp^%^22^%^3Atrue^%^2C^%^22^%^24initial_person_info^%^22^%^3A^%^7B^%^22r^%^22^%^3A^%^22https^%^3A^%^2F^%^2Freaddy.ai^%^2Fsignin^%^22^%^2C^%^22u^%^22^%^3A^%^22https^%^3A^%^2F^%^2Freaddy.ai^%^2Fhome^%^22^%^7D^%^7D^" ^
  -H ^"origin: https://readdy.ai^" ^
  -H ^"pragma: no-cache^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://readdy.ai/home/<USER>/d47de6a6-a5fe-4661-8513-b349d01fa833^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"134^\^", ^\^"Not:A-Brand^\^";v=^\^"24^\^", ^\^"Google Chrome^\^";v=^\^"134^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?0^" ^
  -H ^"sec-ch-ua-platform: ^\^"Windows^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36^" ^
  --data-raw ^"^{^\^"sessionKey^\^":^\^"d47de6a6-a5fe-4661-8513-b349d01fa833^\^",^\^"desc^\^":^\^"**1. Overall Layout Structure**^\^\n^\^\nThe banking interface follows a modern, clean design with a white background and professional color scheme (navy blue, white, and subtle gray accents) to maintain trust and professionalism.**2. Header Section**^\^\n^\^\n- Top navigation bar with bank logo on the left^\^\n^\^\n- User profile icon and notifications bell on the right^\^\n^\^\n- Global search functionality in the center^\^\n^\^\n- Secondary navigation menu below with main banking categories**3. Agent Selection Panel**^\^\n^\^\n- Horizontal scrollable card layout showcasing different AI agents^\^\n^\^\n- Each agent card contains:^\^\n^\^\n- Agent avatar/icon^\^\n^\^\n- Agent name and specialization (Investment Advisor, Loan Specialist, etc.)  ^\^\n^\^\n- Brief description of capabilities^\^\n^\^\n- ^\^\^\^"Connect^\^\^\^" button^\^\n^\^\n- Visual indicator showing agent availability status**4. Main Dashboard Area**^\^\n^\^\n- Grid layout with modular widgets^\^\n^\^\n- Account summary widget showing balances^\^\n^\^\n- Quick action buttons for common transactions^\^\n^\^\n- Recent activity feed^\^\n^\^\n- Personalized recommendations section**5. Agent Interaction Interface**^\^\n^\^\n- Chat-style interface that opens when an agent is selected^\^\n^\^\n- Split screen capability:^\^\n^\^\n- Agent conversation on one side^\^\n^\^\n- Relevant forms/documents on the other^\^\n^\^\n- Rich media support for document sharing^\^\n^\^\n- Progress indicators for ongoing processes**6. Footer Section**^\^\n^\^\n- Quick links to important banking services^\^\n^\^\n- Security status indicator^\^\n^\^\n- Help and support contact information^\^\n^\^\n- Legal disclaimers and privacy policy links^\^\n^\^\n**7. Responsive Elements**^\^\n^\^\n- Collapsible sidebars^\^\n^\^\n- Expandable chat windows^\^\n^\^\n- Floating action buttons for quick access to agents^\^\n^\^\n- Progressive disclosure of complex features^\^\n^\^\n**8. Security Features**^\^\n^\^\n- Session timeout indicator^\^\n^\^\n- Two-factor authentication integration^\^\n^\^\n- Encryption status display^\^\n^\^\n- Secure document handling indicators^\^\n^\^\nThis design prioritizes user accessibility while maintaining the professional standards expected in banking applications, with AI agents seamlessly integrated into the traditional banking interface.^\^",^\^"recordId^\^":2207890,^\^"query^\^":^\^"UI element: ^<button class=^\^\^\^"bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center^\^\^\^"^>^\^\n^<div class=^\^\^\^"w-5 h-5 flex items-center justify-center mr-2^\^\^\^"^>^\^\n^<i class=^\^\^\^"ri-customer-service-2-line^\^\^\^"^>^</i^>^\^\n^</div^>^\^\nGet Support^\^\n^</button^>^\^\nWhen the user clicks the ^\^\^\^"Get Support^\^\^\^" button, a support dialog modal should appear, similar to the existing connect-modal. This dialog should include support options like live chat with a customer service representative, phone support, email support, and a form to submit support tickets. The dialog should have fields for the user to describe their issue, select a category, and attach any relevant files. It should also display estimated wait times for different support channels.^\^\nImportant: Use the ID selector when looking for elements.^\^",^\^"style^\^":^\^"light^\^",^\^"color^\^":^\^"^\^",^\^"borderRadius^\^":^\^"medium^\^",^\^"language^\^":^\^"English^\^",^\^"framework^\^":^\^"html^\^",^\^"lib^\^":^\^"^\^",^\^"messages^\^":^[^],^\^"force^\^":false,^\^"seq^\^":1^}^"




  event:startMsg
data:I

event:startMsg
data:'

event:startMsg
data:l

event:startMsg
data:l

event:startMsg
data: 

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data:l

event:startMsg
data:p

event:startMsg
data: 

event:startMsg
data:y

event:startMsg
data:o

event:startMsg
data:u

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:d

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:s

event:startMsg
data:u

event:startMsg
data:p

event:startMsg
data:p

event:startMsg
data:o

event:startMsg
data:r

event:startMsg
data:t

event:startMsg
data: 

event:startMsg
data:d

event:startMsg
data:i

event:startMsg
data:a

event:startMsg
data:l

event:startMsg
data:o

event:startMsg
data:g

event:startMsg
data: 

event:startMsg
data:m

event:startMsg
data:o

event:startMsg
data:d

event:startMsg
data:a

event:startMsg
data:l

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:n

event:startMsg
data:d

event:startMsg
data: 

event:startMsg
data:i

event:startMsg
data:t

event:startMsg
data:s

event:startMsg
data: 

event:startMsg
data:f

event:startMsg
data:u

event:startMsg
data:n

event:startMsg
data:c

event:startMsg
data:t

event:startMsg
data:i

event:startMsg
data:o

event:startMsg
data:n

event:startMsg
data:a

event:startMsg
data:l

event:startMsg
data:i

event:startMsg
data:t

event:startMsg
data:y

event:startMsg
data:.

event:startMsg
data: 

event:startMsg
data:H

event:startMsg
data:e

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data:'

event:startMsg
data:s

event:startMsg
data: 

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:m

event:startMsg
data:o

event:startMsg
data:d

event:startMsg
data:i

event:startMsg
data:f

event:startMsg
data:i

event:startMsg
data:c

event:startMsg
data:a

event:startMsg
data:t

event:startMsg
data:i

event:startMsg
data:o

event:startMsg
data:n

event:startMsg
data::

event:startMsg
data:
data:

event:startMsg
data:
data:

event:startMsgFinish
data:

event:data
data:<!DOCTYPE html>
data:

event:data
data:<html lang="en">
data:

event:data
data:<head>
data:

event:data
data:<meta charset="UTF-8">
data:

event:data
data:<meta name="viewport" content="width=device-width, initial-scale=1.0">
data:

event:data
data:<title>Agentic Banking Platform</title>
data:

event:data
data:<script src="https://cdn.tailwindcss.com/3.4.16"></script>
data:

event:data
data:<script>tailwind.config={theme:{extend:{colors:{primary:'#0F3460',secondary:'#E94560'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
data:

event:data
data:<link rel="preconnect" href="https://fonts.googleapis.com">
data:

event:data
data:<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
data:

event:data
data:<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
data:

event:data
data:<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
data:

event:data
data:<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
data:

event:data
data:<script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
data:

event:data
data:<style>
data:

event:data
data::where([class^="ri-"])::before { content: "\f3c2"; }
data:

event:data
data:body {
data:

event:data
data:font-family: 'Inter', sans-serif;
data:

event:data
data:background-color: #f9fafb;
data:

event:data
data:}
data:

event:data
data:.agent-cards::-webkit-scrollbar {
data:

event:data
data:height: 6px;
data:

event:data
data:}
data:

event:data
data:.agent-cards::-webkit-scrollbar-track {
data:

event:data
data:background: #f1f1f1;
data:

event:data
data:border-radius: 10px;
data:

event:data
data:}
data:

event:data
data:.agent-cards::-webkit-scrollbar-thumb {
data:

event:data
data:background: #d1d5db;
data:

event:data
data:border-radius: 10px;
data:

event:data
data:}
data:

event:data
data:.agent-cards::-webkit-scrollbar-thumb:hover {
data:

event:data
data:background: #9ca3af;
data:

event:data
data:}
data:

event:data
data:input[type="number"]::-webkit-inner-spin-button,
data:

event:data
data:input[type="number"]::-webkit-outer-spin-button {
data:

event:data
data:-webkit-appearance: none;
data:

event:data
data:margin: 0;
data:

event:data
data:}
data:

event:data
data:.custom-switch {
data:

event:data
data:position: relative;
data:

event:data
data:display: inline-block;
data:

event:data
data:width: 46px;
data:

event:data
data:height: 24px;
data:

event:data
data:}
data:

event:data
data:.custom-switch input {
data:

event:data
data:opacity: 0;
data:

event:data
data:width: 0;
data:

event:data
data:height: 0;
data:

event:data
data:}
data:

event:data
data:.slider {
data:

event:data
data:position: absolute;
data:

event:data
data:cursor: pointer;
data:

event:data
data:top: 0;
data:

event:data
data:left: 0;
data:

event:data
data:right: 0;
data:

event:data
data:bottom: 0;
data:

event:data
data:background-color: #e5e7eb;
data:

event:data
data:transition: .4s;
data:

event:data
data:border-radius: 24px;
data:

event:data
data:}
data:

event:data
data:.slider:before {
data:

event:data
data:position: absolute;
data:

event:data
data:content: "";
data:

event:data
data:height: 18px;
data:

event:data
data:width: 18px;
data:

event:data
data:left: 3px;
data:

event:data
data:bottom: 3px;
data:

event:data
data:background-color: white;
data:

event:data
data:transition: .4s;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:}
data:

event:data
data:input:checked + .slider {
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:input:checked + .slider:before {
data:

event:data
data:transform: translateX(22px);
data:

event:data
data:}
data:

event:data
data:.custom-radio {
data:

event:data
data:display: flex;
data:

event:data
data:align-items: center;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-radio-input {
data:

event:data
data:appearance: none;
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border: 2px solid #d1d5db;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:margin-right: 8px;
data:

event:data
data:position: relative;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-radio-input:checked {
data:

event:data
data:border-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:.custom-radio-input:checked::after {
data:

event:data
data:content: "";
data:

event:data
data:position: absolute;
data:

event:data
data:top: 3px;
data:

event:data
data:left: 3px;
data:

event:data
data:width: 8px;
data:

event:data
data:height: 8px;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox {
data:

event:data
data:display: flex;
data:

event:data
data:align-items: center;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox-input {
data:

event:data
data:appearance: none;
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border: 2px solid #d1d5db;
data:

event:data
data:border-radius: 4px;
data:

event:data
data:margin-right: 8px;
data:

event:data
data:position: relative;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox-input:checked {
data:

event:data
data:border-color: #0F3460;
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox-input:checked::after {
data:

event:data
data:content: "";
data:

event:data
data:position: absolute;
data:

event:data
data:top: 2px;
data:

event:data
data:left: 5px;
data:

event:data
data:width: 5px;
data:

event:data
data:height: 10px;
data:

event:data
data:border: solid white;
data:

event:data
data:border-width: 0 2px 2px 0;
data:

event:data
data:transform: rotate(45deg);
data:

event:data
data:}
data:

event:data
data:.custom-range {
data:

event:data
data:-webkit-appearance: none;
data:

event:data
data:width: 100%;
data:

event:data
data:height: 6px;
data:

event:data
data:border-radius: 5px;
data:

event:data
data:background: #e5e7eb;
data:

event:data
data:outline: none;
data:

event:data
data:}
data:

event:data
data:.custom-range::-webkit-slider-thumb {
data:

event:data
data:-webkit-appearance: none;
data:

event:data
data:appearance: none;
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:background: #0F3460;
data:

event:data
data:cursor: pointer;
data:

event:data
data:border: 2px solid white;
data:

event:data
data:box-shadow: 0 0 2px rgba(0,0,0,0.2);
data:

event:data
data:}
data:

event:data
data:.custom-range::-moz-range-thumb {
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:background: #0F3460;
data:

event:data
data:cursor: pointer;
data:

event:data
data:border: 2px solid white;
data:

event:data
data:box-shadow: 0 0 2px rgba(0,0,0,0.2);
data:

event:data
data:}
data:

event:data
data:.tab-button.active {
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:color: white;
data:

event:data
data:}
data:

event:data
data:</style>
data:

event:data
data:</head>
data:

event:data
data:<body>
data:

event:data
data:<!-- Header -->
data:

event:data
data:<header class="bg-white shadow-sm">
data:

event:data
data:<!-- Top Navigation -->
data:

event:data
data:<div class="px-6 py-4 flex items-center justify-between border-b border-gray-100">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<h1 class="text-2xl font-['Pacifico'] text-primary mr-10">logo</h1>
data:

event:data
data:<div class="relative mx-4 hidden md:block">
data:

event:data
data:<div class="absolute inset-y-0 left-0 flex items-center pl-3">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400">
data:

event:data
data:<i class="ri-search-line"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<input type="text" placeholder="Search for services, transactions..." class="pl-10 pr-4 py-2 w-80 rounded-full bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm">
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center space-x-6">
data:

event:data
data:<div class="relative">
data:

event:data
data:<div class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
data:

event:data
data:<i class="ri-notification-3-line text-gray-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="absolute top-0 right-0 w-2 h-2 rounded-full bg-red-500"></span>
data:

event:data
data:</div>
data:

event:data
data:<div class="relative">
data:

event:data
data:<div class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
data:

event:data
data:<i class="ri-question-line text-gray-600"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center space-x-3">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium">JD</div>
data:

event:data
data:<div class="hidden md:block">
data:

event:data
data:<p class="text-sm font-medium">James Donovan</p>
data:

event:data
data:<p class="text-xs text-gray-500">Premium Customer</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-down-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Secondary Navigation -->
data:

event:data
data:<nav class="px-6 py-3 flex items-center space-x-8">
data:

event:data
data:<a href="#" class="text-primary font-medium text-sm border-b-2 border-primary pb-2">Dashboard</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Accounts</a>
data:

event:data
data:<a href="https://readdy.ai/home/<USER>/df284f75-2c96-47a8-a3c6-409025e282e0" data-readdy="true" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Payments</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Investments</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Loans</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Cards</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Insurance</a>
data:

event:data
data:</nav>
data:

event:data
data:</header>
data:

event:data
data:<main class="container mx-auto px-6 py-8">
data:

event:data
data:<!-- Welcome Banner -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 mb-8 flex justify-between items-center">
data:

event:data
data:<div>
data:

event:data
data:<h2 class="text-xl font-semibold mb-2">Welcome back, James!</h2>
data:

event:data
data:<p class="text-gray-600">Tuesday, May 20, 2025 • Last login: Today at 09:15 AM</p>
data:

event:data
data:</div>
data:

event:data
data:<button id="get-support-btn" class="bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center mr-2">
data:

event:data
data:<i class="ri-customer-service-2-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Get Support
data:

event:data
data:</button>
data:

event:data
data:
data:

event:data
data:<!-- Support Dialog Modal -->
data:

event:data
data:<div id="support-modal" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
data:

event:data
data:<div class="bg-white rounded-lg w-full max-w-2xl mx-4">
data:

event:data
data:<div class="p-6 border-b border-gray-100">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="text-xl font-semibold">Customer Support</h3>
data:

event:data
data:<button id="close-support-modal" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
data:

event:data
data:<i class="ri-close-line text-gray-500"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div class="p-6">
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
data:

event:data
data:<div class="p-4 border border-gray-100 rounded">
data:

event:data
data:<div class="flex items-center mb-3">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
data:

event:data
data:<i class="ri-message-3-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h4 class="font-medium">Live Chat</h4>
data:

event:data
data:<p class="text-sm text-green-600">Available Now</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Estimated wait time: 2 minutes</p>
data:

event:data
data:<button class="w-full bg-primary text-white py-2 rounded-button whitespace-nowrap">Start Chat</button>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div class="p-4 border border-gray-100 rounded">
data:

event:data
data:<div class="flex items-center mb-3">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
data:

event:data
data:<i class="ri-phone-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h4 class="font-medium">Phone Support</h4>
data:

event:data
data:<p class="text-sm text-green-600">Available 24/7</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Current wait time: 5-7 minutes</p>
data:

event:data
data:<button class="w-full bg-primary text-white py-2 rounded-button whitespace-nowrap">Call Now</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div class="mb-8">
data:

event:data
data:<h4 class="font-medium mb-4">Submit Support Ticket</h4>
data:

event:data
data:<form id="support-ticket-form">
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Issue Category</label>
data:

event:data
data:<div class="relative">
data:

event:data
data:<select class="w-full pl-4 pr-10 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm appearance-none">
data:

event:data
data:<option value="">Select category</option>
data:

event:data
data:<option value="account">Account Access</option>
data:

event:data
data:<option value="transaction">Transaction Issues</option>
data:

event:data
data:<option value="technical">Technical Support</option>
data:

event:data
data:<option value="security">Security Concerns</option>
data:

event:data
data:<option value="other">Other</option>
data:

event:data
data:</select>
data:

event:data
data:<div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center pointer-events-none">
data:

event:data
data:<i class="ri-arrow-down-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Description</label>
data:

event:data
data:<textarea rows="4" placeholder="Please describe your issue..." class="w-full px-4 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm"></textarea>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Attachments</label>
data:

event:data
data:<div class="relative">
data:

event:data
data:<input type="file" multiple class="hidden" id="support-attachments">
data:

event:data
data:<button type="button" onclick="document.getElementById('support-attachments').click()" class="w-full px-4 py-2 border border-gray-200 rounded-button text-gray-700 hover:bg-gray-50 text-sm flex items-center justify-center">
data:

event:data
data:<i class="ri-upload-2-line mr-2"></i>
data:

event:data
data:Upload Files
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-xs text-gray-500 mt-1">Max file size: 10MB. Supported formats: PDF, JPG, PNG</p>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Preferred Contact Method</label>
data:

event:data
data:<div class="space-y-2">
data:

event:data
data:<label class="flex items-center">
data:

event:data
data:<input type="radio" name="contact_method" value="email" class="mr-2">
data:

event:data
data:<span class="text-sm text-gray-700">Email</span>
data:

event:data
data:</label>
data:

event:data
data:<label class="flex items-center">
data:

event:data
data:<input type="radio" name="contact_method" value="phone" class="mr-2">
data:

event:data
data:<span class="text-sm text-gray-700">Phone</span>
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</form>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:
data:

event:data
data:<div class="p-6 border-t border-gray-100">
data:

event:data
data:<div class="flex justify-end space-x-3">
data:

event:data
data:<button id="cancel-support" class="px-4 py-2 border border-gray-200 rounded-button text-gray-700 hover:bg-gray-50 whitespace-nowrap">Cancel</button>
data:

event:data
data:<button id="submit-support" class="px-4 py-2 bg-primary text-white rounded-button whitespace-nowrap">Submit Ticket</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Agent Selection Panel -->
data:

event:data
data:<div class="mb-8">
data:

event:data
data:<div class="flex justify-between items-center mb-4">
data:

event:data
data:<h2 class="text-lg font-semibold">Banking Agents</h2>
data:

event:data
data:<a href="#" class="text-primary text-sm flex items-center">
data:

event:data
data:View all
data:

event:data
data:<div class="w-4 h-4 flex items-center justify-center ml-1">
data:

event:data
data:<i class="ri-arrow-right-s-line"></i>
data:

event:data
data:</div>
data:

event:data
data:</a>
data:

event:data
data:</div>
data:

event:data
data:<div class="agent-cards flex space-x-5 overflow-x-auto pb-4">
data:

event:data
data:<!-- Investment Advisor Agent -->
data:

event:data
data:<div id="agent-1" class="bg-white rounded shadow-sm p-5 min-w-[280px] border border-gray-100">
data:

event:data
data:<div class="flex items-start justify-between mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
data:

event:data
data:<i class="ri-line-chart-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Investment Advisor</h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
data:

event:data
data:<span class="text-xs text-green-600">Available now</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100">
data:

event:data
data:<i class="ri-more-2-fill text-gray-500"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-4">Get personalized investment strategies and portfolio management advice.</p>
data:

event:data
data:<button id="connect-agent-1" class="w-full bg-primary text-white py-2 rounded-button whitespace-nowrap">Connect</button>
data:

event:data
data:</div>
data:

event:data
data:<!-- Loan Specialist Agent -->
data:

event:data
data:<div id="agent-2" class="bg-white rounded shadow-sm p-5 min-w-[280px] border border-gray-100">
data:

event:data
data:<div class="flex items-start justify-between mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
data:

event:data
data:<i class="ri-bank-card-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Loan Specialist</h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
data:

event:data
data:<span class="text-xs text-green-600">Available now</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100">
data:

event:data
data:<i class="ri-more-2-fill text-gray-500"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-4">Find the best loan options and get pre-approved with personalized rates.</p>
data:

event:data
data:<button id="connect-agent-2" class="w-full bg-primary text-white py-2 rounded-button whitespace-nowrap">Connect</button>
data:

event:data
data:</div>
data:

event:data
data:<!-- Financial Planner Agent -->
data:

event:data
data:<div id="agent-3" class="bg-white rounded shadow-sm p-5 min-w-[280px] border border-gray-100">
data:

event:data
data:<div class="flex items-start justify-between mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3">
data:

event:data
data:<i class="ri-calendar-check-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Financial Planner</h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
data:

event:data
data:<span class="text-xs text-green-600">Available now</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100">
data:

event:data
data:<i class="ri-more-2-fill text-gray-500"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-4">Create comprehensive financial plans for retirement, education, and more.</p>
data:

event:data
data:<button id="connect-agent-3" class="w-full bg-primary text-white py-2 rounded-button whitespace-nowrap">Connect</button>
data:

event:data
data:</div>
data:

event:data
data:<!-- Tax Advisor Agent -->
data:

event:data
data:<div id="agent-4" class="bg-white rounded shadow-sm p-5 min-w-[280px] border border-gray-100">
data:

event:data
data:<div class="flex items-start justify-between mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 mr-3">
data:

event:data
data:<i class="ri-file-list-3-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Tax Advisor</h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span class="w-2 h-2 rounded-full bg-yellow-500 mr-2"></span>
data:

event:data
data:<span class="text-xs text-yellow-600">Busy</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100">
data:

event:data
data:<i class="ri-more-2-fill text-gray-500"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-4">Get tax optimization strategies and assistance with filing requirements.</p>
data:

event:data
data:<button id="connect-agent-4" class="w-full bg-gray-200 text-gray-500 py-2 rounded-button whitespace-nowrap">Join Queue</button>
data:

event:data
data:</div>
data:

event:data
data:<!-- Insurance Advisor Agent -->
data:

event:data
data:<div id="agent-5" class="bg-white rounded shadow-sm p-5 min-w-[280px] border border-gray-100">
data:

event:data
data:<div class="flex items-start justify-between mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center text-teal-600 mr-3">
data:

event:data
data:<i class="ri-shield-check-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Insurance Advisor</h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
data:

event:data
data:<span class="text-xs text-green-600">Available now</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100">
data:

event:data
data:<i class="ri-more-2-fill text-gray-500"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-4">Compare insurance options and find the right coverage for your needs.</p>
data:

event:data
data:<button id="connect-agent-5" class="w-full bg-primary text-white py-2 rounded-button whitespace-nowrap">Connect</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Connect Dialog Modal -->
data:

event:data
data:<div id="connect-modal" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
data:

event:data
data:<div class="bg-white rounded-lg w-full max-w-lg mx-4">
data:

event:data
data:<div class="p-6 border-b border-gray-100">
data:

event:data
data:<div class="flex justify-between items-start">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div id="modal-agent-icon" class="w-12 h-12 rounded-full flex items-center justify-center mr-4"></div>
data:

event:data
data:<div>
data:

event:data
data:<h3 id="modal-agent-name" class="text-xl font-semibold"></h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span id="modal-status-dot" class="w-2 h-2 rounded-full mr-2"></span>
data:

event:data
data:<span id="modal-status-text" class="text-sm"></span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<button id="close-modal" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
data:

event:data
data:<i class="ri-close-line text-gray-500"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-6">
data:

event:data
data:<div class="mb-6">
data:

event:data
data:<h4 class="font-medium mb-2">Expertise Areas</h4>
data:

event:data
data:<div id="modal-expertise" class="flex flex-wrap gap-2"></div>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-6">
data:

event:data
data:<h4 class="font-medium mb-2">About</h4>
data:

event:data
data:<p id="modal-description" class="text-sm text-gray-600"></p>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-6">
data:

event:data
data:<h4 class="font-medium mb-2">Response Time</h4>
data:

event:data
data:<div class="flex items-center text-sm text-gray-600">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center mr-2">
data:

event:data
data:<i class="ri-time-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span id="modal-response-time"></span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Your Message</label>
data:

event:data
data:<textarea id="initial-message" rows="3" placeholder="Type your message here..." class="w-full px-4 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm"></textarea>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-6 border-t border-gray-100">
data:

event:data
data:<div class="flex justify-end space-x-3">
data:

event:data
data:<button id="cancel-connect" class="px-4 py-2 border border-gray-200 rounded-button text-gray-700 hover:bg-gray-50 whitespace-nowrap">Cancel</button>
data:

event:data
data:<button id="start-chat" class="px-4 py-2 bg-primary text-white rounded-button whitespace-nowrap">Start Chat</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Main Dashboard Area -->
data:

event:data
data:<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
data:

event:data
data:<!-- Left Column -->
data:

event:data
data:<div class="lg:col-span-2 space-y-8">
data:

event:data
data:<!-- Quick Actions -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6">
data:

event:data
data:<h2 class="text-lg font-semibold mb-4">Quick Actions</h2>
data:

event:data
data:<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
data:

event:data
data:<button class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-bank-transfer-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Transfer</span>
data:

event:data
data:</button>
data:

event:data
data:<button id="pay-bills-btn" class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-bill-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Pay Bills</span>
data:

event:data
data:</button>
data:

event:data
data:<button class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-bank-card-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Cards</span>
data:

event:data
data:</button>
data:

event:data
data:<button id="service-request-btn" class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-customer-service-2-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Service Requests</span>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Account Summary</h2>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<div class="px-1 py-1 bg-gray-100 rounded-full flex">
data:

event:data
data:<button class="tab-button active px-4 py-1 rounded-full text-sm whitespace-nowrap">Accounts</button>
data:

event:data
data:<button class="tab-button px-4 py-1 rounded-full text-sm whitespace-nowrap">Cards</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<!-- Checking Account -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
data:

event:data
data:<i class="ri-wallet-3-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Premium Checking</h3>
data:

event:data
data:<p class="text-xs text-gray-500">**** 4582</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$24,568.92</p>
data:

event:data
data:<p class="text-xs text-green-600">+$1,250.00 today</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Savings Account -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-4">
data:

event:data
data:<i class="ri-safe-2-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">High-Yield Savings</h3>
data:

event:data
data:<p class="text-xs text-gray-500">**** 7891</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$58,423.45</p>
data:

event:data
data:<p class="text-xs text-gray-500">3.25% APY</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Investment Account -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-4">
data:

event:data
data:<i class="ri-stock-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Investment Portfolio</h3>
data:

event:data
data:<p class="text-xs text-gray-500">**** 3456</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$142,987.63</p>
data:

event:data
data:<p class="text-xs text-green-600">+2.8% this month</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="mt-6 flex justify-between">
data:

event:data
data:<button class="bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center mr-2">
data:

event:data
data:<i class="ri-add-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Add Account
data:

event:data
data:</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-4 py-2 rounded-button whitespace-nowrap">View All Accounts</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Portfolio Performance -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Portfolio Performance</h2>
data:

event:data
data:<select class="bg-white border border-gray-200 rounded text-sm px-3 py-1.5 pr-8 text-gray-700">
data:

event:data
data:<option>Last 30 days</option>
data:

event:data
data:<option>Last 90 days</option>
data:

event:data
data:<option>This year</option>
data:

event:data
data:<option>All time</option>
data:

event:data
data:</select>
data:

event:data
data:</div>
data:

event:data
data:<div id="portfolio-chart" class="w-full h-64"></div>
data:

event:data
data:<div class="grid grid-cols-3 gap-4 mt-6">
data:

event:data
data:<div class="text-center">
data:

event:data
data:<p class="text-sm text-gray-500">Total Value</p>
data:

event:data
data:<p class="font-semibold text-lg">$142,987.63</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-center">
data:

event:data
data:<p class="text-sm text-gray-500">Return</p>
data:

event:data
data:<p class="font-semibold text-lg text-green-600">+$4,235.89</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-center">
data:

event:data
data:<p class="text-sm text-gray-500">YTD Performance</p>
data:

event:data
data:<p class="font-semibold text-lg text-green-600">+8.2%</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Recent Activity -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Recent Activity</h2>
data:

event:data
data:<a href="#" class="text-primary text-sm">View All</a>
data:

event:data
data:</div>
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<!-- Transaction 1 -->
data:

event:data
data:<div class="flex justify-between items-center pb-4 border-b border-gray-100">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
data:

event:data
data:<i class="ri-arrow-down-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Salary Deposit</h3>
data:

event:data
data:<p class="text-xs text-gray-500">Today, 09:15 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="font-semibold text-green-600">+$5,280.00</p>
data:

event:data
data:</div>
data:

event:data
data:<!-- Transaction 2 -->
data:

event:data
data:<div class="flex justify-between items-center pb-4 border-b border-gray-100">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mr-4">
data:

event:data
data:<i class="ri-arrow-up-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Whole Foods Market</h3>
data:

event:data
data:<p class="text-xs text-gray-500">Yesterday, 06:42 PM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="font-semibold text-red-600">-$128.45</p>
data:

event:data
data:</div>
data:

event:data
data:<!-- Transaction 3 -->
data:

event:data
data:<div class="flex justify-between items-center pb-4 border-b border-gray-100">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mr-4">
data:

event:data
data:<i class="ri-arrow-up-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Amazon.com</h3>
data:

event:data
data:<p class="text-xs text-gray-500">May 19, 2025, 11:23 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="font-semibold text-red-600">-$87.32</p>
data:

event:data
data:</div>
data:

event:data
data:<!-- Transaction 4 -->
data:

event:data
data:<div class="flex justify-between items-center pb-4 border-b border-gray-100">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 mr-4">
data:

event:data
data:<i class="ri-exchange-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Transfer to Savings</h3>
data:

event:data
data:<p class="text-xs text-gray-500">May 18, 2025, 03:45 PM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="font-semibold text-red-600">-$1,000.00</p>
data:

event:data
data:</div>
data:

event:data
data:<!-- Transaction 5 -->
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mr-4">
data:

event:data
data:<i class="ri-arrow-up-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Starbucks Coffee</h3>
data:

event:data
data:<p class="text-xs text-gray-500">May 18, 2025, 08:12 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="font-semibold text-red-600">-$6.45</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Right Column -->
data:

event:data
data:<div class="space-y-8">
data:

event:data
data:<!-- Account Summary -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Account Summary</h2>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<div class="px-1 py-1 bg-gray-100 rounded-full flex">
data:

event:data
data:<button class="tab-button active px-4 py-1 rounded-full text-sm whitespace-nowrap">Accounts</button>
data:

event:data
data:<button class="tab-button px-4 py-1 rounded-full text-sm whitespace-nowrap">Cards</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<!-- Checking Account -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
data:

event:data
data:<i class="ri-wallet-3-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Premium Checking</h3>
data:

event:data
data:<p class="text-xs text-gray-500">**** 4582</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$24,568.92</p>
data:

event:data
data:<p class="text-xs text-green-600">+$1,250.00 today</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Savings Account -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-4">
data:

event:data
data:<i class="ri-safe-2-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">High-Yield Savings</h3>
data:

event:data
data:<p class="text-xs text-gray-500">**** 7891</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$58,423.45</p>
data:

event:data
data:<p class="text-xs text-gray-500">3.25% APY</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Investment Account -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-4">
data:

event:data
data:<i class="ri-stock-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Investment Portfolio</h3>
data:

event:data
data:<p class="text-xs text-gray-500">**** 3456</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$142,987.63</p>
data:

event:data
data:<p class="text-xs text-green-600">+2.8% this month</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="mt-6 flex justify-between">
data:

event:data
data:<button class="bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center mr-2">
data:

event:data
data:<i class="ri-add-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Add Account
data:

event:data
data:</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-4 py-2 rounded-button whitespace-nowrap">View All Accounts</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<h2 class="text-lg font-semibold mb-4">Quick Actions</h2>
data:

event:data
data:<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
data:

event:data
data:<button class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-bank-transfer-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Transfer</span>
data:

event:data
data:</button>
data:

event:data
data:<button id="pay-bills-btn" class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-bill-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Pay Bills</span>
data:

event:data
data:</button>
data:

event:data
data:<button class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-bank-card-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Cards</span>
data:

event:data
data:</button>
data:

event:data
data:<button id="service-request-btn" class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center text-primary mb-2">
data:

event:data
data:<i class="ri-customer-service-2-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-700">Service Requests</span>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Agent Interaction -->
data:

event:data
data:<div class="bg-white rounded shadow-sm overflow-hidden">
data:

event:data
data:<div class="bg-primary p-4 flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white mr-3">
data:

event:data
data:<i class="ri-line-chart-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-white font-medium">Investment Advisor</h3>
data:

event:data
data:<div class="flex items-center mt-1">
data:

event:data
data:<span class="w-2 h-2 rounded-full bg-green-400 mr-2"></span>
data:

event:data
data:<span class="text-xs text-white/80">Active conversation</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="w-8 h-8 flex items-center justify-center rounded-full bg-white/10 text-white">
data:

event:data
data:<i class="ri-file-list-line"></i>
data:

event:data
data:</button>
data:

event:data
data:<button class="w-8 h-8 flex items-center justify-center rounded-full bg-white/10 text-white">
data:

event:data
data:<i class="ri-close-line"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="h-80 p-4 overflow-y-auto border-b border-gray-100">
data:

event:data
data:<!-- Message 1 -->
data:

event:data
data:<div class="flex mb-4">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3 flex-shrink-0">
data:

event:data
data:<i class="ri-robot-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="bg-gray-100 rounded-lg rounded-tl-none p-3 max-w-[80%]">
data:

event:data
data:<p class="text-sm">Hello James! I've analyzed your portfolio and noticed that your tech stocks are overperforming. Would you like to discuss rebalancing options?</p>
data:

event:data
data:<p class="text-xs text-gray-500 mt-1">10:15 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Message 2 -->
data:

event:data
data:<div class="flex justify-end mb-4">
data:

event:data
data:<div class="bg-primary text-white rounded-lg rounded-tr-none p-3 max-w-[80%]">
data:

event:data
data:<p class="text-sm">Yes, I'd like to see what options are available. I'm particularly interested in diversifying more into renewable energy.</p>
data:

event:data
data:<p class="text-xs text-white/80 mt-1">10:18 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Message 3 -->
data:

event:data
data:<div class="flex mb-4">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3 flex-shrink-0">
data:

event:data
data:<i class="ri-robot-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="bg-gray-100 rounded-lg rounded-tl-none p-3 max-w-[80%]">
data:

event:data
data:<p class="text-sm">Great choice! Renewable energy has shown strong growth potential. Based on your risk profile and goals, I recommend considering these options:</p>
data:

event:data
data:<ul class="text-sm list-disc pl-5 mt-2">
data:

event:data
data:<li>Clean Energy ETF (ICLN) - Low risk, moderate returns</li>
data:

event:data
data:<li>NextEra Energy (NEE) - Established utility with renewable focus</li>
data:

event:data
data:<li>Brookfield Renewable (BEP) - Global diversification in renewables</li>
data:

event:data
data:</ul>
data:

event:data
data:<p class="text-xs text-gray-500 mt-1">10:20 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Message 4 -->
data:

event:data
data:<div class="flex justify-end mb-4">
data:

event:data
data:<div class="bg-primary text-white rounded-lg rounded-tr-none p-3 max-w-[80%]">
data:

event:data
data:<p class="text-sm">I like the sound of the Clean Energy ETF. What percentage of my portfolio would you recommend allocating to this?</p>
data:

event:data
data:<p class="text-xs text-white/80 mt-1">10:22 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Message 5 -->
data:

event:data
data:<div class="flex">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3 flex-shrink-0">
data:

event:data
data:<i class="ri-robot-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="bg-gray-100 rounded-lg rounded-tl-none p-3 max-w-[80%]">
data:

event:data
data:<p class="text-sm">Based on your current portfolio and risk tolerance, I would recommend allocating 15-20% to the Clean Energy ETF. This provides good exposure while maintaining diversification. Would you like me to prepare a detailed rebalancing plan?</p>

event:data
data:
data:

event:data
data:<p class="text-xs text-gray-500 mt-1">10:24 AM</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<div class="relative">
data:

event:data
data:<input type="text" placeholder="Type your message..." class="w-full pl-4 pr-12 py-3 rounded-full bg-gray-50 border-none focus:ring-2 focus:ring-primary/20">
data:

event:data
data:<button class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white">
data:

event:data
data:<i class="ri-send-plane-fill"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Personalized Offers -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6">
data:

event:data
data:<h2 class="text-lg font-semibold mb-4">Personalized Offers</h2>
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<!-- Offer 1 -->
data:

event:data
data:<div class="border border-gray-100 rounded overflow-hidden">
data:

event:data
data:<div class="bg-blue-50 p-4">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="font-medium text-blue-800">Premium Credit Card</h3>
data:

event:data
data:<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Pre-approved</span>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-blue-700 mt-1">3% cashback on all purchases</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Based on your spending habits, you could earn up to $750 in cashback annually.</p>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Apply Now</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Learn More</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Offer 2 -->
data:

event:data
data:<div class="border border-gray-100 rounded overflow-hidden">
data:

event:data
data:<div class="bg-green-50 p-4">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="font-medium text-green-800">Home Equity Line of Credit</h3>
data:

event:data
data:<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">5.25% APR</span>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-green-700 mt-1">Up to $150,000 available</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Tap into your home's equity with our competitive rates and flexible terms.</p>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Check Rate</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Learn More</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Financial Goals -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6">
data:

event:data
data:<div class="flex justify-between items-center mb-4">
data:

event:data
data:<h2 class="text-lg font-semibold">Financial Goals</h2>
data:

event:data
data:<button class="text-primary text-sm flex items-center">
data:

event:data
data:<div class="w-4 h-4 flex items-center justify-center mr-1">
data:

event:data
data:<i class="ri-add-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Add Goal
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<!-- Goal 1 -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4">
data:

event:data
data:<div class="flex justify-between items-center mb-2">
data:

event:data
data:<h3 class="font-medium">Vacation Fund</h3>
data:

event:data
data:<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">On Track</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-2">
data:

event:data
data:<div class="flex justify-between text-sm mb-1">
data:

event:data
data:<span class="text-gray-600">$3,500 of $5,000</span>
data:

event:data
data:<span class="text-gray-600">70%</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-full bg-gray-200 rounded-full h-2">
data:

event:data
data:<div class="bg-blue-500 h-2 rounded-full" style="width: 70%"></div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-xs text-gray-500">Target date: August 15, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<!-- Goal 2 -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4">
data:

event:data
data:<div class="flex justify-between items-center mb-2">
data:

event:data
data:<h3 class="font-medium">Emergency Fund</h3>
data:

event:data
data:<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Completed</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-2">
data:

event:data
data:<div class="flex justify-between text-sm mb-1">
data:

event:data
data:<span class="text-gray-600">$15,000 of $15,000</span>
data:

event:data
data:<span class="text-gray-600">100%</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-full bg-gray-200 rounded-full h-2">
data:

event:data
data:<div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-xs text-gray-500">Completed on: April 10, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<!-- Goal 3 -->
data:

event:data
data:<div class="border border-gray-100 rounded p-4">
data:

event:data
data:<div class="flex justify-between items-center mb-2">
data:

event:data
data:<h3 class="font-medium">New Car</h3>
data:

event:data
data:<span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Needs Attention</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-2">
data:

event:data
data:<div class="flex justify-between text-sm mb-1">
data:

event:data
data:<span class="text-gray-600">$8,200 of $35,000</span>
data:

event:data
data:<span class="text-gray-600">23%</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-full bg-gray-200 rounded-full h-2">
data:

event:data
data:<div class="bg-yellow-500 h-2 rounded-full" style="width: 23%"></div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-xs text-gray-500">Target date: December 31, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</main>
data:

event:data
data:<!-- Footer -->
data:

event:data
data:<footer class="bg-white border-t border-gray-100 mt-12">
data:

event:data
data:<div class="container mx-auto px-6 py-8">
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">Banking</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Checking Accounts</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Savings Accounts</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Credit Cards</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Loans</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Mortgages</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">Investments</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Retirement Planning</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Brokerage Accounts</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Wealth Management</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Financial Advisors</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Research & Insights</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">Support</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Help Center</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Contact Us</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Security Center</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Fraud Prevention</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Report an Issue</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">About Us</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Our Story</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Careers</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Investor Relations</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Community Impact</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">News & Press</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="border-t border-gray-100 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
data:

event:data
data:<div class="mb-4 md:mb-0">
data:

event:data
data:<p class="text-sm text-gray-500">© 2025 Banking Platform. All rights reserved.</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-6">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400 mr-2">
data:

event:data
data:<i class="ri-shield-check-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-500">Secure Banking</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400 mr-2">
data:

event:data
data:<i class="ri-lock-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-500">256-bit Encryption</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400 mr-2">
data:

event:data
data:<i class="ri-time-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-500">24/7 Monitoring</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-4 mt-4 md:mt-0">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-facebook-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-twitter-x-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-linkedin-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-instagram-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</footer>
data:

event:data
data:<!-- Pay Bills Modal -->
data:

event:data
data:<div id="pay-bills-modal" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
data:

event:data
data:<div class="bg-white rounded-lg w-full max-w-2xl mx-4">
data:

event:data
data:<div class="p-6 border-b border-gray-100">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="text-xl font-semibold">Pay Bills</h3>
data:

event:data
data:<button id="close-bills-modal" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
data:

event:data
data:<i class="ri-close-line text-gray-500"></i>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-6">
data:

event:data
data:<div class="mb-6">
data:

event:data
data:<h4 class="font-medium mb-4">Recent Bills</h4>
data:

event:data
data:<div class="space-y-3">
data:

event:data
data:<div class="flex items-center justify-between p-4 border border-gray-100 rounded">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
data:

event:data
data:<i class="ri-flashlight-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h5 class="font-medium">Electric Company</h5>
data:

event:data
data:<p class="text-sm text-gray-500">Due in 5 days</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$124.50</p>
data:

event:data
data:<button class="text-primary text-sm">Pay Now</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center justify-between p-4 border border-gray-100 rounded">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-cyan-100 flex items-center justify-center text-cyan-600 mr-4">
data:

event:data
data:<i class="ri-water-flash-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h5 class="font-medium">Water Utility</h5>
data:

event:data
data:<p class="text-sm text-gray-500">Due in 12 days</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$78.25</p>
data:

event:data
data:<button class="text-primary text-sm">Pay Now</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center justify-between p-4 border border-gray-100 rounded">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center text-orange-600 mr-4">
data:

event:data
data:<i class="ri-wifi-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h5 class="font-medium">Internet Service</h5>
data:

event:data
data:<p class="text-sm text-gray-500">Due in 8 days</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="text-right">
data:

event:data
data:<p class="font-semibold">$89.99</p>
data:

event:data
data:<button class="text-primary text-sm">Pay Now</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-6">
data:

event:data
data:<h4 class="font-medium mb-4">Pay New Bill</h4>
data:

event:data
data:<form id="pay-bill-form">
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Biller / Service Provider</label>
data:

event:data
data:<div class="relative">
data:

event:data
data:<select class="w-full pl-4 pr-10 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm appearance-none">
data:

event:data
data:<option value="">Select provider</option>
data:

event:data
data:<option value="electric">Electric Company</option>
data:

event:data
data:<option value="water">Water Utility</option>
data:

event:data
data:<option value="internet">Internet Service</option>
data:

event:data
data:<option value="phone">Phone Company</option>
data:

event:data
data:<option value="gas">Gas Utility</option>
data:

event:data
data:<option value="other">Other</option>
data:

event:data
data:</select>
data:

event:data
data:<div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center pointer-events-none">
data:

event:data
data:<i class="ri-arrow-down-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Account Number</label>
data:

event:data
data:<input type="text" placeholder="Enter account number" class="w-full px-4 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm">
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Amount</label>
data:

event:data
data:<div class="relative">
data:

event:data
data:<div class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500">$</div>
data:

event:data
data:<input type="number" placeholder="0.00" class="w-full pl-8 pr-4 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm">
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label class="block text-sm font-medium mb-2">Due Date</label>
data:

event:data
data:<input type="date" class="w-full px-4 py-2 rounded bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm">
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-4">
data:

event:data
data:<label class="block text-sm font-medium mb-2">Payment Method</label>
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-3 gap-3">
data:

event:data
data:<label class="relative flex items-center p-4 border border-gray-200 rounded cursor-pointer hover:bg-gray-50">
data:

event:data
data:<input type="radio" name="payment" value="checking" class="absolute opacity-0">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
data:

event:data
data:<i class="ri-bank-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="font-medium text-sm">Checking</p>
data:

event:data
data:<p class="text-xs text-gray-500">**** 4582</p>
data:

event:data
data:</div>
data:

event:data
data:</label>
data:

event:data
data:<label class="relative flex items-center p-4 border border-gray-200 rounded cursor-pointer hover:bg-gray-50">
data:

event:data
data:<input type="radio" name="payment" value="savings" class="absolute opacity-0">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
data:

event:data
data:<i class="ri-safe-2-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="font-medium text-sm">Savings</p>
data:

event:data
data:<p class="text-xs text-gray-500">**** 7891</p>
data:

event:data
data:</div>
data:

event:data
data:</label>
data:

event:data
data:<label class="relative flex items-center p-4 border border-gray-200 rounded cursor-pointer hover:bg-gray-50">
data:

event:data
data:<input type="radio" name="payment" value="credit" class="absolute opacity-0">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3">
data:

event:data
data:<i class="ri-bank-card-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="font-medium text-sm">Credit Card</p>
data:

event:data
data:<p class="text-xs text-gray-500">**** 6789</p>
data:

event:data
data:</div>
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<label class="flex items-center mb-4">
data:

event:data
data:<input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
data:

event:data
data:<span class="ml-2 text-sm text-gray-600">Set up automatic payments for this bill</span>
data:

event:data
data:</label>
data:

event:data
data:</div>
data:

event:data
data:</form>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-6 border-t border-gray-100">
data:

event:data
data:<div class="flex justify-end space-x-3">
data:

event:data
data:<button id="cancel-payment" class="px-4 py-2 border border-gray-200 rounded-button text-gray-700 hover:bg-gray-50 whitespace-nowrap">Cancel</button>
data:

event:data
data:<button id="submit-payment" class="px-4 py-2 bg-primary text-white rounded-button whitespace-nowrap">Schedule Payment</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<script>
data:

event:data
data:document.addEventListener('DOMContentLoaded', function() {
data:

event:data
data:const connectModal = document.getElementById('connect-modal');
data:

event:data
data:const payBillsModal = document.getElementById('pay-bills-modal');
data:

event:data
data:const supportModal = document.getElementById('support-modal');
data:

event:data
data:const getSupportBtn = document.getElementById('get-support-btn');
data:

event:data
data:const closeSupportModal = document.getElementById('close-support-modal');
data:

event:data
data:const cancelSupport = document.getElementById('cancel-support');
data:

event:data
data:const submitSupport = document.getElementById('submit-support');
data:

event:data
data:const payBillsBtn = document.getElementById('pay-bills-btn');
data:

event:data
data:const closeBillsModal = document.getElementById('close-bills-modal');
data:

event:data
data:const cancelPayment = document.getElementById('cancel-payment');
data:

event:data
data:
data:

event:data
data:function openSupportModal() {
data:

event:data
data:  supportModal.classList.remove('hidden');
data:

event:data
data:}
data:

event:data
data:
data:

event:data
data:function closeSupportModal() {
data:

event:data
data:  supportModal.classList.add('hidden');
data:

event:data
data:}
data:

event:data
data:
data:

event:data
data:getSupportBtn.addEventListener('click', openSupportModal);
data:

event:data
data:closeSupportModal.addEventListener('click', closeSupportModal);
data:

event:data
data:cancelSupport.addEventListener('click', closeSupportModal);
data:

event:data
data:
data:

event:data
data:submitSupport.addEventListener('click', function() {
data:

event:data
data:  const form = document.getElementById('support-ticket-form');
data:

event:data
data:  const formData = new FormData(form);
data:

event:data
data:  // Here you would typically implement the ticket submission logic
data:

event:data
data:  closeSupportModal();
data:

event:data
data:});
data:

event:data
data:const submitPayment = document.getElementById('submit-payment');
data:

event:data
data:const closeModal = document.getElementById('close-modal');
data:

event:data
data:function openPayBillsModal() {
data:

event:data
data:payBillsModal.classList.remove('hidden');
data:

event:data
data:}
data:

event:data
data:function closePayBillsModal() {
data:

event:data
data:payBillsModal.classList.add('hidden');
data:

event:data
data:}
data:

event:data
data:payBillsBtn.addEventListener('click', openPayBillsModal);
data:

event:data
data:closeBillsModal.addEventListener('click', closePayBillsModal);
data:

event:data
data:cancelPayment.addEventListener('click', closePayBillsModal);
data:

event:data
data:submitPayment.addEventListener('click', function() {
data:

event:data
data:const form = document.getElementById('pay-bill-form');
data:

event:data
data:const formData = new FormData(form);
data:

event:data
data:// Here you would typically implement the payment processing logic
data:

event:data
data:// For demo purposes, we'll just close the modal
data:

event:data
data:closePayBillsModal();
data:

event:data
data:});
data:

event:data
data:// Style the payment method radio buttons
data:

event:data
data:const paymentMethods = document.querySelectorAll('input[name="payment"]');
data:

event:data
data:paymentMethods.forEach(method => {
data:

event:data
data:method.addEventListener('change', function() {
data:

event:data
data:// Remove selected style from all labels
data:

event:data
data:paymentMethods.forEach(m => {
data:

event:data
data:m.closest('label').classList.remove('border-primary', 'bg-primary/5');
data:

event:data
data:});
data:

event:data
data:// Add selected style to checked label
data:

event:data
data:if (this.checked) {
data:

event:data
data:this.closest('label').classList.add('border-primary', 'bg-primary/5');
data:

event:data
data:}
data:

event:data
data:});
data:

event:data
data:});
data:

event:data
data:const cancelConnect = document.getElementById('cancel-connect');
data:

event:data
data:const startChat = document.getElementById('start-chat');
data:

event:data
data:const initialMessage = document.getElementById('initial-message');
data:

event:data
data:const agentData = {
data:

event:data
data:'agent-1': {
data:

event:data
data:name: 'Investment Advisor',
data:

event:data
data:icon: 'ri-line-chart-line',
data:

event:data
data:iconBg: 'bg-blue-100',
data:

event:data
data:iconColor: 'text-blue-600',
data:

event:data
data:status: 'Available now',
data:

event:data
data:statusColor: 'bg-green-500',
data:

event:data
data:statusTextColor: 'text-green-600',
data:

event:data
data:expertise: ['Portfolio Management', 'Stock Analysis', 'ETFs', 'Retirement Planning'],
data:

event:data
data:description: 'Specialized in creating personalized investment strategies with over 10 years of experience in portfolio management and market analysis.',
data:

event:data
data:responseTime: 'Usually responds within 2 minutes'
data:

event:data
data:},
data:

event:data
data:'agent-2': {
data:

event:data
data:name: 'Loan Specialist',
data:

event:data
data:icon: 'ri-bank-card-line',
data:

event:data
data:iconBg: 'bg-green-100',
data:

event:data
data:iconColor: 'text-green-600',
data:

event:data
data:status: 'Available now',
data:

event:data
data:statusColor: 'bg-green-500',
data:

event:data
data:statusTextColor: 'text-green-600',
data:

event:data
data:expertise: ['Personal Loans', 'Mortgages', 'Business Loans', 'Debt Consolidation'],
data:

event:data
data:description: 'Expert in loan solutions with deep knowledge of lending products and credit assessment.',
data:

event:data
data:responseTime: 'Usually responds within 5 minutes'
data:

event:data
data:},
data:

event:data
data:'agent-3': {
data:

event:data
data:name: 'Financial Planner',
data:

event:data
data:icon: 'ri-calendar-check-line',
data:

event:data
data:iconBg: 'bg-purple-100',
data:

event:data
data:iconColor: 'text-purple-600',
data:

event:data
data:status: 'Available now',
data:

event:data
data:statusColor: 'bg-green-500',
data:

event:data
data:statusTextColor: 'text-green-600',
data:

event:data
data:expertise: ['Financial Planning', 'Retirement', 'Estate Planning', 'Tax Strategy'],
data:

event:data
data:description: 'Certified Financial Planner helping clients achieve their long-term financial goals through comprehensive planning.',
data:

event:data
data:responseTime: 'Usually responds within 3 minutes'
data:

event:data
data:},
data:

event:data
data:'agent-4': {
data:

event:data
data:name: 'Tax Advisor',
data:

event:data
data:icon: 'ri-file-list-3-line',
data:

event:data
data:iconBg: 'bg-amber-100',
data:

event:data
data:iconColor: 'text-amber-600',
data:

event:data
data:status: 'Busy',
data:

event:data
data:statusColor: 'bg-yellow-500',
data:

event:data
data:statusTextColor: 'text-yellow-600',
data:

event:data
data:expertise: ['Tax Planning', 'Tax Returns', 'Business Tax', 'International Tax'],
data:

event:data
data:description: 'Experienced tax professional providing strategic tax planning and compliance services.',
data:

event:data
data:responseTime: 'Currently unavailable - Join queue for assistance'
data:

event:data
data:},
data:

event:data
data:'agent-5': {
data:

event:data
data:name: 'Insurance Advisor',
data:

event:data
data:icon: 'ri-shield-check-line',
data:

event:data
data:iconBg: 'bg-teal-100',
data:

event:data
data:iconColor: 'text-teal-600',
data:

event:data
data:status: 'Available now',
data:

event:data
data:statusColor: 'bg-green-500',
data:

event:data
data:statusTextColor: 'text-green-600',
data:

event:data
data:expertise: ['Life Insurance', 'Health Insurance', 'Property Insurance', 'Risk Assessment'],
data:

event:data
data:description: 'Insurance specialist helping clients find the right coverage for their needs and budget.',
data:

event:data
data:responseTime: 'Usually responds within 4 minutes'
data:

event:data
data:}
data:

event:data
data:};
data:

event:data
data:function openConnectModal(agentId) {
data:

event:data
data:const agent = agentData[agentId];
data:

event:data
data:document.getElementById('modal-agent-name').textContent = agent.name;
data:

event:data
data:document.getElementById('modal-agent-icon').className = `w-12 h-12 rounded-full ${agent.iconBg} ${agent.iconColor} flex items-center justify-center`;
data:

event:data
data:document.getElementById('modal-agent-icon').innerHTML = `<i class="${agent.icon} ri-lg"></i>`;
data:

event:data
data:document.getElementById('modal-status-dot').className = `w-2 h-2 rounded-full ${agent.statusColor} mr-2`;
data:

event:data
data:document.getElementById('modal-status-text').className = `text-sm ${agent.statusTextColor}`;
data:

event:data
data:document.getElementById('modal-status-text').textContent = agent.status;
data:

event:data
data:const expertiseContainer = document.getElementById('modal-expertise');
data:

event:data
data:expertiseContainer.innerHTML = agent.expertise.map(exp =>
data:

event:data
data:`<span class="px-3 py-1 rounded-full bg-gray-100 text-gray-700 text-sm">${exp}</span>`
data:

event:data
data:).join('');
data:

event:data
data:document.getElementById('modal-description').textContent = agent.description;
data:

event:data
data:document.getElementById('modal-response-time').textContent = agent.responseTime;
data:

event:data
data:initialMessage.value = '';
data:

event:data
data:connectModal.classList.remove('hidden');
data:

event:data
data:}
data:

event:data
data:function closeConnectModal() {
data:

event:data
data:connectModal.classList.add('hidden');
data:

event:data
data:}
data:

event:data
data:document.querySelectorAll('[id^="connect-agent-"]').forEach(button => {
data:

event:data
data:button.addEventListener('click', () => {
data:

event:data
data:const agentId = button.closest('[id^="agent-"]').id;
data:

event:data
data:openConnectModal(agentId);
data:

event:data
data:});
data:

event:data
data:});
data:

event:data
data:closeModal.addEventListener('click', closeConnectModal);
data:

event:data
data:cancelConnect.addEventListener('click', closeConnectModal);
data:

event:data
data:startChat.addEventListener('click', () => {
data:

event:data
data:if (initialMessage.value.trim()) {
data:

event:data
data:// Here you would typically implement the chat initiation logic
data:

event:data
data:closeConnectModal();
data:

event:data
data:// For demo purposes, we'll just close the modal
data:

event:data
data:} else {
data:

event:data
data:initialMessage.classList.add('ring-2', 'ring-red-500');
data:

event:data
data:setTimeout(() => {
data:

event:data
data:initialMessage.classList.remove('ring-2', 'ring-red-500');
data:

event:data
data:}, 2000);
data:

event:data
data:}
data:

event:data
data:});
data:

event:data
data:// Portfolio Chart
data:

event:data
data:const portfolioChart = document.getElementById('portfolio-chart');
data:

event:data
data:const portfolioChartInstance = echarts.init(portfolioChart);
data:

event:data
data:const portfolioOption = {
data:

event:data
data:animation: false,
data:

event:data
data:tooltip: {
data:

event:data
data:trigger: 'axis',
data:

event:data
data:backgroundColor: 'rgba(255, 255, 255, 0.9)',
data:

event:data
data:borderColor: '#f0f0f0',
data:

event:data
data:textStyle: {
data:

event:data
data:color: '#1f2937'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:grid: {
data:

event:data
data:top: 10,
data:

event:data
data:right: 0,
data:

event:data
data:bottom: 30,
data:

event:data
data:left: 0,
data:

event:data
data:containLabel: true
data:

event:data
data:},
data:

event:data
data:xAxis: {
data:

event:data
data:type: 'category',
data:

event:data
data:data: ['Apr 20', 'Apr 27', 'May 4', 'May 11', 'May 18', 'May 25'],
data:

event:data
data:axisLine: {
data:

event:data
data:lineStyle: {
data:

event:data
data:color: '#e5e7eb'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:axisLabel: {
data:

event:data
data:color: '#6b7280'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:yAxis: {
data:

event:data
data:type: 'value',
data:

event:data
data:axisLine: {
data:

event:data
data:show: false
data:

event:data
data:},
data:

event:data
data:axisLabel: {
data:

event:data
data:color: '#6b7280'
data:

event:data
data:},
data:

event:data
data:splitLine: {
data:

event:data
data:lineStyle: {
data:

event:data
data:color: '#f3f4f6'
data:

event:data
data:}
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:series: [
data:

event:data
data:{
data:

event:data
data:name: 'Portfolio Value',
data:

event:data
data:type: 'line',
data:

event:data
data:smooth: true,
data:

event:data
data:symbol: 'none',
data:

event:data
data:lineStyle: {
data:

event:data
data:width: 3,
data:

event:data
data:color: 'rgba(87, 181, 231, 1)'
data:

event:data
data:},
data:

event:data
data:areaStyle: {
data:

event:data
data:color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
data:

event:data
data:{
data:

event:data
data:offset: 0,
data:

event:data
data:color: 'rgba(87, 181, 231, 0.2)'
data:

event:data
data:},
data:

event:data
data:{
data:

event:data
data:offset: 1,
data:

event:data
data:color: 'rgba(87, 181, 231, 0.05)'
data:

event:data
data:}
data:

event:data
data:])
data:

event:data
data:},
data:

event:data
data:data: [135800, 138200, 137500, 139800, 141200, 142987]
data:

event:data
data:}
data:

event:data
data:]
data:

event:data
data:};
data:

event:data
data:portfolioChartInstance.setOption(portfolioOption);
data:

event:data
data:// Responsive chart resize
data:

event:data
data:window.addEventListener('resize', function() {
data:

event:data
data:portfolioChartInstance.resize();
data:

event:data
data:});
data:

event:data
data:// Tab Switching
data:

event:data
data:const tabButtons = document.querySelectorAll('.tab-button');
data:

event:data
data:tabButtons.forEach(button => {
data:

event:data
data:button.addEventListener('click', function() {
data:

event:data
data:// Remove active class from all buttons
data:

event:data
data:tabButtons.forEach(btn => {
data:

event:data
data:btn.classList.remove('active');
data:

event:data
data:});
data:

event:data
data:// Add active class to clicked button
data:

event:data
data:this.classList.add('active');
data:

event:data
data:});
data:

event:data
data:});
data:

event:data
data:});
data:

event:data
data:</script>
data:

event:data
data:</body>
data:

event:data
data:</html>

event:endMsg
data:
data:

event:endMsg
data:
data:

event:endMsg
data:I've added a support dialog modal with various support options and a ticket submission form. The modal includes:
data:

event:endMsg
data:
data:

event:endMsg
data:1. Quick access options for live chat and phone support
data:

event:endMsg
data:2. A support ticket form with:
data:

event:endMsg
data:   - Issue category selection
data:

event:endMsg
data:   - Description field
data:

event:endMsg
data:   - File attachment option
data:

event:endMsg
data:   - Contact method preference
data:

event:endMsg
data:3. Estimated wait times for different support channels
data:

event:endMsg
data:
data:

event:endMsg
data:The modal follows the same design patterns as the existing modals and integrates seamlessly with the current UI. Would you like me to add any additional features or make any adjustments?

event:response
data:{"record_id":2300392,"request_id":"a2e8b8d4-6810-49b5-9c92-c3716f98b0bc","error":null}

