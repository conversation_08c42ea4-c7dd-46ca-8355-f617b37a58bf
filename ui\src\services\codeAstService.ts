const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

export interface CodeParseResult {
  success: boolean;
  ast?: any;
  metadata?: {
    imports: Array<{
      source: string;
      specifiers: Array<{
        type: string;
        imported?: string;
        local: string;
      }>;
    }>;
    exports: Array<{
      type: string;
      declaration?: string;
    }>;
  };
  language: string;
  error?: string;
}

export interface ASTMetadata {
  type: string;
  language: string;
  metadata: any;
  transformationsApplied: string[];
}

export interface FormattedCodeEvent {
  type: string;
  code: string;
}

export interface TransformationHook {
  type: string;
  config?: Record<string, any>;
}

export interface StreamingOptions {
  streamTokens?: boolean;
  tokenBatchSize?: number;
  tokenBatchTimeout?: number;
}

export interface CodeGenerationOptions extends StreamingOptions {
  language?: string;
  transformations?: TransformationHook[];
  format?: boolean;
  parseAST?: boolean;
}

class CodeASTService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/llm/v2`;
  }

  /**
   * Generate plan using SSE
   */
  generatePlan(
    prompt: string,
    provider: string = '',
    options: StreamingOptions = {},
    onToken?: (token: string) => void,
    onError?: (error: string) => void,
    onComplete?: () => void
  ): EventSource {
    // Convert the request data to URL params
    const params = new URLSearchParams({
      prompt,
      provider,
      ...Object.entries(options).reduce((acc, [key, value]) => {
        acc[key] = String(value);
        return acc;
      }, {} as Record<string, string>)
    });

    // Create event source with query parameters
    const eventSource = new EventSource(
      `${this.baseUrl}/plan?${params.toString()}`,
      { withCredentials: true }
    );

    // Add event listeners
    eventSource.addEventListener('startMsg', (event: MessageEvent) => {
      onToken?.(event.data);
    });

    eventSource.addEventListener('data', (event: MessageEvent) => {
      onToken?.(event.data);
    });

    eventSource.addEventListener('error', (event: Event) => {
      console.error('SSE error:', event);
      onError?.('Connection error');
      eventSource.close();
    });

    eventSource.addEventListener('end', () => {
      onComplete?.();
      eventSource.close();
    });

    return eventSource;
  }

  /**
   * Generate code with AST processing using SSE
   */
  generateCodeWithAST(
    plan: string,
    provider: string = 'openai',
    options: CodeGenerationOptions = {},
    onToken?: (token: string) => void,
    onASTMetadata?: (metadata: ASTMetadata) => void,
    onFormattedCode?: (event: FormattedCodeEvent) => void,
    onError?: (error: string) => void,
    onComplete?: () => void
  ): EventSource {
    // Convert the request data to URL params
    const params = new URLSearchParams({
      plan: plan,
      provider: provider,
      ...Object.entries(options).reduce((acc, [key, value]) => {
        // Handle nested objects by stringifying them
        if (typeof value === 'object') {
          acc[key] = JSON.stringify(value);
        } else {
          acc[key] = String(value);
        }
        return acc;
      }, {} as Record<string, string>)
    });

    // Create event source with query parameters
    const eventSource = new EventSource(
      `${this.baseUrl}/generate-with-ast?${params.toString()}`,
      { withCredentials: true }
    );

    // Add event listeners
    eventSource.addEventListener('startMsg', (event: MessageEvent) => {
      onToken?.(event.data);
    });

    eventSource.addEventListener('data', (event: MessageEvent) => {
      onToken?.(event.data);
    });

    eventSource.addEventListener('ast_metadata', (event: MessageEvent) => {
      try {
        onASTMetadata?.(JSON.parse(event.data));
      } catch (error) {
        console.error('Error parsing AST metadata:', error);
      }
    });

    eventSource.addEventListener('formatted_code', (event: MessageEvent) => {
      try {
        onFormattedCode?.(JSON.parse(event.data));
      } catch (error) {
        console.error('Error parsing formatted code:', error);
      }
    });

    eventSource.addEventListener('error', (event: Event) => {
      console.error('SSE error:', event);
      onError?.('Connection error');
      eventSource.close();
    });

    eventSource.addEventListener('end', () => {
      onComplete?.();
      eventSource.close();
    });

    return eventSource;
  }

  readonly createTransformations = {
    format: (): TransformationHook => ({
      type: 'format',
      config: {}
    }),

    renameVariable: (oldName: string, newName: string): TransformationHook => ({
      type: 'renameVariable',
      config: { oldName, newName }
    }),

    optimizeImports: (): TransformationHook => ({
      type: 'optimizeImports',
      config: {}
    }),

    removeDeadCode: (keepFunctions: string[] = []): TransformationHook => ({
      type: 'removeDeadCode',
      config: { keepFunctions }
    }),

    sanitizeHTML: (options: Record<string, any> = {}): TransformationHook => ({
      type: 'sanitizeHTML',
      config: options
    }),

    improveAccessibility: (options: Record<string, any> = {}): TransformationHook => ({
      type: 'improveAccessibility',
      config: options
    })
  } as const;
}

export const codeAstService = new CodeASTService();
export default codeAstService;
