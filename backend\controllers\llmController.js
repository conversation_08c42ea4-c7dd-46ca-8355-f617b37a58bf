const llmService = require('../services/llmService');
const withSSE = require('../services/sse/withSSE');

/**
 * Check if user is authenticated
 */
function checkUserAuthenticated(req) {
  // Log debug info
  console.log("[Auth Debug] Cookies:", req.cookies);
  console.log("[Auth Debug] isAuthenticated:", req.isAuthenticated?.());
  
  // Check if session exists
  const hasSession = Boolean(req.cookies['connect.sid']);
  if (!hasSession) {
    console.log("[Auth Debug] No session cookie found");
    return false;
  }

  // For now, just check if session exists
  // Later we can add more robust checks
  return true;
}

/**
 * Generate UI from prompt
 */
async function generateUI(req, res) {
  try {
    if (!checkUserAuthenticated(req)) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const result = await llmService.generateUIFromPrompt(prompt);
    res.json(result);
  } catch (error) {
    console.error('Error generating UI:', error);
    res.status(500).json({ error: error.message });
  }
}

/**
 * Generate HTML from prompt
 */
async function generateHTML(req, res) {
  try {
    if (!checkUserAuthenticated(req)) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const result = await llmService.generateHTMLFromPrompt(prompt);
    res.json(result);
  } catch (error) {
    console.error('Error generating HTML:', error);
    res.status(500).json({ error: error.message });
  }
}

/**
 * Streaming endpoint for JS/HTML
 */
async function streamResponse(req, res, next) {
  try {
    const { prompt } = req.query;

    if (!checkUserAuthenticated(req)) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const response = await llmService.streamResponse(prompt, res);
    res.write(response);
    res.end();
  } catch (error) {
    next(error);
  }
}

// Export all functions so they can be used by other files
module.exports = {
  generateUI,
  generateHTML,
  streamResponse: withSSE(streamResponse),
  checkUserAuthenticated  // Export auth check for reuse
};
