import React from 'react';

interface ProgressIndicatorProps {
  percentage: number;
  phase: 'analyzing' | 'generating' | 'finalizing' | 'complete';
  estimatedTimeRemaining: number;
  bytesReceived: number;
  showDetails?: boolean;
  size?: 'small' | 'medium' | 'large';
  position?: 'center' | 'bottom';
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  percentage,
  phase,
  estimatedTimeRemaining,
  bytesReceived,
  showDetails = true,
  size = 'medium',
  position = 'center'
}) => {
  const phaseMessages = {
    analyzing: 'Analyzing your request...',
    generating: 'Generating your prototype...',
    finalizing: 'Adding final touches...',
    complete: 'Complete!'
  };

  const phaseIcons = {
    analyzing: '🔍',
    generating: '⚡',
    finalizing: '✨',
    complete: '✅'
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number): string => {
    if (ms <= 0) return '0s';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  const sizeClasses = {
    small: {
      container: 'w-16 h-16',
      text: 'text-sm',
      details: 'text-xs'
    },
    medium: {
      container: 'w-20 h-20',
      text: 'text-base',
      details: 'text-sm'
    },
    large: {
      container: 'w-24 h-24',
      text: 'text-lg',
      details: 'text-base'
    }
  };

  const positionClasses = {
    center: 'flex flex-col items-center justify-center',
    bottom: 'flex items-center space-x-4'
  };

  const currentSize = sizeClasses[size];
  const currentPosition = positionClasses[position];

  if (position === 'bottom') {
    return (
      <div className="fixed bottom-4 left-4 right-4 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg p-4 z-50">
        <div className={currentPosition}>
          {/* Circular Progress */}
          <div className={`relative ${currentSize.container} flex-shrink-0`}>
            <svg className={`${currentSize.container} transform -rotate-90`}>
              <circle
                cx="50%" cy="50%" r="36"
                stroke="currentColor"
                strokeWidth="4"
                fill="transparent"
                className="text-gray-200"
              />
              <circle
                cx="50%" cy="50%" r="36"
                stroke="currentColor"
                strokeWidth="4"
                fill="transparent"
                strokeDasharray={`${2 * Math.PI * 36}`}
                strokeDashoffset={`${2 * Math.PI * 36 * (1 - percentage / 100)}`}
                className="text-blue-600 transition-all duration-500 ease-out"
                style={{
                  strokeLinecap: 'round'
                }}
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`${currentSize.text} font-semibold text-gray-900`}>
                {Math.round(percentage)}%
              </span>
            </div>
          </div>

          {/* Progress Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-lg">{phaseIcons[phase]}</span>
              <p className={`font-medium text-gray-900 ${currentSize.text}`}>
                {phaseMessages[phase]}
              </p>
            </div>
            
            {showDetails && (
              <div className="flex items-center space-x-4 text-gray-600">
                <span className={currentSize.details}>
                  {formatBytes(bytesReceived)} received
                </span>
                {estimatedTimeRemaining > 0 && (
                  <span className={currentSize.details}>
                    {formatTime(estimatedTimeRemaining)} remaining
                  </span>
                )}
              </div>
            )}

            {/* Linear Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${percentage}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Center position (original design)
  return (
    <div className="absolute inset-0 flex flex-col items-center justify-center bg-white/80 backdrop-blur-sm z-30">
      <div className="flex flex-col items-center space-y-4 max-w-md mx-auto p-6">
        {/* Circular Progress */}
        <div className={`relative ${currentSize.container}`}>
          <svg className={`${currentSize.container} transform -rotate-90`}>
            <circle
              cx="50%" cy="50%" r="36"
              stroke="currentColor"
              strokeWidth="6"
              fill="transparent"
              className="text-gray-200"
            />
            <circle
              cx="50%" cy="50%" r="36"
              stroke="currentColor"
              strokeWidth="6"
              fill="transparent"
              strokeDasharray={`${2 * Math.PI * 36}`}
              strokeDashoffset={`${2 * Math.PI * 36 * (1 - percentage / 100)}`}
              className="text-blue-600 transition-all duration-500 ease-out"
              style={{
                strokeLinecap: 'round'
              }}
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={`${currentSize.text} font-semibold text-gray-900`}>
              {Math.round(percentage)}%
            </span>
          </div>
        </div>

        {/* Progress Details */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <span className="text-2xl">{phaseIcons[phase]}</span>
            <p className={`font-medium text-gray-900 ${currentSize.text}`}>
              {phaseMessages[phase]}
            </p>
          </div>
          
          {showDetails && (
            <div className="space-y-1">
              <p className={`text-gray-600 ${currentSize.details}`}>
                {formatBytes(bytesReceived)} • {formatTime(estimatedTimeRemaining)} remaining
              </p>
            </div>
          )}
        </div>

        {/* Linear Progress Bar */}
        <div className="w-full max-w-xs bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${percentage}%` }}
          />
        </div>

        {/* Phase Indicators */}
        <div className="flex items-center space-x-2 mt-4">
          {(['analyzing', 'generating', 'finalizing'] as const).map((p, index) => (
            <div
              key={p}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                p === phase
                  ? 'bg-blue-600 scale-125'
                  : percentage > (index + 1) * 30
                  ? 'bg-green-500'
                  : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressIndicator;
