event:startMsg
data:I

event:startMsg
data:'

event:startMsg
data:l

event:startMsg
data:l

event:startMsg
data: 

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data:l

event:startMsg
data:p

event:startMsg
data: 

event:startMsg
data:y

event:startMsg
data:o

event:startMsg
data:u

event:startMsg
data: 

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data:m

event:startMsg
data:o

event:startMsg
data:v

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:r

event:startMsg
data:i

event:startMsg
data:s

event:startMsg
data:k

event:startMsg
data: 

event:startMsg
data:l

event:startMsg
data:e

event:startMsg
data:v

event:startMsg
data:e

event:startMsg
data:l

event:startMsg
data: 

event:startMsg
data:e

event:startMsg
data:l

event:startMsg
data:e

event:startMsg
data:m

event:startMsg
data:e

event:startMsg
data:n

event:startMsg
data:t

event:startMsg
data:s

event:startMsg
data: 

event:startMsg
data:f

event:startMsg
data:r

event:startMsg
data:o

event:startMsg
data:m

event:startMsg
data: 

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:p

event:startMsg
data:a

event:startMsg
data:g

event:startMsg
data:e

event:startMsg
data:.

event:startMsg
data: 

event:startMsg
data:H

event:startMsg
data:e

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:a

event:startMsg
data:r

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:t

event:startMsg
data:h

event:startMsg
data:e

event:startMsg
data: 

event:startMsg
data:n

event:startMsg
data:e

event:startMsg
data:c

event:startMsg
data:e

event:startMsg
data:s

event:startMsg
data:s

event:startMsg
data:a

event:startMsg
data:r

event:startMsg
data:y

event:startMsg
data: 

event:startMsg
data:c

event:startMsg
data:h

event:startMsg
data:a

event:startMsg
data:n

event:startMsg
data:g

event:startMsg
data:e

event:startMsg
data:s

event:startMsg
data::

event:startMsg
data:
data:

event:startMsg
data:
data:

event:startMsgFinish
data:

event:data
data:<!DOCTYPE html>
data:

event:data
data:<html lang="en">
data:

event:data
data:<head>
data:

event:data
data:<meta charset="UTF-8">
data:

event:data
data:<meta name="viewport" content="width=device-width, initial-scale=1.0">
data:

event:data
data:<title>Investment Portfolio - Agentic Banking Platform</title>
data:

event:data
data:<script src="https://cdn.tailwindcss.com/3.4.16"></script>
data:

event:data
data:<script>tailwind.config={theme:{extend:{colors:{primary:'#0F3460',secondary:'#E94560'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
data:

event:data
data:<link rel="preconnect" href="https://fonts.googleapis.com">
data:

event:data
data:<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
data:

event:data
data:<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
data:

event:data
data:<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
data:

event:data
data:<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
data:

event:data
data:<script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
data:

event:data
data:<style>
data:

event:data
data::where([class^="ri-"])::before { content: "\f3c2"; }
data:

event:data
data:body {
data:

event:data
data:font-family: 'Inter', sans-serif;
data:

event:data
data:background-color: #f9fafb;
data:

event:data
data:}
data:

event:data
data:input[type="number"]::-webkit-inner-spin-button,
data:

event:data
data:input[type="number"]::-webkit-outer-spin-button {
data:

event:data
data:-webkit-appearance: none;
data:

event:data
data:margin: 0;
data:

event:data
data:}
data:

event:data
data:.custom-switch {
data:

event:data
data:position: relative;
data:

event:data
data:display: inline-block;
data:

event:data
data:width: 46px;
data:

event:data
data:height: 24px;
data:

event:data
data:}
data:

event:data
data:.custom-switch input {
data:

event:data
data:opacity: 0;
data:

event:data
data:width: 0;
data:

event:data
data:height: 0;
data:

event:data
data:}
data:

event:data
data:.slider {
data:

event:data
data:position: absolute;
data:

event:data
data:cursor: pointer;
data:

event:data
data:top: 0;
data:

event:data
data:left: 0;
data:

event:data
data:right: 0;
data:

event:data
data:bottom: 0;
data:

event:data
data:background-color: #e5e7eb;
data:

event:data
data:transition: .4s;
data:

event:data
data:border-radius: 24px;
data:

event:data
data:}
data:

event:data
data:.slider:before {
data:

event:data
data:position: absolute;
data:

event:data
data:content: "";
data:

event:data
data:height: 18px;
data:

event:data
data:width: 18px;
data:

event:data
data:left: 3px;
data:

event:data
data:bottom: 3px;
data:

event:data
data:background-color: white;
data:

event:data
data:transition: .4s;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:}
data:

event:data
data:input:checked + .slider {
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:input:checked + .slider:before {
data:

event:data
data:transform: translateX(22px);
data:

event:data
data:}
data:

event:data
data:.custom-radio {
data:

event:data
data:display: flex;
data:

event:data
data:align-items: center;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-radio-input {
data:

event:data
data:appearance: none;
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border: 2px solid #d1d5db;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:margin-right: 8px;
data:

event:data
data:position: relative;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-radio-input:checked {
data:

event:data
data:border-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:.custom-radio-input:checked::after {
data:

event:data
data:content: "";
data:

event:data
data:position: absolute;
data:

event:data
data:top: 3px;
data:

event:data
data:left: 3px;
data:

event:data
data:width: 8px;
data:

event:data
data:height: 8px;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox {
data:

event:data
data:display: flex;
data:

event:data
data:align-items: center;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox-input {
data:

event:data
data:appearance: none;
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border: 2px solid #d1d5db;
data:

event:data
data:border-radius: 4px;
data:

event:data
data:margin-right: 8px;
data:

event:data
data:position: relative;
data:

event:data
data:cursor: pointer;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox-input:checked {
data:

event:data
data:border-color: #0F3460;
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:}
data:

event:data
data:.custom-checkbox-input:checked::after {
data:

event:data
data:content: "";
data:

event:data
data:position: absolute;
data:

event:data
data:top: 2px;
data:

event:data
data:left: 5px;
data:

event:data
data:width: 5px;
data:

event:data
data:height: 10px;
data:

event:data
data:border: solid white;
data:

event:data
data:border-width: 0 2px 2px 0;
data:

event:data
data:transform: rotate(45deg);
data:

event:data
data:}
data:

event:data
data:.custom-range {
data:

event:data
data:-webkit-appearance: none;
data:

event:data
data:width: 100%;
data:

event:data
data:height: 6px;
data:

event:data
data:border-radius: 5px;
data:

event:data
data:background: #e5e7eb;
data:

event:data
data:outline: none;
data:

event:data
data:}
data:

event:data
data:.custom-range::-webkit-slider-thumb {
data:

event:data
data:-webkit-appearance: none;
data:

event:data
data:appearance: none;
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:background: #0F3460;
data:

event:data
data:cursor: pointer;
data:

event:data
data:border: 2px solid white;
data:

event:data
data:box-shadow: 0 0 2px rgba(0,0,0,0.2);
data:

event:data
data:}
data:

event:data
data:.custom-range::-moz-range-thumb {
data:

event:data
data:width: 18px;
data:

event:data
data:height: 18px;
data:

event:data
data:border-radius: 50%;
data:

event:data
data:background: #0F3460;
data:

event:data
data:cursor: pointer;
data:

event:data
data:border: 2px solid white;
data:

event:data
data:box-shadow: 0 0 2px rgba(0,0,0,0.2);
data:

event:data
data:}
data:

event:data
data:.tab-button.active {
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:color: white;
data:

event:data
data:}
data:

event:data
data:.period-button.active {
data:

event:data
data:background-color: #0F3460;
data:

event:data
data:color: white;
data:

event:data
data:}
data:

event:data
data:</style>
data:

event:data
data:</head>
data:

event:data
data:<body>
data:

event:data
data:<!-- Header -->
data:

event:data
data:<header class="bg-white shadow-sm">
data:

event:data
data:<!-- Top Navigation -->
data:

event:data
data:<div class="px-6 py-4 flex items-center justify-between border-b border-gray-100">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<h1 class="text-2xl font-['Pacifico'] text-primary mr-10">logo</h1>
data:

event:data
data:<div class="relative mx-4 hidden md:block">
data:

event:data
data:<div class="absolute inset-y-0 left-0 flex items-center pl-3">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400">
data:

event:data
data:<i class="ri-search-line"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<input type="text" placeholder="Search for investments, funds, stocks..." class="pl-10 pr-4 py-2 w-80 rounded-full bg-gray-50 border-none focus:ring-2 focus:ring-primary/20 text-sm">
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center space-x-6">
data:

event:data
data:<div class="relative">
data:

event:data
data:<div class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
data:

event:data
data:<i class="ri-notification-3-line text-gray-600"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="absolute top-0 right-0 w-2 h-2 rounded-full bg-red-500"></span>
data:

event:data
data:</div>
data:

event:data
data:<div class="relative">
data:

event:data
data:<div class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
data:

event:data
data:<i class="ri-question-line text-gray-600"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center space-x-3">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium">JD</div>
data:

event:data
data:<div class="hidden md:block">
data:

event:data
data:<p class="text-sm font-medium">James Donovan</p>
data:

event:data
data:<p class="text-xs text-gray-500">Premium Customer</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-down-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Secondary Navigation -->
data:

event:data
data:<nav class="px-6 py-3 flex items-center space-x-8">
data:

event:data
data:<a href="https://readdy.ai/home/<USER>/d47de6a6-a5fe-4661-8513-b349d01fa833" data-readdy="true" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Dashboard</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Accounts</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Payments</a>
data:

event:data
data:<a href="#" class="text-primary font-medium text-sm border-b-2 border-primary pb-2">Investments</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Loans</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Cards</a>
data:

event:data
data:<a href="#" class="text-gray-600 hover:text-primary text-sm pb-2 border-b-2 border-transparent">Insurance</a>
data:

event:data
data:</nav>
data:

event:data
data:</header>
data:

event:data
data:<main class="container mx-auto px-6 py-8">
data:

event:data
data:<!-- Investment Overview -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 mb-8">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-xl font-semibold">Investment Portfolio Overview</h2>
data:

event:data
data:<div class="flex items-center space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center mr-2">
data:

event:data
data:<i class="ri-download-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Export Report
data:

event:data
data:</button>
data:

event:data
data:<div class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
data:

event:data
data:<i class="ri-more-2-fill text-gray-500"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
data:

event:data
data:<div class="bg-gray-50 rounded p-4">
data:

event:data
data:<p class="text-sm text-gray-500 mb-1">Total Portfolio Value</p>
data:

event:data
data:<p class="text-2xl font-semibold">$246,782.53</p>
data:

event:data
data:<div class="flex items-center mt-1 text-green-600">
data:

event:data
data:<div class="w-4 h-4 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-up-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm">+$5,428.32 (2.2%)</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="bg-gray-50 rounded p-4">
data:

event:data
data:<p class="text-sm text-gray-500 mb-1">YTD Return</p>
data:

event:data
data:<p class="text-2xl font-semibold text-green-600">+8.7%</p>
data:

event:data
data:<p class="text-sm text-gray-500 mt-1">vs. S&P 500: +7.2%</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="bg-gray-50 rounded p-4">
data:

event:data
data:<p class="text-sm text-gray-500 mb-1">Next Dividend Payment</p>
data:

event:data
data:<p class="text-2xl font-semibold">$1,245.62</p>
data:

event:data
data:<p class="text-sm text-gray-500 mt-1">Expected on June 15, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="bg-gray-50 rounded p-4">
data:

event:data
data:<p class="text-sm text-gray-500 mb-1">Next Dividend Payment</p>
data:

event:data
data:<p class="text-2xl font-semibold">$1,245.62</p>
data:

event:data
data:<p class="text-sm text-gray-500 mt-1">Expected on June 15, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="mb-6">
data:

event:data
data:<div class="flex justify-between items-center mb-4">
data:

event:data
data:<h3 class="font-medium">Portfolio Performance</h3>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<div class="px-1 py-1 bg-gray-100 rounded-full flex">
data:

event:data
data:<button class="period-button active px-3 py-1 rounded-full text-sm whitespace-nowrap">1M</button>
data:

event:data
data:<button class="period-button px-3 py-1 rounded-full text-sm whitespace-nowrap">3M</button>
data:

event:data
data:<button class="period-button px-3 py-1 rounded-full text-sm whitespace-nowrap">6M</button>
data:

event:data
data:<button class="period-button px-3 py-1 rounded-full text-sm whitespace-nowrap">1Y</button>
data:

event:data
data:<button class="period-button px-3 py-1 rounded-full text-sm whitespace-nowrap">All</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div id="performance-chart" class="w-full h-72"></div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Asset Allocation and Investment Accounts -->
data:

event:data
data:<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
data:

event:data
data:<!-- Asset Allocation -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 lg:col-span-1">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Asset Allocation</h2>
data:

event:data
data:<button class="text-primary text-sm flex items-center">
data:

event:data
data:<div class="w-4 h-4 flex items-center justify-center mr-1">
data:

event:data
data:<i class="ri-refresh-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Rebalance
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:<div id="allocation-chart" class="w-full h-64 mb-6"></div>
data:

event:data
data:<div class="space-y-3">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-3 h-3 rounded-full bg-[#57B5E7] mr-2"></div>
data:

event:data
data:<span class="text-sm">Stocks</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<span class="text-sm font-medium">$123,391.26</span>
data:

event:data
data:<span class="text-xs text-gray-500 ml-2">50%</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-3 h-3 rounded-full bg-[#8DD3C7] mr-2"></div>
data:

event:data
data:<span class="text-sm">Bonds</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<span class="text-sm font-medium">$61,695.63</span>
data:

event:data
data:<span class="text-xs text-gray-500 ml-2">25%</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-3 h-3 rounded-full bg-[#FBBF72] mr-2"></div>
data:

event:data
data:<span class="text-sm">Real Estate</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<span class="text-sm font-medium">$37,017.38</span>
data:

event:data
data:<span class="text-xs text-gray-500 ml-2">15%</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-3 h-3 rounded-full bg-[#FC8D62] mr-2"></div>
data:

event:data
data:<span class="text-sm">Cash</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<span class="text-sm font-medium">$24,678.26</span>
data:

event:data
data:<span class="text-xs text-gray-500 ml-2">10%</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Investment Accounts -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 lg:col-span-2">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Investment Accounts</h2>
data:

event:data
data:<button class="bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center mr-2">
data:

event:data
data:<i class="ri-add-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Add Account
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:<div class="space-y-4">
data:

event:data
data:<!-- Account 1 -->
data:

event:data
data:<div class="border border-gray-100 rounded p-5">
data:

event:data
data:<div class="flex justify-between items-start mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
data:

event:data
data:<i class="ri-funds-box-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Retirement Portfolio (401k)</h3>
data:

event:data
data:<p class="text-sm text-gray-500">**** 7823 • Managed by Vanguard</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="font-semibold text-lg">$142,987.63</p>
data:

event:data
data:<p class="text-sm text-green-600 text-right">+2.8% this month</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="grid grid-cols-3 gap-4 mb-4">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">YTD Return</p>
data:

event:data
data:<p class="text-sm font-medium">+9.2%</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Last Contribution</p>
data:

event:data
data:<p class="text-sm font-medium">$1,500.00 (May 15)</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Last Contribution</p>
data:

event:data
data:<p class="text-sm font-medium">$1,500.00 (May 15)</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="text-primary text-sm border border-gray-200 px-3 py-1.5 rounded-button whitespace-nowrap">View Details</button>
data:

event:data
data:<button class="text-primary text-sm border border-gray-200 px-3 py-1.5 rounded-button whitespace-nowrap">Manage</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Account 2 -->
data:

event:data
data:<div class="border border-gray-100 rounded p-5">
data:

event:data
data:<div class="flex justify-between items-start mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-4">
data:

event:data
data:<i class="ri-stock-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Brokerage Account</h3>
data:

event:data
data:<p class="text-sm text-gray-500">**** 3456 • Self-managed</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="font-semibold text-lg">$78,456.29</p>
data:

event:data
data:<p class="text-sm text-green-600 text-right">+3.5% this month</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="grid grid-cols-3 gap-4 mb-4">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">YTD Return</p>
data:

event:data
data:<p class="text-sm font-medium">+11.7%</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Last Trade</p>
data:

event:data
data:<p class="text-sm font-medium">AAPL (May 19)</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Last Trade</p>
data:

event:data
data:<p class="text-sm font-medium">AAPL (May 19)</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="text-primary text-sm border border-gray-200 px-3 py-1.5 rounded-button whitespace-nowrap">View Details</button>
data:

event:data
data:<button class="text-primary text-sm border border-gray-200 px-3 py-1.5 rounded-button whitespace-nowrap">Trade</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Account 3 -->
data:

event:data
data:<div class="border border-gray-100 rounded p-5">
data:

event:data
data:<div class="flex justify-between items-start mb-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-4">
data:

event:data
data:<i class="ri-building-line ri-lg"></i>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="font-medium">Real Estate Investment Trust</h3>
data:

event:data
data:<p class="text-sm text-gray-500">**** 9245 • Managed by BlackRock</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="font-semibold text-lg">$25,338.61</p>
data:

event:data
data:<p class="text-sm text-red-600 text-right">-0.5% this month</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="grid grid-cols-3 gap-4 mb-4">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">YTD Return</p>
data:

event:data
data:<p class="text-sm font-medium">+4.3%</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Dividend Yield</p>
data:

event:data
data:<p class="text-sm font-medium">4.2% annually</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Dividend Yield</p>
data:

event:data
data:<p class="text-sm font-medium">4.2% annually</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="text-primary text-sm border border-gray-200 px-3 py-1.5 rounded-button whitespace-nowrap">View Details</button>
data:

event:data
data:<button class="text-primary text-sm border border-gray-200 px-3 py-1.5 rounded-button whitespace-nowrap">Manage</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Recent Activities and Investment Opportunities -->
data:

event:data
data:<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
data:

event:data
data:<!-- Recent Activities -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 lg:col-span-1">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Recent Activities</h2>
data:

event:data
data:<a href="#" class="text-primary text-sm">View All</a>
data:

event:data
data:</div>
data:

event:data
data:<div class="space-y-5">
data:

event:data
data:<!-- Activity 1 -->
data:

event:data
data:<div class="flex items-start">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 mt-1 flex-shrink-0">
data:

event:data
data:<i class="ri-arrow-down-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex-1">
data:

event:data
data:<div class="flex justify-between items-start">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-sm font-medium">Dividend Received</h3>
data:

event:data
data:<p class="text-xs text-gray-500">AAPL • May 20, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm font-medium text-green-600">+$342.18</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Activity 2 -->
data:

event:data
data:<div class="flex items-start">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 mt-1 flex-shrink-0">
data:

event:data
data:<i class="ri-exchange-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex-1">
data:

event:data
data:<div class="flex justify-between items-start">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-sm font-medium">Stock Purchase</h3>
data:

event:data
data:<p class="text-xs text-gray-500">MSFT • May 19, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm font-medium text-red-600">-$2,845.50</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Activity 3 -->
data:

event:data
data:<div class="flex items-start">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3 mt-1 flex-shrink-0">
data:

event:data
data:<i class="ri-funds-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex-1">
data:

event:data
data:<div class="flex justify-between items-start">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-sm font-medium">401k Contribution</h3>
data:

event:data
data:<p class="text-xs text-gray-500">Vanguard • May 15, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm font-medium text-green-600">+$1,500.00</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Activity 4 -->
data:

event:data
data:<div class="flex items-start">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 mr-3 mt-1 flex-shrink-0">
data:

event:data
data:<i class="ri-arrow-up-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex-1">
data:

event:data
data:<div class="flex justify-between items-start">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-sm font-medium">Stock Sale</h3>
data:

event:data
data:<p class="text-xs text-gray-500">TSLA • May 12, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm font-medium text-green-600">+$4,128.75</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Activity 5 -->
data:

event:data
data:<div class="flex items-start">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 mt-1 flex-shrink-0">
data:

event:data
data:<i class="ri-arrow-down-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex-1">
data:

event:data
data:<div class="flex justify-between items-start">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-sm font-medium">Dividend Received</h3>
data:

event:data
data:<p class="text-xs text-gray-500">VTI • May 10, 2025</p>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm font-medium text-green-600">+$215.92</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Investment Opportunities -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 lg:col-span-2">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Investment Opportunities</h2>
data:

event:data
data:<button class="text-primary text-sm flex items-center">
data:

event:data
data:<div class="w-4 h-4 flex items-center justify-center mr-1">
data:

event:data
data:<i class="ri-settings-3-line"></i>
data:

event:data
data:</div>
data:

event:data
data:Customize
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
data:

event:data
data:<!-- Opportunity 1 -->
data:

event:data
data:<div class="border border-gray-100 rounded overflow-hidden">
data:

event:data
data:<div class="bg-blue-50 p-4">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="font-medium text-blue-800">Clean Energy ETF</h3>
data:

event:data
data:<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Recommended</span>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-blue-700 mt-1">Low risk, moderate returns</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<div class="flex justify-between mb-3">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Expected Return</p>
data:

event:data
data:<p class="text-sm font-medium">7-9% annually</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Min. Investment</p>
data:

event:data
data:<p class="text-sm font-medium">$1,000</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Diversified portfolio of companies in renewable energy sector.</p>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Invest Now</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Learn More</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Opportunity 2 -->
data:

event:data
data:<div class="border border-gray-100 rounded overflow-hidden">
data:

event:data
data:<div class="bg-green-50 p-4">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="font-medium text-green-800">Technology Growth Fund</h3>
data:

event:data
data:<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">High Growth</span>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-green-700 mt-1">Higher risk, higher returns</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<div class="flex justify-between mb-3">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Expected Return</p>
data:

event:data
data:<p class="text-sm font-medium">12-15% annually</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Min. Investment</p>
data:

event:data
data:<p class="text-sm font-medium">$5,000</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Focused on emerging tech companies with high growth potential.</p>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Invest Now</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Learn More</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Opportunity 3 -->
data:

event:data
data:<div class="border border-gray-100 rounded overflow-hidden">
data:

event:data
data:<div class="bg-purple-50 p-4">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="font-medium text-purple-800">Dividend Income Portfolio</h3>
data:

event:data
data:<span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">Stable Income</span>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-purple-700 mt-1">Low risk, steady income</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<div class="flex justify-between mb-3">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Dividend Yield</p>
data:

event:data
data:<p class="text-sm font-medium">4.5% annually</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Min. Investment</p>
data:

event:data
data:<p class="text-sm font-medium">$2,500</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Collection of high-dividend stocks for regular income generation.</p>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Invest Now</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Learn More</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Opportunity 4 -->
data:

event:data
data:<div class="border border-gray-100 rounded overflow-hidden">
data:

event:data
data:<div class="bg-amber-50 p-4">
data:

event:data
data:<div class="flex justify-between items-center">
data:

event:data
data:<h3 class="font-medium text-amber-800">Global Market Index Fund</h3>
data:

event:data
data:<span class="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full">Diversified</span>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-amber-700 mt-1">Moderate risk, balanced returns</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="p-4">
data:

event:data
data:<div class="flex justify-between mb-3">
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Expected Return</p>
data:

event:data
data:<p class="text-sm font-medium">8-10% annually</p>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<p class="text-xs text-gray-500">Min. Investment</p>
data:

event:data
data:<p class="text-sm font-medium">$1,000</p>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<p class="text-sm text-gray-600 mb-3">Broad exposure to global markets for balanced portfolio growth.</p>
data:

event:data
data:<div class="flex space-x-2">
data:

event:data
data:<button class="bg-primary text-white px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Invest Now</button>
data:

event:data
data:<button class="border border-gray-200 text-gray-700 px-3 py-1.5 rounded-button text-sm whitespace-nowrap">Learn More</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Action Buttons and Top Holdings -->
data:

event:data
data:<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
data:

event:data
data:<!-- Action Buttons -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 lg:col-span-1">
data:

event:data
data:<h2 class="text-lg font-semibold mb-6">Quick Actions</h2>
data:

event:data
data:<div class="grid grid-cols-1 gap-4">
data:

event:data
data:<button class="flex items-center justify-between p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
data:

event:data
data:<i class="ri-exchange-dollar-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">Buy/Sell Securities</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-right-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</button>
data:

event:data
data:<button class="flex items-center justify-between p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
data:

event:data
data:<i class="ri-bank-transfer-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">Transfer Funds</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-right-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</button>
data:

event:data
data:<button class="flex items-center justify-between p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3">
data:

event:data
data:<i class="ri-repeat-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">Set up Auto-Invest</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-right-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</button>
data:

event:data
data:<button class="flex items-center justify-between p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 mr-3">
data:

event:data
data:<i class="ri-calendar-check-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">Schedule Advisor Meeting</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center">
data:

event:data
data:<i class="ri-arrow-right-s-line text-gray-400"></i>
data:

event:data
data:</div>
data:

event:data
data:</button>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<!-- Top Holdings -->
data:

event:data
data:<div class="bg-white rounded shadow-sm p-6 lg:col-span-2">
data:

event:data
data:<div class="flex justify-between items-center mb-6">
data:

event:data
data:<h2 class="text-lg font-semibold">Top Holdings</h2>
data:

event:data
data:<button class="text-primary text-sm">View All</button>
data:

event:data
data:</div>
data:

event:data
data:<div class="overflow-x-auto">
data:

event:data
data:<table class="min-w-full">
data:

event:data
data:<thead>
data:

event:data
data:<tr class="border-b border-gray-100">
data:

event:data
data:<th class="text-left py-3 px-4 text-sm font-medium text-gray-500">Symbol</th>
data:

event:data
data:<th class="text-left py-3 px-4 text-sm font-medium text-gray-500">Name</th>
data:

event:data
data:<th class="text-right py-3 px-4 text-sm font-medium text-gray-500">Shares</th>
data:

event:data
data:<th class="text-right py-3 px-4 text-sm font-medium text-gray-500">Avg. Cost</th>
data:

event:data
data:<th class="text-right py-3 px-4 text-sm font-medium text-gray-500">Current Price</th>
data:

event:data
data:<th class="text-right py-3 px-4 text-sm font-medium text-gray-500">Market Value</th>
data:

event:data
data:<th class="text-right py-3 px-4 text-sm font-medium text-gray-500">Return</th>
data:

event:data
data:</tr>
data:

event:data
data:</thead>
data:

event:data
data:<tbody>
data:

event:data
data:<tr class="border-b border-gray-100">
data:

event:data
data:<td class="py-3 px-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-2">
data:

event:data
data:<i class="ri-apple-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">AAPL</span>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td class="py-3 px-4 text-sm">Apple Inc.</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">125</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$142.35</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$178.92</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$22,365.00</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right text-green-600">+25.7%</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr class="border-b border-gray-100">
data:

event:data
data:<td class="py-3 px-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-2">
data:

event:data
data:<i class="ri-microsoft-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">MSFT</span>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td class="py-3 px-4 text-sm">Microsoft Corp.</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">85</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$245.18</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$312.45</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$26,558.25</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right text-green-600">+27.4%</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr class="border-b border-gray-100">
data:

event:data
data:<td class="py-3 px-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-2">
data:

event:data
data:<i class="ri-amazon-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">AMZN</span>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td class="py-3 px-4 text-sm">Amazon.com Inc.</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">30</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$3,125.50</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$3,342.88</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$100,286.40</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right text-green-600">+6.9%</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr class="border-b border-gray-100">
data:

event:data
data:<td class="py-3 px-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-2">
data:

event:data
data:<i class="ri-tesla-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">TSLA</span>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td class="py-3 px-4 text-sm">Tesla Inc.</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">40</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$742.25</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$698.54</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$27,941.60</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right text-red-600">-5.9%</td>
data:

event:data
data:</tr>
data:

event:data
data:<tr>
data:

event:data
data:<td class="py-3 px-4">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-2">
data:

event:data
data:<i class="ri-vimeo-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="font-medium">VTI</span>
data:

event:data
data:</div>
data:

event:data
data:</td>
data:

event:data
data:<td class="py-3 px-4 text-sm">Vanguard Total Stock Market ETF</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">110</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$198.75</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$212.36</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right">$23,359.60</td>
data:

event:data
data:<td class="py-3 px-4 text-sm text-right text-green-600">+6.8%</td>
data:

event:data
data:</tr>
data:

event:data
data:</tbody>
data:

event:data
data:</table>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</main>
data:

event:data
data:<!-- Footer -->
data:

event:data
data:<footer class="bg-white border-t border-gray-100 mt-12">
data:

event:data
data:<div class="container mx-auto px-6 py-8">
data:

event:data
data:<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">Banking</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Checking Accounts</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Savings Accounts</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Credit Cards</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Loans</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Mortgages</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">Investments</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Retirement Planning</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Brokerage Accounts</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Wealth Management</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Financial Advisors</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Research & Insights</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">Support</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Help Center</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Contact Us</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Security Center</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Fraud Prevention</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Report an Issue</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:<div>
data:

event:data
data:<h3 class="text-lg font-semibold mb-4">About Us</h3>
data:

event:data
data:<ul class="space-y-2">
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Our Story</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Careers</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Investor Relations</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">Community Impact</a></li>
data:

event:data
data:<li><a href="#" class="text-gray-600 hover:text-primary text-sm">News & Press</a></li>
data:

event:data
data:</ul>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="border-t border-gray-100 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
data:

event:data
data:<div class="mb-4 md:mb-0">
data:

event:data
data:<p class="text-sm text-gray-500">© 2025 Banking Platform. All rights reserved.</p>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-6">
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400 mr-2">
data:

event:data
data:<i class="ri-shield-check-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-500">Secure Banking</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400 mr-2">
data:

event:data
data:<i class="ri-lock-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-500">256-bit Encryption</span>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex items-center">
data:

event:data
data:<div class="w-5 h-5 flex items-center justify-center text-gray-400 mr-2">
data:

event:data
data:<i class="ri-time-line"></i>
data:

event:data
data:</div>
data:

event:data
data:<span class="text-sm text-gray-500">24/7 Monitoring</span>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:<div class="flex space-x-4 mt-4 md:mt-0">
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-facebook-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-twitter-x-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-linkedin-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:<div class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-primary hover:text-white transition-colors">
data:

event:data
data:<i class="ri-instagram-fill"></i>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</div>
data:

event:data
data:</footer>
data:

event:data
data:<script>
data:

event:data
data:document.addEventListener('DOMContentLoaded', function() {
data:

event:data
data:// Performance Chart
data:

event:data
data:const performanceChart = document.getElementById('performance-chart');
data:

event:data
data:const performanceChartInstance = echarts.init(performanceChart);
data:

event:data
data:const performanceOption = {
data:

event:data
data:animation: false,
data:

event:data
data:tooltip: {
data:

event:data
data:trigger: 'axis',
data:

event:data
data:backgroundColor: 'rgba(255, 255, 255, 0.9)',
data:

event:data
data:borderColor: '#f0f0f0',
data:

event:data
data:textStyle: {
data:

event:data
data:color: '#1f2937'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:grid: {
data:

event:data
data:top: 10,
data:

event:data
data:right: 10,
data:

event:data
data:bottom: 30,
data:

event:data
data:left: 50,
data:

event:data
data:containLabel: true
data:

event:data
data:},
data:

event:data
data:xAxis: {
data:

event:data
data:type: 'category',
data:

event:data
data:data: ['Apr 21', 'Apr 28', 'May 5', 'May 12', 'May 19', 'May 21'],
data:

event:data
data:axisLine: {
data:

event:data
data:lineStyle: {
data:

event:data
data:color: '#e5e7eb'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:axisLabel: {
data:

event:data
data:color: '#6b7280'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:yAxis: {
data:

event:data
data:type: 'value',
data:

event:data
data:axisLine: {
data:

event:data
data:show: false
data:

event:data
data:},
data:

event:data
data:axisLabel: {
data:

event:data
data:color: '#6b7280',
data:

event:data
data:formatter: '${value}k'
data:

event:data
data:},
data:

event:data
data:splitLine: {
data:

event:data
data:lineStyle: {
data:

event:data
data:color: '#f3f4f6'
data:

event:data
data:}
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:series: [
data:

event:data
data:{
data:

event:data
data:name: 'Portfolio Value',
data:

event:data
data:type: 'line',
data:

event:data
data:smooth: true,
data:

event:data
data:symbol: 'none',
data:

event:data
data:lineStyle: {
data:

event:data
data:width: 3,
data:

event:data
data:color: 'rgba(87, 181, 231, 1)'
data:

event:data
data:},
data:

event:data
data:areaStyle: {
data:

event:data
data:color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
data:

event:data
data:{
data:

event:data
data:offset: 0,
data:

event:data
data:color: 'rgba(87, 181, 231, 0.2)'
data:

event:data
data:},
data:

event:data
data:{
data:

event:data
data:offset: 1,
data:

event:data
data:color: 'rgba(87, 181, 231, 0.05)'
data:

event:data
data:}
data:

event:data
data:])
data:

event:data
data:},
data:

event:data
data:data: [235, 238, 240, 242, 245, 246.8]
data:

event:data
data:}
data:

event:data
data:]
data:

event:data
data:};
data:

event:data
data:performanceChartInstance.setOption(performanceOption);
data:

event:data
data:// Asset Allocation Chart
data:

event:data
data:const allocationChart = document.getElementById('allocation-chart');
data:

event:data
data:const allocationChartInstance = echarts.init(allocationChart);
data:

event:data
data:const allocationOption = {
data:

event:data
data:animation: false,
data:

event:data
data:tooltip: {
data:

event:data
data:trigger: 'item',
data:

event:data
data:formatter: '{b}: {c} ({d}%)',
data:

event:data
data:backgroundColor: 'rgba(255, 255, 255, 0.9)',
data:

event:data
data:borderColor: '#f0f0f0',
data:

event:data
data:textStyle: {
data:

event:data
data:color: '#1f2937'
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:series: [
data:

event:data
data:{
data:

event:data
data:name: 'Asset Allocation',
data:

event:data
data:type: 'pie',
data:

event:data
data:radius: ['40%', '70%'],
data:

event:data
data:avoidLabelOverlap: false,
data:

event:data
data:itemStyle: {
data:

event:data
data:borderRadius: 8,
data:

event:data
data:borderColor: '#fff',
data:

event:data
data:borderWidth: 2
data:

event:data
data:},
data:

event:data
data:label: {
data:

event:data
data:show: false
data:

event:data
data:},
data:

event:data
data:emphasis: {
data:

event:data
data:label: {
data:

event:data
data:show: false
data:

event:data
data:}
data:

event:data
data:},
data:

event:data
data:labelLine: {
data:

event:data
data:show: false
data:

event:data
data:},
data:

event:data
data:data: [
data:

event:data
data:{ value: 50, name: 'Stocks', itemStyle: { color: 'rgba(87, 181, 231, 1)' } },
data:

event:data
data:{ value: 25, name: 'Bonds', itemStyle: { color: 'rgba(141, 211, 199, 1)' } },
data:

event:data
data:{ value: 15, name: 'Real Estate', itemStyle: { color: 'rgba(251, 191, 114, 1)' } },
data:

event:data
data:{ value: 10, name: 'Cash', itemStyle: { color: 'rgba(252, 141, 98, 1)' } }
data:

event:data
data:]
data:

event:data
data:}
data:

event:data
data:]
data:

event:data
data:};
data:

event:data
data:allocationChartInstance.setOption(allocationOption);
data:

event:data
data:// Period Button Switching
data:

event:data
data:const periodButtons = document.querySelectorAll('.period-button');
data:

event:data
data:periodButtons.forEach(button => {
data:

event:data
data:button.addEventListener('click', function() {
data:

event:data
data:// Remove active class from all buttons
data:

event:data
data:periodButtons.forEach(btn => {
data:

event:data
data:btn.classList.remove('active');
data:

event:data
data:});
data:

event:data
data:// Add active class to clicked button
data:

event:data
data:this.classList.add('active');
data:

event:data
data:});
data:

event:data
data:});
data:

event:data
data:// Responsive chart resize
data:

event:data
data:window.addEventListener('resize', function() {
data:

event:data
data:performanceChartInstance.resize();
data:

event:data
data:allocationChartInstance.resize();
data:

event:data
data:});
data:

event:data
data:});
data:

event:data
data:</script>
data:

event:data
data:</body>
data:

event:data
data:</html>

event:endMsg
data:
data:

event:endMsg
data:
data:

event:endMsg
data:I've removed all risk level indicators from the portfolio overview cards and investment account details. Each has been replaced with more relevant financial metrics like dividend payments, trading activity, and contribution history.

event:response
data:{"record_id":2208378,"request_id":"798fe6c5-e6e7-411a-b2ec-aa3d45f8fff7","error":null}

