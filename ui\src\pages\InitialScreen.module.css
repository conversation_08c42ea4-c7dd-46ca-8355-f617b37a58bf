.fullPage {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-light, #f9fafb);
  padding: 2rem 1rem;
}

.heading {
  font-size: 2.25rem;
  font-weight: 800;
  color: #4f46e5;
  margin-bottom: 2rem;
  text-align: center;
}

.options {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.optionCard {
  background: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.08);
  padding: 2.5rem 2rem;
  min-width: 260px;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border 0.2s, box-shadow 0.2s, transform 0.2s;
  outline: none;
}

.optionCard:hover,
.optionCard:focus {
  border: 2px solid #6366f1;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.12);
  transform: translateY(-2px) scale(1.03);
}

.selected {
  border: 2px solid #4f46e5;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.16);
  transform: scale(1.04);
}

.optionIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.optionTitle {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.optionDesc {
  color: #6b7280;
  font-size: 1rem;
  text-align: center;
}

.templatesList {
  display: flex;
  gap: 1.25rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.templateCard {
  background: #f3f4f6;
  border-radius: 0.5rem;
  padding: 1.25rem 1.5rem;
  min-width: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border 0.2s, box-shadow 0.2s, transform 0.2s;
  outline: none;
}

.templateCard:hover,
.templateCard:focus {
  border: 2px solid #6366f1;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.10);
  transform: translateY(-2px) scale(1.03);
}

.templateIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.templateName {
  font-size: 1.1rem;
  color: #4f46e5;
  font-weight: 600;
}

.cta {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

/* Dark mode styles */
:global(.dark) .fullPage {
  background: #1f2937;
}

:global(.dark) .optionCard {
  background: #1f2937;
  color: #f9fafb;
}

:global(.dark) .optionTitle {
  color: #f9fafb;
}

:global(.dark) .optionDesc {
  color: #d1d5db;
}

:global(.dark) .templateCard {
  background: #374151;
  color: #f9fafb;
}

:global(.dark) .templateName {
  color: #a5b4fc;
}

/* Responsive styles */
@media (max-width: 768px) {
  .options {
    flex-direction: column;
    gap: 1.25rem;
  }
  .templatesList {
    flex-direction: column;
    gap: 1rem;
  }
}
