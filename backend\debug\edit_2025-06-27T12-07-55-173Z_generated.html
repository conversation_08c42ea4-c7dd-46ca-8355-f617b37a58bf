<div id="task-1" class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 flex items-center justify-between">
          <div class="flex items-center">
            <input id="task-1-checkbox" type="checkbox" class="h-5 w-5 text-blue-600 rounded focus:ring-blue-500">
            <div class="ml-3">
              <label for="task-1-checkbox" class="text-yellow-500 font-medium">Complete project prototype</label>
              <div class="flex items-center mt-1 text-sm text-yellow-500">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">Work</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">High</span>
                <span class="ml-2"><i class="far fa-calendar-alt mr-1"></i> Due tomorrow</span>
              </div>
            </div>
          </div>
          <div class="flex space-x-2">
            <button id="edit-task-1-btn" data-action="openModal" data-target="edit-task-modal" class="text-gray-500 hover:text-blue-600">
              <i class="fas fa-pencil-alt"></i>
            </button>
            <button id="delete-task-1-btn" data-action="openModal" data-target="delete-task-modal" class="text-gray-500 hover:text-red-600">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>