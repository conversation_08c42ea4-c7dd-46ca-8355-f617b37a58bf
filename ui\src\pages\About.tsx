import { Link } from 'react-router-dom';
import { ThemeToggle } from '../components/ThemeToggle';
import { useTheme } from '../contexts/ThemeContext';
import styles from './About.module.css';

export function About() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className={`${styles.container} ${isDark ? styles.darkContainer : ''}`}>
      <header className={`${styles.header} ${isDark ? styles.darkHeader : ''}`}>
        <div className={styles.headerContent}>
          <h1 className={`${styles.title} ${isDark ? styles.darkTitle : ''}`}>About</h1>
          <ThemeToggle />
        </div>
      </header>
      
      <main className={styles.main}>
        <div className={styles.content}>
          <div className={`${styles.card} ${isDark ? styles.darkCard : ''}`}>
            <div className={styles.cardContent}>
              <h2 className={`${styles.cardTitle} ${isDark ? styles.darkCardTitle : ''}`}>
                About This Project
              </h2>
              <div className={`${styles.cardText} ${isDark ? styles.darkCardText : ''}`}>
                <p className={styles.paragraph}>
                  This is a demo project showcasing React 19 with TypeScript and modern best practices.
                  It demonstrates the use of functional components, hooks, context API, and error boundaries.
                </p>
                <p className={styles.paragraph}>
                  The project structure follows a clean, modular approach with separate directories for
                  components, hooks, contexts, and pages.
                </p>
                <p className={styles.paragraph}>
                  Styling is done with CSS Modules, providing a component-scoped approach to building
                  responsive and maintainable UI.
                </p>
              </div>
              
              <Link to="/" className={styles.button}>
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
