/**
 * Test page for prototype versioning system
 * Demonstrates and tests all versioning rules and behaviors
 */

import React, { useState, useEffect } from 'react';
import { prototypeVersioningService } from '../services/prototypeVersioningService';
import { prototypeVersionService } from '../services/prototypeVersionService';

export default function PrototypeVersioningTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [prototypeId] = useState(999); // Test prototype ID

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runVersioningTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      addResult('🚀 Starting prototype versioning tests...');

      // Test 1: Initial version creation
      addResult('📝 Test 1: Creating initial version (V1)');
      const initialHtml = '<div>Initial prototype content</div>';
      const versionId1 = await prototypeVersioningService.createInitialVersion(
        prototypeId,
        initialHtml
      );
      addResult(versionId1 ? '✅ Initial version created successfully' : '❌ Failed to create initial version');

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 2: Content modification version (immediate)
      addResult('📝 Test 2: Creating content modification version (immediate)');
      const modifiedHtml = '<div>Modified prototype content</div>';
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        modifiedHtml,
        'manual_edit',
        {
          changeDescription: 'Test content modification',
          immediate: true
        }
      );
      addResult('✅ Content modification version created');

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 3: Debounced version creation
      addResult('📝 Test 3: Testing debounced version creation');
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        '<div>Debounced content 1</div>',
        'manual_edit',
        { changeDescription: 'Debounced test 1' }
      );
      
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        '<div>Debounced content 2</div>',
        'manual_edit',
        { changeDescription: 'Debounced test 2' }
      );
      
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        '<div>Debounced content 3</div>',
        'manual_edit',
        { changeDescription: 'Debounced test 3 (final)' }
      );
      
      addResult('⏱️ Debounced version creation scheduled (only last one should create version)');

      // Wait for debounce to complete
      await new Promise(resolve => setTimeout(resolve, 3000));
      addResult('✅ Debounced version creation completed');

      // Test 4: Version retrieval
      addResult('📝 Test 4: Retrieving version history');
      const versions = await prototypeVersionService.getVersionHistory(prototypeId, 10, 0);
      addResult(`✅ Retrieved ${versions.length} versions`);
      
      versions.forEach((version, index) => {
        addResult(`  V${version.version_number}: ${version.operation_type} - ${version.change_description || 'No description'}`);
      });

      // Test 5: Version statistics
      addResult('📝 Test 5: Getting version statistics');
      const stats = await prototypeVersionService.getVersionStats(prototypeId);
      if (stats) {
        addResult(`✅ Stats: ${stats.total_versions} total versions, latest: V${stats.latest_version}`);
      } else {
        addResult('❌ Failed to get version statistics');
      }

      // Test 6: Version creation rules
      addResult('📝 Test 6: Testing version creation rules');
      
      const shouldCreateTests = [
        {
          context: { isFirstPage: true, isContentModification: false, isPageNavigation: false, isNewPageAddition: false, isUIStateChange: false },
          expected: true,
          description: 'First page creation'
        },
        {
          context: { isFirstPage: false, isContentModification: true, isPageNavigation: false, isNewPageAddition: false, isUIStateChange: false },
          expected: true,
          description: 'Content modification'
        },
        {
          context: { isFirstPage: false, isContentModification: false, isPageNavigation: true, isNewPageAddition: false, isUIStateChange: false },
          expected: false,
          description: 'Page navigation'
        },
        {
          context: { isFirstPage: false, isContentModification: false, isPageNavigation: false, isNewPageAddition: true, isUIStateChange: false },
          expected: false,
          description: 'New page addition'
        },
        {
          context: { isFirstPage: false, isContentModification: false, isPageNavigation: false, isNewPageAddition: false, isUIStateChange: true },
          expected: false,
          description: 'UI state change'
        }
      ];

      shouldCreateTests.forEach(test => {
        const result = prototypeVersioningService.shouldCreateVersion(test.context);
        const status = result === test.expected ? '✅' : '❌';
        addResult(`  ${status} ${test.description}: ${result} (expected: ${test.expected})`);
      });

      addResult('🎉 All tests completed!');

    } catch (error) {
      addResult(`❌ Test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-8 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Prototype Versioning System Test</h1>
        
        {/* Test Controls */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Controls</h2>
          <div className="flex space-x-4">
            <button
              onClick={runVersioningTests}
              disabled={isRunning}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                isRunning
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isRunning ? 'Running Tests...' : 'Run Versioning Tests'}
            </button>
            
            <button
              onClick={clearResults}
              disabled={isRunning}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors disabled:opacity-50"
            >
              Clear Results
            </button>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Test Prototype ID:</strong> {prototypeId}</p>
            <p><strong>Note:</strong> This test uses a mock prototype ID for testing purposes.</p>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-xl font-semibold text-gray-900">Test Results</h2>
          </div>
          
          <div className="p-6">
            {testResults.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <p>No test results yet. Click "Run Versioning Tests" to start.</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg text-sm font-mono ${
                      result.includes('✅')
                        ? 'bg-green-50 text-green-800 border border-green-200'
                        : result.includes('❌')
                        ? 'bg-red-50 text-red-800 border border-red-200'
                        : result.includes('⏱️')
                        ? 'bg-yellow-50 text-yellow-800 border border-yellow-200'
                        : result.includes('🚀') || result.includes('🎉')
                        ? 'bg-blue-50 text-blue-800 border border-blue-200'
                        : 'bg-gray-50 text-gray-800 border border-gray-200'
                    }`}
                  >
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Test Documentation */}
        <div className="mt-8 bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Versioning Rules Tested</h2>
          <div className="space-y-4 text-sm text-gray-700">
            <div>
              <h3 className="font-semibold text-gray-900">✅ Version Creation Triggers:</h3>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Initial prototype creation with first page content</li>
                <li>HTML content edits/updates to existing pages</li>
                <li>AI-generated content modifications</li>
                <li>Manual content changes through editor</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900">❌ Version Creation Exclusions:</h3>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Adding new pages to existing prototype</li>
                <li>Navigation between existing pages</li>
                <li>UI state changes (view mode, panel visibility)</li>
                <li>Loading existing page content without modifications</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900">⚙️ Technical Features:</h3>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Debounced version creation (2.5 second delay)</li>
                <li>Graceful error handling</li>
                <li>Integration with existing backend infrastructure</li>
                <li>Real-time UI updates via custom events</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
