import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styles from './CookieConsent.module.css';

/**
 * <PERSON><PERSON> Consent Banner Component
 * Displays a banner at the bottom of the page to inform users about cookie usage
 */
export const CookieConsent: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if the user has already accepted cookies
    const hasAccepted = localStorage.getItem('cookieConsent') === 'accepted';
    if (!hasAccepted) {
      // Show the banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    // Save the consent in localStorage
    localStorage.setItem('cookieConsent', 'accepted');
    setIsVisible(false);
  };

  const handleDecline = () => {
    // Save the decline in localStorage
    localStorage.setItem('cookieConsent', 'declined');
    setIsVisible(false);
    
    // Optionally, disable Google Analytics
    if (window.gtag) {
      // This doesn't actually disable GA, but it's a signal that the user declined
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.cookieBanner}>
      <div className={styles.content}>
        <p className={styles.message}>
          We use cookies to enhance your experience, analyze site traffic, and for marketing purposes.
          By continuing to browse, you agree to our use of cookies.
          <Link to="/privacy" className={styles.link}>Learn more</Link>
        </p>
        <div className={styles.buttons}>
          <button 
            className={`${styles.button} ${styles.declineButton}`}
            onClick={handleDecline}
          >
            Decline
          </button>
          <button 
            className={`${styles.button} ${styles.acceptButton}`}
            onClick={handleAccept}
          >
            Accept
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
