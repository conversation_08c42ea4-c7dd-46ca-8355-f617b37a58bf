# Prototype-Level Versioning System Implementation

## Overview

This document describes the complete implementation of the prototype-level versioning system that integrates with the existing backend versioning infrastructure while following specific version creation rules.

## Architecture

### Core Components

1. **`prototypeVersioningService.ts`** - Core versioning logic and API integration
2. **`usePrototypeVersioning.ts`** - React hook for automatic versioning integration
3. **`PrototypeVersionSwitcher.tsx`** - UI component for version navigation
4. **`usePrototypeVersions.ts`** - Hook for version state management
5. **Backend API endpoint** - `POST /api/llm/v3/versions/:prototypeId`

## Version Creation Rules

### ✅ **Create Versions For:**

#### 1. Initial Version Creation (V1)
- **Trigger**: When the first page is created in a new prototype
- **Operation Type**: `'generation'`
- **Change Description**: `"Initial prototype version"`
- **Implementation**: Automatic detection of first page creation

#### 2. Content Modification Versioning (V2, V3, V4...)
- **Triggers**:
  - HTML content edits via editor
  - AI-generated content modifications from chat
  - Manual content changes through code editor
  - Preview panel interactions that modify content
- **Operation Types**: 
  - `'manual_edit'` for user edits
  - `'generation'` for AI modifications
- **Debouncing**: 2.5 second delay to avoid rapid successive versions

### ❌ **Do NOT Create Versions For:**

#### 1. New Page Addition
- Adding additional pages to existing prototype
- Pages are added to current prototype without version increment

#### 2. Page Navigation
- Switching between existing pages
- Loading existing page content

#### 3. UI State Changes
- View mode changes (code/preview)
- Panel visibility toggles
- Resizing panels

## Technical Implementation

### Backend Integration

#### API Endpoint
```javascript
POST /api/llm/v3/versions/:prototypeId
```

#### Request Body
```json
{
  "html": "string (required)",
  "css": "string (optional)",
  "change_description": "string (optional)",
  "operation_type": "generation|manual_edit|restoration",
  "user_prompt": "string (optional)",
  "llm_response": "string (optional)"
}
```

#### Response
```json
{
  "success": true,
  "versionId": 123,
  "message": "Prototype version created successfully"
}
```

### Frontend Integration

#### Automatic Versioning Hook
```typescript
const { isVersionCreationInProgress } = usePrototypeVersioning({
  prototypeId: projectId || null,
  htmlContent: state.htmlContent,
  isGenerating: state.isGenerating,
  currentPageId: state.currentPageId,
  pages: state.pages,
  onVersionCreated: (versionId) => {
    // Refresh version list
    refreshVersions();
  }
});
```

#### Version Navigation Component
```typescript
<PrototypeVersionSwitcher
  versions={versionLabels}
  currentVersion={currentVersionLabel}
  onVersionChange={switchToVersion}
  isLoading={isVersionLoading || isVersionCreationInProgress}
/>
```

## Key Features

### 1. Debounced Version Creation
- **Delay**: 2.5 seconds
- **Purpose**: Prevent multiple versions from rapid successive edits
- **Behavior**: Only the last edit in the debounce window creates a version

### 2. Context-Aware Version Detection
```typescript
const versioningContext = {
  isFirstPage: isFirstPage(),
  isContentModification: isContentModification(),
  isPageNavigation: isPageNavigation(),
  isNewPageAddition: isNewPageAddition(),
  isUIStateChange: false
};
```

### 3. Operation Type Detection
- **Generation**: Detected when `isGenerating` state transitions from `true` to `false`
- **Manual Edit**: Detected when content changes without generation state
- **Immediate Creation**: Generation completions create versions immediately (no debounce)

### 4. Error Handling
- **Graceful Degradation**: Version creation failures don't block editor functionality
- **Retry Logic**: Failed versions can be retried
- **User Feedback**: Error states displayed in UI

### 5. Real-time UI Updates
```typescript
// Custom event for version creation completion
window.dispatchEvent(new CustomEvent('prototypeVersionCreated', {
  detail: { prototypeId, versionId, operationType }
}));
```

## Integration Points

### Editor Hook Integration
```typescript
// In EditorPageV3Refactored.tsx
const { isVersionCreationInProgress } = usePrototypeVersioning({
  prototypeId: projectId || null,
  htmlContent: state.htmlContent,
  isGenerating: state.isGenerating,
  currentPageId: state.currentPageId,
  pages: state.pages,
  onVersionCreated: refreshVersions
});
```

### Version Switcher Integration
- **Position**: Bottom-left corner of center panel
- **Visibility**: Only shown when versions exist
- **Loading States**: Shows spinner during version creation
- **Error Display**: Shows version errors with dismiss functionality

## Database Compatibility

### Existing Table Structure
The system uses the existing `prototype_versions` table:
```sql
CREATE TABLE prototype_versions (
  id SERIAL PRIMARY KEY,
  prototype_id INTEGER NOT NULL,
  version_number INTEGER NOT NULL,
  html TEXT NOT NULL,
  css TEXT,
  change_description TEXT,
  operation_type VARCHAR(50) NOT NULL,
  user_prompt TEXT,
  llm_response TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Version Service Integration
Uses existing `backend/services/versionService.js`:
- `createVersion()` - Create new version
- `getVersionHistory()` - Retrieve version list
- `getVersion()` - Get specific version
- `getVersionStats()` - Get version statistics

## Testing

### Test Page
`PrototypeVersioningTest.tsx` provides comprehensive testing:
- Initial version creation
- Content modification versioning
- Debounced version creation
- Version retrieval and statistics
- Rule validation

### Test Scenarios
1. **Initial Version**: First page creation → V1
2. **Content Edit**: Manual content change → V2 (debounced)
3. **AI Generation**: Chat-based generation → V3 (immediate)
4. **Page Addition**: New page added → No version created
5. **Navigation**: Switch pages → No version created

## Performance Considerations

### Debouncing Strategy
- **Timer Management**: Clears previous timers on new edits
- **Memory Cleanup**: Removes versioning state on component unmount
- **State Tracking**: Prevents duplicate version creation

### API Optimization
- **Batch Operations**: Debouncing reduces API calls
- **Error Recovery**: Failed requests don't block subsequent operations
- **Caching**: Version lists cached and refreshed on creation

## Monitoring and Debugging

### Console Logging
```typescript
console.log('🎯 Creating initial version for new prototype');
console.log('📝 Rule 1: Creating initial version for first page');
console.log('⏱️ Debounced version creation scheduled');
console.log('✅ Prototype version created successfully');
```

### Event Tracking
- Version creation events
- Rule evaluation logging
- Error state tracking
- Performance metrics

## Future Enhancements

### Potential Improvements
1. **Version Branching**: Support for parallel version development
2. **Version Comparison**: Visual diff between versions
3. **Rollback Confirmation**: User confirmation for version restoration
4. **Batch Operations**: Multiple version operations
5. **Version Metadata**: Enhanced version descriptions and tags

### Scalability Considerations
1. **Version Limits**: Implement version count limits per prototype
2. **Storage Optimization**: Compress old version content
3. **Performance Monitoring**: Track version creation performance
4. **User Preferences**: Configurable debounce timing

## Conclusion

The prototype-level versioning system provides automatic, rule-based version management that integrates seamlessly with the existing editor infrastructure while maintaining compatibility with the backend versioning service. The implementation follows the specified rules precisely and provides a robust foundation for prototype version management.
