/**
 * Prototype Version Switcher Component
 * Clean, minimal version navigation with arrow controls and glassmorphism effects
 */

import React, { useState, useEffect } from 'react';

interface PrototypeVersionSwitcherProps {
  /** Array of version labels (e.g., ["V1", "V2", "V3"]) */
  versions?: string[];
  /** Currently selected version */
  currentVersion?: string;
  /** Callback when version changes */
  onVersionChange?: (version: string) => void;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  isLoading?: boolean;
}

export default function PrototypeVersionSwitcher({
  versions = ['V1'],
  currentVersion,
  onVersionChange,
  className = '',
  isLoading = false
}: PrototypeVersionSwitcherProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Initialize current index based on currentVersion prop
  useEffect(() => {
    if (currentVersion && versions.includes(currentVersion)) {
      const index = versions.indexOf(currentVersion);
      setCurrentIndex(index);
    } else if (versions.length > 0) {
      // Default to latest version
      setCurrentIndex(versions.length - 1);
    }
  }, [currentVersion, versions]);

  const goToPrevious = () => {
    if (currentIndex > 0 && !isLoading) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      onVersionChange?.(versions[newIndex]);
    }
  };

  const goToNext = () => {
    if (currentIndex < versions.length - 1 && !isLoading) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      onVersionChange?.(versions[newIndex]);
    }
  };

  const isFirstVersion = currentIndex === 0;
  const isLastVersion = currentIndex === versions.length - 1;
  const displayVersion = versions[currentIndex] || 'V1';

  if (versions.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Left Arrow Button */}
      <button
        onClick={goToPrevious}
        disabled={isFirstVersion || isLoading}
        className={`
          group relative w-10 h-10 rounded-xl flex items-center justify-center
          backdrop-blur-sm bg-white/80 border border-white/20
          shadow-lg hover:shadow-xl transition-all duration-300 ease-out
          ${isFirstVersion || isLoading 
            ? 'opacity-40 cursor-not-allowed' 
            : 'hover:bg-white/90 hover:border-white/30 hover:scale-105 active:scale-95'
          }
          before:absolute before:inset-0 before:rounded-xl before:bg-gradient-to-br 
          before:from-white/10 before:to-transparent before:opacity-0 
          ${!isFirstVersion && !isLoading ? 'hover:before:opacity-100' : ''}
          before:transition-opacity before:duration-300
        `}
        title="Previous version"
        aria-label="Previous version"
      >
        <svg 
          className={`w-5 h-5 transition-colors duration-200 ${
            isFirstVersion || isLoading ? 'text-gray-400' : 'text-gray-600 group-hover:text-gray-800'
          }`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          strokeWidth={2}
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      {/* Current Version Display */}
      <div className={`
        relative px-6 py-3 rounded-xl flex items-center justify-center min-w-[80px]
        backdrop-blur-sm bg-gradient-to-br from-white/90 to-white/70
        border border-white/30 shadow-lg hover:shadow-xl
        transition-all duration-300 ease-out hover:scale-105
        before:absolute before:inset-0 before:rounded-xl 
        before:bg-gradient-to-br before:from-blue-500/5 before:to-purple-500/5
        before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300
        ${isLoading ? 'animate-pulse' : ''}
      `}>
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="text-gray-600 text-sm font-medium">Loading...</span>
          </div>
        ) : (
          <span className="text-gray-900 text-lg font-semibold tracking-wide">
            {displayVersion}
          </span>
        )}
        
        {/* Subtle inner glow */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"></div>
      </div>

      {/* Right Arrow Button */}
      <button
        onClick={goToNext}
        disabled={isLastVersion || isLoading}
        className={`
          group relative w-10 h-10 rounded-xl flex items-center justify-center
          backdrop-blur-sm bg-white/80 border border-white/20
          shadow-lg hover:shadow-xl transition-all duration-300 ease-out
          ${isLastVersion || isLoading 
            ? 'opacity-40 cursor-not-allowed' 
            : 'hover:bg-white/90 hover:border-white/30 hover:scale-105 active:scale-95'
          }
          before:absolute before:inset-0 before:rounded-xl before:bg-gradient-to-br 
          before:from-white/10 before:to-transparent before:opacity-0 
          ${!isLastVersion && !isLoading ? 'hover:before:opacity-100' : ''}
          before:transition-opacity before:duration-300
        `}
        title="Next version"
        aria-label="Next version"
      >
        <svg 
          className={`w-5 h-5 transition-colors duration-200 ${
            isLastVersion || isLoading ? 'text-gray-400' : 'text-gray-600 group-hover:text-gray-800'
          }`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          strokeWidth={2}
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
        </svg>
      </button>

      {/* Version Count Indicator (optional) */}
      {versions.length > 1 && (
        <div className="ml-2 px-2 py-1 rounded-lg bg-gray-100/80 backdrop-blur-sm">
          <span className="text-xs text-gray-500 font-medium">
            {currentIndex + 1} of {versions.length}
          </span>
        </div>
      )}
    </div>
  );
}

// Export a demo component for testing
export function PrototypeVersionSwitcherDemo() {
  const [selectedVersion, setSelectedVersion] = useState('V3');
  const [isLoading, setIsLoading] = useState(false);
  
  const versions = ['V1', 'V2', 'V3', 'V4', 'V5'];

  const handleVersionChange = (version: string) => {
    setIsLoading(true);
    console.log('Version changed to:', version);
    
    // Simulate loading
    setTimeout(() => {
      setSelectedVersion(version);
      setIsLoading(false);
    }, 500);
  };

  return (
    <div className="p-8 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Prototype Version Switcher</h1>
        
        {/* Demo Container */}
        <div className="relative bg-white/60 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Interactive Demo</h2>
          
          {/* Mock Content */}
          <div className="bg-gray-900 rounded-lg p-6 mb-6 text-green-400 font-mono text-sm">
            <div>&lt;div className="prototype"&gt;</div>
            <div className="ml-4">&lt;h1&gt;{selectedVersion} Content&lt;/h1&gt;</div>
            <div className="ml-4">&lt;p&gt;This is prototype version {selectedVersion}&lt;/p&gt;</div>
            <div>&lt;/div&gt;</div>
          </div>
          
          {/* Version Switcher positioned at bottom-left */}
          <div className="absolute bottom-6 left-6">
            <PrototypeVersionSwitcher
              versions={versions}
              currentVersion={selectedVersion}
              onVersionChange={handleVersionChange}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">✨ Design Features</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• Glassmorphism effects with backdrop blur</li>
              <li>• Soft shadows and smooth hover animations</li>
              <li>• Gradient backgrounds and subtle borders</li>
              <li>• Disabled states for boundary versions</li>
              <li>• Loading states with spinner animation</li>
            </ul>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">⚡ Behavior</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• Arrow navigation between versions</li>
              <li>• Auto-disable at first/last version</li>
              <li>• Version change callbacks</li>
              <li>• Responsive design</li>
              <li>• Accessibility support</li>
            </ul>
          </div>
        </div>

        {/* Code Example */}
        <div className="bg-white/60 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200/50 bg-gray-50/50">
            <h3 className="text-lg font-semibold text-gray-900">Usage Example</h3>
          </div>
          <div className="p-6">
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
{`<PrototypeVersionSwitcher
  versions={['V1', 'V2', 'V3', 'V4']}
  currentVersion="V3"
  onVersionChange={(version) => {
    console.log('Switched to:', version);
  }}
  isLoading={false}
/>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
