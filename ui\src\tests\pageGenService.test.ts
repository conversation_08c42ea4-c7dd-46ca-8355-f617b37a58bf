/**
 * Tests for pageGenService
 * Tests API interactions for page and project management
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { 
  getSession, 
  getPageList, 
  getProjectList, 
  updatePage, 
  deletePage 
} from '../services/pageGenService';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('pageGenService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getSession', () => {
    test('should fetch session data successfully', async () => {
      const mockSession = {
        id: 'session-123',
        content: '<div>Test content</div>',
        created_at: '2024-01-01T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockSession,
      });

      const result = await getSession('session-123');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/page_gen/session/session-123',
        expect.objectContaining({
          method: 'GET',
          credentials: 'include',
        })
      );
      expect(result).toEqual(mockSession);
    });

    test('should handle session not found', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Session not found' }),
      });

      await expect(getSession('invalid-session')).rejects.toThrow();
    });

    test('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(getSession('session-123')).rejects.toThrow('Network error');
    });
  });

  describe('getPageList', () => {
    test('should fetch page list successfully', async () => {
      const mockPages = [
        { id: 1, name: 'Page 1', content: '<div>Page 1</div>' },
        { id: 2, name: 'Page 2', content: '<div>Page 2</div>' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ pages: mockPages }),
      });

      const result = await getPageList(123);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/page_gen/project/123/pages',
        expect.objectContaining({
          method: 'GET',
          credentials: 'include',
        })
      );
      expect(result).toEqual({ pages: mockPages });
    });

    test('should handle empty page list', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ pages: [] }),
      });

      const result = await getPageList(123);

      expect(result).toEqual({ pages: [] });
    });

    test('should handle invalid project ID', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Project not found' }),
      });

      await expect(getPageList(999)).rejects.toThrow();
    });
  });

  describe('getProjectList', () => {
    test('should fetch project list with pagination', async () => {
      const mockProjects = [
        { id: 1, name: 'Project 1', description: 'Test project 1' },
        { id: 2, name: 'Project 2', description: 'Test project 2' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          projects: mockProjects,
          total: 2,
          page: 1,
          limit: 50
        }),
      });

      const result = await getProjectList(1, 50);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/page_gen/projects?page=1&limit=50',
        expect.objectContaining({
          method: 'GET',
          credentials: 'include',
        })
      );
      expect(result.projects).toEqual(mockProjects);
      expect(result.total).toBe(2);
    });

    test('should handle empty project list', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          projects: [],
          total: 0,
          page: 1,
          limit: 50
        }),
      });

      const result = await getProjectList(1, 50);

      expect(result.projects).toEqual([]);
      expect(result.total).toBe(0);
    });

    test('should use default pagination parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          projects: [],
          total: 0,
          page: 1,
          limit: 10
        }),
      });

      await getProjectList();

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/page_gen/projects?page=1&limit=10',
        expect.any(Object)
      );
    });
  });

  describe('updatePage', () => {
    test('should update page name successfully', async () => {
      const mockUpdatedPage = {
        id: 1,
        name: 'Updated Page Name',
        content: '<div>Page content</div>'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockUpdatedPage,
      });

      const result = await updatePage(1, 'Updated Page Name');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/page_gen/page/1',
        expect.objectContaining({
          method: 'PUT',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ name: 'Updated Page Name' }),
        })
      );
      expect(result).toEqual(mockUpdatedPage);
    });

    test('should handle update validation errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid page name' }),
      });

      await expect(updatePage(1, '')).rejects.toThrow();
    });

    test('should handle page not found during update', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Page not found' }),
      });

      await expect(updatePage(999, 'New Name')).rejects.toThrow();
    });
  });

  describe('deletePage', () => {
    test('should delete page successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      const result = await deletePage(1);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/page_gen/page/1',
        expect.objectContaining({
          method: 'DELETE',
          credentials: 'include',
        })
      );
      expect(result).toEqual({ success: true });
    });

    test('should handle page not found during deletion', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Page not found' }),
      });

      await expect(deletePage(999)).rejects.toThrow();
    });

    test('should handle deletion of page with dependencies', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ error: 'Page has dependencies' }),
      });

      await expect(deletePage(1)).rejects.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle network timeouts', async () => {
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(getSession('session-123')).rejects.toThrow('Request timeout');
    });

    test('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      await expect(getSession('session-123')).rejects.toThrow('Invalid JSON');
    });

    test('should handle server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal server error' }),
      });

      await expect(getSession('session-123')).rejects.toThrow();
    });
  });

  describe('Authentication', () => {
    test('should handle unauthorized requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ error: 'Unauthorized' }),
      });

      await expect(getSession('session-123')).rejects.toThrow();
    });

    test('should include credentials in all requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      await getSession('session-123');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          credentials: 'include',
        })
      );
    });
  });
});
