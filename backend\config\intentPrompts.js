// intentPrompts.js
// Generates prompts for the Intent Generation API

/**
 * Generates a prompt for the LLM based on extracted elements.
 * @param {Array} elements
 * @returns {string} Prompt string
 */
function getIntentPrompt(elements) {
  if (!elements || !elements.length) return 'No interactive elements found in the HTML.';
  let prompt = 'Analyze the following UI elements and generate user intent for each:\n';
  elements.forEach((el, idx) => {
    prompt += `\n${idx + 1}. Selector: ${el.selector}, Tag: ${el.tagName}, Text: "${el.text}"`;
  });
  prompt += '\nReturn a JSON array of intents for each selector.';
  return prompt;
}

module.exports = { getIntentPrompt };
