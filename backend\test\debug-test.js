const service = require('../services/llmServiceV3');

const sampleHTML = `
<div id="app">
  <header class="bg-blue-600">
    <nav class="nav">
      <h1>Dashboard</h1>
      <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
      </ul>
    </nav>
  </header>
  <main>
    <div id="chart-container">Chart here</div>
  </main>
</div>
`;

console.log('🔍 Debugging Fragment Extraction for h1:\n');

// Test h1 extraction manually
console.log('HTML content:');
console.log(sampleHTML);
console.log('\nLooking for h1 tag...');

const h1Regex = /<h1[^>]*>.*?<\/h1>/gis;
const h1Match = sampleHTML.match(h1Regex);
console.log('h1 regex match:', h1Match);

// Test the service method
console.log('\nTesting service.extractFragment("h1"):');
const fragment = service.extractFragment(sampleHTML, 'h1');
console.log('Result:', fragment);

console.log('\n🎯 Debugging Title Pattern:\n');

const titlePrompt = "change the title to Admin Panel";
console.log(`Testing prompt: "${titlePrompt}"`);

const titlePattern = /change.*text.*to|update.*text|text.*to|change.*title.*to|update.*title|title.*to/i;
console.log('Pattern test result:', titlePattern.test(titlePrompt));

// Test each part of the pattern
const patterns = [
  /change.*text.*to/i,
  /update.*text/i, 
  /text.*to/i,
  /change.*title.*to/i,
  /update.*title/i,
  /title.*to/i
];

patterns.forEach((pattern, i) => {
  console.log(`Pattern ${i+1} (${pattern}):`, pattern.test(titlePrompt));
});

console.log('\n🧪 Testing analyzePromptIntent:');
service.analyzePromptIntent(titlePrompt, sampleHTML).then(result => {
  console.log('Result:', JSON.stringify(result, null, 2));
}).catch(err => {
  console.error('Error:', err.message);
});
