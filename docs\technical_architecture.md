# JustPrototype Technical Architecture

This document provides a detailed overview of the JustPrototype application's technical architecture, focusing on the prompt generation and saving flow.

## System Overview

JustPrototype is a web application that allows users to generate prototypes using LLM (Large Language Model) technology. The application consists of:

1. **Frontend**: React-based UI hosted in the `/ui` directory
2. **Backend**: Node.js/Express server hosted in the `/backend` directory
3. **Database**: PostgreSQL database for storing user data, prompts, and usage logs

## Component Architecture

### Frontend Components

- **React UI**: User interface for entering prompts and viewing generated content
- **API Client**: Handles communication with the backend API
- **State Management**: Manages application state and user session

### Backend Components

- **Express Server**: Handles HTTP requests and routes
- **Controllers**: Process requests and coordinate between services
  - `llmController.js`: Handles LLM-related operations
  - `prototypeController.js`: Manages prototype operations
- **Services**: Provide business logic and external integrations
  - `llmService.js`: Communicates with LLM providers
  - `promptDbService.js`: Handles database operations for prompts
  - `billingService.js`: Manages token usage and billing
  - `llmHelpers.js`: Provides utility functions for LLM operations
- **Database**: PostgreSQL database with tables for users, prompts, iterations, and usage logs

## Prompt System

### Prompt Rules and Constraints

The system enforces strict rules for code generation through comprehensive prompts:

1. **HTML Structure Rules**
   - Proper tag closure and nesting
   - Correct attribute spacing (no extra spaces)
   - Complete heading tags (prevent malformed tags)
   - Clean formatting and indentation
   - No content outside main app div

2. **JavaScript Standards**
   - Script tag requirements (`<script data-exec="inline">`)
   - String handling (single vs double quotes)
   - Required error handling (try-catch blocks)
   - Proper semicolon usage
   - String concatenation rules

3. **Chart Configuration Rules**
   - Direct color value syntax (`color: '#0d9488'`)
   - Required error handling blocks
   - Consistent option formatting
   - Standard patterns for line, bar, and pie charts

These rules are enforced through the system prompt in `promptUtils.js` to ensure consistent, high-quality code generation.

## Data Flow

### Prompt Generation Flow

1. User enters a prompt in the UI
2. Frontend sends a POST request to `/api/llm/plan` with the prompt
3. `llmController.js` receives the request and calls `llmService.js`
4. `llmService.js` sends the prompt to the LLM provider and receives the response
5. `llmController.js` logs token usage via `logTokenUsage` in `llmHelpers.js`
6. `llmController.js` saves the prompt and its first iteration via `savePromptAndIteration` in `llmHelpers.js`
7. After successful code generation, the system automatically:
   - Creates a new prototype with a timestamp-based title
   - Uses the plan as the prototype description
   - Links the prototype to the original prompt
   - Saves the generated code as HTML content
8. The response (including prototype info) is sent back to the frontend
9. Frontend updates the UI with the generated content and prototype details

### Streaming vs Non-Streaming

The application supports both streaming and non-streaming responses:

- **Streaming**: Response is sent in chunks as it's generated
  - Sets up streaming headers
  - Captures streamed content for logging
  - Logs token usage and saves prompt after streaming completes
  
- **Non-Streaming**: Complete response is sent at once
  - Waits for complete response from LLM
  - Logs token usage and saves prompt
  - Returns complete response

## Database Schema

### Users Table
- `id`: Primary key
- `email`: User's email
- `display_name`: User's display name
- `google_id`: Google ID for OAuth
- `quota_tokens`: Token quota
- `token_usage`: Tokens used
- `prototype_count`: Number of prototypes created
- `created_at`: Timestamp

### Prompts Table
- `id`: Primary key
- `user_id`: Foreign key to users table
- `prompt_text`: The prompt text
- `created_at`: Timestamp

### Prompt_Iterations Table
- `id`: Primary key
- `prompt_id`: Foreign key to prompts table
- `iteration_number`: Iteration number
- `input_text`: Input text for this iteration
- `output_text`: Output text for this iteration
- `created_at`: Timestamp

### Usage_Log Table
- `id`: Primary key
- `user_id`: Foreign key to users table
- `event`: Event type (e.g., 'llm_plan', 'llm_code')
- `tokens_used`: Number of tokens used
- `context`: Context information
- `details`: JSON details
- `created_at`: Timestamp

## Key Functions

### llmHelpers.js

#### logTokenUsage
```javascript
async function logTokenUsage(req, event, inputText, outputText, context, isStreaming = false)
```
- Logs token usage for a user
- Parameters:
  - `req`: Express request object
  - `event`: Event type (e.g., 'llm_plan', 'llm_code')
  - `inputText`: Input text
  - `outputText`: Output text
  - `context`: Context information
  - `isStreaming`: Whether this is a streaming request
- Returns: Boolean indicating success

#### savePromptAndIteration
```javascript
async function savePromptAndIteration(req, promptText, outputText, iterationNumber = 1)
```
- Saves a prompt and its first iteration
- Parameters:
  - `req`: Express request object
  - `promptText`: Prompt text
  - `outputText`: Output text
  - `iterationNumber`: Iteration number (default: 1)
- Returns: Prompt ID or null if failed

#### savePromptIteration
```javascript
async function savePromptIteration(promptId, inputText, outputText, iterationNumber)
```
- Saves a prompt iteration
- Parameters:
  - `promptId`: Prompt ID
  - `inputText`: Input text
  - `outputText`: Output text
  - `iterationNumber`: Iteration number
- Returns: Iteration ID or null if failed

#### setupStreamingResponse
```javascript
function setupStreamingResponse(res, contentType = 'text/plain')
```
- Sets up streaming response
- Parameters:
  - `res`: Express response object
  - `contentType`: Content type (default: 'text/plain')
- Returns: Function to retrieve streamed content

### llmController.js

#### generatePlan
```javascript
async function generatePlan(req, res)
```
- Generates a feature plan from a prompt
- Handles both streaming and non-streaming responses
- Logs token usage and saves prompt

#### generateCode
```javascript
async function generateCode(req, res)
```
- Generates code from a plan
- Handles both streaming and non-streaming responses
- Logs token usage and saves prompt iteration if promptId is provided
- Implements automatic prototype saving:
  - Creates a new prototype with timestamp-based title
  - Uses plan as description
  - Links to original prompt via prompt_id
  - Returns prototype info in response
  - Continues normal flow if auto-save fails

## Error Handling

The application implements robust error handling:

1. **Token Usage Logging**: Errors are caught and logged but don't block the application
2. **Prompt Saving**: Errors are caught and logged, with appropriate fallbacks
3. **LLM Service**: Errors from external APIs are properly handled and reported
4. **Database Operations**: Database errors are caught and logged with specific error codes

## LLM V2 Implementation

The V2 implementation introduces an improved architecture using LangChain.js, featuring enhanced streaming capabilities and better separation of concerns.

### New Architecture Components

1. **LLM Factory (`services/llm/LLMFactory.js`)**
   - Provider-agnostic factory pattern
   - Zero-temperature deterministic responses
   - Configurable through environment variables
   - Supports OpenAI and Anthropic models

2. **SSE Handling (`services/sse/`)**
   - `SSECallbackHandler.js`: Manages SSE events
   - `StreamCaptureMiddleware.js`: Captures tokens for logging
   - Three event types: start, data, end
   - Improved error handling and reporting

3. **System Prompts (`services/llm/systemPrompts.js`)**
   - Centralized prompt management
   - Consistent instructions across operations
   - Domain-specific configurations
   - Easy maintenance and updates

4. **Main Service (`services/llmServiceV2.js`)**
   - Orchestrates all components
   - Uses LangChain.js for LLM interactions
   - Handles streaming and provider selection
   - Clear separation of concerns

### New API Endpoints

All V2 endpoints are accessible at `/api/llm/v2/*` and coexist with the original endpoints:

1. **Plan Generation**: `/api/llm/v2/plan`
2. **Code Generation**: `/api/llm/v2/generate`
3. **Element Modification**: `/api/llm/v2/modify-element`
4. **Content Modification**: `/api/llm/v2/modify-content`

### Integration with Existing System

The V2 implementation:
1. Uses the same database schema and token logging
2. Maintains compatibility with existing frontend
3. Enhances streaming capabilities
4. Improves error handling and reporting
5. Adds provider flexibility

### Key Improvements

1. **Streaming**
   - Real-time token streaming
   - Better progress indication
   - Improved error reporting
   - Efficient token capture

2. **Provider Management**
   - Easy provider switching
   - Consistent configuration
   - Simple provider addition
   - Environment-based defaults

3. **Code Organization**
   - Clear module boundaries
   - Improved testability
   - Better maintainability
   - Simplified extensions

4. **Error Handling**
   - Structured SSE errors
   - Detailed error events
   - Clean error recovery
   - Consistent error format

## Performance Considerations

1. **Timeouts**: Prompt saving operations have timeouts to prevent hanging
2. **Streaming**: Streaming responses improve perceived performance
3. **Error Recovery**: The application continues to function even if non-critical operations fail
4. **SSE Efficiency**: V2 implementation uses efficient SSE handling for real-time updates
5. **Provider Optimization**: Zero-temperature setting ensures consistent, fast responses
