import { UINode } from './uiNode';

export interface SSEEventData<T = any> extends Event {
  data: string;  // JSON string that will be parsed to type T
}

export interface StartEventData {
  message: string;
}

export interface TokenEventData {
  text: string;
}

export interface ErrorEventData {
  error: string;
}

export interface EndEventData {
  message: string;
  snapshotId: string;
  ast: UINode;
}

export type StartEvent = SSEEventData<StartEventData>;
export type TokenEvent = SSEEventData<TokenEventData>;
export type ErrorEvent = SSEEventData<ErrorEventData>;
export type EndEvent = SSEEventData<EndEventData>;
