// Script to check if required database functions exist
const { pool } = require('./services/promptDbService');

async function checkDatabaseFunctions() {
  console.log('Checking database functions...');
  
  try {
    // Check if the database is accessible
    const pingResult = await pool.query('SELECT 1 as ping');
    console.log(`Database connection: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    
    // Check if tables exist
    const tables = ['users', 'prompts', 'prompt_iterations', 'prototypes', 'usage_log'];
    for (const table of tables) {
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        ) as exists
      `, [table]);
      
      console.log(`Table '${table}' exists: ${tableCheck.rows[0].exists}`);
    }
    
    // Check if functions exist
    const functions = ['use_tokens_and_log', 'increment_prototype_count', 'get_user_quota'];
    for (const func of functions) {
      const funcCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM pg_proc
          WHERE proname = $1
        ) as exists
      `, [func]);
      
      console.log(`Function '${func}' exists: ${funcCheck.rows[0].exists}`);
    }
    
    // Check if the prompts table has the correct structure
    try {
      const promptsColumns = await pool.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'prompts'
      `);
      
      console.log('Prompts table columns:');
      promptsColumns.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type})`);
      });
    } catch (error) {
      console.error('Error checking prompts table structure:', error.message);
    }
    
    // Check if the usage_log table has the correct structure
    try {
      const usageLogColumns = await pool.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'usage_log'
      `);
      
      console.log('Usage_log table columns:');
      usageLogColumns.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type})`);
      });
    } catch (error) {
      console.error('Error checking usage_log table structure:', error.message);
    }
    
    // Check if the use_tokens_and_log function has the correct signature
    try {
      const funcSignature = await pool.query(`
        SELECT pg_get_functiondef(oid) as definition
        FROM pg_proc
        WHERE proname = 'use_tokens_and_log'
      `);
      
      if (funcSignature.rows.length > 0) {
        console.log('Function use_tokens_and_log definition:');
        console.log(funcSignature.rows[0].definition);
      } else {
        console.log('Function use_tokens_and_log not found');
      }
    } catch (error) {
      console.error('Error checking use_tokens_and_log function:', error.message);
    }
    
    // Try to execute the use_tokens_and_log function with test data
    try {
      console.log('Testing use_tokens_and_log function...');
      const testResult = await pool.query(`
        SELECT use_tokens_and_log(
          (SELECT id FROM users LIMIT 1), -- Get a valid user ID
          10, -- tokens used
          'test_event', -- event name
          'Test context', -- context
          '{"test": "data"}' -- details
        ) as success
      `);
      
      console.log(`Test result: ${testResult.rows[0].success}`);
    } catch (error) {
      console.error('Error testing use_tokens_and_log function:', error.message);
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the check
checkDatabaseFunctions().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
