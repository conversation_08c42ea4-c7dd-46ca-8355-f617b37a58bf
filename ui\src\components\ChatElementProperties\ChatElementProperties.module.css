.chatPropertiesContainer {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 18px;
  border-bottom: 1px solid #eee;
  background-color: #f9fafb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.elementInfo {
  display: flex;
  align-items: center;
}

.elementTag {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tagIcon {
  width: 16px;
  height: 16px;
  border: 1px dashed #999;
  border-radius: 2px;
}

.tagName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.headerActions {
  display: flex;
  gap: 8px;
}

.actionButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
}

.actionButton:hover {
  background-color: #e0e0e0;
  color: #333;
}

.closeButton {
  color: #666;
  background-color: #f0f0f0;
  padding: 6px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: #e74c3c;
  color: white;
}

.elementsContainer {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
}

.elementSection {
  padding: 12px 16px;
}

.propertiesContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.propertyRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.propertyGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.propertyInputGroup {
  display: flex;
  gap: 8px;
}

.propertyInputWithIcon {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
  padding: 4px 8px;
  flex: 1;
}

.directionIcon {
  color: #666;
  width: 16px;
  height: 16px;
}

.propertyLabel {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.textInput,
.numberInput,
.colorText {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  width: 100px;
  text-align: right;
}

.selectInput {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  width: 100px;
  text-align: center;
}

.textareaInput {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  width: 100%;
  resize: vertical;
  min-height: 60px;
}

.textInput:focus,
.numberInput:focus,
.selectInput:focus,
.colorText:focus,
.textareaInput:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.colorInputContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100px;
  position: relative;
}

.colorPreview {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #ddd;
  cursor: pointer;
}

.colorText {
  flex: 1;
  cursor: pointer;
}

.colorPickerBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
}

.colorPickerWrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.numberInputContainer {
  display: flex;
  align-items: center;
  width: 100px;
}

.numberInput {
  width: 100%;
  text-align: right;
  border: none;
  background: transparent;
  padding: 4px;
  font-size: 13px;
  color: #333;
}

.numberInput:focus {
  outline: none;
}

.unitLabel {
  color: #666;
  font-size: 12px;
  margin-left: 4px;
  width: 20px;
}

.advancedSection {
  margin-top: 16px;
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.advancedHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 0;
}

.advancedTitle {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.advancedContent {
  margin-top: 12px;
}

.cssTextarea {
  width: 100%;
  min-height: 80px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  resize: vertical;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #eee;
  background-color: #f8f8f6;
}

.askAIButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  margin-top: 8px;
}

.askAIButton:hover {
  background-color: #f0f0f0;
}

.generateFunctionalityButton {
  background-color: #fff8e1;
  border-color: #ffca28;
  color: #f57c00;
}

.generateFunctionalityButton:hover {
  background-color: #fff3cd;
}

.noElementsMessage {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}
